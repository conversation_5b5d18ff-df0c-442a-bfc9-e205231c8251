<?php

//stampa del registro degli esami per ogni singolo studente
$data_stampa = $data_Day . "/" . $data_Month . "/" . $data_Year;
if($studente['esito_terza_media'] == 'SI')
{
	$dati_classe = estrai_classi_studente((int) $id_stud);

    $mat_commissioni = estrai_classi_sottocommissioni_commissione_medie((int) $studente['id_classe']);
    foreach($mat_commissioni as $commissione)
    {
        if($commissione['id_classe'] == $commissione['id_classe_commissione_medie'])
        {
            $abbinamenti_commissari = estrai_abbinamenti_commissari((int) $commissione['id_commissione']);
            foreach($abbinamenti_commissari['esterni'] as $singolo_abbinamento)
            {

                if($singolo_abbinamento['dati_commissario']['ruolo_commissario_est'] == 'P')
                {
                    $nome_presidente = $singolo_abbinamento['nome']
                                       . ' ' .
                                       $singolo_abbinamento['cognome'];
                }
            }
        }
    }
	$esito = '';

	if($studente['giudizio_sintetico_esame_terza_media'] >= 6)
	{
		$esito = "Ha superato l'esame di Stato";
	}
	else
	{
		$esito = "non ha superato l'esame di Stato";
	}

	$grado_preparazione = '';
	switch($studente['giudizio_finale_1_medie'])
	{
		//{{{ <editor-fold defaultstate="collapsed" desc="Grado preparazione">
		case 'accettabile':
			$grado_preparazione = 'accettabile';
			break;
		case 'abb_sodd':
			$grado_preparazione = 'abbastanza soddisfacente';
			break;
		case 'soddisfacente':
			$grado_preparazione = 'soddisfacente';
			break;
		case 'm_sodd':
			$grado_preparazione = 'molto soddisfacente';
			break;
		default:
			$grado_preparazione = '';
			break;
		//}}} </editor-fold>
	}

	$capacita = '';
	switch($studente['giudizio_finale_2_medie'])
	{
		//{{{ <editor-fold defaultstate="collapsed" desc="capacità">
		case 'scarsa':
			$capacita = 'scarsa';
			break;
		case 'quasi_suff':
			$capacita = 'quasi sufficiente';
			break;
		case 'sufficiente':
			$capacita = 'sufficiente';
			break;
		case 'buona':
			$capacita = 'buona';
			break;
		case 'molto_buona':
			$capacita = 'molto_buona';
			break;
		default:
			$capacita = '';
			break;
		//}}} </editor-fold>
	}

	$livello_maturazione = '';
	switch($studente['giudizio_finale_3_medie'])
	{
		//{{{ <editor-fold defaultstate="collapsed" desc="Livello maturazione globale">
		case 'p_suff':
			$livello_maturazione = 'pienamente sufficiente';
			break;
		case 'positivo':
			$livello_maturazione = 'positivo';
		break;
        case 'm_positivo':
            $livello_maturazione = 'molto positivo';
            break;
        case 'adeguato':
			$livello_maturazione = 'adeguato ai livelli di partenza';
			break;
		case 'n_d_a':
			$livello_maturazione = 'non del tutto adeguato';
			break;
		default:
			$livello_maturazione = '';
			break;
		//}}} </editor-fold>
	}

	$consiglio_orientativo = '';
	switch($studente['consiglio_terza_media'])
	{
		//{{{ <editor-fold defaultstate="collapsed" desc="Consiglio orientativo">
		case 'prof':
            $consiglio_orientativo = 'un istituto professionale';
        break;
        case 'itis':
            $consiglio_orientativo = 'un istituto tecnico';
        break;
        case 'liceo':
            $consiglio_orientativo = 'un liceo';
        break;
        case 'profess':
            $consiglio_orientativo = 'un corso di formazione professionale';
        break;
		case 'lic_cla':
            $consiglio_orientativo = 'Liceo classico';
        break;
        case 'lic_art':
            $consiglio_orientativo = 'Liceo artistico';
        break;
        case 'lic_mus':
            $consiglio_orientativo = 'Liceo musicale';
        break;
        case 'lic_sci_ling':
            $consiglio_orientativo = 'Liceo scientifico e linguistico';
        break;
		case 'lic_su':
            $consiglio_orientativo = 'Liceo scienze umane e scientifico';
        break;
        case 'ist_tec':
            $consiglio_orientativo = 'Istituzione scolastica di istruzione tecnica';
        break;
        case 'tec_prof':
            $consiglio_orientativo = 'Istituto tecnico e professionale regionale';
        break;
        case 'lic_tec':
            $consiglio_orientativo = 'Istituzione scolastica di istruzione liceale e tecnica';
        break;
	    case 'prof_agr':
            $consiglio_orientativo = 'Istituto tecnico professionale agrario';
        break;
        case 'prof_alb':
            $consiglio_orientativo = 'Istituto professionale regionale alberghiero';
        break;
		case 'prof_indru':
            $consiglio_orientativo = 'Istituto professionale industria e artigianato';
        break;
        default:
            $consiglio_orientativo = '';
        break;
		//}}} </editor-fold>
	}

    $consiglio_orientativo_trentino = [];
    $valori_consiglio_orientativo = estrai_valori_consiglio_orientativo_trentino($studente['consiglio_orientativo_trentino']);
    foreach ($valori_consiglio_orientativo as $valore_singolo)
    {
        $consiglio_orientativo_trentino[] = $valore_singolo['descrizione'];
    }
    $testo_consiglio_orientativo =  implode(', ', $consiglio_orientativo_trentino);

    $lode = stripos($studente['giudizio_sintetico_esame_terza_media'], 'lode') !== false ? true : false;
    $esame_voto_num = filter_var($studente['giudizio_sintetico_esame_terza_media'], FILTER_SANITIZE_NUMBER_INT);
    $voto_esame_stampa = traduci_numero_in_lettere($esame_voto_num)."/Decimi" . ($lode ? ', con Lode' : '');


    $param_consigli = [
            'anno_scolastico' => str_replace('/', '_', estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE'))
        ];
    $elenco_consigli = nextapi_call('/school/structure/consiglio_orientativo', 'GET', $param_consigli, $current_key);
    foreach($elenco_consigli as $consiglio){
        if ($consiglio['id_consiglio_orientativo_template'] == $studente['id_consiglio_orientativo']){
            $testo_consiglio_orientativo = $consiglio['descrizione'];
        }
    }



	//{{{ <editor-fold defaultstate="collapsed" desc="Pagina unica">
//	$pdf->AddPage('L');
//	$pdf->SetAutoPageBreak("off",1);
//	$pdf->SetFillColor(200);
//
//	$pdf->SetXY(10, 10);
//	$pdf->SetFont('Times','', 16);
//	$pdf->CellFitScale(110, 20, "GENERALITA' DEL CANDIDATO",1,0,'C',0);
//	$pdf->CellFitScale(160, 20, "RISULTATO DELL'ESAME DI LICENZA",1,1,'C',0);
//	$y_corrente_base = $pdf->GetY();
//
//	//riga 1
//	$y_base = $pdf->GetY();
//	$pdf->SetFont('Times','B',$dimensione_font);
	if($numero_registro_generale > 0)
	{
		//stampo "numero registro generale" progressivo
		$stringa_1 = 'N.' . $numero_registro_generale;
		$numero_registro_generale++;
	}
	else
	{
		//stampo "numero registro generale" vuoto
		$stringa_1 = 'N.' . $studente["registro"];
	}
//    $stringa_2 = 'Classe ' . $dati_classe[0]['classe'] . ' Sezione ' . $dati_classe[0]['sezione'];
//
//	$pdf->CellFitScale(110, 10, $stringa_1,'RL',1,'L');
//	$pdf->CellFitScale(110, 10, $stringa_2,'RL',1,'L');
//	$pdf->CellFitScale(110, 10, '','RL',1,'L');
//	$pdf->CellFitScale(110, 8, $studente['cognome'],'RL',1,'L');
//	$pdf->SetFont('Times','',8);
//	$pdf->CellFitScale(110, 5, 'COGNOME','RL',1,'L');
//	$pdf->CellFitScale(110, 10, '','RL',1,'L');
//	$pdf->SetFont('Times','B',$dimensione_font);
//	$pdf->CellFitScale(110, 8, $studente['nome'],'RL',1,'L');
//	$pdf->SetFont('Times','',8);
//	$pdf->CellFitScale(110, 5, 'NOME','RL',1,'L');
//	$pdf->CellFitScale(110, 10, '','RL',1,'L');
//	$pdf->SetFont('Times','B',$dimensione_font);
//
//	$pdf->CellFitScale(110, 10, 'Nato a: ' . $studente['descrizione_comune_nascita_array']['descrizione'] . '(' . $studente['descrizione_comune_nascita_array']['codice'] . ')','RL',1,'L');
//	$pdf->CellFitScale(110, 10, 'Il ' . $studente['data_nascita_ext'],'RL',1,'L');
//	if($studente['mat_esito']['tipo_studente'] != 'Privatista')
//	{
//		$pdf->CellFitScale(110, 10, 'Proveniente da questa scuola','RL',1,'L');
//		$pdf->CellFitScale(110, 10, 'Fornito di ammissione in seguito a scrutinio','RL',1,'L');
//	}
//	else
//	{
//		$pdf->CellFitScale(110, 10, '','RL',1,'L');
//		$pdf->CellFitScale(110, 10, '','RL',1,'L');
//	}
//
//    $pdf->CellFitScale(110, 10, 'Residente a: ' . $studente['descrizione_comune_residenza']['descrizione'] . ' (' . $studente['descrizione_comune_residenza']['codice'] . ')','RL',1,'L');
//    $pdf->CellFitScale(110, 10, $studente['indirizzo_residenza'],'RL',1,'L');
//    $pdf->CellFitScale(110, 9, 'Lingua straniera: ','RL',1,'L');
//	$pdf->SetFont('Times','',8);
//
//    $arr_lingue = $studente['lingue_straniere'];
//    $n_lang = 0;
//    foreach ($arr_lingue as $singola_lingua){
//        if($singola_lingua !=''){
//            $pdf->CellFitScale(110, 5, '  ' . $singola_lingua,'RL',1,'L');
//            $n_lang += 5;
//        }
//    }
//
//
//    $pdf->CellFitScale(110, 25 - $n_lang, '','BRL',0,'L');
//
//  	$pdf->SetFont('Times','',$dimensione_font);
//    $x_base = 120;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, 'Giudizio complessivo sul livello di maturazione raggiunto dal candidato (2)','RL',0,'L');
//    $pdf->SetXY(130,$y_base);
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,8, '','RL',0,'L');
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, "Dall'andamento del triennio, dalle prove scritte e dal colloquio pluridisciplinare è ",'RL',1,'L');
//	$y_base += 7;
//	$pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, "accertato che il candidato ha conseguito un grado di preparazione complessivo: " . $grado_preparazione,'RL',0,'L');
//    $y_base += 10;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, "e che la capacità di organizzazione e di rielaborazione delle conoscenze acquisite è: " . $capacita ,'RL',0,'L');
//    $y_base += 10;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, 'Il livello di maturazione globale risulta: ' . $livello_maturazione,'RL',0,'L');
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, '','RL',0,'L');
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, '','RL',0,'L');
//
//    $y_base += 10;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, 'Il presidente sulla base del giudizio della commissione, dichiara che il candidato è stato (3)','RL',0,'L');
//    $pdf->SetXY(130,$y_base);
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->SetFont('Times','B',$dimensione_font);
//	$pdf->CellFitScale(160,10, $esito,'RL',0,'L');
//	$pdf->SetFont('Times','',$dimensione_font);
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, 'Con la valutazione di ' .$studente['giudizio_sintetico_esame_terza_media'] ,'RL',0,'L');
//
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, 'Consiglio orientativo : ' .$consiglio_orientativo,'RL',0,'L');
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,11, 'NOTE : .....................................................................................................................................','RL',0,'L');
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,11, '........................, ........................................','RL',0,'L');
//	$y_base += 11;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,11, '','RL',0,'L');
//
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(80,10, $dati_classe[0]['descrizione_comuni'].', ...............','L',0,'L');
//	$pdf->CellFitScale(80,10, 'IL PRESIDENTE DELLA COMMISSIONE','R',0,'C');
//    $y_base += 10;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(80,10, '','L',0,'L');
//	$pdf->CellFitScale(80,10, $nome_presidente,'R',0,'C');
//
//    $y_base += 10;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, '','RL',0,'L');
//
//   	$pdf->SetFont('Times','',8);
//    $y_base += 8;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, '(2)   Il giudizio complessivo, positivo o negativo, viene comunicato per iscritto a richiesta degli interessati','RL',0,'L');
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, '(3)   Licenziato / Non Licenziato','RL',0,'L');
//    $y_base += 7;
//    $pdf->SetXY($x_base,$y_base);
//	$pdf->CellFitScale(160,10, '(4)   Valutazione espressa in decimi','RLB',0,'L');
 	//}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina unica">

    $labels = [
        'candidato'  => 'Il Presidente, sulla base del giudizio della Commissione dichiara che ||min_illa|| candidat||min_oa|| ',
        'giudizio'  => 'Motivato giudizio complessivo sul grado di formazione e del livello globale di maturazione raggiunto da||min_llla|| candidat||min_oa||:'

    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $min_illa = "la";
        $min_llla = "lla";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $min_llla = "l";
        $min_illa = "il";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||min_illa||", $min_illa, $labels[$k]);
        $labels[$k] = str_replace("||min_llla||", $min_llla, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }

    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

	$pdf->AddPage('P');
	$pdf->SetAutoPageBreak("off",1);
	$pdf->SetFillColor(200);

	$pdf->SetXY(10, 25);
    inserisci_intestazione_pdf($pdf, $dati_classe[0]['id_classe']);
	$pdf->SetFont('Times','', $dimensione_font);
    $pdf->writeHTMLCell( 120, 0, '','','Classe: <b>' . $dati_classe[0]['classe'] . $dati_classe[0]['sezione'].'</b>', 0, 0, false, true, 'L');
    $pdf->writeHTMLCell( 0, 0, '','','Anno Scolastico <b>' . $anno_scolastico_attuale . '</b>', 0, 1, false, true, 'C');
    if ($stampa_num_protocollo == 'SI')
    {
        $pdf->ln(3);
        $pdf->writeHTMLCell( 0, 0, '','','Protocollo n. _____________', 0, 1, false, true, 'L');
        $pdf->ln(5);
    } else {
        $pdf->ln(9);
    }
    $pdf->writeHTMLCell( 0, 0, '','','<b>'.$stringa_1.'. '.$studente['cognome'].' '.$studente['nome'].'</b>' , 0, 1, false, true, 'L');
    $pdf->writeHTMLCell( 0, 0, '','', 'Luogo e data di nascita: '.$studente['descrizione_comune_nascita_array']['descrizione'] . ' (' . $studente['descrizione_comune_nascita_array']['codice'] . ')'
            .' il '. $studente['data_nascita_ext'], 0, 1, false, true, 'L');
    $pdf->writeHTMLCell( 0, 0, '','',  'Residenza: '.$studente['descrizione_comune_residenza']['descrizione']. ' (' . $studente['descrizione_comune_residenza']['codice'] . ') - '.$studente['indirizzo'], 0, 1, false, true, 'L');

    if ($stampa_sezione_lingue == 'SI')
    {
        $pdf->CellFitScale(110, 9, 'Lingua comunitaria: ',0,1,'L');
        $pdf->SetFont('Times','',9);

        $arr_lingue = $studente['lingue_straniere'];
        foreach ($arr_lingue as $singola_lingua){
            if($singola_lingua !=''){
                $pdf->CellFitScale(110, 5, '  ' . $singola_lingua,0,1,'L');
            }
        }
    }

    if ($stampa_sezione_voto_ammissione == 'SI')
    {
        $pdf->CellFitScale(110, 9, "Voto di ammissione all'esame: {$studente['voto_ammissione_medie']}",0,1,'L');
    }

    $pdf->ln(13);
	$pdf->SetFont('Times','B', $dimensione_font+4);

	$pdf->CellFitScale(0, 12, "RISULTATO DELL'ESAME DI STATO",0,1,'C',0);
	$pdf->CellFitScale(0, 12, "CONCLUSIVO DEL PRIMO CICLO DI ISTRUZIONE",0,1,'C',0);
    $pdf->ln(15);
	$pdf->SetFont('Times','', $dimensione_font);
    $pdf->writeHTMLCell( 0, 0, '','',$labels['candidato'].' <br><b>'.$studente['cognome'].' '.$studente['nome'].'</b>', 0, 1, false, true, 'C');
    $pdf->ln(5);
//	$pdf->CellFitScale(0, 0, "ha superato esame",0,1,'C',0);
	$pdf->CellFitScale(0, 0, $esito,0,1,'C',0);
    $pdf->ln(5);
	$pdf->CellFitScale(0, 0, "con il seguente VOTO: ".$voto_esame_stampa,0,1,'C',0);
    $pdf->ln(11);
	$pdf->SetFont('Times','B', $dimensione_font);
    $pdf->writeHTMLCell( 0, 0, '','',$labels['giudizio'], 0, 1, false, true, 'L');
	$pdf->SetFont('Times','', $dimensione_font);
    $pdf->writeHTMLCell( 0, 0, '','',$studente['giudizio_descrittivo_finale_esame_terza_media'], 0, 1, false, true, 'L'); 
    $pdf->ln(5);
    $pdf->writeHTMLCell( 0, 0, '','','<b>CONSIGLIO ORIENTATIVO:</b> '.$testo_consiglio_orientativo, 0, 1, false, true, 'L');
//    $pdf->writeHTMLCell( 0, 0, '','','<b>INTEGRAZIONE CONSIGLIO ORIENTATIVO:</b> '.$consiglio_orientativo, 0, 1, false, true, 'L');

    $pdf->ln(20);
	$pdf->CellFitScale(110,0, $studente['descrizione_comuni'].', '. $data_stampa,0,0,'L');
    if ($stampa_firma_omessa == 'SI')
    {
        $pdf->MultiCell(80, 0, 'IL PRESIDENTE'."\n".$nome_presidente, 0, 'C', false, 1, '', '', true, 0, false, true, 0, 'M');
        $pdf->SetFont('Times', '', 7);
        $pdf->CellFitScale(110,0, '',0,0,'L');
        $pdf->CellFitScale(0, 2, "firma omessa ai sensi dell'art. 3, D.to Lgs. 12.02.1993, n. 39.", 0, 1, 'C');
    }
    else {
        $pdf->MultiCell(80, 30, 'IL PRESIDENTE'."\n".$nome_presidente, 'B', 'C', false, 1, '', '', true, 0, false, true, 30, 'M');
    }
}