<?php
/*
 * Tipo: Pagella Intermedia myschoolticino
 * Nome: ee_12345_generica_myschoolticino_01
 * Richie<PERSON> da: myschoolticino
 *
 * Materie particolari:
 * -
 * -
 */
/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'ee_12345_generica_myschoolticino_01', 'Documenti della scuola elementare My Kid', 1, 'ee_12345_generica_myschoolticino_01', 1);
 */

$orientamento = 'P';
if ($periodo_pagella == 'finale') {
    $periodo = 9;
} elseif ($periodo_pagella == 'intermedia') {
    $periodo = 7;
}

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $id_classe = $parametri_stampa['id_classe'];
    $data_att = "{$parametri_stampa['data_day']}/{$parametri_stampa['data_month']}/{$parametri_stampa['data_year']}";
    $periodo = $parametri_stampa['periodo'];
    $formato = $parametri_stampa['formato'];

    $id_studente = $studente['id_studente'];
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_pagella = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo, $studente['id_studente']);

    $luogo_nascita = $provincia_nascita = '';
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .=  ' (' . $stato['descrizione'] . ')';
            $provincia_nascita = ' ';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    $arr_voti = [];
    $professori = [];
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            && (
               !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA', 'RELIGIONE', 'SOSTEGNO', 'OPZIONALE'])
                )
            )
        {
            $arr_voti[$id_materia] = $voti_pagella[$id_materia];

            foreach ($materia['professori'] as $id_professore)
            {
                $professori[$id_professore] = $id_professore;
            }
        }

        // religione
        if ($materia['in_media_pagelle'] != 'NV' &&
                (
                    ($materia['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 0)
                    ||
                    ($materia['tipo_materia'] == 'ALTERNATIVA' && $studente['esonero_religione'] == 1)
                )
            )
        {
            $arr_voti[$id_materia] = $voti_pagella[$id_materia];

            foreach ($materia['professori'] as $id_professore)
            {
                $professori[$id_professore] = $id_professore;
            }
        }

        // condotta
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {
           $arr_voti[$id_materia] = $voti_pagella[$id_materia];

            foreach ($materia['professori'] as $id_professore)
            {
                $professori[$id_professore] = $id_professore;
            }
        }
    }
    $arr_voti_new = [];
    foreach ($arr_voti as $id_materia => $voto) {
        $includi = false;
        foreach ($voto['campi_liberi'] as $campo_libero) {
            if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) ) {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value != '') {
                    $includi = true;
                    break;
                }
            }
        }
        if ($includi) {
            $arr_voti_new[$id_materia] = $arr_voti[$id_materia];
        }
    }
    $arr_voti = $arr_voti_new;

    $arr_mat_to_tit = [
        "apllicazione" => "Sviluppo personale/personal development",
        "applicazione" => "Sviluppo personale/personal development",
        "condotta"     => "Rapporti sociali/social development",
        "studio dell'ambiente"   => "Studio dell’ambiente/Study of the environment",
        "lingua inglese" => "English/Lingua Inglese",
        "lingua francese" => "Lingua francese/French",
        "francese" => "Lingua francese/French",
        "educazione musicale" => "Educazione musicale/Music",
        "attivit&agrave; grafico pittoriche" => "Attività grafico-pittoriche e creative/Expressive Arts and Design",
        "attività grafico pittoriche" => "Attività grafico-pittoriche e creative/Expressive Arts and Design",
        "educazione fisica" => "Educazione fisica/Physical education",
        "religione" => "Educazione religiosa/ Religious education",
        "educazione religiosa" => "Educazione religiosa/ Religious education",

        "competenza personale"  => "Competenza personale, sociale e capacità di imparare ad imparare.
Personal and Social Competencies and Approaches to Learning.",
        "lingua italiana"   => "Area linguistica - Italiano
Literacy - Italian Language",
        "lingua inglese" => "Literacy - English Language
Area linguistica - Inglese",
        "lingua francese" => "Literacy - French Language
Area linguistica - Francese",
        "francese" => "Literacy - French Language
Area linguistica - Francese",
        "matematica"  => "Area matematica
Mathematics",
        "educazione cosmica"  => "Educazione cosmica (Metodo Montessori)
Cosmic Education (Montessori Philosophy)",
        "studio dell'ambiente"  => "Outdoor and Sustainability Education",
        "tecnologia"  => "Tecnologia
Technology",
        "arti visive"  => "Arti visive
Visual Arts",
        "educazione musicale"  => "Educazione musicale
Music Education",
        "educazione religiosa"  => "Educazione religiosa
Religious Education",
        "educazione motoria"  => "Educazione motoria
Physical Education",

        "Area linguistica - Italiano"   => "Area linguistica - Italiano
Literacy - Italian Language",
        "Area Linguistica - Inglese"  => "Area Linguistica - Inglese
Literacy - English Language",
        "Area linguistica - Francese"  => "Area linguistica - Francese
Literacy - French Language",
        "Area matematica"  => "Area matematica
Mathematics",
        "Educazione cosmica (Metodo Montessori)"  => "Educazione cosmica (Metodo Montessori)
Cosmic Education (Montessori Philosophy)",
        "Outdoor and Sustainability Education"  => "Outdoor and Sustainability Education",
        ""  => "",
        ""  => "",
        ""  => "",


    ];

    if ($periodo == 9) {
        $desc_periodo = 'Semestre II<br><i>Term 2</i>';
        $desc_doc = 'FINE ANNO';
        $desc_doc_eng = 'END OF YEAR';
    }
    else {
        $desc_periodo = 'Semestre I<br><i>Term 1</i>';
        $desc_doc = 'FINE SEMESTRE';
        $desc_doc_eng = 'MID TERM';
    }

    // Dizionario temporaneo
    $labels = [
        "pag_firme_1"    =>
"
Firma dei docenti/teachers’ signatures<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
<br>
Data/Date<br>
_______________________________________________________________________________________________<br>
<br>
<br>
Firma dell’autorità parentale/Signature of person exercising parental authority<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
<br>
Data/Date<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
<br>
",

        "pag_firme_2"    =>
"Firma dell’autorità parentale / Signature of person exercising parental authority<br>
<br>
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<br><br>
<br>
Data / Date . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .",

        "doc_ita"       => "<b>INDICAZIONI PER LA LETTURA DEI DOCUMENTI SCOLASTICI</b><br><br>
<b>1. Scheda personale descrittiva</b><br><br>
La scuola comunica alla famiglia la progressione degli apprendimenti dell'alunno consegnando due volte all'anno, a febbraio e a giugno, in formato digitale, tramite il portale MasterCom, una scheda personale descrittiva. Nella scheda viene esposta la valutazione descrittiva dello sviluppo delle competenze raggiunte dall'alunno per area curricolare da parte dei docenti responsabili.
<br><br>
La scheda personale descrittiva fa riferimento ai traguardi di apprendimento previsti dai piani disciplinari per il 1°ciclo e il 2° ciclo di scuola elementare secondo il Piano di Studi della scuola dell'obbligo del Canton Ticino. La valutazione delle competenze è articolata sulla base di progressivi livelli di padronanza, che forniscono una base per l'attribuzione a fine anno scolastico di una nota (voto) espressa nella valutazione certificativa.
<br><br>
Le famiglie sono tenute a dare conferma della presa visione dei documenti scolastici attraverso la riconsegna dell'apposito modulo in segreteria entro 15 giorni dalla pubblicazione delle schede.
<br><br><br>
<b>2. Scheda di valutazione certificativa numerica</b><br><br>
Al fine di rendere unitario il percorso e il sistema di valutazione con le scuole del territorio, a fine anno viene consegnata, oltre alla scheda descrittiva, una scheda di valutazione certificativa numerica, con attribuzione di note finali e la decisione sulla promozione dell'alunno.
<br><br>
\"Le note vanno dal 3 al 6; dove la nota 6 rappresenta il meglio, la nota 4 la sufficienza. È concesso l'uso dei mezzi punti\" (crf. Art. 22a del \"Regolamento delle scuole comunali\", 3 luglio 1996 - stato 1° agosto 2023), se si determina una situazione valutativa intermedia tra livelli.
<br><br>
\"La promozione al termine delle classi I, III e IV elementare può avvenire anche se gli allievi non hanno completamente raggiunto i traguardi stabiliti dal piano di studio. Il passaggio del ciclo, ovvero la promozione al termine delle classi II e V elementare, presuppone, di regola, il raggiungimento delle competenze fondamentali per la prosecuzione del percorso scolastico\" (crf. Ibidem \"Regolamento delle scuole comunali\", 3 luglio 1996 - stato 1° agosto 2023) .
<br><br><br>
<b>3. Conservazione dei documenti scolastici</b><br><br>
Le schede personali sono conservate nell'archivio digitale di MasterCom fintanto che l'account è attivo.
Una copia di backup è conservata nell'archivio scolastico.
Si suggerisce di scaricare la scheda personale per vostro archivio.",


        "doc_eng"       => "<b>INSTRUCTIONS</b><br><br>
<b>1. Descriptive personal profile</b><br><br>
The school informs the family of the pupil's learning progress by handing over a descriptive personal
<br><br>
profile twice a year, in February and June, in digital format, via the MasterCom portal. The report contains a descriptive assessment of the development of the pupil's skills for each curricular area by each subject teacher.
<br><br>
The descriptive personal  profile refers to the learning objectives set out in the subject plans for the 1st and 2nd cycle of Primary school according to the Canton Ticino's school curriculum for compulsory education. The assessment of competences is articulated on the basis of progressive levels of proficiency, which provide a basis for the assignment of a mark (grade) in the certified progress report at the end of the school year.
<br><br>
Families are required to confirm that they have read the school documents by returning the appropriate form to the secretary's office within 15 days of publication of the reports.
<br>
<br><br>
<b>2. Certified numerical progress report</b><br><br>

In order to make the course of studies and the assessment system uniform with the schools in the area, at the end of the year a certified numerical progress report is handed out, in addition to the descriptive personal profile, with the allocation of final marks and the decision on the pupil's promotion.<br>
<br>
\"Marks range from 3 to 6; where 6 is highest and 4 a pass. The use of half marks is allowed\" (crf. Art. 22a of the \"Municipal School Regulations\", 3 July 1996 - status 1 August 2023), if an intermediate assessment situation between levels is determined.<br>
<br>
\"Promotion at the end of Years I, III and IV can take place even if pupils have not completely reached the targets set in the curriculum. The advancement to the next cycle, i.e. promotion at the end of Primary classes II and V, implies, as a rule, the attainment of the fundamental competences in order progress in their school career\" (crf. Ibid \"Municipal School Regulations\", 3 July 1996 - status 1 August 2023).<br>
<br><br>
<b>3. Storage of school records</b><br><br>
Personal records are kept in the MasterCom digital archive as long as the account is active. A backup copy is kept in the school archive.<br>
We recommend that you download the personal file for your archive."
    ];

    if ($formato == 'A5') {
        $labels["pag_firme_2"] =
            "Firma dell’autorità parentale / Signature of person exercising parental authority<br>
<br>
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<br><br>
<br>
Data / Date . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .";
    }


    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }

    $cls_st = $studente['classe'];
    if ($studente['classe'] == 4) {
        if (in_array(strtoupper("{$studente['cognome']} {$studente['nome']}"),
                [
                    "ADRIANO FEDERICO",
                    "BELUSSI SAMUELE",
                    "DIAMANTIS GALLO ELIAN",
                    "FLACHSMANN JAMES",
                    "FOSSATI MAELLE",
                    "GODIO PIETRO",
                    "LONGHI AURORA",
                    "MARZORATI LUCA EMANUELE",
                    "SOMARUGA ALICE NOEMI",
                ])
            ) {
            $cls_st = 5;
            $studente['classe'] = 5;
        }
    }


    $firma_dirigente = $firma_docenti = '';
    switch ($studente['classe']) {
        case 1:
            $firma_docenti = '<img src="immagini_scuola/patrizia-lattuada.png" height="30" width="130"> <br>'
                . '<img src="immagini_scuola/elena-digonzelli.png" height="30" width="130">';
            break;
        case 2:$firma_docenti = '<img src="immagini_scuola/littlewood_suzanne.png" height="30" width="130"> <br>'
                . '<img src="immagini_scuola/barbara-simoncelli.png" height="30" width="130">';
            break;
        case 3:
            $firma_docenti = '<img src="immagini_scuola/rengifo_july.jpg" height="35" width="130"> <br>'
                . '<img src="immagini_scuola/jessica-caprari.png" height="30" width="130">';
            break;
        case 4:
        case 5:
            $firma_docenti = '<img src="immagini_scuola/casnati-claudia.png" height="30" width="130"> <br>'
                . '<img src="immagini_scuola/samantha-rae.png" height="30" width="130"> <br>'
                // . '<img src="immagini_scuola/cowan-arianna.png" height="30" width="130"> <br>'
                ;
            break;
    }
    $firma_dirigente = '<img src="immagini_scuola/micaela-mecocci.png" height="30" width="130">';


    // ordinamento diverso professori per classe 1SE
    if ($studente['classe'] == 1 && $studente['sezione'] == 'SE') {
        $prof_nths = array_keys($professori);

        $professori = array_slice($professori, 0, 1) +
                array( $prof_nths[2] => $professori[$prof_nths[2]] ) +
                array( $prof_nths[1] => $professori[$prof_nths[1]] ) +
                array_slice($professori, 2);
    }
    $stringa_docenti = '';
    foreach($professori as $prof) {
        $prof = estrai_utente($prof);
        if (stripos('utente', $prof['cognome'])===false && stripos('utente', $prof['nome'])===false
                && stripos('Terzaquarta', $prof['nome'])===false){
            $stringa_docenti[] = ucwords(strtolower("{$prof['cognome']} {$prof['nome']}"));
        }
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="pdf">
    $fnt = 'helvetica';
    $fd = ($formato == 'A4' ? 10 : 8);
    $y_pg = ($formato == 'A4' ? 40 : 25);
    $fd_1 = ($formato == 'A4' ? 14 : 9);
    $y_pg_1 = ($formato == 'A4' ? 70 : 40);
    $h = ($formato == 'A4' ? 30 : 15);
    $opg = ($formato == 'A4' ? 240 : 150);

    // Image($file, $x='', $y='', $w=0, $h=0, $type='', $link='', $align='', $resize=false, $dpi=300, $palign='', $ismask=false, $imgmask=false, $border=0, $fitbox=false, $hidden=false, $fitonpage=false)
    $pdf->AddPage('P');
    $pdf->SetAutoPageBreak("off", 0);
    $pdf->SetFont($fnt, '', $fd_1+2);
    $img_bg = 'immagini_scuola/carta_intestata.jpg';
    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
    $pdf->SetY($y_pg_1);
    $ys = $pdf->GetY();
//    $pdf->ln(6);
//    $pdf->SetFont($fnt, 'B', $fd_1+2);
//    $pdf->writeHTMLCell(0, 0, '', '', "Documenti della scuola elementare My Kid<br>
//Documents for My Kid Primary School", 0, 1, false, true, 'C');
//    $pdf->ln(4);
//    $pdf->SetFont($fnt, '', $fd_1);
//    $pdf->writeHTMLCell(0, 0, '', '', "Anno Scolastico $anno_scolastico_attuale<br>Academic year $anno_scolastico_attuale", 0, 1, false, true, 'C');
//    $pdf->ln(6);
//    $pdf->writeHTMLCell(0, $pdf->GetY()-$ys, '', $ys, "", 1, 1, false, true, 'C');
//
//    $pdf->ln(20);
//    $ys = $pdf->GetY();
//    $pdf->ln(6);
//    $pdf->writeHTMLCell(0, 0, '', '',
//        "Nome dell’alunno / Pupil’s name: {$studente['nome']} {$studente['cognome']}<br>"
//        . "Data di nascita / Date of Birth: ".date('d/m/Y', $studente['data_nascita'])."<br>"
//        . "Domicilio / Address: {$studente['indirizzo']}<br>"
//        . "Cittadinanza / Nationality: ".estrai_nazione($studente['cittadinanza'])['descrizione']."<br>"
//        . "Entrata nella scuola / Joined the school: <br>"
//        . "Uscita dalla scuola / Left the school: <br>"
//        . "Motivazione / Reason: ", 0, 1, false, true, 'L');
//    $pdf->ln(6);
//    $pdf->writeHTMLCell(0, $pdf->GetY()-$ys, '', $ys, "", 1, 1, false, true, 'C');
    $pdf->SetY($y_pg);
    $pdf->ln(18);
    $pdf->SetFont($fnt, '', $fd_1+4);
    $pdf->writeHTMLCell(0, 0, '', '', "Scheda personale descrittiva<br><i>Progress Report</i>", 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Anno Scolastico<br><i>School Year</i><br>$anno_scolastico_attuale", 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "$desc_periodo", 0, 1, false, true, 'C');
    $pdf->ln(12);
    $pdf->writeHTMLCell(0, 0, '', '', "{$studente['nome']} {$studente['cognome']}", 0, 1, false, true, 'C');
    $pdf->ln(12);
    $pdf->writeHTMLCell(0, 0, '', '', "Scuola Primaria<br><i>Primary School</i>", 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Classe {$cls_st}<br>Year {$cls_st}", 0, 1, false, true, 'C');

    $pdf->AddPage('P');
    $pdf->SetY($y_pg_1);
    $pdf->SetFont($fnt, '', $fd);
    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
    $pdf->writeHTMLCell(0, 0, '', $y_pg, $labels['doc_ita'], 0, 0, false, true, 'L');

    $pdf->AddPage('P');
    $pdf->SetY($y_pg_1);
    $pdf->SetFont($fnt, '', $fd);
    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
    $pdf->writeHTMLCell(0, 0, '', $y_pg, $labels['doc_eng'], 0, 0, false, true, 'L');

    $pdf->AddPage('P');
    $pdf->SetY($y_pg_1);
    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
    $pdf->SetY($y_pg-8);
    $pdf->SetFont($fnt, 'B', $fd+1);
    $pdf->writeHTMLCell(0, 0, '', '', "SCHEDA PERSONALE DESCRITTIVA DI $desc_doc – CLASSE $cls_st elementare", 0, 1, false, true, 'L');
    $pdf->ln(1);
    $pdf->writeHTMLCell(0, 0, '', '', "DESCRIPTIVE $desc_doc_eng SCHOOL REPORT – Primary Year $cls_st", 0, 1, false, true, 'L');
    $pdf->ln(1);
    $pdf->writeHTMLCell(0, 0, '', '', "Scuola elementare My Kid di Castel San Pietro/My Kid Primary School, Castel San Pietro", 0, 1, false, true, 'L');
    $pdf->ln(1);
    $pdf->writeHTMLCell(0, 0, '', '', "Anno Scolastico $anno_scolastico_attuale/Academic Year $anno_scolastico_attuale", 0, 1, false, true, 'L');
    $pdf->ln(1);
    $pdf->writeHTMLCell(0, 0, '', '', "Allievo/Pupil: {$studente['cognome']} {$studente['nome']}", 0, 1, false, true, 'L');
//    $pdf->ln(1);
//    $pdf->writeHTMLCell(0, 0, '', '', "Docenti/Teachers: ". implode(', ', $stringa_docenti), 0, 1, false, true, 'L');
    $pdf->ln(3);
//    $pdf->SetFont($fnt, 'B', $fd+2);
//    $pdf->writeHTMLCell(0, 0, '', '', "Considerazioni generali/general comments", 0, 1, false, true, 'L');
//    $pdf->ln(1);
//    $pdf->AddPage('P');
//    $pdf->SetFont($fnt, '', $fd);
//    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
//    $pdf->SetY($y_pg);
//    reset($arr_voti);
//    foreach ($arr_voti as $id_materia => $voto) {
//        $desc_materia = $arr_mat_to_tit[strtolower($voto['descrizione'])];
//        if ($desc_materia != '') {
//            $first_key = $id_materia;
//        }
//    }
//    echo "$first_key";
    reset($arr_voti);
    $first_key = key($arr_voti);
    $pdf->SetFont($fnt, '', $fd);
    foreach ($arr_voti as $id_materia => $voto) {
        $desc_materia = $arr_mat_to_tit[strtolower($voto['descrizione'])];
        if ( ! $desc_materia != '') {
            $desc_materia = $voto['descrizione'];
        }
//        if ($desc_materia != '')
//        {
            $h_mat_tot = 6;
            foreach ($voto['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) ) {
                    if (stripos($campo_libero['nome'],'Assenze giustificate') === false
                            and
                        stripos($campo_libero['nome'],'Assenze arbitrarie') === false
                            and
                        stripos($campo_libero['nome'],'Decisione di fine anno') === false
                            and
                        stripos($campo_libero['nome'],'Osservazioni generali') === false
                        )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
    //                    if ($value != '') {
                            $campo_libero['nome'] = str_replace(".", ".<br>&#x2022;   ", $campo_libero['nome']);
                            if (
                                    substr($campo_libero['nome'], -16) == ".<br>&#x2022;   "
                                ) {
                                $campo_libero['nome'] = substr($campo_libero['nome'], 0, -15);
                            }
                            $value = str_replace(["\r", "\n"], "<br>", $value);
                            $value = str_replace("<br><br>", "<br>", $value);


                            $h_mat_tot += 7 + $pdf->getStringHeight(0, $value, true, true, '', 1);
    //                    }
                    }
                }
            }

//            $pdf->ln(2);
            if ($first_key != $id_materia || $pdf->GetY()+$h_mat_tot>$opg)
//            if ($pdf->GetY()+$h_mat_tot>$opg)
            {
                $pdf->AddPage('P');
                $pdf->SetFont($fnt, '', $fd);
                $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
                $pdf->SetY($y_pg-7);
            }

            $desc_materia = str_replace("/", "\n", $desc_materia);
            $pdf->SetFont($fnt, 'B', $fd);
            $pdf->SetFillColor(0, 92, 153);
            $pdf->SetTextColor(255, 255, 255);
            $pdf->MultiCell(0, 0, $desc_materia, 1, 'L', 1, 1, '', '', true, 1, $ishtml=false, true, 0);
            $pdf->SetFillColor(255, 255, 255);
            $pdf->SetTextColor(0, 0, 0);

            foreach ($voto['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) ) {
                    if (stripos($campo_libero['nome'],'Assenze giustificate') === false
                            and
                        stripos($campo_libero['nome'],'Assenze arbitrarie') === false
                            and
                        stripos($campo_libero['nome'],'Decisione di fine anno') === false
                            and
                        stripos($campo_libero['nome'],'Osservazioni generali') === false
                        )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
    //                    if ($value != '') {
                            $campo_libero['nome'] = str_replace(".", ".<br>&#x2022;   ", $campo_libero['nome']);
                            if (
                                    substr($campo_libero['nome'], -16) == ".<br>&#x2022;   "
                                ) {
                                $campo_libero['nome'] = substr($campo_libero['nome'], 0, -15);
                            }
                            // $value = str_replace(["\r", "\n"], "<br>", $value);
                            // $value = str_replace("<br><br>", "<br>", $value);
                            $value = htmlspecialchars((string)$value, ENT_QUOTES, 'UTF-8');
                            $value = nl2br($value);
                            $value = decode($value);
                            $pdf->SetFont($fnt, 'B', $fd);
                            $pdf->SetFillColor(153, 204, 255);
                            $pdf->MultiCell(0, 0, " &#x2022;   {$campo_libero['nome']}", 1, 'L', 1, 1, '', '', true, 1, $ishtml=true, true, 0);
                            $pdf->SetFont($fnt, '', $fd);
                            $pdf->SetFillColor(242, 242, 242);
//                            $pdf->MultiCell(0, 0, "Commento del docente", 1, 'L', 1, 1, '', '', true, 1, $ishtml=true, true, 0);
//                            $pdf->writeHTMLCell(0, 8, '', '', $campo_libero['nome'], 1, 1, false, true, 'L');

                            if (
                                    ($studente['id_studente'] ==1001635
                                    ||
                                    $studente['id_studente'] == 1001661)
                                    &&
                                    stripos($campo_libero['nome'],'lezioni collettive') !== false
                                )
                            {$pdf->SetFont($fnt, '', $fd-1);}

                            $prof_nomi = [];
                            foreach ($voto['nomi_professori'] as $prf) {
                                if (($studente['classe']==4 || $studente['classe']==5) && stripos($desc_materia,'inglese') !== false) {
                                    if ($cls_st == 4) {
                                        if (stripos($prf,'Samantha') !== false) {
                                            $prof_nomi[] = $prf;
                                        }
                                    } elseif ($cls_st == 5) {
                                        if (stripos($prf,'Cowan') !== false) {
                                            $prof_nomi[] = $prf;
                                        }
                                    }
                                } elseif (($studente['classe']==4 || $studente['classe']==5) && stripos($desc_materia,'francese') !== false) { 
                                    if ($cls_st == 4) {
                                        if (stripos($prf, 'Bouille')!==false){ 
                                            $prof_nomi[] = $prf;
                                        }
                                    } elseif ($cls_st == 5) {
                                        // if (stripos($prf, 'Peverelli')!==false){ 
                                            $prof_nomi[0] = 'Martine Peverelli';
                                        // }
                                    }
                                } else {
                                    $prof_nomi[] = $prf;
                                }
                            }
                            $prof_nomi = implode(',', $prof_nomi);
                            if (stripos($desc_materia,'Montessori')!==false && ($studente['sezione']=='AZZ' || $studente['sezione']=='VER')) {
                                $pdf->writeHTMLCell(0, 18, '', '', ($value), 'TLR', 1, false, true, 'L');
                                if (stripos($campo_libero['nome'],'utilizzo del materiale')!==false) {
                                    $pdf->writeHTMLCell(0, 0, '', '', '('.$prof_nomi.')', 'BLR', 1, false, true, 'L');
                                }
                            } else {
                                $pdf->writeHTMLCell(0, $h, '', '', ($value), 'TLR', 1, false, true, 'L');
                                $pdf->writeHTMLCell(0, 0, '', '', '('.$prof_nomi.')', 'BLR', 1, false, true, 'L');
                            }
    //                    }
                    }
                }
            }
//        }
    }

    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Docenti titolari / Class Teachers: $firma_docenti", 0, 1, false, true, 'L');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Supervisione Didattica e Pedagogica $firma_dirigente", 0, 1, false, true, 'L');


    $pdf->AddPage('P');
    $pdf->SetFont($fnt, '', $fd+2);
    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
    $pdf->SetY($y_pg);
    $pdf->writeHTMLCell(0, 0, '', '', "Scheda personale descrittiva<br><i>Progress Report</i>", 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Anno Scolastico<br><i>School Year</i><br>$anno_scolastico_attuale", 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "$desc_periodo", 0, 1, false, true, 'C');
    $pdf->ln(12);
    $pdf->writeHTMLCell(0, 0, '', '', "{$studente['nome']} {$studente['cognome']}", 0, 1, false, true, 'C');
    $pdf->ln(12);
    $pdf->writeHTMLCell(0, 0, '', '', "Scuola Primaria<br><i>Primary School</i>", 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Classe {$cls_st}<br>Year {$cls_st}", 0, 1, false, true, 'C');
    $pdf->ln(8);
    $pdf->writeHTMLCell(0, 0, '', '', "Consegna / Delivered on: $data_att", 0, 1, false, true, 'C');
    if ($formato == 'A4') {
        $pdf->ln(28);
    }else{
        $pdf->ln(6);
    }
    $pdf->writeHTMLCell(0, 0, '', '', $labels['pag_firme_2'], 0, 1, false, true, 'L');
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'         => $id_classe,
    'data_day'          => $data_Day,
    'data_month'        => $data_Month,
    'data_year'         => $data_Year,
    'periodo_pagella'   => $periodo_pagella,
    'periodo'           => $periodo,
    'orientamento'      => $orientamento,
    'formato'           => $formato

];

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
