<?php
/*
 * Tipo: Pagella fine anno primaria in A3
 * Nome: ee_12345_finale_A3_icmezzolombardopaganella_tn_01
 * Richiesta da: IC Mezzolombardo Paganella
 * Data: 2018/05/28
 *
 * Materie particolari:
 *
 */

/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H','ee_12345_finale_A3_icmezzolombardopaganella_tn_01','Pagella fine anno per le classi della scuola Primaria', 1, 'IC Mezzolombardo Paganella', 2);

UPDATE elenco_stampe_personalizzate
    SET descrizione = 'Pagellina per le classi della scuola Primaria'
    WHERE valore = 'ee_12345_intermedia_icmezzolombardopaganella_tn_01';
*/

$stampa_personalizzata = 'SI';

function genera_stampa(&$pdf, $studente, $parametri_stampa)
{
//{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">

    // -- PARAMETRI

    // Campo Materia Opzionale
    $campo_materia_opzionale = "Laboratori";

    $id_classe = $parametri_stampa['id_classe'];
    $data_Day = $parametri_stampa['data_day'];
    $data_Month = $parametri_stampa['data_month'];
    $data_Year = $parametri_stampa['data_year'];
    $periodo_pagella = $parametri_stampa['periodo_pagella'];

    $data_inizio_lezioni_prm = estrai_parametri_singoli('DATA_INIZIO_LEZIONI', (int) $id_classe, 'classe');
    $data_inizio_secondo_quadrimestre_prm = estrai_parametri_singoli('DATA_INIZIO_SECONDO_QUADRIMESTRE', (int) $id_classe, 'classe');
    $data_fine_lezioni_prm = estrai_parametri_singoli('DATA_FINE_LEZIONI', (int) $id_classe, 'classe');

    $data_inizio_lezioni = date("d/m/Y", (int)$data_inizio_lezioni_prm);
    $data_inizio_secondo_quadrimestre = date("d/m/Y", (int)$data_inizio_secondo_quadrimestre_prm);
    $data_fine_primo_quadrimestre = date("d/m/Y", (int)$data_inizio_secondo_quadrimestre_prm - 86400);
    $data_fine_lezioni = date("d/m/Y", (int)$data_fine_lezioni_prm);

    $font_testo = 'times';
    // -----------------------

    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $as_attuale = explode("/", $anno_scolastico_attuale);

    $id_studente = $studente['id_studente'];
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);

    // Estrazione voti
    $voti_pagella_primo_quadrimestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 7, $studente['id_studente']);
    $voti_pagella_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $studente['id_studente']);

    $arr_voti_finale = $arr_opzionali = [];
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

        $aggiungi_annotazioni = false;
        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            &&
//            (
//                ($studente['esonero_religione'] == 0 && $materia['tipo_materia'] != 'ALTERNATIVA')
//                ||
//                ($studente['esonero_religione'] == 1 && $materia['tipo_materia'] != 'RELIGIONE')
//            )
//            &&
            !in_array($materia['tipo_materia'],['CONDOTTA','OPZIONALE'])
        )
        {
            if ($studente['esonero_religione'] == 1 && $materia['tipo_materia'] == 'RELIGIONE')
            {
                $arr_voti_finale[$id_materia]['id_materia'] = $id_materia;
                $arr_voti_finale[$id_materia]['descrizione'] = $materia['descrizione'];
                $arr_voti_finale[$id_materia]['primo_quadrimestre']['voto_pagellina'] = 'ESONERO';
                $arr_voti_finale[$id_materia]['finale']['voto_pagellina'] = 'ESONERO';
                $aggiungi_annotazioni = true;
            }
            else
            {
                $arr_voti_finale[$id_materia]['id_materia'] = $id_materia;
                $arr_voti_finale[$id_materia]['descrizione'] = $materia['descrizione'];
                $arr_voti_finale[$id_materia]['primo_quadrimestre'] = $voti_pagella_primo_quadrimestre[$id_materia];
                $arr_voti_finale[$id_materia]['finale'] = $voti_pagella_finale[$id_materia];
                $aggiungi_annotazioni = true;
            }
        }

        if ($materia['tipo_materia'] == 'CONDOTTA')
        {
            $arr_condotta['primo_quadrimestre'] = $voti_pagella_primo_quadrimestre[$id_materia];
            $arr_condotta['finale'] = $voti_pagella_finale[$id_materia];

            foreach ($voti_pagella_primo_quadrimestre[$id_materia]['campi_liberi'] as $campo_libero)
            {
                if (
                        (
                            in_array($id_materia, $campo_libero['abbinamenti']['materie'])
                            ||
                            $campo_libero['abbinamenti']['materie'][0] == 0
                        )
                        && $campo_libero['nome'] != ""
                        && $campo_libero['id_padre'] == ""
                        && strpos(strtoupper($campo_libero['nome']), 'FACOLTA') === false
                        && strpos(strtoupper($campo_libero['nome']), 'ALTERNATIVA') === false
                        && strpos(strtoupper($campo_libero['nome']), 'AFO') === false
                        && strpos(strtoupper($campo_libero['nome']), 'IRC') === false
                    )
                {
                    $valore = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($valore != 'Altro')
                    {
                        $arr_giudizi_finale['giudizio_primo_quadrimestre'] .= $valore;
                    }
                }
                elseif (strpos(strtoupper($campo_libero['nome']), 'FACOLTA') !== false
                     || strpos(strtoupper($campo_libero['nome']), 'ALTERNATIVA') !== false
                     || strpos(strtoupper($campo_libero['nome']), 'AFO') !== false
                     || strpos(strtoupper($campo_libero['nome']), 'IRC') !== false)
                {
                    $val = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                        $arr_opzionali['primo_quadrimestre'] .= $val . "\n";
                    }
                }
            }
            foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero)
            {
                if (
                        (
                            in_array($id_materia, $campo_libero['abbinamenti']['materie'])
                            ||
                            $campo_libero['abbinamenti']['materie'][0] == 0
                        )
                        && $campo_libero['nome'] != ""
                        && $campo_libero['id_padre'] == ""
                        && strpos(strtoupper($campo_libero['nome']), 'FACOLTA') === false
                        && strpos(strtoupper($campo_libero['nome']), 'ALTERNATIVA') === false
                        && strpos(strtoupper($campo_libero['nome']), 'AFO') === false
                        && strpos(strtoupper($campo_libero['nome']), 'IRC') === false
                    )
                {
                    $valore = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($valore != 'Altro')
                    {
                        $arr_giudizi_finale['giudizio_finale'] .= $valore;
                    }
                }
                elseif (strpos(strtoupper($campo_libero['nome']), 'FACOLTA') !== false
                        || strpos(strtoupper($campo_libero['nome']), 'ALTERNATIVA') !== false
                        || strpos(strtoupper($campo_libero['nome']), 'AFO') !== false
                        || strpos(strtoupper($campo_libero['nome']), 'IRC') !== false)
                {
                    $val = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                        $arr_opzionali['finale'] .= $val . "\n";
                    }
                }
            }
        }

        if ($materia['tipo_materia'] == 'OPZIONALE' && (strpos(strtoupper($materia['descrizione']), 'OPZIONAL') !== false))
        {
            $voto_intermedio_tradotto = "Non iscritto";
            $voto_finale_tradotto = "Non iscritto";
            $campo_libero_intermedio = '';
            $campo_libero_finale = '';

            foreach ($voti_pagella_primo_quadrimestre[$id_materia]['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voti_pagella_primo_quadrimestre[$id_materia]['voto_pagellina'])
                {
                    $voto_intermedio_tradotto = $significato['valore'];
                    foreach ($voti_pagella_primo_quadrimestre[$id_materia]['campi_liberi'] as $campo_libero)
                    {
                        if (strpos(strtoupper($campo_libero['nome']), 'ANNOTAZION') !== false)
                        {
                            $val = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($val != '') {
                                $campo_libero_intermedio = $val;
                            }
                        }
                    }
                }
            }

            // $arr_opzionali['primo_quadrimestre'] .= $materia['descrizione'] . ": " . $voto_intermedio_tradotto;
            $arr_opzionali['primo_quadrimestre'] .= ($campo_libero_intermedio != '') ? " (" . $campo_libero_intermedio . ")\n" : "\n";

            foreach ($voti_pagella_finale[$id_materia]['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voti_pagella_finale[$id_materia]['voto_pagellina'])
                {
                    $voto_finale_tradotto = $significato['valore'];
                    foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero)
                    {
                        if (strpos(strtoupper($campo_libero['nome']), 'ANNOTAZION') !== false)
                        {
                            $val = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($val != '') {
                                $campo_libero_finale = $val;
                            }
                        }
                    }
                }
            }
            // $arr_opzionali['finale'] .= $materia['descrizione'] . ": " . $voto_finale_tradotto;
            $arr_opzionali['finale'] .= ($campo_libero_finale != '') ? " (" . $campo_libero_finale . ")\n" : "\n";
        }
    }

    // debiti
    $debiti_as_precedente = estrai_debiti_studente($studente["id_studente"], ($as_attuale[0] -1) . "/" . ($as_attuale[1] -1));

    $debiti_as_attuale = estrai_debiti_studente($studente["id_studente"], $anno_scolastico_attuale);

    $arr_debiti_prec = $arr_debiti_attuale = [];

    foreach ($debiti_as_precedente as $debito)
    {
        $testo_debito = $debito['descrizione_materia'] . " (A.S " . ($as_attuale[0] -1) . "/" . ($as_attuale[1] -1) . "): ";

        if ($debito['debito_recuperato'] == "NO")
        {
            $testo_debito .= "non recuperata carenza " . strtolower($debito['tipo_debito']);
        }
        else
        {
            $testo_debito .= "carenza recuperata il " . date('d/m/Y', $debito['data_recupero_debito']);
        }
        $arr_debiti_prec[] = $testo_debito;
    }

    foreach ($debiti_as_attuale as $debito)
    {
        $testo_debito = $debito['descrizione_materia'] . " (A.S " . $anno_scolastico_attuale . "): ";

        if ($debito['debito_recuperato'] == "NO")
        {
            $testo_debito .= "non recuperata carenza " . strtolower($debito['tipo_debito']);
        }
        else
        {
            $testo_debito .= "carenza recuperata il " . date('d/m/Y', $debito['data_recupero_debito']);
        }
        $arr_debiti_attuale[] = $testo_debito;
    }


    foreach ($arr_voti_finale as $id_mat => $voto)
    {
        if ($anno_scolastico_attuale >= '2020/2021'
                &&
            in_array($voto['descrizione'], ['MUSICA CLIL', 'ARTE E IMMAGINE CLIL', 'SCIENZE MOTORIE E SPORTIVE CLIL'])
                &&
                (
                    ($studente['codice_indirizzi']=='EEA' && $studente['classe']<=2)
                        ||
                    ($studente['codice_indirizzi']=='EED' && $studente['classe']<=2)
                        ||
                    ($studente['codice_indirizzi']=='EEDR' && $studente['classe']<=2)
                        ||
                    ($studente['codice_indirizzi']=='EEN' && $studente['classe']<=2)
                        ||
                    ($studente['codice_indirizzi']=='EENSR' && $studente['classe']<=2)
                        ||
                    ($studente['codice_indirizzi']=='EESB' && $studente['classe']<=2)
                )
            )
        {
            unset($arr_voti_finale[$id_mat]);
        }
    }


// Dizionario temporaneo
    $labels = [
        "tipo_dirigente"                    => "IL DIRIGENTE",
        "p1_titolo_repubblica"              => "REPUBBLICA ITALIANA",
        "p1_titolo_trento"                  => "PROVINCIA AUTONOMA DI TRENTO",
        "p1_titolo_tipo_scuola"             => "Scuola Secondaria di Secondo Grado",
        'p1_anno_scolastico'                => "Anno Scolastico",
        "p1_studente"                       => "Student||min_essa||",
        "p1_alunno"                         => "Alunn||min_oa||",
        "p1_iscrizione_classe"              => "iscritto alla Classe",
        "p1_sezione"                        => "Sezione",
        'p1_titolo'                         => "DOCUMENTO DI VALUTAZIONE",
        "p1_studente"                       => "||min_lola|| student||min_essa||",
        "p1_data_nascita"                   => " il ",
        "p1_a_nascita"                      => "nat||min_oa|| a ",
        "p1_di_studente"                    => "del||min_lola|| student||min_essa||",
        "p1_iscrizione_anno"                => "iscritt||min_oa|| al ",
        "p1_classe"                         => "Classe: ",
        "p1_sezione"                        => "Sezione",
        "p1_indirizzo"                      => "Indirizzo: ",
        "p1_provenienza"                    => "Provenienza",
        "p1_titolo_amm"                     => "Titolo di Ammissione (2)",
        "p1_iscrizione_volte"               => "Iscrizione per la ",
        "p1_volta"                          => " volta (3)",
        "p1_validazione_1"                  => "L||min_oa|| student||min_essa||",
        "p1_validazione_2"                  => "ha frequentato almeno il 75% dell&#039;orario annuale. &Egrave;",
        "p1_validazione_3"                  => "ammess||min_oa|| alla valutazione finale.",
        "p1_intestazione_fronte_0"          => "___________PROVINCIA AUTONOMA DI TRENTO___________",
        "p1_intestazione_fronte_1"          => "Servizio istruzione e formazione del secondo grado, Universit&agrave; e ricerca",
        "p1_anno_scolastico"                => "ANNO SCOLASTICO",
        "p1_monte_ore_annuo"                => "Monte Ore Annuo: ",
        "p1_percorso_1"                     => "&deg; anno del percorso triennale di Istruzione e Formazione Professionale",
        "p1_percorso_2"                     => "&deg; anno di Istruzione e Formazione Professionale",

        "p23_titolo_materia_1"              => "INSEGNAMENTI",
        "p23_titolo_materia_2"              => "AMBITI DI COMPETENZA",
        "p23_titolo_intermedio"             => "VALUTAZIONE PERIODICA",
        "p23_titolo_finale"                 => "VALUTAZIONE ANNUALE",
        "p23_titolo_crediti"                => "ESAMI",
        "p23_titolo_scritto"                => "SCRITTO",
        "p23_titolo_orale"                  => "ORALE",
        "p23_titolo_pratico"                => "VOTO UNICO",
        "p23_titolo_ass_int"                => "Ore assenza",
        "p23_titolo_unico"                  => "VOTO UNICO\n(in lettere)",
        "p23_titolo_ass_tot"                => "TOTALE\n Ore assenza",
        "p23_titolo_periodo_1"              => "I PERIODO dal ",
        "p23_titolo_periodo_2"              => "II PERIODO dal ",
        "p23_titolo_unico_qual"             => "VOTO UNICO (in lettere)",
        "p23_desc_comportamento"            => "CAPACIT&Agrave; RELAZIONALE",
        "p23_titolo_credito_scolastico"     => "CREDITO SCOLASTICO (4)",
        "p23_testo_media_finale"            => "MEDIA DEI VOTI CONSEGUITI\nNELLO SCRUTINIO FINALE",
        "p23_titolo_crediti_terza"          => "CREDITO SCOLASTICO\nCLASSE TERZA",
        "p23_titolo_crediti_quarta"         => "CREDITO SCOLASTICO\nCLASSE QUARTA",
        "p23_titolo_crediti_quinta"         => "CREDITO SCOLASTICO\nCLASSE QUINTA",
        "p23_titolo_totale_crediti"         => "TOTALE DEI CREDITI:",
        "p23_testo_credito_attribuito"      => "CREDITO ATTRIBUITO",
        "p23_testo_credito_integrato"       => "INTEGRAZIONE SUPERAMENTO CARENZE",
        "p23_titolo_annotazioni_intermedio" => "ANNOTAZIONI - primo quadrimestre\nSuperamento Carenze Formative anno scolastico",
        "p23_titolo_annotazioni"            => "ANNOTAZIONI (5)",
        "p23_titolo_valutazione_intermedia" => "VALUTAZIONE PERIODICA",
        "p23_titolo_valutazione_finale"     => "VALUTAZIONE FINALE",
        "p23_titolo_annotazioni_finale"     => "ANNOTAZIONI - secondo quadrimestre\nCarenze Formative anno scolastico",
        "p23_testo_firma_dirigente"         => "firma del ",
        "p23_titolo_opzionali"              => "ATTIVITA' OPZIONALI FACOLTATIVE ED EVENTUALE ATTIVITA' DIDATTICA ALTERNATIVA ALL'IRC",
        "p23_titolo_opzionali_1"            => "I QUADRIMESTRE\nATTIVITA' OPZIONALI FACOLTATIVE",
        "p23_titolo_opzionali_2"            => "II QUADRIMESTRE\nATTIVITA' OPZIONALI FACOLTATIVE",
        "p23_gradi_valutazione"             => "Gradi della valutazione: OTTIMO, DISTINTO, BUONO, DISCRETO, SUFFICIENTE, NON SUFFICIENTE",

        "p4_testo_firma_responsabili"       => "Firma di uno dei responsabili",
        "p4_testo_firma_genitori"           => "Firma di un genitore",
        "p4_presa_visione"                  => "(per presa visione)",
        "p4_titolo_giudizio_1"              => "GIUDIZIO GLOBALE",
        "p4_titolo_giudizio_2"              => "Valutazione dei progressi nell'apprendimento e nello sviluppo personale e sociale del||min_lola|| student||min_essa||",
        "p4_risultato_finale"               => "RISULTATO FINALE (6)",
        "p4_valutazione"                    => "Valutazione dei progressi nell&#039;apprendimento e nello sviluppo personale e sociale de||min_lola|| student||min_essa||",
        "p4_titolo_nullaosta"               => "NULLA OSTA",
        "p4_testo_nullaosta"                => "Per il trasferimento dell||min_oa|| student||min_essa|| ad altro Istituto, in base alle vigenti disposizioni",
        "p4_titolo_note"                    => "NOTE",
        "p4_note_1"                         => "Denominazione e intitolazione (se presente) dell&#039;Istituto; sede;\nper le scuole paritarie indicare il provvedimento di riconoscimento della parit&agrave;",
        "p4_note_2"                         => "Promozione; Idoneit&agrave;; Qualifica; (Idoneit&agrave; all&#039;ultima classe a seguito di esito positivo dell&#39;esame preliminare e mancato superamento esami di Stato\n- Idoneit&agrave; riconosciuta dal Consiglio di classe ai sensi dell&#039;art. 6 D.P.R. 12/7/2000, n. 257)",
        "p4_note_3"                         => "Prima; Seconda; Terza",
        "p4_note_4"                         => "Il punteggio del credito &eacute; calcolato in base al D.M. n. 42 del 22/05/2007 e al D.P.P. n. 22 del 07/10/2010",
		"p4_note_5"                         => "Il riquadro &eacute; a disposizione dell&#039;Istituzione per eventuali esplicitazioni riguardanti il percorso scolastico, compreso il piano di studi seguito dallo/a studente/studentessa. In ogni caso deve essere  utilizzato per le seguenti finalit&agrave;:\n- annotazione delle materie che evidenziano carenze negli apprendimenti (D.P.P. n. 22 del 07/10/2010);\n- annotazione del recupero delle carenze negli apprendimenti riferite all&#039;anno scolastico precedente;\n- ulteriori eventuali annotazioni od indicazione del rilascio di certificazione",
        "p4_note_6"                         => "Ammesso/a - Non ammesso/a alla classe successiva o all&#039;esame di stato",

    ];


//    print "<pre>";
//    print_r($arr_voti_finale);
//    print "</pre>";
//
//    $labels = estrai_dizionaro_stampe_personalizzate('ss_12345_finale_A3_primiero_tn_01');

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F')
    {
        $min_oa = "a";
        $min_essa = "essa";
        $minl_la = "lla";
        $min_lola = "la";

		$studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
		$studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_essa = "e";
        $minl_la = "l";
        $min_lola = "lo";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label)
    {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||minl_la||", $minl_la, $labels[$k]);
        $labels[$k] = str_replace("||min_essa||", $min_essa, $labels[$k]);
        $labels[$k] = str_replace("||min_lola||", $min_lola, $labels[$k]);
    // temp
        $labels[$k] = decode($labels[$k]);
    }

    // Esiti con carenze (da nascondere come da richiesta della scuola)
    $studente['esito'] = str_replace(' con carenze', '*', $studente['esito']);
    $studente['esito'] = str_replace(' con debito formativo', '*', $studente['esito']);


    // -- FRONTE
    $pdf->AddPage('L');
    $pdf->SetAutoPageBreak("off", 1);

    //{{{ <editor-fold defaultstate="collapsed" desc="PAGINA 4">
    // -------- PAGINA 4 --------
    $x_rel = 30;
    $y_rel = 7;
    $dim_font = 12;

    // Giudizio globale
    $pdf->ln(10);
    $pdf->SetFont($font_testo, 'B', $dim_font);
    $pdf->CellFitScale(180, 0, $labels['p4_titolo_giudizio_1'], 0, 1, 'C');
    $pdf->ln(1);
    $pdf->CellFitScale(180, 0, $labels['p4_titolo_giudizio_2'], 0, 1, 'C');
    $pdf->ln(1);

    $testo_giudizio_primo_quadrmestre = $arr_giudizi_finale['giudizio_primo_quadrimestre'];
    $testo_giudizio_finale = $arr_giudizi_finale['giudizio_finale'];

    $pdf->SetFont($font_testo, 'B', $dim_font);
    $pdf->CellFitScale(180, 10, "I QUADRIMESTRE", "LTR", 1, 'C');
    $pdf->SetFont($font_testo, '', $dim_font);
    $pdf->MultiCell(180, 80, $testo_giudizio_primo_quadrmestre, "LBR", 'L');

    $pdf->ln(10);

    $pdf->SetFont($font_testo, 'B', $dim_font);
    $pdf->CellFitScale(180, 10, "II QUADRIMESTRE", "LTR", 1, 'C');
    $pdf->SetFont($font_testo, '', $dim_font);
    $pdf->MultiCell(180, 80, $testo_giudizio_finale, "LBR", 'L');

    // Firma dirigente e genitori
    $pdf->SetY(230);
    $pdf->SetFont($font_testo, 0, $dim_font);
    $pdf->CellFitScale(70, 0, "Il Dirigente Scolastico", 0, 0, "C");
    $pdf->CellFitScale(40, 0, "", 0, 0, "C");
    $pdf->CellFitScale(70, 0, $labels['p4_testo_firma_genitori'], 0, 1, "C");

    $pdf->CellFitScale(70, 0, $studente['nome_dirigente'], 0, 0, "C");
    $pdf->CellFitScale(40, 0, "", 0, 0, "C");
    $pdf->CellFitScale(70, 0, "o di un responsabile del minore", 0, 1, "C");

    $pdf->SetFont($font_testo, 0, $dim_font - 2);
    $pdf->CellFitScale(110, 0, "", 0, 0, "C");
    $pdf->CellFitScale(70, 0, $labels['p4_presa_visione'], 0, 1, "C");

    $pdf->CellFitScale(5, 0, "", 0, 0, "C");
    $pdf->CellFitScale(65, 15, "", "B", 0, "C");
    $pdf->CellFitScale(45, 0, "", 0, 0, "C");
    $pdf->CellFitScale(65, 15, "", "B", 1, "C");


    //}}} </editor-fold>

//    $x_base = 220;
    $x_base = 235;
    $y_base = 0;
    $x_rel = $x_base + 30;
    $dim_font = 14;

	$y_rel = $y_base + 10;
    $pdf->SetXY($x_base, $y_rel);

    //{{{ <editor-fold defaultstate="collapsed" desc="PAGINA 1">
    // -------- PAGINA 1 --------

    $pdf->SetFillColor(255, 255, 255);
    $pdf->SetFont($font_testo, 0, 18);

    // |
    // |
    // Logo pagella
        //$pdf->Image('immagini_scuola/logo.jpg', $x_rel - 30, $y_rel + 15, 180, 30);
    // |
    // |

    // Logo Repubblica
    $pdf->Image('immagini_scuola/logo_repubblica.jpg', $x_rel - 21, $y_rel + 5, 20, 20);

    // Logo Trento
    $pdf->Image('immagini_scuola/logo_trento.jpg', $x_rel + 123 , $y_rel + 5, 15, 20);

    $pdf->SetFont($font_testo, '', $dim_font - 4);
    $pdf->SetXY($x_base, $y_rel + 27);
    $pdf->CellFitScale(40, 0, "REPUBBLICA", 0, 0, 'C');
    $pdf->CellFitScale(100, 0, "", 0, 0, 'C');
    $pdf->CellFitScale(40, 0, "PROVINCIA", 0, 1, 'C');
    $pdf->SetX($x_base);
    $pdf->CellFitScale(40, 0, "ITALIANA", 0, 0, 'C');
    $pdf->CellFitScale(100, 0, "", 0, 0, 'C');
    $pdf->CellFitScale(40, 0, "AUTONOMA DI TRENTO", 0, 1, 'C');

    $pdf->ln(7);

    // Dati Scuola
    $pdf->SetXY($x_base + 25, $y_rel + 30);
    $pdf->SetFont($font_testo, "B", $dim_font + 4);
    $pdf->CellFitScale(130, 0, "ISTITUTO COMPRENSIVO", 0, 1, 'C');

    $pdf->ln(3);
    $pdf->SetX($x_base);
    $pdf->SetFont($font_testo, "B", $dim_font + 2);
    $pdf->CellFitScale(180, 0, "MEZZOLOMBARDO - PAGANELLA", 0, 1, 'C');

    $dati_sede = estrai_sede_singola($studente['id_sede_indirizzi']);
//    $pdf->ln(15);
//    $pdf->SetX($x_base);
//    $pdf->SetFont($font_testo, "BI", $dim_font);
//    $pdf->CellFitScale(15, 0, 'Sede', 0, 0, 'L');
//    $pdf->CellFitScale(100, 0, $dati_sede['descrizione_comune'], 0, 0, 'L');
//    $pdf->CellFitScale(70, 0, $dati_sede['indirizzo'], 0, 1, 'L');

    $pdf->ln(5);
    $pdf->SetX($x_base);
    $pdf->SetFont($font_testo, "B", $dim_font + 2);
    $pdf->CellFitScale(180, 0, $studente['descrizione_indirizzi'], 0, 1, 'C');

    // Anno scolastico
    $pdf->ln(10);
    $pdf->SetX($x_base);
    $pdf->CellFitScale(180, 0, $labels['p1_anno_scolastico'] . " " . $anno_scolastico_attuale, 0, 1, 'C');

    // Classe sezione
    $pdf->ln(10);
    $pdf->SetX($x_base);
    $pdf->SetFont($font_testo, 0, $dim_font);
    $pdf->CellFitScale(20, 0, "Classe", 0, 0, 'L');
    $pdf->SetFont($font_testo, "B", $dim_font);
    $pdf->CellFitScale(30, 0, $studente['classe'], 0, 0, 'L');
    $pdf->SetFont($font_testo, 0, $dim_font);
    $pdf->CellFitScale(25, 0, "Sezione", 0, 0, 'L');
    $pdf->SetFont($font_testo, "B", $dim_font);
    $pdf->CellFitScale(60, 0, $studente['sezione'], 0, 1, 'L');

    // --Anagrafica dello studente
    $pdf->ln(13);
    $pdf->SetX($x_base);
    $pdf->SetFont($font_testo, "B", $dim_font + 18);
    $pdf->CellFitScale(180, 0, $labels['p1_titolo'], 0, 1, 'C');

    // Riga nome
    $pdf->ln(13);
    $pdf->SetX($x_base);
    $pdf->SetFont($font_testo, 0, $dim_font);
    $pdf->CellFitScale(30, 0, $labels['p1_alunno'], 0, 0, 'L');
    $pdf->SetFont($font_testo, 'B', $dim_font);
    $pdf->CellFitScale(150, 0, strtoupper($studente['cognome']) . ' ' . strtoupper($studente['nome']), 0, 1, 'L');

    // Luogo nascita
    $luogo_nascita_finale = $studente['descrizione_nascita'] == '' ? $studente['citta_nascita_straniera'] : $studente['descrizione_nascita'];
    $provincia_nascita_finale = $studente['provincia_nascita_da_comune'] == '' ? $studente['descrizione_stato_nascita'] : $studente['provincia_nascita_da_comune'];

    $pdf->ln(5);
    $pdf->SetX($x_base);
    $pdf->SetFont($font_testo, 0, $dim_font);
    $pdf->CellFitScale(30, 0, $labels['p1_a_nascita'], 0, 0, 'L');
    $pdf->SetFont($font_testo, 'B', $dim_font);
    $pdf->CellFitScale(70, 0, $luogo_nascita_finale . " (" . $provincia_nascita_finale . ")", 0, 0, 'L');
    $pdf->SetFont($font_testo, 0, $dim_font);
    $pdf->CellFitScale(20, 0, "il", 0, 0, 'L');
    $pdf->SetFont($font_testo, 'B', $dim_font);
    $pdf->CellFitScale(40, 0, $studente['data_nascita_ext'], 0, 1, 'L');

    // Data nascita
//    $pdf->ln(5);
//    $pdf->SetX($x_base);
//    $pdf->SetFont($font_testo, 0, $dim_font);
//    $pdf->CellFitScale(30, 0, "il", 0, 0, 'L');
//    $pdf->SetFont($font_testo, 'B', $dim_font);
//    $pdf->CellFitScale(100, 0, $studente['data_nascita_ext'], 0, 1, 'L');

    // Attestato
    switch($studente['esito'])
    {
        case "Ammesso alla classe successiva":
        case "Ammessa alla classe successiva":
            $esito = "l'alunn{$min_oa} è stat{$min_oa} Ammess{$min_oa} alla classe successiva";
            break;
        case "Non ammesso alla classe successiva":
        case "Non ammessa alla classe successiva":
            $esito = "l'alunn{$min_oa} è stat{$min_oa} Non ammess{$min_oa} alla classe successiva";
            break;
        case "In corso":
            $esito = "l'alunn{$min_oa} è In corso";
            break;
        case "Iscritto":
            $esito = "l'alunn{$min_oa} è Iscritto";
            break;
        case "Ammesso al successivo grado dell'istruzione obbligatoria":
        case "Ammessa al successivo grado dell'istruzione obbligatoria":
            $esito = "l'alunn{$min_oa} è stat{$min_oa} Ammess{$min_oa} al successivo grado dell'istruzione obbligatoria";
            break;
        case "Non ammesso al successivo grado dell'istruzione obbligatoria":
        case "Non ammessa al successivo grado dell'istruzione obbligatoria":
            $esito = "l'alunn{$min_oa} è stat{$min_oa} Non ammess{$min_oa} al successivo grado dell'istruzione obbligatoria";
            break;
        default:
            $esito = "";
            break;
    }

    $pdf->ln(15);
    $pdf->SetX($x_base);
    $pdf->SetFont($font_testo, 'B', $dim_font + 2);
    $pdf->CellFitScale(180, 15, "ATTESTATO", "LRT", 1, 'C');

    $pdf->SetX($x_base);
    $pdf->SetFont($font_testo, 0, $dim_font);
    $pdf->CellFitScale(180, 0, "Vista la valutazione del Consiglio di Classe, si attesta che", "LR", 1, 'C');

    $pdf->SetX($x_base);
    $pdf->CellFitScale(180, 10, $esito, "LR", 1, 'C');

    $pdf->SetX($x_base);
    $pdf->CellFitScale(180, 5, "", "LR", 1, "L");

    // Luogo e data e firma dirigente
    $pdf->SetX($x_base);
    $pdf->CellFitScale(120, 0, $dati_sede['descrizione_comune'] . ", " . $data_Day . "/" . $data_Month . "/" . $data_Year, "L", 0, "L");
    $pdf->CellFitScale(60, 0, "Il Dirigente Scolastico", "R", 1, "C");

    $pdf->SetX($x_base);
    $pdf->CellFitScale(120, 7, "", "L", 0, "L");
    $pdf->SetFont($font_testo, 0, $dim_font);
    $pdf->CellFitScale(60, 7, $studente['nome_dirigente'], "R", 1, "C");

    $pdf->SetX($x_base);
    $pdf->CellFitScale(180, 0, "", "LRB", 0, "L");
    //}}} </editor-fold>

    // -- RETRO
    $pdf->AddPage('L', "A3");
    $pdf->SetAutoPageBreak("off", 1);
    //{{{ <editor-fold defaultstate="collapsed" desc="PAGINE 2 - 3">

    // -------- PAGINE 2 E 3 --------
    // Gestite insieme visto che è un singolo paginone
    // Doppia pagina (p2 materie p3 voti)
    // Larghezza A3 = 420

    $x_rel = 30;
    $y_rel = 7;
    $h_riga = 20;
    $dim_font = 12;

    $pdf->SetXY($x_rel - 20, $y_rel);


    // Intestazione
    $pdf->SetFont($font_testo, "B", $dim_font);
    if ($studente['classe'] < 3)
    {
        $titolo_materie = "AREE DI APPRENDIMENTO";
    }
    else
    {
        $titolo_materie = "DISCIPLINA";
    }

    $pdf->MultiCell(125, $h_riga, $titolo_materie, "B", 'C', false, 0, "", "", true, 0, false, true, $h_riga, "M");
    $pdf->MultiCell(5, $h_riga, "", 0, 'C', false, 0, "", "", true, 0, false, true, $h_riga, "M");
    $pdf->SetFont($font_testo, "B", $dim_font - 1);
    $pdf->MultiCell(53, $h_riga, "I QUADRIMESTRE VALUTAZIONE DEGLI APPRENDIMENTI", "TBL", 'C', false, 0, "", "", true, 0, false, true, $h_riga, "M");
    $pdf->SetFont($font_testo, "BI", $dim_font);
    $pdf->MultiCell(79, $h_riga, "Annotazioni", "BTR", 'C', false, 0, "", "", true, 0, false, true, $h_riga, "M");
    $pdf->SetFont($font_testo, "B", $dim_font);
    $pdf->MultiCell(15, $h_riga, "", 0, 'C', false, 0, "", "", true, 0, false, true, $h_riga, "M");
    $pdf->SetFont($font_testo, "B", $dim_font - 1);
    $pdf->MultiCell(50, $h_riga, "II QUADRIMESTRE VALUTAZIONE DEGLI APPRENDIMENTI", "BTL", 'C', false, 0, "", "", true, 0, false, true, $h_riga, "M");
    $pdf->SetFont($font_testo, "BI", $dim_font);
    $pdf->MultiCell(77, $h_riga, "Annotazioni", "BTR", 'C', false, 1, "", "", true, 0, false, true, $h_riga, "M");
    $pdf->SetFont($font_testo, "B", $dim_font);


    // Righe voti
    $cont_righe = 0;
    $pdf->SetFont($font_testo, 0, $dim_font);
    foreach ($arr_voti_finale as $materia)
    {
        $pdf->SetX($x_rel - 20);
        $voto_intermedio_tradotto = "";
        $voto_finale_tradotto = "";
        $annotazione_primo_quadrimestre = "";
        $annotazione_finale = "";
        $opzionali_primo_quadrimestre = "";
        $opzionali_finale = "";

//        if ($materia['primo_quadrimestre']['voto_pagellina'] == 'ESONERO')
//        {
//            $voto_intermedio_tradotto = "Non si avvale";
//        }

        foreach ($materia['primo_quadrimestre']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $materia['primo_quadrimestre']['voto_pagellina'])
            {
                $voto_intermedio_tradotto = decode($significato['valore']);
            }
        }

//        if ($materia['finale']['voto_pagellina'] == 'ESONERO')
//        {
//            $voto_finale_tradotto = "Non si avvale";
//        }

        foreach ($materia['finale']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $materia['finale']['voto_pagellina'])
            {
                $voto_finale_tradotto = decode($significato['valore']);
            }
        }

        if ($materia['primo_quadrimestre']['voto_pagellina'] != 'ESONERO' || $materia['finale']['voto_pagellina'] != 'ESONERO')
        {
            foreach ($materia['primo_quadrimestre']['campi_liberi'] as $key => $campo_libero)
            {
                if (strpos(strtoupper($campo_libero['nome']), 'ANNOTAZION') !== false) {
                    $val = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                        $annotazione_primo_quadrimestre = $val;
                    }
                }
            }

            foreach ($materia['finale']['campi_liberi'] as $key => $campo_libero)
            {
                if (strpos(strtoupper($campo_libero['nome']), 'ANNOTAZION') !== false) {
                    $val = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                        $annotazione_finale = $val;
                    }
                }
            }

            $controllo_altezza = [];
            $controllo_altezza[] = $pdf->MultiCellNbLines(125, $materia['descrizione']) * 9;
            $controllo_altezza[] = $pdf->MultiCellNbLines(74, $annotazione_primo_quadrimestre) * 9;
            $controllo_altezza[] = $pdf->MultiCellNbLines(70, $annotazione_finale) * 9;
            $riga_max = max($controllo_altezza);
            $pdf->MultiCell(125, $riga_max, $materia['descrizione'], "B", 'L', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->MultiCell(5, $riga_max, "", 0, 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->MultiCell(3, $riga_max, "", "L", 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->MultiCell(48, $riga_max, $voto_intermedio_tradotto, "B", 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->MultiCell(4, $riga_max, "", 0, 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->SetFont($font_testo, "I", $dim_font);
            $pdf->MultiCell(74, $riga_max, $annotazione_primo_quadrimestre, "B", 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->SetFont($font_testo, 0, $dim_font);
            $pdf->MultiCell(3, $riga_max, "", "R", 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->MultiCell(15, $riga_max, "", "", 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->MultiCell(3, $riga_max, "", "L", 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->MultiCell(48, $riga_max, $voto_finale_tradotto, "B", 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->MultiCell(4, $riga_max, "", 0, 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->SetFont($font_testo, "I", $dim_font);
            $pdf->MultiCell(69, $riga_max, $annotazione_finale, "B", 'C', false, 0, "", "", true, 0, false, true, $riga_max, "M");
            $pdf->SetFont($font_testo, 0, $dim_font);
            $pdf->MultiCell(3, $riga_max, "", "R", 'C', false, 1, "", "", true, 0, false, true, $riga_max, "M");

            $cont_righe += $riga_max;
        }
    }

    $pdf->CellFitScale(130, 0, "", 0, 0, 'C');
    $pdf->CellFitScale(132, 0, "", "LBR", 0, 'C');
    $pdf->CellFitScale(15, 0, "", 0, 0, 'C');
    $pdf->CellFitScale(127, 0, "", "LBR", 1, 'C');

    $x_corrente = $pdf->getX();
    $y_corrente = $pdf->getY();
    $pdf->SetFont($font_testo, '', $dim_font - 2);
    $pdf->setXY(277, 210);
    $pdf->StartTransform();
    $pdf->Rotate(90);
    $pdf->CellFitScale(185, 0, $labels['p23_gradi_valutazione'], 0, 1, 'C');
    $pdf->StopTransform();
    $pdf->setXY($x_corrente, $y_corrente);

    // PEI
    if ($studente['pei'] == 'SI') {
        $pdf->ln(2);
        $pdf->MultiCell(193, 0, "Valutazione riferita al P.E.I.", 0,'C', false, 1, "", "", true, 0, false, true, 20, "T");
    }

    $pdf->SetXY($x_rel - 20, 220);
    $pdf->SetFont($font_testo, "B", $dim_font);
    $pdf->MultiCell(195, 0, "I QUADRIMESTRE", "RLT", 'C', false, 0, "", "", true, 0, false, true, 0, "M");
    $pdf->MultiCell(14, 0, "", 0, 'C', false, 0, "", "", true, 0, false, true, 0, "M");
    $pdf->MultiCell(195, 0, "II QUADRIMESTRE", "RLT", 'C', false, 1, "", "", true, 0, false, true, 0, "M");

    $pdf->SetX($x_rel - 20);
    $pdf->MultiCell(195, 14, $labels['p23_titolo_opzionali'], "LRB", 'C', false, 0, "", "", true, 0, false, true, 14, "M");
    $pdf->MultiCell(14, 14, "", 0, 'C', false, 0, "", "", true, 0, false, true, 14, "M");
    $pdf->MultiCell(195, 14, $labels['p23_titolo_opzionali'], "LRB", 'C', false, 1, "", "", true, 0, false, true, 14, "M");

    $pdf->SetFont($font_testo, 0, $dim_font);
    $pdf->SetX($x_rel - 20);
    $pdf->MultiCell(195, 40, "\n" . $arr_opzionali['primo_quadrimestre'], "RLB", 'L', false, 0);
    $pdf->MultiCell(14, 0, "", 0, 'C', false, 0);
    $pdf->MultiCell(195, 40, "\n" . $arr_opzionali['finale'], "LRB", 'L');
    //}}} </editor-fold>

//}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe' => $id_classe,
    'data_day' => $data_Day,
    'data_month' => $data_Month,
    'data_year' => $data_Year,
    'periodo_pagella' => $periodo_pagella,
];

switch($output_stampa)
{
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF('L', 'mm', 'A3');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>".  implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/'.$rel_dir;
        $temp_dir = $base_dir.substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir.'/';

        exec ('mkdir '.$temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec ('find '.$base_dir.'/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF('L', 'mm', 'A3');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome='export'.date('YmdHi').'.zip';
        exec ('zip -j '. $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd').".pdf");
        exec ('rm -fr '.$dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='".$rel_dir.$nome."';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF('L', 'mm', 'A3');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF('L', 'mm', 'A3');
        foreach ($elenco_studenti as $studente)
        {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
