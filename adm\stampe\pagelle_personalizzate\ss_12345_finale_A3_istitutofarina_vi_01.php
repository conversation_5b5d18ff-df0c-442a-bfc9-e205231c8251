<?php
/*
 * Tipo: Pagella Fine Anno Scuola Secondaria di II Grado istitutofarina_vi
 * Nome: ss_12345_finale_A3_istitutofarina_vi_01
 * Richiesta da: istitutofarina_vi
 * Data: 8/6/22
 *
 * Materie particolari:
 *
 */
/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G', 'ss_12345_finale_A3_istitutofarina_vi_01', 'Pagella Fine Anno Scuola Secondaria di II Grado', 1, 'istitutofarina_vi', 1);
 */

$orientamento = 'L';
$formato = 'A3';
$periodo_pagella = 'finale';

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'                     => $id_classe,
    'periodo_pagella'               => $periodo_pagella,
    'orientamento'                  => $orientamento,

    'data_attestato_day'            => $data_attestato_Day,
    'data_attestato_month'          => $data_attestato_Month,
    'data_attestato_year'           => $data_attestato_Year,

    'data_attestato_IQ_day'            => $data_attestato_IQ_Day,
    'data_attestato_IQ_month'          => $data_attestato_IQ_Month,
    'data_attestato_IQ_year'           => $data_attestato_IQ_Year,

    'stampa_attestato'              => $stampa_attestato,
    'parametro_stampa_solo_giudizi_sospesi'     => $parametro_stampa_solo_giudizi_sospesi,
    'stampa_solo_promossi'          => $stampa_solo_promossi,

    'stampa_moduli'     => $stampa_moduli,
];

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">

    $stampa_solo_promossi = $parametri_stampa['stampa_solo_promossi'];
    $parametro_stampa_solo_giudizi_sospesi = $parametri_stampa['parametro_stampa_solo_giudizi_sospesi'];
    $da_giudizio_sospeso = 'NO';
    $recuperi_inseriti = 'NO';

    $stampa_moduli = $parametri_stampa['stampa_moduli'];
    $orientamento = $parametri_stampa['orientamento'];
    $id_classe = $parametri_stampa['id_classe'];


    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $data_attestato_day = $parametri_stampa['data_attestato_day'];
    $data_attestato_month = $parametri_stampa['data_attestato_month'];
    $data_attestato_year = $parametri_stampa['data_attestato_year'];

    $data_attestato_IQ_day = $parametri_stampa['data_attestato_IQ_day'];
    $data_attestato_IQ_month = $parametri_stampa['data_attestato_IQ_month'];
    $data_attestato_IQ_year = $parametri_stampa['data_attestato_IQ_year'];

    $id_studente = $studente['id_studente'];
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .=  ' (' . $stato['descrizione'] . ')';
            $provincia_nascita = ' ';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    // Estrazione voti
    $voti_p1 = estrai_voti_pagellina_studente_multi_classe($id_classe, 7, $studente['id_studente']);
    $voti_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $studente['id_studente']);

    $arr_voti_finale = $condotta = [];

    $scrutinato = false;
    $monteore_finale = 0;
    $assenze_finale = 0;[];
    $media_voti = $voti_tot_somma_finale = $numero_materie = 0;
    $ann1 = $ann2 = '';
    $arr_materie_sospese = $materie_sospese = [];

    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $monteore_finale += $voti_finale[$id_materia]['monteore_totale'];
        $assenze_finale += $voti_finale[$id_materia]['ore_assenza'];

        if ($materia['in_media_pagelle'] == 'SI') {
            $voti_tot_somma_finale += $voti_finale[$id_materia]['voto_pagellina'];
            $numero_materie++;
        }

        if ($materia['in_media_pagelle'] != 'NV' && //$materia['in_media_pagelle'] != 'NO' &&
            ( !in_array($materia['tipo_materia'], ['RELIGIONE', 'ALTERNATIVA', 'CONDOTTA', 'SOSTEGNO', 'OPZIONALE']) )
            )
        {
            $arr_voti_finale[$id_materia]['p1'] = $voti_p1[$id_materia];
            $arr_voti_finale[$id_materia]['finale'] = $voti_finale[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA')
        {
            $condotta[$id_materia]['p1'] = $voti_p1[$id_materia];
            $condotta[$id_materia]['finale'] = $voti_finale[$id_materia];

            foreach ($voti_p1[$id_materia]['campi_liberi'] as $campo_libero) {
//                if (stripos($campo_libero['nome'], '') !== false) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($value !== '') {
                        $ann1 .= $value . "<br>";
                    }
//                }
            }
            foreach ($voti_finale[$id_materia]['campi_liberi'] as $campo_libero) {
//                if (stripos($campo_libero['nome'], '') !== false) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($value !== '') {
//                        $ann2 .= $value . "<br>";
                    }
//                }
            }
        }

        // scrutinio
        if ($voti_finale[$id_materia]['voto_pagellina'] > 0 && $voti_finale[$id_materia]['voto_pagellina'] != '')
        {
            $scrutinato = true;
        }

        // recuperi inseriti
        if ($materia['in_media_pagelle'] != 'NV')
        {
            if ($voti_finale[$id_materia]['tipo_recupero']!=''
                    ||
                $voti_finale[$id_materia]['esito_recupero']!='')
            {
                $recuperi_inseriti = 'SI';
                $materie_sospese[] = $materia['descrizione'];
                $arr_materie_sospese[$id_materia] = $voti_finale[$id_materia];
            }
        }
    }
    $arr_voti_tot = $arr_voti_finale + $condotta;
    $media_voti = round($voti_tot_somma_finale / $numero_materie, 2);

    if (!empty($materie_sospese)) {
        $ann2 = "Materie con giudizio sospeso: ". implode(', ', $materie_sospese)."<br>". $ann2;
    }

    if($recuperi_inseriti == 'SI')
    {
        $da_giudizio_sospeso = 'SI';
    }
    if (
           ($stampa_solo_promossi == 'SI' && $studente['esito'] != 'Ammesso alla classe successiva')
                ||
           ($parametro_stampa_solo_giudizi_sospesi == 'SI' && $da_giudizio_sospeso != 'SI')
                ||
           ($parametro_stampa_solo_giudizi_sospesi == 'NI' && $da_giudizio_sospeso == 'SI')
                ||
           ($parametro_stampa_solo_giudizi_sospesi == 'SP' && !($da_giudizio_sospeso == 'SI' && stripos($studente['esito'], 'non') === false && $studente['esito']!='Giudizio Sospeso') )
        )
    {
        return 'NO';
    }

    $bocciato = false;
    if ( stripos($studente['esito'], 'non ammesso') !== false ) {
        $bocciato = true;
    }

    // Validazione anno
    if ($monteore_finale > 0)
    {
        $perc_assenza = round($assenze_finale / $monteore_finale, 2) * 100;
        if ($perc_assenza < 25)
        {
            $validazione_anno = "SI";
        }
        else
        {
            $validazione_anno = ($scrutinato) ? "DEROGA" : "NO";
        }
    }
    else
    {
        $validazione_anno = "SI";
    }


    // CURRICULUM
    $anno_scolastico_corrente = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $a = explode('/', $anno_scolastico_corrente);
    $anno_inizio = (int) $a[0];
    $anno_fine = (int) $a[1];
    $curriculum_studente = estrai_curriculum_studente((int) $id_studente);
    $anno_scolastico_corrente = $anno_inizio . "/" . $anno_fine;
    $anno_scolastico_precedente = ($anno_inizio - 1) . "/" . ($anno_fine - 1);
    //estraggo i dati di provenienza e di quante volte è ripetente
    $provenienza = '';
    for($cont_curr=0; $cont_curr <count($curriculum_studente); $cont_curr++)
    {
        if($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_precedente)
        {
            if($studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                    $provenienza = $curriculum_studente[$cont_curr]['classe_desc'].' '.$curriculum_studente[$cont_curr]['nome_scuola'];
            }
            else
            {
                    $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
            }
        }
        else {
            $classe_attuale = $curriculum_studente[$cont_curr]['classe'];

            if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_corrente
                && $classe_attuale == $curriculum_studente[$cont_curr]['classe']
                && $studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola']
                && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                if (($curriculum_studente[$cont_curr]['esito'] == 'Iscritto') || ($cont_curr <count($curriculum_studente) -1)) {

                    // Nel caso nel curriculum non sia stata inserita la scuola di provenienza
                    if($curriculum_studente[$cont_curr]['nome_scuola'] != "") {
                        $provenienza = $curriculum_studente[$cont_curr]['nome_scuola'];
                    } else {
                        $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
                    }
                }
            }
        }
    }
    if ($provenienza == '3a media') {
        $provenienza = "3^ scuola secondaria di I grado";
    }

    // titolo di ammissione
    $esito_precedente = estrai_esito_e_volte_iscritto_da_curriculum_studente((int) $studente['id_studente'], $anno_scolastico_precedente);
    $titolo_ammissione = $esito_precedente['esito'];
    if ($titolo_ammissione == 'Giudizio Sospeso') {
        $titolo_ammissione = 'Ammesso alla classe successiva';
    }

    // numero volte iscritto alla classe
    $numero_volte_iscritto_studente_classe = $studente['mat_esito']['numero_volte_iscritto'];
    $numero_volte_iscritto_studente_classe_trad = '';
    switch ($numero_volte_iscritto_studente_classe) {
        case 1: $numero_volte_iscritto_studente_classe_trad = 'prima';break;
        case 2: $numero_volte_iscritto_studente_classe_trad = 'seconda';break;
        case 3: $numero_volte_iscritto_studente_classe_trad = 'terza';break;
    }

    if ($validazione_anno == 'SI') {
        $attp4 = 'ha/<strike>non ha</strike> ';
    } else {
        $attp4 = '<strike>ha</strike>/non ha ';
    }

    //{{{ <editor-fold defaultstate="collapsed" desc="dizionario">
    // Dizionario temporaneo
    $labels = [
        "validita"                  => "VALIDITÀ DELL’ANNO SCOLASTICO",
        "validita_dpr"              => "(Art. 14, comma 7 del D.P.R. n. 122/2009)",
        "validita_fini"             => "Ai fini della validità dell’anno e dell’ammissione allo scrutinio finale, l’alunn||min_oa||*:",

        "scrutinio_desc"            => "Ai fini della validità dell’anno e dell’ammissione allo scrutinio finale, l’alunn||min_oa||:",
        "scrutinio_desc_val_1"      => "ha frequentato per almeno tre quarti dell’orario annuale;",
        "scrutinio_desc_val_2"      => "non ha frequentato per almeno tre quarti dell’orario annuale, ma ha usufruito della deroga;",
        "scrutinio_desc_val_3"      => "non ha frequentato per almeno tre quarti dell’orario annuale.",

        "dirigente"                 => "Il Dirigente scolastico\nprof. {$studente['nome_dirigente']}",
        "titolo"                    => "Ministero dell’Istruzione e del Merito",
        "scuola_tipo"               => "Istituzione\nscolastica",
        "scuola_tipo_val"           => "Scuola Secondaria\ndi Secondo Grado\nParitaria",

        "titolo_scheda"             => "SCHEDA DI VALUTAZIONE",

        "alunno"                    => "dell’alunn||min_oa|| ",
        "alunno_1"                  => "L'alunn||min_oa|| ",

        "attestato_desc"            => "Visti i risultati conseguiti si dichiara che",
        "attestato_ammissione_1"    => "l’alunn||min_oa||",
        "attestato_ammissione_2"    => "è stat||min_oa|| ",

        "valutazioni"               => "VALUTAZIONI PERIODICHE",

        "attestato_att"             => "si attesta che l'alunn||min_oa|| è ",
        "attestato_att2_terze"      => "ammess||min_oa|| all'Esame di Stato conclusivo del primo ciclo di istruzione",

        "nota_1pag"                 => "Il presente certificato non può essere prodotto agli organi della pubblica amministrazione o ai privati gestori di pubblici servizi (art. 15, comma 1, L. 183/11)",

        "dati_titolo"               => "Posizione scolastica dello studente",
        "dati_anno"                 => "Anno Scolastico ",
        "dati_registro"             => "N. REGISTRO GEN.",
        "dati_classe"               => "CLASSE",
        "dati_provenienza"          => "PROVENIENZA",
        "dati_ammissione"           => "TITOLO DI AMMISSIONE <sup>(1)</sup>",
        "dati_indirizzo"            => "INDIRIZZO",
        "iscrizione_n_volte"        => "ISCRIZIONE PER LA " . $numero_volte_iscritto_studente_classe .  " VOLTA <sup>(2)</sup>",
        "dati_firma_dirigente"      => "Il Dir. Serv. Gen. E Amm <sup>(3)</sup>",

        "attestato_txt"             =>
"Visti gli atti d’ufficio e accertato che l’alunn||min_oa||, ai fini della validità dell’anno scolastico (comma 1, art. 11, decreto legislativo 19 febbraio 2004, n. 59),
$attp4 frequentato le lezioni e le attività didattiche per almeno i ¾ dell’orario personale previsto;<br><br>
Visti gli atti d’ufficio e la valutazione del Consiglio di classe, si attesta che {$studente['cognome']} {$studente['nome']} è stat||min_oa||",

        "note_titolo"               => "NOTE",
        "note_tot"                  =>
            "<table>
                <tr>
                  <td width=\"4%\"><i>(1)</i></td>
                  <td width=\"96%\">PROMOZIONE; IDONEITA’; QUALIFICA; Idoneità all’ultima classe a seguito di esito positivo dell’esame preliminare e mancato superamento esami di Stato.</td>
                </tr>
                <tr>
                  <td><i>(2)</i></td>
                  <td>PRIMA; SECONDA; TERZA.</td>
                </tr>
                <tr>
                  <td><i>(3)</i></td>
                  <td>La firma è omessa ai sensi dell’Art. 3, D.to Lgs. 12/02/1993, n. 39.</td>
                </tr>
                <tr>
                  <td><i>(4)</i></td>
                  <td>“Il riquadro può essere utilizzato anche:<br>
              - per l’annotazione delle materie Art. 4, comma 6 del D.P.R. 122/2009;<br>
              - per l’annotazione prevista dall’Art. 9, comma 1 del D.P.R. 122/2009;<br>
              - per eventuali altre annotazioni o indicazione di rilascio di certificazione”.</td>
                </tr>
                <tr>
                  <td><i>(5)</i></td>
                  <td>Per le classi terminali indicare: ammess||min_oa|| agli esami – non ammess||min_oa|| agli esami.</td>
                </tr>
                <tr>
                  <td><i>(6)</i></td>
                  <td>Solo per esami di qualifica professionale.</td>
                </tr>
                <tr>
                  <td><i>(7)</i></td>
                  <td>promoss||min_oa|| – non promoss||min_oa||.<br>Per le classi terminali indicare: ammess||min_oa|| – non ammess||min_oa||.</td>
                </tr>
          </table>",

        "studente"                => "STUDENT||max_eessa||: ",

    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
        $studente['esito'] = str_replace('Licenziato', 'Licenziata', $studente['esito']);
        $studente['esito'] = str_replace('licenziato', 'licenziata', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k=>$label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }
    //}}} </editor-fold>
    //}}} </editor-fold>

    // Validazione ammissione scrutinio
    $scrutinio_testo = '';
//$validazione_anno = 'DEROGA'; # fissato per il corona virus
    switch ($validazione_anno) {
        case 'SI':
            $scrutinio_testo = $labels['scrutinio_desc_val_1'] ;
            break;
        case 'DEROGA':
            $scrutinio_testo = $labels['scrutinio_desc_val_2'];
            break;
        case 'NO':
            $scrutinio_testo = $labels['scrutinio_desc_val_3'];
            break;
    }

    $studente['esito'] = str_replace('esame di stato', "all'Esame di Stato", $studente['esito']);

    $firma_img = '';
//    if (file_exists('immagini_scuola/firma_dirigente.png')) {
//        $firma_img = '<img src="immagini_scuola/firma_dirigente.png" height="30" width="130">';
//    }

    $timbro_img = '';
//    if (file_exists('immagini_scuola/timbro.png')) {
//        $timbro_img = '<img src="immagini_scuola/timbro.png" height="40" width="40">';
//    }

    // PDF parametri
    $fnt = 'helvetica';
    $fd = 10;
    $h_header = 10;
    $img_logo = '';
    if (file_exists('immagini_scuola/logo_poloni.png'))        {
        $img_logo = 'immagini_scuola/logo_poloni.png';
    }

    if (
            $stampa_moduli != 'SI'
            ||
            (
            empty($arr_materie_sospese)
            )
        )
    {
    $pdf->AddPage($orientamento, 'A3');
    $pdf->SetAutoPageBreak("off", 0);
    $pdf->SetFont($fnt, '', $fd);
    $y_start = $pdf->getY();
    $page_width = $pdf->getPageWidth();

    $page_dims = $pdf->getPageDimensions();
    $m_sinistro = $page_dims['lm'];
    $m_top = $page_dims['tm'];
    $m_destro = $page_dims['rm'];

    $half_page = $page_width / 2;
    $w_page_parts = $half_page - $m_destro - $m_sinistro;
    $xst2 = $half_page + $m_sinistro;

    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 4-1">
    $tbl_scrut = '<table cellpadding="4">';
    $scrut_label_1 = '<tr><td width="5%"></td><td width="95%">' . $labels['scrutinio_desc_val_1'].'</td></tr>';
    $scrut_label_2 = '<tr><td width="5%"></td><td width="95%">' . $labels['scrutinio_desc_val_2'].'</td></tr>';
    $scrut_label_3 = '<tr><td width="5%"></td><td width="95%">' . $labels['scrutinio_desc_val_3'].'</td></tr>';
    switch ($validazione_anno) {
        case 'SI':
            $scrut_label_1 = '<tr><td width="5%" align="center">X</td><td width="95%">' . $labels['scrutinio_desc_val_1'].'</td></tr>';
            break;
        case 'DEROGA':
            $scrut_label_2 = '<tr><td width="5%" align="center">X</td><td width="95%">' . $labels['scrutinio_desc_val_2'].'</td></tr>';
            break;
        case 'NO':
            $scrut_label_3 = '<tr><td width="5%" align="center">X</td><td width="95%">' . $labels['scrutinio_desc_val_3'].'</td></tr>';
            break;
    }
    $tbl_scrut .= $scrut_label_1.$scrut_label_2.$scrut_label_3.'</table>';

    $scrutinio_tot = $labels['validita_fini'] . '<br><br>' . $tbl_scrut . '<br>' . '<font size="-2">'. '<br><i> * barrare la voce che interessa</i>'.'</font>';
    $y_attestato_box = $pdf->GetY();
    $pdf->Ln(4);
    $pdf->SetFont($fnt, 'B', $fd+4);
    $pdf->CellFitScale($w_page_parts, 0, $labels['validita'], 0, 1, 'C');
    $pdf->SetFont($fnt, 'B', $fd+1);
    $pdf->CellFitScale($w_page_parts, 0, $labels['validita_dpr'], 0, 1, 'C');
    $pdf->Ln(4);
    $pdf->SetFont($fnt, '', $fd);
    $pdf->writeHTMLCell($w_page_parts-5, 0, $pdf->GetX()+5, '', $scrutinio_tot, 0, 1);
    $pdf->Ln(2);
    $pdf->writeHTMLCell($w_page_parts, $pdf->GetY()-$y_attestato_box, '', $y_attestato_box, '', 1, 1);
    $pdf->Ln(8);

    $pdf->SetFont($fnt, 'B', $fd+4);
    $pdf->CellFitScale($w_page_parts, 0, 'RISULTATO FINALE', 0, 1, 'C');
    $pdf->SetFont($fnt, 'B', $fd);
//    if ($studente['classe'] == 5) {
//        $pdf->CellFitScale($w_page_parts, 0, '(ai sensi dell’ O.M. n 10 del 16-05-2020)', 0, 1, 'C');
//    }
//    else {
//        $pdf->CellFitScale($w_page_parts, 0, '(ai sensi dell’ O.M. n 11 del 16-05-2020)', 0, 1, 'C');
//    }
    $pdf->Ln(2);
    $pdf->SetFont($fnt, '', $fd);
    $pdf->CellFitScale($w_page_parts, 0, $labels['attestato_desc'], 0, 1, 'C');
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $labels['attestato_ammissione_1'].' <b>'.$studente['cognome'].' '.$studente['nome'].'</b>', 0, 1, false, true, 'C');
    $pdf->CellFitScale($w_page_parts, 0, $labels['attestato_ammissione_2'].$studente['esito'], 0, 1, 'C');

    $pdf->SetFont($fnt, '', $fd);
    $pdf->ln(8);

    $comune_data = 'Vicenza' . ',  ' . "$data_attestato_day/$data_attestato_month/$data_attestato_year";
    $pdf->CellFitScale(0, 0, $comune_data, 0, 1);
    $pdf->ln(15);

    $pdf->SetFont($fnt, '', $fd);
    $firme = '<table align="center">
            <tr>
                <td width="35%">___________________________<br>Il (i) genitore (i) o chi ne fa le veci</td>
                <td width="30%"></td>
                <td width="35%">___________________________<br>Il Dirigente Scolastico<br><i>prof. '.$studente['nome_dirigente'].'</i><sup>(3)</sup></td>
            </tr>
        </table>';
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $firme);
    $pdf->ln(35);


    $curr_x_ln = $pdf->GetX() + 5;
    $curr_y_ln = $pdf->GetY();
//    $pdf->Line($curr_x_ln, $curr_y_ln, $curr_x_ln + $w_page_parts-5, $curr_y_ln);
    $pdf->Ln(4);
//    $pdf->SetFont($fnt, 'B', $fd + 1);
//    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $labels['note_titolo'], 0, 1, false, true, 'C');
    $pdf->Ln(4);
    $pdf->SetFont($fnt, '', $fd-1);
    $pdf->writeHTMLCell($w_page_parts-20, 0, $m_sinistro+15, '', $labels['note_tot'], 0, 1, false, true, 'L');
    $pdf->SetFont($fnt, '', $fd);
//    $pdf->Ln(8);

//    $pdf->SetFont($fnt, 'I', $fd-1);
//    $pdf->MultiCell($w_page_parts, 0, $labels['nota_1pag'], 0, 'L');
//    $pdf->SetFont($fnt, '', $fd);


    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1">
    $pdf->SetFont($fnt, '', $fd-1);
    $pdf->SetXY($xst2+135, 15);
    $pdf->CellFitScale(0, 0, "MODELLO SP-QP", 0, 1, 'C');

    $pdf->Image('immagini_scuola/logo_repubblica_colori.jpg', $xst2 + ($w_page_parts/2) - 10, $m_top+3, 25, 25, 'JPG', false);
    $pdf->setY($m_top+$h_header+12);
    $pdf->Ln(5);
    $pdf->SetFont($fnt, 'I', $fd + 9);
    $pdf->SetX($xst2);
    $pdf->MultiCell($w_page_parts, 0, $labels['titolo'], 0, 'C');
    $pdf->Ln(6);
    $pdf->SetFont($fnt, 'B', $fd + 2);
    $pdf->SetX($xst2);
//    $pdf->MultiCell($w_page_parts*0.35, 18, $labels['scuola_tipo'], 1, 'C', false, 0, '', '', true, 0, false, true, 18, 'M');
//    $pdf->SetFont($fnt, '', $fd + 2);
//    $pdf->MultiCell($w_page_parts*0.65, 18, "Scuola Secondaria di II grado", 1, 'C', false, 1, '', '', true, 0, false, true, 18, 'M');;
//    $pdf->Ln(8);
////    echo_debug($studente);
//    $scuola_desc_val =
//            'ISTITUTO "FARINA" - Paritario'
//            . "\n{$studente['descrizione_tipi_scuole']}"
//            . "\nVia IV novembre, 34 -36100 Vicenza"
//            . "\nE-mail: <EMAIL>";
//    $pdf->SetFont($fnt, 'B', $fd + 2);
//    $pdf->SetX($xst2);
//    $pdf->MultiCell($w_page_parts*0.35, 28, $labels['scuola_tipo_val'], 1, 'C', false, 0, '', '', true, 0, false, true, 28, 'M');
//    $pdf->SetFont($fnt, 'B', $fd + 2);
//    $pdf->MultiCell($w_page_parts*0.65, 28, $scuola_desc_val, 1, 'C', false, 1, '', '', true, 0, false, true, 28, 'M');
    $pdf->MultiCell($w_page_parts, 22, "Istituto Omnicomprensivo Paritario ''FARINA''
Via IV Novembre, 34 - 36100 VICENZA
Tel. 0444/513561 - Fax. 0444/303277
E-mail:  <EMAIL>", 1, 'C', false, 1, '', '', true, 0, false, true, 22, 'M');
    $pdf->Ln(4);
    $pdf->SetX($xst2);
    $pdf->MultiCell($w_page_parts, 18, "Scuola Secondaria di 2° Grado Paritaria
{$studente['descrizione_scuola']}
Codice meccanografico {$studente['codice_meccanografico_secondario']}", 1, 'C', false, 1, '', '', true, 0, false, true, 18, 'M');;

    $pdf->Ln(6);
    $pdf->SetFont($fnt, 'B', $fd + 10);
    $pdf->SetX($xst2);
    $pdf->CellFitScale(0, 0, $labels['titolo_scheda'], 0, 1, 'C');
    $pdf->SetFont($fnt, 'B', $fd + 5);
    $pdf->SetX($xst2);
    $pdf->CellFitScale(0, 0, "Anno Scolastico $anno_scolastico_attuale", 0, 1, 'C');
    $pdf->Ln(6);

    $larghezza_max_cella = $w_page_parts;
	$altezza_cella = 5;

	$pdf->SetX($xst2);
    $pdf->SetCellPadding( 1 );
    $pdf->SetFont($fnt, 'B', $fd + 4);
	$pdf->CellFitScale($larghezza_max_cella, $altezza_cella+4, 'Dati anagrafici dello studente', 'TRL', 1, 'C');
    $pdf->SetFont($fnt, '', $fd);

	$pdf->SetX($xst2);
    $pdf->SetFont($fnt, 'B', $fd + 2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['cognome'], 'L', 0, 'C');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['nome'], 0, 0, 'C',FALSE,'',0, FALSE,'T', 'B');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['codice_fiscale'], 'R', 1, 'C');

	$pdf->SetX($xst2);
    $pdf->SetFont($fnt, '', $fd-2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella+3, 'COGNOME', 'L', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella+3, 'NOME', 0, 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella+3, 'CODICE FISCALE', 'R', 1, 'C',FALSE,'',0, FALSE,'T', 'T');

    $luogo_nascita = ($studente['descrizione_nascita'] == 'COMUNE ESTERO' || !$studente['descrizione_nascita']) ? $studente['citta_nascita_straniera'] : $studente['descrizione_nascita'];
	$pdf->SetX($xst2);
    $pdf->SetFont($fnt, 'B', $fd + 2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['data_nascita_ext'], 'L', 0, 'C');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $luogo_nascita, 0, 0, 'C');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, str_replace(array('(', ')'), '', $studente['provincia_nascita_da_comune']), 'R', 1, 'C');

	$pdf->SetX($xst2);
    $pdf->SetFont($fnt, '', $fd-2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella+3, 'DATA DI NASCITA', 'LB', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella+3, 'COMUNE DI NASCITA', 'B', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella+3, 'PROV. O STATO ESTERO', 'RB', 1, 'C',FALSE,'',0, FALSE,'T', 'T');


    $pdf->SetFont($fnt, '', $fd);

    $pdf->ln(5);

    $tbl_dati = # tablre border to style="border:1px solid"
        '<table  style="border:0.1px solid black"  align="center">
            <tr>
                <td width="50%"><b>' . $labels['dati_titolo'] . '</b></td>
                <td width="50%"><b>' . $labels['dati_anno'] . $anno_scolastico_attuale . '</b></td>
            </tr>
            <tr>
                <td>&nbsp;</td>
            </tr>

            <tr>
                <td width="40%"><b>' . $studente['matricola'] . '</b></td>
                <td width="30%"><b>' . $studente['classe']. '^ '.$studente['sezione'] . '</b></td>
                <td width="30%"><b>' . $provenienza . '</b></td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['dati_registro'] . '</font></td>
                <td><font size="-2">' . $labels['dati_classe'] . '</font></td>
                <td><font size="-2">' . $labels['dati_provenienza'] . '</font></td>
            </tr>

            <tr>
                <td>&nbsp;</td>
            </tr>

            <tr>
                <td width="40%"><b>' . $titolo_ammissione . '</b></td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['dati_ammissione'] . '</font></td>
            </tr>

            <tr>
                <td>&nbsp;</td>
            </tr>

            <tr>
                <td width="40%"><b>' . $studente['descrizione_indirizzi'] . '</b></td>
                <td width="60%">' . $labels['iscrizione_n_volte'] . '</td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['dati_indirizzo'] . '</font></td>
                <td></td>
            </tr>

            <tr>
                <td>&nbsp;</td>
            </tr>

            <tr>
                <td width="100%" align="right">'.
//            $firma_img.
            '__________________<br><font size="-2">' . $labels['dati_firma_dirigente'] . '</font></td>
            </tr>
        </table>';
    $pdf->writeHTMLCell(0, 0, $xst2, '', $tbl_dati, 0, 1);

//    $attestato_p1 =
//        '<table  style="border:0.1px solid black" cellpadding="3" align="center">'
//            . '<tr>'
//                . '<td><b>ATTESTATO</b></td>'
//            . '</tr>'
//            . '<tr>'
//                . '<td align="justify">'.$labels['attestato_txt'].'</td>
//            </tr>
//            <tr align="center">
//                <td><b>'.$studente['esito'].'</b></td>'
//        . '</tr>'
//        . '</table>';
//    $pdf->SetFont($fnt, '', $fd);
//    $pdf->ln(4);
//	$pdf->SetX($xst2);
//    $pdf->writeHTMLCell(0, 0, $xst2, '',$attestato_p1, 0, 1);
    $pdf->ln(4);
    $firme = '<table>
            <tr>
                <td width="35%">'.'Vicenza' . ', ' . $data_attestato_day . '/' . $data_attestato_month . '/' . $data_attestato_year.'</td>
                <td width="15%"align="right">'.$timbro_img.'</td>
                <td width="50%" align="center">'.$firma_img.'<br>______________________</td>
            </tr>
                <td width="50%"></td>
                <td width="50%" align="center">Il Dirigente Scolastico<br><i>prof. '.$studente['nome_dirigente'].'</i><sup>(3)</sup></td>
            </tr>
        </table>';
	$pdf->SetX($xst2);
    $pdf->writeHTMLCell(0, 0, $xst2, '',$firme);

   //}}} </editor-fold>
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2-3: Voti">
    $pdf->AddPage($orientamento, 'A3');

    $pdf->SetFont($fnt, 'B');
    $pdf->CellFitScale($w_page_parts*0.24, 0, $studente['cognome'], 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.24, 0, $studente['nome'] , 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.20, 0, $studente['codice_fiscale'], 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.20, 0, $studente['codice_ministeriale'], 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.12, 0, $anno_scolastico_attuale, 1, 1, 'C');
    $pdf->SetFont($fnt, '', $fd-3);
    $pdf->CellFitScale($w_page_parts*0.24, 0, 'COGNOME', 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.24, 0, 'NOME', 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.20, 0, 'CODICE FISCALE', 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.20, 0, 'CODICE ISTITUTO', 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.12, 0, 'ANNO SCOLASTICO', 1, 1, 'C');

    $pdf->SetXY($xst2, 10);
    $pdf->SetFont($fnt, 'B');
    $pdf->CellFitScale($w_page_parts*0.24, 0, $studente['cognome'], 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.24, 0, $studente['nome'] , 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.20, 0, $studente['codice_fiscale'], 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.20, 0, $studente['codice_ministeriale'], 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.12, 0, $anno_scolastico_attuale, 1, 1, 'C');
    $pdf->SetX($xst2);
    $pdf->SetFont($fnt, '', $fd-3);
    $pdf->CellFitScale($w_page_parts*0.24, 0, 'COGNOME', 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.24, 0, 'NOME', 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.20, 0, 'CODICE FISCALE', 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.20, 0, 'CODICE ISTITUTO', 1, 0, 'C');
    $pdf->CellFitScale($w_page_parts*0.12, 0, 'ANNO SCOLASTICO', 1, 1, 'C');


    //MultiCell( $w, $h, $txt, $border = 0, $align = 'J', $fill = false, $ln = 1, $x = '', $y = '', $reseth = true, $stretch = 0, $ishtml = false, $autopadding = true, $maxh = 0, $valign = 'T', $fitcell = false )
    $pdf->SetY(26);

    $pdf->SetFont($fnt, 'B', $fd);
    $pdf->MultiCell( $w_page_parts*0.40, 20, "DISCIPLINE", 1, 'C', false, 0, '', '', true, 1, false, true, 20, 'M', true);
    $xaftdiscipline = $pdf->GetX();
    $pdf->MultiCell( $w_page_parts*0.60, 12, "VALUTAZIONE PERIODICA\nTRIMESTRE", 1, 'C', false, 1, '', '', true, 1, false, true, 12, 'M', true);
    $pdf->SetX($xaftdiscipline);
    $pdf->MultiCell( $w_page_parts*0.45, 8, "VOTO UNICO", 1, 'C', false, 0, '', '', true, 1, false, true, 8, 'M', true);
    $pdf->MultiCell( $w_page_parts*0.15, 8, "Ore Assenza", 1, 'C', false, 1, '', '', true, 1, false, true, 8, 'M', true);

    $pdf->SetXY($xst2, 26);
    $pdf->SetFont($fnt, 'B', $fd);
    $pdf->MultiCell( $w_page_parts*0.40, 20, "DISCIPLINE", 1, 'C', false, 0, '', '', true, 1, false, true, 20, 'M', true);
    $xaftdiscipline = $pdf->GetX();
    $pdf->MultiCell( $w_page_parts*0.60, 8, "SCRUTINIO FINALE", 1, 'C', false, 1, '', '', true, 1, false, true, 8, 'M', true);
    $pdf->SetX($xaftdiscipline);
    $pdf->MultiCell( $w_page_parts*0.45, 12, "VOTO UNICO", 1, 'C', false, 0, '', '', true, 1, false, true, 12, 'M', true);
    $pdf->MultiCell( $w_page_parts*0.15, 12, "Totale\nOre Assenza", 1, 'C', false, 1, '', '', true, 1, false, true, 12, 'M', true);

    $pdf->SetY(50);

//    echo_debug($arr_voti_tot);
    foreach ($arr_voti_tot as $voto)
    {
        $voto_p1 = $voto['p1'];
        $voto_fin = $voto['finale'];

        $desc = $v1num = $vfinnum = $v1trad = $vfintrad = $ass1 = $assfin ='';

        $desc = $voto_fin['descrizione'];
        $ass1 = traduci_minuti_in_ore_minuti($voto_p1['ore_assenza'], 'ORE_ARROTONDATE');
        $assfin =  traduci_minuti_in_ore_minuti($voto_fin['ore_assenza'], 'ORE_ARROTONDATE');

        foreach ($voto_fin['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $voto_fin['voto_pagellina']) {
                $vfintrad = decode(strtoupper($significato['valore_pagella'])).'/decimi';
                $vfinnum = $voto_fin['voto_pagellina'].'/10';
            }
        }

        foreach ($voto_p1['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $voto_p1['voto_pagellina']) {
                $v1trad= decode(strtoupper($significato['valore_pagella'])).'/decimi';
                $v1num = $voto_p1['voto_pagellina'].'/10';
            }
        }

        $pdf->SetFont($fnt, '', $fd);

        $pdf->MultiCell( $w_page_parts*0.40, 9, $desc, 1, 'L', false, 0, '', '', true, 1, false, true, 9, 'M', true);
        $pdf->MultiCell( $w_page_parts*0.18, 9, $v1num, 1, 'C', false, 0, '', '', true, 1, false, true, 9, 'M', true);
        $pdf->MultiCell( $w_page_parts*0.27, 9, $v1trad, 1, 'C', false, 0, '', '', true, 1, false, true, 9, 'M', true);
        $pdf->MultiCell( $w_page_parts*0.15, 9, $ass1, 1, 'C', false, 0, '', '', true, 1, false, true, 9, 'M', true);

        $pdf->SetX($xst2);
        $pdf->MultiCell( $w_page_parts*0.40, 9, $desc, 1, 'L', false, 0, '', '', true, 1, false, true, 9, 'M', true);
        $pdf->MultiCell( $w_page_parts*0.18, 9, $vfinnum, 1, 'C', false, 0, '', '', true, 1, false, true, 9, 'M', true);
        $pdf->MultiCell( $w_page_parts*0.27, 9, $vfintrad, 1, 'C', false, 0, '', '', true, 1, false, true, 9, 'M', true);
        $pdf->MultiCell( $w_page_parts*0.15, 9, $assfin, 1, 'C', false, 1, '', '', true, 1, false, true, 9, 'M', true);


//            $pdf->MultiCell( $w_page_parts*0.30, 9, $cella_descrizione_materia, 1, 'L', false, 0, '', '', true, 1, false, true, 8, 'M', true);
//            $pdf->CellFitScale( $w_page_parts*0.05, 9, $voto_IQ_scritto, 1, 0, 'C', false, '', 1, 0);
//            $pdf->CellFitScale( $w_page_parts*0.14, 9, $voto_tradotto_IQ_scritto, 1, 0, 'C', false, '', 1, 0);
//            $pdf->CellFitScale( $w_page_parts*0.05, 9, $voto_IQ_orale, 1, 0, 'C', false, '', 1, 0);
//            $pdf->CellFitScale( $w_page_parts*0.14, 9, $voto_tradotto_IQ_orale, 1, 0, 'C', false, '', 1, 0);
//            $pdf->CellFitScale( $w_page_parts*0.05, 9, $voto_IQ_pratico, 1, 0, 'C', false, '', 1, 0);
//            $pdf->CellFitScale( $w_page_parts*0.14, 9, $voto_tradotto_IQ_pratico, 1, 0, 'C', false, '', 1, 0);
////            $pdf->CellFitScale( $w_page_parts*0.05, 9, $voto_IQ_unico, 1, 0, 'C', false, '', 1, 0);
////            $pdf->CellFitScale( $w_page_parts*0.14, 9, $voto_tradotto_IQ_unico, 1, 0, 'C', false, '', 1, 0);
//            $pdf->CellFitScale( $w_page_parts*0.13, 9, $IQ_assenze_trad, 1, 0, 'C', false, '', 1, 0);
//
//
//            $pdf->SetX($xst2);
//            $pdf->MultiCell( $w_page_parts*0.40, 9, $cella_descrizione_materia, 1, 'L', false, 0, '', '', true, 1, false, true, 8, 'M', true);
//            $pdf->CellFitScale( $w_page_parts*0.08, 9, $voto_finale, 1, 0, 'C', false, '', 1, 0);
//            $pdf->CellFitScale( $w_page_parts*0.22, 9, $voto_finale_tradotto, 1, 0, 'C', false, '', 1, 1);
//            $pdf->CellFitScale( $w_page_parts*0.15, 9, $esito_df, 1, 0, 'C', false, '', 1, 1);
//            $pdf->CellFitScale( $w_page_parts*0.15, 9, $finale_assenze_trad, 1, 1, 'C', false, '', 1, 0);
    }
    $pdf->Ln(6);

    $pdf->SetFont($fnt, '', $fd);

    $crediti_stampa = '';
    $tot_crediti = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'] + $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'] + $studente['crediti_quinta'] + $studente['crediti_finali_agg'];

    $txt_media_voti = '';
//    print_r( $studente);
    if ( $studente['tipo_indirizzo'] == 5 ) { // liceo linguistico quadriennale
        switch ($studente['classe'])
        {
            case 2:
                $crediti_stampa = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'];
                break;
            case 3:
                $crediti_stampa = $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'];
                break;
            case 4:
                $crediti_stampa = $studente['crediti_quinta'] + $studente['crediti_finali_agg'];
                break;
        }
        if (!$bocciato && $studente['classe']>=2) {
            $txt_media_voti = $media_voti; //($studente['classe']>2 && $bocciato==false?$media_voti:'')
        }
    }
    else
    {   // indirizzi normali
        switch ($studente['classe'])
        {
            case 3:
                $crediti_stampa = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'];
                break;
            case 4:
                $crediti_stampa = $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'];
                break;
            case 5:
                $crediti_stampa = $studente['crediti_quinta'] + $studente['crediti_finali_agg'];
                break;
        }
        if (!$bocciato && $studente['classe']>2) {
            $txt_media_voti = $media_voti; //($studente['classe']>2 && $bocciato==false?$media_voti:'')
        }
    }
//echo_debug($studente);
    $tbl_esame = $riga_crediti_5 = '';
    if ($studente['classe'] == 5
            ||
        (
            $studente['tipo_indirizzo'] == 5 && $studente['classe'] == 4
        )) {
//        $tbl_esame = '
//                <tr>
//                    <td><b>ESAMI</b></td>
//                </tr>
//                <tr>
//                    <td><b>Voto unico (in lettere) <sup>(6)</sup>:</b> '.traduci_numero_in_lettere($studente['voto_qualifica']).'</td>
//                </tr>
//            ';
        $riga_crediti_5 = '<tr align="left"><td colspan="2">Totale dei crediti: '.$tot_crediti.'</td></tr>';
    }

    $tbl_cred = '<table border="0.1px" align="center" cellpadding="2">'.$tbl_esame.'
            <tr>
                <td width="100%"><b>CREDITO SCOLASTICO</b></td>
            </tr>
            <tr>
                <td width="50%"><font size="-2">Media dei voti conseguiti nello scrutinio finale:</font><br><b>'.$txt_media_voti.'</b></td>
                <td width="50%"><font size="-2">Credito scolastico attribuito nell\'anno scolastico in corso:</font><br><b>'.$crediti_stampa.'</b></td>
            </tr>
            '.$riga_crediti_5.'
        </table>';
    $pdf->writeHTMLCell($w_page_parts, 0, $xst2, '', $tbl_cred, 0, 1);
    $pdf->ln(3);
//    }

    $tbl_annotazioni_1 = '<table border="0.1px" cellpadding="2">
            <tr align="center">
                <td><b>ANNOTAZIONI <sup>(4)</sup></b></td>
            </tr>
            <tr>
                <td>'.$ann1.'</td>
            </tr>
        </table>';
    $tbl_annotazioni_2 = '<table border="0.1px" cellpadding="2">
            <tr align="center">
                <td><b>ANNOTAZIONI <sup>(4)(5)</sup></b></td>
            </tr>
            <tr>
                <td>'.$ann2.'</td>
            </tr>
        </table>';

    $y_st_ann = $pdf->GetY();
    $pdf->writeHTMLCell($w_page_parts, 0, '', '',$tbl_annotazioni_1, 0, 1);
    $y_fine_ann1 = $pdf->GetY();
    $pdf->SetY($y_st_ann);
    $pdf->writeHTMLCell($w_page_parts, 0, $xst2, '', $tbl_annotazioni_2, 0, 1);
    if ($y_fine_ann1 > $pdf->GetY()) {
        $pdf->SetY($y_fine_ann1);
    }
    $pdf->ln(3);

    $pdf->writeHTMLCell($w_page_parts, $h_header, '', '', 'Vicenza' . ", <b>$data_attestato_IQ_day/$data_attestato_IQ_month/$data_attestato_IQ_year</b>");
    $pdf->writeHTMLCell($w_page_parts, $h_header, $xst2, '', 'Vicenza' . ", <b>$data_attestato_day/$data_attestato_month/$data_attestato_year</b>");
    $pdf->ln(10);
    $firme_2 = '<table align="center">
            <tr>
                <td width="35%">___________________________<br>Il (i) genitore (i) o chi ne fa le veci</td>
                <td width="30%"></td>
                <td width="35%">___________________________<br>Il Dirigente Scolastico<br><i>prof. '.$studente['nome_dirigente'].'</i><sup>(3)</sup></td>
            </tr>
        </table>';
    $pdf->SetFont($fnt, '', $fd);
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $firme_2);

    $pdf->SetFont($fnt, '', $fd);
    $pdf->writeHTMLCell($w_page_parts, 0, $xst2, '', $firme_2);

    //}}} </editor-fold>
    }

    if ($stampa_moduli == 'SI' && !empty($arr_materie_sospese))
    {
        $pdf->AddPage('P', 'A4');
        $pdf->SetFont($fnt, '', 10);

//        if (file_exists('immagini_scuola/logo_istituto.png'))
//        {
//            $pdf->Image('immagini_scuola/logo_istituto.png', 15,15, 15,0, 'PNG', '', 'C');
//        }
//
//        $pdf->writeHTMLCell( 0, 0, 38,'',$intestazione, 0, 1, false, true, 'C');
        inserisci_intestazione_pdf($pdf, (int) $id_classe);
        $pdf->SetFont($fnt, 'B', 12);
        $pdf->CellFitScale(0, 0, 'SCHEDA PER IL RECUPERO', 0, 1, 'C');
        $pdf->ln(1);
        $pdf->CellFitScale(0, 0, 'COMUNICAZIONE ALLO STUDENTE E ALLA FAMIGLIA', 0, 1, 'C');
        $pdf->ln(1);
        $pdf->writeHTMLCell( 0, 0, '','',"ANNO SCOLASTICO $anno_scolastico_attuale", 0, 1, false, true, 'C');
        $pdf->SetFont($fnt, '', 10);

        $pdf->ln(2);
        $pdf->writeHTMLCell( 0, 0, '','','Vicenza' . ", $data_attestato_day/$data_attestato_month/$data_attestato_year", 0, 1, false, true, 'L');
        $pdf->ln(1);
        $pdf->writeHTMLCell( 0, 0, '','',"{$labels['studente']} <b>{$studente['cognome']} {$studente['nome']}</b>", 0, 1, false, true, 'L');
        $pdf->ln(2);
        $pdf->writeHTMLCell( 0, 0, '','',"CLASSE: <b>{$studente['classe']}^{$studente['sezione']} {$studente['descrizione_indirizzi']}</b>", 0, 1, false, true, 'L');
        $pdf->ln(4);

        $pdf->writeHTMLCell( 0, 0, '','',
"Il consiglio della classe, ha rilevato che nella/nelle seguenti disciplina/e non sono stati raggiunti gli obiettivi didattici e propone gli interventi di seguito indicati. Il giudizio di ammissione alla classe successiva rimane sospeso fino ad avvenuto recupero attraverso l’esame programmato per fine agosto.", 0, 1, false, true, 'L');
        $pdf->ln(3);

        // tabella
        $tbl = '<table border="0.1px" cellpadding="2">'
                . '<tr align="center">'
                    . '<td width="16%"><b>DISCIPLINE</b></td>'
                    . '<td width="9%"></td>'
                    . '<td width="75%" colspan="3"><b>MODALITA\' DEL RECUPERO</b></td>'
                . '</tr>'
                . '<tr align="center">'
                    . '<td width="16%">MATERIA</td>'
                    . '<td width="9%"><b>Voto da recuperare</b></td>'
                    . '<td width="40%"><b>Obiettivi didattici</b></td>'
                    . '<td width="19%" align="left"><b>Attivita\' previste</b><br><i>(corso di recupero, attivita\' in itinere, approfondimento su consegna data...)</i></td>'
                    . '<td width="16%"><b>Modalita\' di verifica del recupero</b></td>'
                . '</tr>';
        foreach ($arr_materie_sospese as $rec) {
            $obiettivi = $attivita = $modalita_rec = '';
            foreach ($rec['campi_liberi'] as $campo_libero) {
                if (in_array($rec['id_materia'], $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if (stripos($campo_libero['nome'], 'Obiettivi didattici') !== false) {
                        $obiettivi .= $value;
                    }
                    elseif (stripos($campo_libero['nome'], ' previste') !== false) {
                        $attivita .= $value;
                    }
                    elseif (stripos($campo_libero['nome'], 'Modalit&agrave; di verifica  del recupero') !== false) {
                        $modalita_rec .= $value;
                    }
                }
            }

            $tbl .=
                 '<tr>'
                    . '<td>'.$rec['descrizione'].'</td>'
                    . '<td align="center">'.$rec['voto_pagellina'].'</td>'
                    . '<td>'.$obiettivi.'</td>'
                    . '<td>'.$attivita.'</td>'
                    . '<td>'.$modalita_rec.'</td>'
                . '</tr>';
        }
        $tbl .= '</table>';
        $pdf->SetFont($fnt, '', 8);
        $pdf->writeHTMLCell( 0, 0, '','',$tbl, 0, 1);
        $pdf->SetFont($fnt, '', 9);
        $pdf->ln(3);

        $pdf->writeHTMLCell( 0, 0, '','',"La scuola, così come previsto dal PTOF si impegna ad attuare gli interventi segnalati per permettere il recupero e chiede un’effettiva collaborazione alla famiglia e l’impegno responsabile dello studente/della studentessa.<br>
La scuola sta organizzando i corsi di recupero (finanziati dal Piano Nazionale Coesione 21-27 e quindi gratuiti per le famiglie) <b>dal 23 giugno al 4 luglio 2025</b>: la partecipazione è fortemente consigliata.<br>
La prenotazione obbligatoria dovrà avvenire tramite il form cliccando <a href=\"https://forms.office.com/e/UfBU9CXQDK\" target=\"_blank\" >QUI</a>.", 0, 1, false, true, 'L');
        $pdf->ln(4);
        $pdf->writeHTMLCell( 0, 0, '','',"L’Eventuale non partecipazione dovrà essere segnalata e giustificata dalla famiglia entro lunedì 16 giugno 2025 scrivendo al coordinatore di Classe e al preside tramite Messenger del registro elettronico la seguente comunicazione:<br>
Noi, (cognomi e nomi dei genitori) .......................................................... giustifichiamo l’assenza al corso di
recupero di (indicare la/le materia/e) .......................................................... da parte di (cognome e nome
del/della figlio/a) ................................................. a causa di (motivazione) ..................................................<br>
Ci assumiamo la responsabilità di garantire il recupero tramite le seguenti attività: (descrizione delle attività di recupero)", 0, 1, false, true, 'L');
        $pdf->ln(5);
        $pdf->SetFont($fnt, '', 10);
        $pdf->writeHTMLCell( 0, 0, 120,'',"Il Dirigente Scolastico<br><i>(prof. ".ucwords(strtolower($studente['nome_dirigente'])).")</i><br><br>", 'B', 0, false, true, 'C');

        if (file_exists('immagini_scuola/footer.jpg')) {
            $pdf->Image('immagini_scuola/footer.jpg', 10, $pdf->GetPageHeight() - 25, 190, 20, 'JPG', '', 'C', false, 300, '', false, false, 0, false, false, false);
            // $pdf->writeHTMLCell(190, 20, 10, $pdf->GetPageHeight() - 25, 190, '<img src="immagini_scuola/footer.png">', 0, 1, false, true, 'L');
        }
    }
    //}}} </editor-fold>
}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);

            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);

            if ($da_includere != "NO")
            {
                $file_name = $nome_pagella_per_file . '.pdf';
                $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

                if (file_exists($pagella)) {
                    unlink($pagella);
                }
                $pdf->Output($pagella, "F");

                $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
                $content = file_get_contents($pagella);

                // NOTA: presume vi sia al max un solo file per i criteri di ricerca
                if ($file[0]['id']) {
                    messengerUpdateFile($file[0]['id'], $content);
                } else {
                    // Destinatari: Studente + genitori
                    $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                    messengerSaveFile([
                        'content'    => $content,
                        'hidden'     => false,
                        'mime'       => 'application/pdf',
                        'name'       => $file_name,
                        'owner'      => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_stud_per_stampa_sito,
                            'year'   => "{$anno_inizio}/{$anno_fine}",
                            'period' => $periodo_pagella
                        ],
                        'recipients' => $recipients,
                        'tags'       => ['PAGELLE']
                    ]);
                }

                if (file_exists($pagella)) {
                    $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                    unlink($pagella);
                }
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);
            if ($da_includere != "NO") {
                $pdf->Output($file, "F");
            }
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);

            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);

            if ($da_includere != "NO") {
                $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
                $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

                if (file_exists($pagella)) {
                    unlink($pagella);
                }
                $pdf->Output($pagella, "F");

                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
                $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

                foreach ($pagelle as $pagella) {
                    $external_data[basename($pagella)] = [
                        'hidden'     => false,
                        'mime'       => 'application/pdf',
                        'name'       => basename($pagella),
                        'class'      => $classe_studente,
                        'owner'      => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_stud_per_stampa_sito,
                            'year'   => "{$anno_inizio}/{$anno_fine}",
                            'period' => $descrizione_periodo
                        ],
                        'recipients' => $recipients,
                        'tags'       => ['PAGELLE']
                    ];
                }
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
