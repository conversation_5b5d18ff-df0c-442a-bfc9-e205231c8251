<table width='100%' style="border-collapse: separate;">
    <tr valign='top'>
        <td width='33%' class="stampe_contenitore">
            {* {{{ dettagli studenti *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}A - Dettagli su Studenti{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td align='center'>
                            {mastercom_auto_select name="tipo_stampa" size=7 ondblclick="this.form.submit();" menu_prefix='A' style="width:100%"}
                                curriculum###Curriculum dello studente@@@
                                curriculum_bianco###Curriculum dello studente in bianco@@@
                                foglio_notizie###Foglio Notizie dello studente@@@
                                scheda_personale_candidato_qualifiche###Scheda personale del candidato per gli esami di qualifica@@@
                                scheda_personale_candidato###Scheda personale del candidato per gli esami di maturità@@@
                                stampa_elenco_tasse_studente###Elenco tasse pagate per ogni singolo studente@@@
                                elenco_note_disciplinari###Elenco note
                                {foreach $elenco_optionals as $optional}
                                    {if $optional['nome']=='stampa_dati_medici' && $optional['valore'] == '1'}
                                        @@@stampa_dati_medici_studenti###Dati medici studenti
                                    {/if}
                                {/foreach}
                                {* Stampe personalizzate nella sezione A *}
                                {if !empty($elenco_stampe_personalizzate.A)}
                                    @@@
                                    {foreach from=$elenco_stampe_personalizzate.A item=stampa name=foo}
                                        {$stampa.valore}###{$stampa.descrizione}{if not $smarty.foreach.foo.last}@@@{/if}
                                    {/foreach}
                                {/if}
                            {/mastercom_auto_select}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {* }}} *}
        </td>
        <td width='34%' class="stampe_contenitore">
            {* {{{ elenchi studenti *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}B - Elenchi Studenti{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            {mastercom_auto_select name="tipo_stampa" size=7 ondblclick="this.form.submit();" menu_prefix='B' style="width:100%"}
                                elenchi_studenti###Elenco personalizzato studenti@@@
                                elenco_recuperi###Elenco studenti con tipi di recupero assegnati@@@
                                stampa_riepilogo_esiti_recuperi###Elenco studenti con tipi di recupero e esiti per classe@@@
                                stampa_elenchi###Elenchi studenti per registri di classe@@@
                                stampa_elenchi_barcode###Elenchi studenti con codice a barre di riferimento@@@
                                elenchi_preiscritti###Elenchi studenti preiscritti al prossimo anno scolastico@@@
                                stampa_etichette###Etichette per libretti con dati studenti@@@
                                elenchi_servizi_sottoscritti###Elenchi servizi sottoscritti@@@
                                elenchi_compleanni###Elenchi compleanni@@@
                                stampa_elenchi_cartellini###Elenchi cartellini@@@
                                elenco_recuperi_pai_1920###Elenco Studenti con PAI assegnato
                            {/mastercom_auto_select}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {*//}}} *}
        </td>
        <td width='33%' class="stampe_contenitore">
            {* {{{ assenze studenti *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}C - Assenze Studenti{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            {mastercom_auto_select name="tipo_stampa" size=7 ondblclick="this.form.submit();" menu_prefix='C' style="width:100%"}
                                elenco_assenti_giornalieri###Elenco assenti giornalieri@@@
                                riepilogo_assenze_studente###Riepilogo delle assenze/entrate/uscita con criteri@@@
                                stampa_studenti_non_giustificati###Riepilogo studenti con assenze non giustificate@@@
                                stampa_assenze_non_giustificate###Stampa riepilogo periodi assenze non giustificati@@@
                                stampa_assenze_classe###Stampa riepilogo assenze di una classe@@@
                                riepilogo_permessi###Riepilogo dei permessi assegnati agli studenti@@@
                                export_riepilogo_assenze###Esportazione riepilogo assenze@@@ 
                                stampa_elenco_permessi###Elenco dei permessi assegnati agli studenti
                                {foreach $elenco_optionals as $optional}
                                    {if $optional['nome'] == 'riepilogo_ore_cfp' && $optional['valore'] == '1'}
                                        @@@riepilogo_ore_cfp###Riepilogo conteggio ore per CFP
                                        @@@riepilogo_esportazione_regione###Esportazione ore presenze per Regione
                                    {/if}
                                {/foreach}
                                {* Stampe personalizzate nella sezione C *}
                                {if !empty($elenco_stampe_personalizzate.C)}
                                    @@@
                                    {foreach from=$elenco_stampe_personalizzate.C item=stampa name=foo}
                                        {$stampa.valore}###{$stampa.descrizione}{if not $smarty.foreach.foo.last}@@@{/if}
                                    {/foreach}
                                {/if}
                            {/mastercom_auto_select}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {*//}}} *}
        </td>
    </tr>
    <tr valign='top'>
        <td width='33%' class="stampe_contenitore">
            {* {{{ Stampe Utili *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}D - Stampe Utili{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            {mastercom_auto_select name="tipo_stampa" size=12 ondblclick="this.form.submit();" menu_prefix='D' style="width:100%"}
                                riepilogo_tasse###Riepilogo tasse scolastiche@@@
                                frontespizio_registro_voti###Frontespizio registro voti@@@
                                comunicazioni_per_classe###Riepilogo delle comunicazioni per classe@@@
                                stampa_moduli_master###Stampa moduli prestampati MasterCom@@@
                                stampa_registro_docente_materia###Stampa del registro del prof. voti e assenze@@@
                                stampa_password_studenti###Stampa delle credenziali di accesso degli studenti@@@
                                stampa_password_genitori###Stampa delle credenziali di accesso dei genitori@@@
                                stampa_password_docenti###Stampa delle credenziali di accesso dei docenti@@@
                                stampa_elenco_classi###Stampa dell'elenco delle classi@@@
                                stampa_elenco_materie###Stampa dell'elenco delle materie@@@
                                stampa_orari_classi_docenti###Stampa degli orari delle classi e dei docenti completi di certificazione di firma@@@
                                stampa_firme_giornaliere###Stampa firme giornaliere docenti@@@
                                stampa_colloqui_professori###Elenco disponibilità dei docenti per i colloqui@@@
                                stampa_elenco_colloqui_prenotati###Stampa la lista dei colloqui prenotati@@@
                                stampa_competenze_elementari###Stampa le competenze delle elementari (per il comune di Mantova)@@@
                                elenco_registri_classe###Elenco codici registri classi@@@
                                stampa_reiscrizioni_interne###Stampa nuove iscrizioni interne per educandati@@@
                                comparazione_voti###Comparazione voti studenti della classe 1° scuola secondaria di I grado@@@
                                lista_lezioni###Lista delle lezioni di una classe per materia@@@
                                mac_address_docenti###Lista dispositivi attivi di ogni docente@@@
                                riepilogo_corsi###Riepilogo giornaliero dei corsi@@@
                                riepilogo_risorse###Riepilogo delle risorse@@@
                                riepilogo_docenti_ore_corsi###Riepilogo corsi abbinati a docenti con ore@@@
                                griglia_per_classi###Griglia per classi@@@
                                stampa_elenco_videomeeting###Elenco videomeeting@@@
                                esportazioni_pago_in_rete###Esportazioni per PagoInRete@@@
                                elenco_tag###Elenco dei tag inseriti@@@
                                stampa_registro_classe###Stampa registri di classe per archivio
                                {foreach $elenco_optionals as $optional}
                                    {if $optional['nome'] == 'esportazione_fidae' && $optional['valore'] == '1'}
                                        @@@esportazione_fidae###Esportazione FIDAE
                                    {/if}
                                {/foreach}
                                {* Stampe personalizzate nella sezione D *}
                                {if !empty($elenco_stampe_personalizzate.D)}
                                    @@@
                                    {foreach from=$elenco_stampe_personalizzate.D item=stampa name=foo}
                                        {$stampa.valore}###{$stampa.descrizione}{if not $smarty.foreach.foo.last}@@@{/if}
                                    {/foreach}
                                {/if}
                            {/mastercom_auto_select}


                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {*//}}} *}
        </td>
        <td width='34%' class="stampe_contenitore">
            {* {{{ Organi collegiali *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}E - Organi Collegiali e consigli di classe{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            {mastercom_auto_select name="tipo_stampa" size=12 ondblclick="this.form.submit();" menu_prefix='E' style="width:100%"}
                                convocazioni_rappresentanti###Convocazioni rappresentanti@@@
                                elenchi_rappresentanti###Elenco rappresentanti classe/istituto@@@
                                nomine_rappresentanti###Nomine rappresentanti@@@
                                riepilogo_rappresentanti###Rappresentanti eletti@@@
                                organi_collegiali_genitori###Elenco organi collegiali (solo genitori)@@@
                                organi_collegiali_studenti###Elenco organi collegiali (solo studenti)@@@
                                organi_collegiali_genitori_studenti###Elenco organi collegiali (genitori/studenti insieme)@@@
                                stampa_abbinamenti###Abbinamenti classi-professori-materie (Consigli di classe)@@@
                                stampa_dati_commissione###Stampa i nomi dei commissari divisi per commissione
                            {/mastercom_auto_select}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {* }}} *}
        </td>
        <td width='33%' class="stampe_contenitore">
            {* {{{ Stampe per Esami di Stato *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}F - Stampe per Esami di Stato{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            {mastercom_auto_select name="tipo_stampa_selezionata" size=12 ondblclick="this.form.submit();" menu_prefix='F' style="width:100%"}
                                elenco_candidati###Elenco Candidati@@@
                                elenco_candidati_per_documento###Elenco Candidati per documento di riconoscimento@@@
                                elenco_prove_scritte###Elenco Candidati per prove scritte@@@
                                elenco_prove_orali###Elenco Candidati per prove orali@@@
                                elenco_candidati_con_crediti###Elenco totale Candidati con crediti scolastici@@@
                                elenco_candidati_interni_con_crediti###Elenco Candidati Interni con crediti scolastici@@@
                                elenco_candidati_esterni_con_crediti###Elenco Candidati Esterni con crediti scolastici@@@
                                elenco_risultati_prove_scritte###Elenco Risultati Prove Scritte@@@
                                elenco_risultati_finali###Elenco Risultati Finali@@@
                                elenco_risultati_finali_solo_esito###Elenco Risultati Finali con il solo esito@@@
                                dichiarazione_commissari###Dichiarazioni dei Commissari di non relazione con Candidati@@@
                                registro_esami_stato###Registro degli Esami di Stato@@@
                                stampa_calendari_orali###Stampa del calendario degli esami orali@@@
                                riepilogo_calendari_orali###Stampa del riepilogo del calendario degli esami orali@@@
                                elenco_prove_scritte_per_studente###Stampa risultati degli scritti per singolo studente@@@
                                tabellone_risultati_esposizione###Stampa il tabellone dei risultati delle prove scritte per esposizione@@@
                                quadro_sinottico###Registro dei risultati degli esami - Quadro Sinottico Candidati
                            {/mastercom_auto_select}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='tipo_stampa' value='stampe_per_esami_di_stato'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {* }}} *}
        </td>
    </tr>
    <tr valign='top'>
        <td width='33%' class="stampe_contenitore">
            {* {{{ Stampa Pagelle *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}G - Pagelle Istituti Superiori{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                        {if !empty($elenco_stampe_personalizzate.G) or !empty($elenco_pagelle_word.G)}
                            {mastercom_auto_select name="tipo_stampa" size=7 ondblclick="this.form.submit();" menu_prefix='G' style="width:100%"}
                                {if !empty($elenco_stampe_personalizzate.G)}
                                    {foreach from=$elenco_stampe_personalizzate.G item=stampa name=foo}
                                        {$stampa.valore}###{$stampa.descrizione}{if not $smarty.foreach.foo.last}@@@{/if}
                                    {/foreach}
                                {/if}
                                {if !empty($elenco_pagelle_word.G)}
                                    {foreach from=$elenco_pagelle_word.G item=modello name=foo}
                                        {if $smarty.foreach.foo.first and !empty($elenco_stampe_personalizzate.G)}@@@{/if}pagella_word_{$modello.id_template}###{$modello.nome}{if not $smarty.foreach.foo.last}@@@{/if}
                                    {/foreach}
                                {/if}
                            {/mastercom_auto_select}
                        {else}
                            {mastercom_auto_select name="tipo_pagella" size=7 ondblclick="this.form.submit();" menu_prefix='G' style="width:100%"}
                                PAGELLA###Pagella Scolastica modulo ministeriale prestampato@@@
                                FRONTESPIZIO_PAGELLA###Frontespizio Pagella Scolastica modulo ministeriale prestampato@@@
                                PAGELLA_SUPERIORI_CARTA_BIANCA###Pagella Scolastica su carta bianca@@@
                                PAGELLA_AS2012###Pagella Scolastica su carta bianca modello ministeriale 2012@@@
                                RELIGIONE_AS2012###Pagella Religione su carta bianca modello ministeriale 2012
                            {/mastercom_auto_select}
                            <input type='hidden' name='tipo_stampa' value='stampa_pagelle_ministeriali'>
                        {/if}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {* }}} *}
        </td>
        <td width='34%' class="stampe_contenitore">
            {* {{{ Stampa Pagelle elementari/medie*}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}H - Pagelle Elementari/Medie{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                        {if !empty($elenco_stampe_personalizzate.H) or !empty($elenco_pagelle_word.H)}
                            {mastercom_auto_select name="tipo_stampa" size=7 ondblclick="this.form.submit();" menu_prefix='H' style="width:100%"}
                                {if !empty($elenco_stampe_personalizzate.H)}
                                    {foreach from=$elenco_stampe_personalizzate.H item=stampa name=foo}
                                        {$stampa.valore}###{$stampa.descrizione}{if not $smarty.foreach.foo.last}@@@{/if}
                                    {/foreach}
                                {/if}
                                {if !empty($elenco_pagelle_word.H)}
                                    {foreach from=$elenco_pagelle_word.H item=modello name=foo}
                                        {if $smarty.foreach.foo.first and !empty($elenco_stampe_personalizzate.H)}@@@{/if}pagella_word_{$modello.id_template}###{$modello.nome}{if not $smarty.foreach.foo.last}@@@{/if}
                                    {/foreach}
                                {/if}
                            {/mastercom_auto_select}
                        {else}
                            {mastercom_auto_select name="tipo_pagella" size=7 ondblclick="this.form.submit();" menu_prefix='H' style="width:100%"}
                                PAGELLA_MEDIE###Pagella Scolastica Scuole Medie@@@
                                RELIGIONE_MEDIE###Pagella Religione Scuole Medie
                                {if $param_nuovo_tabellone == 'SI'}
                                    @@@PAGELLA_TEMPLATE_WORD_ELEMENTARI_MEDIE###Pagella Primaria con competenze
                                    @@@REGISTRO_GENERALE_VOTI_COMPETENZE###Registro Generale dei Voti per Competenze
                                {else}
                                    @@@PAGELLA_ELEMENTARI###Pagella Scolastica Scuole Elementari
                                    @@@RELIGIONE_ELEMENTARI###Pagella Religione Scuole Elementari
                                {/if}
                            {/mastercom_auto_select}
                            <input type='hidden' name='tipo_stampa' value='stampa_pagelle_ministeriali'>
                        {/if}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {* }}} *}
        </td>
        <td width='33%' class="stampe_contenitore">
            {* {{{ Stampa Diplomi/Certificati Diploma*}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}I - Diplomi/Certificati vari{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            {mastercom_auto_select name="tipo_pagella" size=7 ondblclick="this.form.submit();" menu_prefix='I' style="width:100%"}
                                DIARIO###Registro dei voti/simil pagella per archivio scolastico@@@
                                REGISTRO_ESAMI###Registro degli esami di stato, per studente, su carta libera@@@
                                CERTIFICATO_DIPLOMA_CARTA_LIBERA###Certificato superamento esame su carta libera@@@
                                CERTIFICATO_DIPLOMA_FRONTE###Certificato superamento esame fronte@@@
                                CERTIFICATO_DIPLOMA_RETRO###Certificato superamento esame retro@@@
                                DIPLOMA###Diploma ministeriale@@@
                                DIPLOMA_QUALIFICA###Diploma ministeriale di qualifica professionale@@@
                                DIPLOMA_MEDIE###Diploma ministeriale per scuole medie@@@
                                CERTIFICAZIONE_COMPETENZE###Certificazione delle Competenze di fine anno@@@
                                STAMPA_CONSIGLIO_ORIENTATIVO###Stampa del Consiglio orientativo
                                {if !empty($elenco_stampe_personalizzate.I)}
                                    {foreach from=$elenco_stampe_personalizzate.I item=stampa name=foo}
                                        @@@{$stampa.valore}###{$stampa.descrizione}
                                    {/foreach}
                                {/if}
                                {if !empty($elenco_pagelle_word.I)}
                                    {foreach from=$elenco_pagelle_word.I item=modello name=foo}
                                        {if $smarty.foreach.foo.first }@@@{/if}pagella_word_{$modello.id_template}###{$modello.nome}{if not $smarty.foreach.foo.last}@@@{/if}
                                    {/foreach}
                                {/if}
                            {/mastercom_auto_select}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='tipo_stampa' value='stampa_pagelle_ministeriali'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {* }}} *}
        </td>
    </tr>
    <tr valign='top'>
        <td width='33%' class="stampe_contenitore">
            {* {{{ Statistiche Ministeriali *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}J - Statistiche Ministeriali{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            {mastercom_auto_select name="tipo_stampa" size=8 ondblclick="this.form.submit();" menu_prefix='J' style="width:100%"}
                                statistiche_istat###Statistiche Ist. Superiori@@@
                                statistiche_istat_non_statali###Statistiche Ist. Superiori non statali@@@
                                statistiche_istat_esiti_scrutini_intermedi###Statistiche Ist. Superiori - Esiti Scrutini Intermedi@@@
                                statistiche_istat_esiti_scrutini###Statistiche Ist. Superiori - Esiti Scrutini Finali@@@
                                statistiche_istat_esiti_esami###Statistiche Ist. Superiori - Rilevazione integrativa degli Esami di Stato@@@
                                statistiche_istat_scuole_medie###Statistiche Ist. I Grado@@@
                                statistiche_istat_scuole_medie_non_statali###Statistiche Ist. I Grado non statali@@@
                                statistiche_istat_esiti_scrutini_intermedi_medie###Statistiche Ist. I Grado - Esiti Scrutini Intermedi@@@
                                statistiche_istat_esiti_scrutini_medie###Statistiche Ist. I Grado - Esiti Scrutini Finali@@@
                                statistiche_istat_elementari###Statistiche Scuole Primarie@@@
                                statistiche_istat_elementari_non_statali###Statistiche Scuole Primarie non statali@@@
                                statistiche_istat_infanzia###Statistiche Scuole dell'Infanzia@@@
                                statistiche_istat_infanzia_non_statali###Statistiche Scuole dell'Infanzia non statali
                            {/mastercom_auto_select}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {* }}} *}
        </td>
        <td width='34%' class="stampe_contenitore">
            {* {{{ Statistiche Scrutini *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}K - Statistiche Scrutini{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            {mastercom_auto_select name="tipo_stampa" size=8 ondblclick="this.form.submit();" menu_prefix='K' style="width:100%"}
                                statistiche_esiti_scrutini###Statistiche esiti scrutini@@@
                                statistiche_tipi_recupero###Statistiche esiti scrutini con tipi di recupero assegnati@@@
                                statistiche_esiti_recuperi###Statistiche esiti recuperi@@@
                                statistiche_promozione###Statistiche di promozione@@@
                                statistiche_emilia_prime###Statistiche scrutini classi prime Regione Emilia Romagna@@@
                                statistiche_mantova###Dati statistici diritto allo studio Comune di Mantova@@@
                                stampa_elenco_marcatori_pagellina###Stampa indicatori di condotta degli scrutini@@@
                                campi_liberi_cfp###Stampa delle competenze per CFP@@@
                                campi_liberi_wizard###Stampa personalizzata indicatori@@@
                                stampa_pagellina_con_campi_liberi###Esportazione excel della pagellina con campi liberi
                            {/mastercom_auto_select}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {* }}} *}
        </td>
        <td width='33%' class="stampe_contenitore">
            {* {{{ Statistiche varie *}
            <form method='post' action='{$SCRIPT_NAME}'>
                <table width='100%'>
                    <tr>
                        <td align='center' class="stampe_titolo">
                            {mastercom_label}L - Statistiche varie{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            {mastercom_auto_select name="tipo_stampa" size=8 ondblclick="this.form.submit();" menu_prefix='L' style="width:100%"}
                                statistiche_voti_professori###Statistiche inserimento voti per professori@@@
                                statistiche_voti_studenti###Statistiche voti assegnati agli studenti per periodo@@@
                                statistiche_comuni###Statistiche di studenti per comune/regione@@@
                                statistiche_assenze_comuni###Statistiche studenti/assenze per comune@@@
                                stampa_riepilogo_assenze_annuale###Riepilogo statistico assenze di una classe@@@
                                stampa_riepilogo_studenti_classi###Elenco totali maschi e femmine per classe@@@
                                stampa_riepilogo_assenze_monteore###Stampa riepilogo assenze monte ore@@@
                                situazione_classe###Stampa della situazione della classe in un giorno specifico@@@
                                stampa_riepilogo_medie_classe###Stampa riepilogo delle medie per classe@@@
                                {if $corsi_abilitati == 'SI'}
                                    stampa_monteore_corsi_classe###Stampa riepilogo monteore corsi per classe@@@
                                    {if $optional_stampa_L11 == 'OK'}
                                        stampa_riepilogo_ore_corsi_per_materia###Stampa riepilogo ore corsi per materia@@@
                                    {/if}
                                {/if}
                                stampa_elenco_studenti_scuola_provenienza###Elenchi studenti suddivisi per scuola di provenienza
                                {if !empty($elenco_stampe_personalizzate.L)}
                                    {foreach from=$elenco_stampe_personalizzate.L item=stampa name=foo}
                                        @@@{$stampa.valore}###{$stampa.descrizione}
                                    {/foreach}
                                {/if}
                            {/mastercom_auto_select}
                        </td>
                    </tr>
                </table>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_display'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
            {* }}} *}
        </td>
    </tr>
</table>
