<?php
/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'scheda_candidato_mm_3_collegiobalbi', 'Scheda candidato esami scuola secondaria I grado', 1, 'collegiobalbi', 9);
 */

$orientamento = 'P';
$formato = 'A4';
$periodo = 29;
$periodo_pagella = "finale";

$elenco_studenti = estrai_studenti_classe($id_classe, true);
$parametri_stampa = [
    'id_classe'       => $id_classe,
    'data_day'        => $data_Day,
    'data_month'      => $data_Month,
    'data_year'       => $data_Year,
    'periodo'         => $periodo,
    'orientamento'    => $orientamento,
    'current_key'    => $current_key,
];

function genera_stampa(&$pdf, $studente, $parametri_stampa)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="">
    $id_studente = $studente['id_studente'];
    $id_classe = $parametri_stampa['id_classe'];
    $data_Day = $parametri_stampa['data_day'];
    $data_Month = $parametri_stampa['data_month'];
    $data_Year = $parametri_stampa['data_year'];
    $current_key = $parametri_stampa['current_key'];
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $mat_commissioni = estrai_classi_sottocommissioni_commissione_medie((int) $id_classe);
    foreach($mat_commissioni as $commissione)
    {
        if($commissione['id_classe'] == $commissione['id_classe_commissione_medie'])
        {
            $abbinamenti_commissari = estrai_abbinamenti_commissari((int) $commissione['id_commissione']);
            foreach($abbinamenti_commissari['esterni'] as $singolo_abbinamento)
            {
                if($singolo_abbinamento['dati_commissario']['ruolo_commissario_est'] == 'P')
                {
                    $nome_presidente = $singolo_abbinamento['nome']
                                       . ' ' .
                                       $singolo_abbinamento['cognome'];
                }
            }
        }
    }
    $consiglio = ''; 
    if($studente['id_consiglio_orientativo'] > 0)
    {
        $param_consigli = [];
        $elenco_consigli_orientativi = nextapi_call('/school/structure/consiglio_orientativo', 'GET', $param_consigli, $current_key);
        $consigli_orientativi_array = [];
        foreach($elenco_consigli_orientativi as $consiglio_tmp){
            if ($studente['id_consiglio_orientativo'] == $consiglio_tmp['id_consiglio_orientativo_template']) {
                $consiglio .= $consiglio_tmp['descrizione'];
            }
        }
    }
    $esito_txt = '';
    if ($studente['giudizio_sintetico_esame_terza_media']>=6) {
        $esito_txt = 'ESAME SUPERATO';
    }
    if ($studente['esito'] == "Non Licenziato" || $studente['esito'] == "Non Licenziata" ||
        $studente['esito'] == "Non ammesso esame di stato" || $studente['esito'] == "Non ammessa esame di stato" ) {
        $esito_txt = 'ESAME NON SUPERATO';
    }

    // Dizionario
    $labels = [
        "candidato" => 'CANDIDAT||max_oa|| '
    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="p4">
    $f = 'Times';
    $fd = 10;
    $pdf->AddPage();
    $pdf->SetAutoPageBreak("off", 1);
    $pdf->Image('immagini_scuola/logo_repubblica_colori.jpg', 94, 15, 22, '', 'JPG', false);
    $pdf->SetY(45);
    $pdf->SetFont($f, 'I', $fd+4);
    $pdf->CellFitScale(0, 0, "Ministero dell’Istruzione", 0, 1, 'C');
    $pdf->ln(4);
    $pdf->SetFont($f, 'B', $fd+2);
    $pdf->CellFitScale(0, 0, "ISTITUTO PARITARIO COLLEGIO BALBI VALIER", 0, 1, 'C');
    $pdf->ln(1);
    $pdf->SetFont($f, '', $fd);
    $pdf->CellFitScale(0, 0, "via Sartori, 47 - PIEVE DI SOLIGO (TV)", 0, 1, 'C');
    $pdf->ln(8);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', "SCHEDA CANDIDATO ESAME CONCLUSIVO I CICLO DI STUDI<br>
A.S. $anno_scolastico_attuale", 0, 1, false, true, 'C');
    $pdf->ln(3);
    $tbl = '<table>'
            . '<tr align="center">'
                . '<td>'.$labels['candidato'].': <b>'."{$studente['cognome']} {$studente['nome']}".'</b></td>'
                . '<td>CLASSE: <b>'."{$studente['classe']}{$studente['sezione']}".'</b></td>'
                . '<td>C. Fisc.: <b>'."{$studente['codice_fiscale']}".'</b></td>'
            . '</tr>'
        . '</table>';
    $pdf->writeHTMLCell(0, 0, '', '', $tbl, 0, 1, false, true, 'C');
    $pdf->ln(4);
    $tbl = '<table cellpadding="3" align="center" border="0.1px">'
            . '<tr>'
                . '<td width="60%"><b>ELEMENTI VALUTATIVI PROVA ESAME</b></td>'
                . '<td width="40%"><b>RISULTATO CONSEGUITO</b></td>'
            . '</tr>'
            . '<tr>'
                . '<td>VOTO DI AMMISSIONE</td>'
                . '<td>'."{$studente['voto_ammissione_medie']}/10".'</td>'
            . '</tr>'
            . '<tr>'
                . '<td>PROVA DI ITALIANO</td>'
                . '<td>'."{$studente['voto_esame_medie_italiano']}/10".'</td>'
            . '</tr>'
            . '<tr>'
                . '<td>PROVA LOGICO-MATEMATICA</td>'
                . '<td>'."{$studente['voto_esame_medie_matematica']}/10".'</td>'
            . '</tr>'
            . '<tr>'
                . '<td>PROVA DI LINGUE</td>'
                . '<td>'."{$studente['voto_esame_medie_inglese']}/10".'</td>'
            . '</tr>'
            . '<tr>'
                . '<td>COLLOQUIO</td>'
                . '<td>'."{$studente['voto_esame_medie_orale']}/10".'</td>'
            . '</tr>'
            . '<tr>'
                . '<td><b>VALUTAZIONE FINALE</b></td>'
                . '<td><b>'."{$studente['giudizio_sintetico_esame_terza_media']}/10".'</b></td>'
            . '</tr>'
            . '<tr>'
                . '<td>ESITO</td>'
                . '<td>'.$esito_txt.'</td>'
            . '</tr>'
        . '</table>';
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', $tbl, 0, 1, false, true, 'C');
    $pdf->ln(6);
    $pdf->writeHTMLCell(0, 40, '', '', "<b>Giudizio sul livello globale di maturazione:</b><br>{$studente["giudizio_descrittivo_finale_esame_terza_media"]}", 1, 1, false, true, 'L');
    $pdf->writeHTMLCell(0, 20, '', '', "<b>Eventuali suggerimenti all’orientamento indicati in sede di scrutinio finale:</b><br>$consiglio", 1, 1, false, true, 'L');
    $pdf->ln(9);
    $pdf->writeHTMLCell(0, 0, '', '', '<table>'
            . '<tr>'
                . '<td align="left" width="50%">'."{$studente['descrizione_comuni']}, lì $data_Day/$data_Month/$data_Year".'</td>'
                . '<td align="right" width="50%"><b>IL PRESIDENTE DELLA COMMISSIONE<br>'.
                    $nome_presidente.'</b><br>'
                    . '<small><i>(Firma autografa sostituita a mezzo stampa<br>
 ai sensi dell art. 3 comma 2 D.Lgs n 9/1993)</i></small></td>'
            . '</tr>'
        . '</table>', 0, 1, false, true, 'C');
    //}}} </editor-fold>
    //}}} </editor-fold>
}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Scheda candidato ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
