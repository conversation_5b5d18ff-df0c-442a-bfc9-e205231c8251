<!DOCTYPE html>
<html lang="it">
    <head>
        <title>{$titolo} - {$nome}</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="google" value="notranslate">
        <link rel="shortcut icon" type="image/x-icon" href="icone/mastercom.ico">
        <link rel="stylesheet" type="text/css" href="css/jquery-ui-1.8.16.custom.css" />
        <script src="https://accounts.google.com/gsi/client" async defer></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jsrsasign/8.0.20/jsrsasign-all-min.js"></script>
        {* <script type="text/javascript" src="libs/jquery/jquery-2.1.1.min.js"></script> *}
        <script type="text/javascript" src="libs/jquery/jquery-3.7.1.min.js"></script>
        <script type="text/javascript" src="libs/jquery-ui-1.14.1/jquery-ui.min.js"></script>
        {* <script type="text/javascript" src="libs/jquery-ui-1.11.0/jquery-ui.min.js"></script> *}
        <script type="text/javascript" src="javascript/messenger.js?v={$js_version}"></script>
        <script type="text/javascript" src="/mastertek-api/ckeditor-mastercom/ckeditor.js"></script>
        <script type="text/javascript" src="javascript/anylink.js?v={$js_version}"></script>

        {* custom elements *}
        <script type="module" src="javascript/custom_element/custom-elements.js?v={$js_version}"></script>

		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.base.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.darkblue.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.bootstrap.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.material.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.office.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.metro.css" type="text/css" />

        <link href="assets/plugins/font-awesome/5.0/css/fontawesome-all.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="assets/fontawesome-free-5.5.0-web/css/all.css">
		<!--link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"-->

		<script type="text/javascript" src="libs/jqwidgets/jqx-all.js"></script>
		<script type="text/javascript" src="libs/jqwidgets/globalization/globalize.js"></script>
        <script type="text/javascript" src="libs/jqwidgets/globalization/globalize.culture.it-IT.js"></script>
        <link rel="stylesheet" type="text/css" href="css/style.css">
        <link rel="stylesheet" href="css/style_microsoft.css">

		<!-- {*{{{*} Questo pezzo di javascript ha il compito di monitorare il tempo di caricamento complessivo di una pagina. In caso di login, invia l'informazione ad un webservice ajax per essere loggato nel db ed effettuare statistichee monitoraggio omogeneo del tempo di esecuzione delle pagine. -->
	   	<script type="text/javascript">
			var login_iniziale 	= '{$login_iniziale}';
			var current_user 	= '{$current_user}';
			var current_key 	= '{$current_key}';
			var tipo_utente 	= '{$form_stato}';

            CLIENT_ID   = '{$client_id_google}';
            API_KEY     = '{$api_key_google}';

			{literal}
            function signOutGoogle() {
                /*{{{ */
                try {
                    //var auth2 = gapi.auth2.getAuthInstance();
//
                    //var isSignedIn = auth2.isSignedIn.get();
                    //if (isSignedIn === true){
                    //    auth2.signOut().then(function () {
                    //        console.log('User signed out.');
                    //        window.open("https://accounts.google.com/logout", "_blank");
                    //    });
                    //}
                } catch (e) { };
                /*}}}*/
            }

            function updateLastTypeLogin(lastType){
                var nextapiPath = "../next-api/v1/";

                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "POST",
                        data: { },
                        url: nextapiPath + "user/update_last_type_login/"+lastType,
                        headers: { 'Authorization': current_key },
                        success: function (data) {
                            resolve(data);
                        },
                        error: function (data) {
                            reject(data);
                        },
                        complete: function (data) {
                            resolve(data);
                        }
                    });
                });
            }

            /*function initGoogle() {
                try {
                    gapi.load('auth2', function(){
                        gapi.auth2.init({client_id: "197393209516-tplrvljplria9qdl35ettvcbkke5r89r.apps.googleusercontent.com"});
                        gapi.auth2.init({client_id: "412329167119-pqd3gkioesph8lnrlbnbp9mntcjoo4k8.apps.googleusercontent.com"});
                    });
                } catch (e) { }
            }*/

			window.addEventListener("load", loadTime, false);
			function loadTime() {
				var now = new Date().getTime();
				var tempo_caricamento = now - performance.timing.navigationStart;
				if (window.console ) console.log(tempo_caricamento);
				if(login_iniziale == 'SI') {
					var query = {
							current_user:  current_user,
							current_key: current_key,
							form_tipo_utente: tipo_utente,
							form_cosa:  tempo_caricamento,
							form_tipo: 'LOGIN LOAD TIME',
							form_azione: 'registra_log_storico'
						};
					$.ajax({ type: "POST",
						 url: "ajat_manager.php",
						 data: query,
						 cache: false,
						 success: function(response) {
						 },
						 complete: function(response) {
						 },
					});
				}

			}
			{/literal}

            $(document).ready(function()
            {
                {if $autenticazione_google == 'SI'}
                    initGoogle();
                {/if}
            });
		</script>
		<!-- {*}}}*} -->


        {literal}
            <script type="text/javascript">
                window.name = 'mastercom_main_window';
            </script>
            <link type="text/css" rel="stylesheet" href="css/dhtmlgoodies_calendar.css?random=20051112" media="screen" />
            <script type="text/javascript" src="javascript/dhtmlgoodies_calendar.js?random=20060118"></script>
		<style>
			body {font-family: "Arial"; background-color: #aaa;}
		</style>
        {/literal}
    </head>
    <body>

		<div id = 'header_mastercom'>

        {include file="include_menu.tpl"}

        <input type='hidden' id='login_centralizzato' value='{$login_centralizzato}'>

        <table width='100%' class='sfondo_scuro_generico bordo_superiore_rilievo'>
            <tr>
                <td width='1%'>
                    <img src='images/logo2.png' height="40" width="69">
                </td>
                <td class="titolo_testo" height="54px" width="40%">
                    <b>{$descrizione_pagina}</b>
                </td>
                {if ($superutente_int == "SI" || $funzione_storici == "1" || $privilegi == "1")}
                    <td>
                        <table width='20%' class='sfondo_scuro_generico'>
                            <tr>
                                <td align='center'>
                                    <form method='post' action='{$SCRIPT_NAME}'>
                                        <SELECT name="anno_consultazione" onChange="this.form.submit();">
                                            <OPTION selected value="">Selezionare l'anno</OPTION>
                                            {section name=cont1 loop=$elenco_db_storici}
                                                <OPTION value="{$elenco_db_storici[cont1].nome}">{$elenco_db_storici[cont1].anno}</OPTION>
                                            {/section}
                                        </SELECT>
                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                        <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                        <input type='hidden' name='stato_secondario' value='visualizza_anni_precedenti_update'>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    </form>
                                </td>
                                {if $privilegi == '1' or $privilegi == '0' or $privilegi == '2'}
                                    <td align='right'>
                                        <form method='post' action='{$SCRIPT_NAME}'>
                                            {mastercom_smart_button nome_pulsante="abilita_modifica_db_storico" icona="sblocca" label_bg='d11a2e' size=$dimensione_immagini contesto="istituto" tipo_bottone="input" descrizione="Abilita la modifica dell'archivio storico attuale" utente_corrente=$current_user}
                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                            <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                            <input type='hidden' name='stato_secondario' value='abilita_modifica_storico'>
                                            <input type='hidden' name='current_user' value='{$current_user}'>
                                            <input type='hidden' name='current_key' value='{$current_key}'>
                                        </form>
                                    </td>
                                    <td align='left'>
                                        <form method='post' action='{$SCRIPT_NAME}'>
                                            {mastercom_smart_button nome_pulsante="disabilita_modifica_db_storico" icona="blocca" label_bg='44621c' size=$dimensione_immagini contesto="istituto" tipo_bottone="input" descrizione="Disabilita la modifica dell'archivio storico attuale" utente_corrente=$current_user}
                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                            <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                                            <input type='hidden' name='stato_secondario' value='disabilita_modifica_storico'>
                                            <input type='hidden' name='current_user' value='{$current_user}'>
                                            <input type='hidden' name='current_key' value='{$current_key}'>
                                        </form>
                                    </td>
                                {/if}
                            </tr>
                        </table>
                    </td>
                {/if}
                <td class="sottotitolo_testo" height="54px">
                    {$info_ultimo_login}
                </td>
                <td width='1%' align='right'>
                     {foreach $elenco_votazioni_aperte as $votazione}
                         <form method='post' action='../votazioni/index.php' target='new_tab_{math equation='rand(10,1000)'}'>

                             <input type='hidden' name='form_user' value='{$votazione.user}'>
                                <input type='hidden' name='form_code' value='{$votazione.code}'>
                             <input type='submit'  value='{$votazione.label}'>
                         </form>
                     {/foreach}
                </td>
                {if $roles_utente['professore'] > 0}
                    <td width='1%' align='right'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            <button type="submit" class="mc-icon-2 margin-right16 ripples" onclick="updateLastTypeLogin('professore')">
                                <div class="mc-icon-img-2">
                                    <i class="fa fa-fw fa-chalkboard-teacher sfondo_viola_old"></i>
                                </div>
                                <div class="mc-icon-text sfondo_viola_old_op20 mc-icon-shadow">{mastercom_label}REGISTRO{/mastercom_label}</div>
                            </button>
                            <input type='hidden' name='form_stato' value='accedi_come'>
                            <input type='hidden' name='tipo_accesso' value='professore'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                {if $roles_utente['studente'] > 0}
                    <td width='1%' align='right'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            <button type="submit" class="mc-icon-2 margin-right16 ripples">
                                <div class="mc-icon-img-2">
                                    <i class="fa fa-fw fa-rocket sfondo_azzurro_old"></i>
                                </div>
                                <div class="mc-icon-text sfondo_azzurro_old_op20 mc-icon-shadow">{mastercom_label}QUADERNO{/mastercom_label}</div>
                            </button>
                            <input type='hidden' name='form_stato' value='accedi_come'>
                            <input type='hidden' name='tipo_accesso' value='studente'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                <td width='1%' align='right'>
                    <form method='post' action='{$SCRIPT_NAME}' target='new_tab_{math equation='rand(10,1000)'}'>
							{mastercom_auto_button
							icona="add"
							size=32
                            descrizione="Apri nuova finestra"
							label_bg='44621c'
							}
                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td>
                {if ($superutente_int == "SI" || $funzione_messaggi == "1" || $privilegi == "1")}
                    {if ($versione_messenger == 'VERSIONE_1') && ($messenger_name != '' || $current_key != '')}
                        <form id="messenger-auth">
                            <input type="hidden" name="certificate">
                            <input type="hidden" name="name">
                        </form>
                        <script type="text/javascript" language="JavaScript">
                            init_messenger('{$messenger_name}', '{$messenger_certificate}', '{$current_key}');
                        </script>
                        <td width='1%' align='right'>
                            <form id="btn-messenger" method="GET" target='MASTERCOM-MESSENGER' action="/messaggi">
                                {mastercom_auto_button
                                    icona="chat"
                                    size=32
                                    descrizione="Messaggistica interna"
                                }
                                <div id="messenger-notify"></div>
                            </form>
                        </td>
                    {elseif $versione_messenger == 'VERSIONE_2'}
                        <td width='1%' align='right'>
                            <form id="btn-messenger" method="POST" target='_blank' action="{$SCRIPT_NAME}">
                                {*{mastercom_auto_button
                                    icona="chat"
                                    size=32
                                    descrizione="Messaggistica interna"
                                }*}
                                <input type="image" src="icone/quaderno/messaggi.svg" height="33" style="position: relative; top: -2px;">
                                <input type='hidden' name='stato_principale' value='messenger'>
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' id="current_key" value='{$current_key}'>
                            </form>
                            <div id="messenger-notify"></div>
                            <script type="text/javascript" language="JavaScript">
                                $(document).ready( function(){
                                    checkMessaggiNuovoMessenger();

                                    var intervalMessaggi = setInterval(function () {
                                        checkMessaggiNuovoMessenger();
                                    }, 60000); // ogni 1 minuto
                                });
                            </script>
                        </td>
                    {/if}
                {/if}
                <!--td  width='1%' align='right'>
                    <form method='post' action='documenti.php' target='_new'>
                        {mastercom_auto_button
							icona="note"
							size=32
                            descrizione="Note di rilascio"
						}
                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                        <input type='hidden' name='form_azione' value='stampa_changelog'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td-->
                {if $superutente_int == "SI"}
                <!--td  width='1%' align='right'>
                    <form method='post' action='merge_assenze.php' target='_blank'>
                        {mastercom_auto_button
							icona="merge"
							size=32
                            descrizione="Merge assenze"
						}
                        <input type='hidden' name='tipo_utente' value='amministratore'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td-->
                {/if}
            </tr>
        </table>
        <table width='100%' class='sfondo_base_generico bordo_inferiore_rilievo'>
            <tr valign='middle'>
                <td>
                    <form method='post' action='{$SCRIPT_NAME}'>
						{mastercom_auto_button
							icona="uscita"
							size=48
							label="ESCI"
							label_bg='d11a2e'
                            descrizione="Esci da Mastercom"
                            onclick='signOutGoogle();'
						}
                        <input type='hidden' name='form_stato' value='logout'>
                        <input type='hidden' name='stato_sesabilita' value='1'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td>

                {if $superutente_int == "SI" and $funzione_setup == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                            estensione='NO'
                            nome_pulsante="setup"
                            icona="manutenzione"
							size=48
							label="MAN."
                            contesto="istituto"
                            tipo_bottone="input"
                            descrizione="Operazioni manutenzione"
                            utente_corrente=$current_user
							label_bg='d11a2e'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='manutenzione_generale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $superutente_int == "SI" and $funzione_setup == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                            estensione='NO'
                            nome_pulsante="setup"
                            icona="manutenzione"
							size=48
							label="MAN.2"
                            contesto="istituto"
                            tipo_bottone="input"
                            descrizione="Operazioni manutenzione"
                            utente_corrente=$current_user
							label_bg='d11a2e'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='manutenzione_generale_new'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if ($superutente_int == "SI" and $funzione_setup == "1") or !empty($importazioni_abilitate)}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                            estensione='NO'
                            nome_pulsante="setup"
                            icona="importa"
							size=48
							label="IMPORTA"
                            contesto="istituto"
                            tipo_bottone="input"
                            descrizione="Operazioni importazione"
                            utente_corrente=$current_user
							label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='importazione_generale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                <td align='center'>
                    <form method='post' action='{$SCRIPT_NAME}' name='form_cambio_pwd'>
						{mastercom_auto_button
							icona="password"
							size=48
							label="PASSWORD"
							tipo_bottone="image"
							label_bg='cc5e00'
							onclick="if (confirm('Si desidera procedere al cambio password?'))
                                    {
                                        document.forms['form_cambio_pwd'].submit();
                                    }"
						}

                        <input type='hidden' name='form_stato' value='form_stato'>
                        <input type='hidden' name='stato_principale' value='cambio_password'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td>

                {if $funzione_utilita == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
							{mastercom_auto_button
							icona="pannello"
							size=48
							label="UTILITA"
                                                        descrizione="Utilita"
							label_bg='44621c'
							}

                            {*<input value="Operazioni manutenzione" src="icone/icona.php?icon=pannello&bullet=M&size=48&label=UTILITA" alt="Operazioni manutenzione" title="Operazioni manutenzione" id="btn-manutenzione-scuola" type="image">*}
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='manutenzione_scuola'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>


                {/if}

                <td width='50%'></td>

                {if $funzione_trova == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                            estensione='NO'
                            nome_pulsante="ricerca_avanzata"
                            immagine="icona.php?icon=trova&bullet=01&size=48&label=TROVA"
                                icona="trova"
                                                            bullet="01"
                                                            size=48
                                                            label="TROVA"
                            contesto="istituto"
                            tipo_bottone="input"
                            descrizione="Nuova ricerca avanzata"
                            utente_corrente=$current_user
                                                            label_bg='1d8eb6'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='ricerca_full'>
                            <input type='hidden' name='stato_secondario' value='ricerca_display'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if ($funzione_trova_base == "1" &&  ($privilegi == "2" || $privilegi == "3"))}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            <button type="submit" class="mc-icon-2 margin0 ripples">
                                <div class="mc-icon-img-2">
                                    <i class="fa fa-fw fa-users sfondo_viola_old"></i>
                                </div>
                                <div class="mc-icon-text sfondo_viola_old_op20 mc-icon-shadow">{mastercom_label}Ricerca Rapida{/mastercom_label}</div>
                            </button>
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='ricerca_base'>
                            <input type='hidden' name='stato_secondario' value='ricerca_base_display'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_ata == "1" and $gestione_personale_MC2 == 'NO'}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="ricerca_avanzata"
                            icona="personale"
							bullet="03"
							size=48
							label="PERSONALE"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Personale"
                        utente_corrente=$current_user
							label_bg='d11a2e'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='gestione_personale_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $superutente_int == "SI" and $funzione_setup == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                                estensione='NO'
                                nome_pulsante="sezione_calendario"
                                icona="calendario"
                                bullet="02"
                                size=48
                                label="CALENDARIO"
                                contesto="istituto"
                                tipo_bottone="input"
                                descrizione="Calendario"
                                utente_corrente=$current_user
                                label_bg='1d8eb6'
                            }

                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='calendario_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_setup == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="setup"
                            icona="setup"
							bullet="04"
							size=48
							label="SETUP"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Impostazioni Generali"
                        utente_corrente=$current_user
							label_bg='cc5e00'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='impostazioni_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $orario_aggiornato == "SI" and $funzione_orario == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_orario"
                             icona="orario"
							bullet="05"
							size=48
							label="ORARIO"
                       contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Orario"
                        utente_corrente=$current_user
							label_bg='cc5e00'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='gestione_orario_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_stampe == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="stampe"
                              icona="stampa"
							bullet="06"
							size=48
							label="STAMPE"
                      contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Stampe Generali"
                        utente_corrente=$current_user
							label_bg='1d8eb6'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='stampe_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_stud == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_studenti"
                            icona="studente"
							bullet="07"
							size=48
							label="STUDENTI"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Studenti"
                        utente_corrente=$current_user
							label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='classi_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_voti == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_voti"
                             icona="voti"
							bullet="08"
							size=48
							label="VOTI"
                       contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Voti"
                        utente_corrente=$current_user
							label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='voti_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_ritardi == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="inserimento_rapido_entrate"
                            icona="ritardo"
							bullet="09"
							size=48
							label="RITARDI"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Inserimento Rapido Entrate in Ritardo"
                        utente_corrente=$current_user
							label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='entrate_ritardo_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_ass == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_assenze"
                             icona="assenza"
							bullet="10"
							size=48
							label="ASSENZE"
                       contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Assenze"
                        utente_corrente=$current_user
							label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='assenze_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_giust == "1" }
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_giustificazioni"
                            icona="giustifica"
							bullet="11"
							size=48
							label="GIUST."
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Giustificazioni"
                        utente_corrente=$current_user
							label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='giustificazioni_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_com == "1" && $abilita_area_comunicazioni == 'SI'}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_comunicazioni"
                            icona="comunicazioni"
							bullet="12"
							size=48
							label="COMUNICAZIONI"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Comunicazioni"
                        utente_corrente=$current_user
							label_bg='1d8eb6'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='comunicazioni_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                {if $funzione_pagel == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_pagelle"
                            icona="pagella"
							bullet="13"
							size=48
							label="PAGELLE"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Pagelle e Pagelline"
                        utente_corrente=$current_user
							label_bg='3c2f67'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='pagelle_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_esami_stato == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_pagelle"
                            icona="esami"
							bullet="14"
							size=48
							label="ESAMI"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione esami di stato"
                        utente_corrente=$current_user
							label_bg='3c2f67'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='esami_stato_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_eventi == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_eventi"
                        immagine="icone_dinamiche/dispimg_icone_menu.php?icona=Globe&text=15&text2=EVENTI GITE"
                            icona="eventi"
							bullet="15"
							size=48
							label="EVENTI"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Eventi e Gite"
                        utente_corrente=$current_user
							label_bg='cc5e00'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='eventi_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                {if $funzione_mensa == "1" and $abilita_servizio_mensa == 'SI'}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_eventi"
                        immagine="icone_dinamiche/dispimg_icone_menu.php?icona=Globe&text=15&text2=EVENTI GITE"
                            icona="mense"
							bullet="16"
							size=48
							label="MENSE"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Mense"
                        utente_corrente=$current_user
							label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='mense_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                {*
                {if $funzione_elms == "1"}
                <td align='center'>
                <form method='post' action='../gestore_lezioni/index.php' target='_editor'>
                {counter print=false assign='cont'}
                {mastercom_smart_button
                estensione='NO'
                nome_pulsante="elms"
                            icona="lezioni"
							bullet="16"
							size=48
							label="LEZIONI"
                contesto="istituto"
                tipo_bottone="input"
                descrizione="Gestione Lezioni"
                utente_corrente=$current_user
							label_bg='44621c'
                }
                <input type='hidden' name='current_key' value='{$current_key}'>
                </form>
                </td>
                {/if}
                *}
                {if $funzione_dirigenti == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                    estensione='NO'
                    nome_pulsante="sezione_dirigenti"
                            icona="dirigenti"
							bullet="17"
							size=48
							label="AMMIN."
                    contesto="istituto"
                    tipo_bottone="input"
                    descrizione="Sezione amministrativa dirigenti"
                    utente_corrente=$current_user
							label_bg='3c2f67'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='amministrazione_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {*{if $optional_funzione_dirigente == "OK"}*}
                    {if $funzione_pannello_dirigente == "1"}
                        <td align='center'>
                            <form method='post' action='{$SCRIPT_NAME}'>
                                {counter print=false assign='cont'}
                                {mastercom_smart_button
                                    estensione='NO'
                                    nome_pulsante="sezione_dirigenti"
                                    icona="dirigente"
                                    bullet="19"
                                    size=48
                                    label="DIRIGENTE"
                                    contesto="istituto"
                                    tipo_bottone="input"
                                    descrizione="Pannello Dirigente"
                                    utente_corrente=$current_user
                                    label_bg='3c2f67'
                                }
                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                <input type='hidden' name='stato_principale' value='dirigente_principale'>
                                <input type='hidden' name='current_user' value='{$current_user}'>
                                <input type='hidden' name='current_key' value='{$current_key}'>
                            </form>
                        </td>
                    {/if}
                {*{/if}*}

                {if ($funzione_fascicoli == "1" || $superutente_int == "SI") && 1 == 2}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                                estensione='NO'
                                nome_pulsante="visualizzazione_comunicazioni"
                                icona="fascicolo"
                                bullet="18"
                                size=48
                                label="FASCICOLI"
                                contesto="istituto"
                                tipo_bottone="input"
                                descrizione="Gestione Fascicoli"
                                utente_corrente=$current_user
                                label_bg='3c2f67'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='fascicoli_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_com == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                                estensione='NO'
                                nome_pulsante="visualizzazione_comunicazioni"
                                icona="smartphone"
                                bullet="19"
                                size=48
                                label="SMS"
                                contesto="istituto"
                                tipo_bottone="input"
                                descrizione="Gestione SMS"
                                utente_corrente=$current_user
                                label_bg='1d8eb6'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='sms_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $superutente_int == "SI" || $privilegi == "1" || ($privilegi == "2" && $funzione_flussi == "1")}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
                                estensione='NO'
                                nome_pulsante="visualizzazione_comunicazioni"
                                icona="flussi"
                                bullet="20"
                                size=48
                                label="FLUSSI"
                                contesto="istituto"
                                tipo_bottone="input"
                                descrizione="Gestione Flussi"
                                utente_corrente=$current_user
                                label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='flussi'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
            </tr>

        </table>
		</div>
        {*if $superutente_int == "SI" and $funzione_setup == "1"}
        {include file="header_amministratore_test.tpl"}
        {/if*}

        {mastercom_smart_message
        nome_pulsante="messaggio_manutenzione"
        contesto="istituto"
        utente_corrente=$current_user
        }
        <table width='100%'>
            <tr>
                <td colspan='3' align='center' width='50%' class="area_smart_message">
                    Operazioni temporaneamente disabilitate per manutenzione in corso
                </td>
            </tr>
        </table>
        {/mastercom_smart_message}

        {mastercom_smart_message
        nome_pulsante="messaggio_visione_storica"
        contesto="istituto"
        utente_corrente=$current_user
        }
        <form method='post' action='{$SCRIPT_NAME}'>
            <br>
            <table width='100%'>
                <tr>
                    <td width="20%"></td>
                    <td align='center' width='50%' class="area_smart_message">
                        Visualizzazione dell'anno scolastico {$anno_scolastico_visualizzato}
                    </td>
                    <td width="20%" align="right">
                        <input type='submit' value='Torna a anno corrente'>
                    </td>
                </tr>
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                {if $stato_principale == 'ricerca_full'}
                    <input type='hidden' name='stato_secondario' value='ricerca_display'>
                {/if}
                <input type='hidden' name='cambio_rapido_anno_consultazione' value='SI'>
                <input type='hidden' name='anno_consultazione' value='{$db_official}'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </table>
        </form>
        {/mastercom_smart_message}

        {mastercom_smart_message
        nome_pulsante="messaggio_visione_storica_con_modifica"
        contesto="istituto"
        utente_corrente=$current_user
        }
        <form method='post' action='{$SCRIPT_NAME}'>
        <br>
            <table width='100%'>
                <tr>
                    <td width="20%"></td>
                    <td align='center' width='50%' class="area_smart_message">
                        Visualizzazione dell'anno scolastico {$anno_scolastico_visualizzato} con possibilità di modifica
                    </td>
                    <td width="20%" align="right">
                        <input type='submit' value='Torna ad anno corrente'>
                    </td>
                </tr>
            </table>

            <input type='hidden' name='form_stato' value='{$form_stato}'>
            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
            {if $stato_principale == 'ricerca_full'}
                <input type='hidden' name='stato_secondario' value='ricerca_display'>
            {/if}
            <input type='hidden' name='cambio_rapido_anno_consultazione' value='SI'>
            <input type='hidden' name='anno_consultazione' value='{$db_official}'>
            <input type='hidden' name='current_user' value='{$current_user}'>
            <input type='hidden' name='current_key' value='{$current_key}'>
        </form>
        {/mastercom_smart_message}

        {mastercom_smart_message
		nome_pulsante="messaggio_cambio_anno"
        contesto="istituto"
        utente_corrente=$current_user
        }
        <table width='100%'>
            <tr>
                <td colspan='3' align='center' width='50%' class="area_smart_message">
                    ATTENZIONE! Cambio d'anno in corso! Operatività limitata fino al termine della procedura.
                </td>
            </tr>
        </table>
        {/mastercom_smart_message}
