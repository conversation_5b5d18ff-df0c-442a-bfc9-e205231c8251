<?php
/*
 * Tipo: Pagella finale infanzia myschoolticino
 * Nome: aa_finale_myschoolticino_01
 * <PERSON><PERSON> da: myschoolticino infanzia
 *
 */
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'aa_finale_myschoolticino_01', 'Documenti della scuola di Infanzia', 1, 'aa_finale_myschoolticino_01', 3);

 */

$orientamento = 'P';
$periodo_pagella = 'finale';
if ($periodo_pagella == 'finale') {
    $periodo = 9;
}else {
    $periodo = 7;
}

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
//    if ($studente['sezione'] == 'AZZ')
//    {
//        $arr_cognomi = ['ardia', 'cicchirillo', 'deceglie', 'falletta', 'ferrucci', 'fossati', 'pesce', 'rossi', 'taverna'];
//        if (!in_array(strtolower(trim($studente['cognome'])), $arr_cognomi)) {
//            return false;
//        }
//    }
//    elseif ($studente['sezione'] == 'VER')
//    {
//        $arr_cognomi = ['antigny', 'bruno', 'calabria', 'latorre', 'luisoni', 'parry', 'ruella', 'somaruga', 'taverna'];
//        if (!in_array(strtolower(trim($studente['cognome'])), $arr_cognomi)) {
//            return;
//        }
//    }

    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $id_classe = $parametri_stampa['id_classe'];
    $data_att = "{$parametri_stampa['data_day']}/{$parametri_stampa['data_month']}/{$parametri_stampa['data_year']}";
    $periodo = $parametri_stampa['periodo'];
    $formato = $parametri_stampa['formato'];

    $id_studente = $studente['id_studente'];
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_pagella = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo, $studente['id_studente']);

    $luogo_nascita = $provincia_nascita = '';
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .=  ' (' . $stato['descrizione'] . ')';
            $provincia_nascita = ' ';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    $arr_voti = [];
    $professori = [];
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            && (
               !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA', 'RELIGIONE', 'SOSTEGNO', 'OPZIONALE'])
                )
            )
        {
            $arr_voti[$id_materia] = $voti_pagella[$id_materia];

            foreach ($materia['professori'] as $id_professore)
            {
                $professori[$id_professore] = $id_professore;
            }
        }

        // religione
        if ($materia['in_media_pagelle'] != 'NV' &&
                (
                    ($materia['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 0)
                    ||
                    ($materia['tipo_materia'] == 'ALTERNATIVA' && $studente['esonero_religione'] == 1)
                )
            )
        {
            $arr_voti[$id_materia] = $voti_pagella[$id_materia];

            foreach ($materia['professori'] as $id_professore)
            {
                $professori[$id_professore] = $id_professore;
            }
        }

        // condotta
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {
           $arr_voti[$id_materia] = $voti_pagella[$id_materia];

            foreach ($materia['professori'] as $id_professore)
            {
                $professori[$id_professore] = $id_professore;
            }
        }
    }
    $arr_voti_new = [];
    foreach ($arr_voti as $id_materia => $voto) {
        $includi = false;
        foreach ($voto['campi_liberi'] as $campo_libero) {
            if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) ) {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value != '') {
                    $includi = true;
                    break;
                }
            }
        }
        if ($includi) {
            $arr_voti_new[$id_materia] = $arr_voti[$id_materia];
        }
    }
    $arr_voti = $arr_voti_new;

    $arr_mat_to_tit = [
        "apllicazione" => "Sviluppo personale/personal development",
        "applicazione" => "Sviluppo personale/personal development",
        "condotta"     => "Rapporti sociali/social development",
        "studio dell'ambiente"   => "Studio dell’ambiente/Study of the environment",
        "lingua inglese" => "English/Lingua Inglese",
        "lingua francese" => "Lingua francese/French",
        "francese" => "Lingua francese/French",
        "educazione musicale" => "Educazione musicale/Music",
        "attivit&agrave; grafico pittoriche" => "Attività grafico-pittoriche e creative/Expressive Arts and Design",
        "attività grafico pittoriche" => "Attività grafico-pittoriche e creative/Expressive Arts and Design",
        "educazione fisica" => "Educazione fisica/Physical education",
        "religione" => "Educazione religiosa/ Religious education",
        "educazione religiosa" => "Educazione religiosa/ Religious education",

        "competenza personale"  => "Competenza personale, sociale e capacità di imparare ad imparare.
Personal and Social Competencies and Approaches to Learning.",
        "lingua italiana"   => "Area linguistica - Italiano
Literacy - Italian Language",
        "lingua inglese" => "Literacy - English Language
Area linguistica - Inglese",
        "lingua francese" => "Literacy - French Language
Area linguistica - Francese",
        "francese" => "Literacy - French Language
Area linguistica - Francese",
        "matematica"  => "Area matematica
Mathematics",
        "educazione cosmica"  => "Educazione cosmica (Metodo Montessori)
Cosmic Education (Montessori Philosophy)",
        "studio dell'ambiente"  => "Outdoor and Sustainability Education",
        "tecnologia"  => "Tecnologia
Technology",
        "arti visive"  => "Arti visive
Visual Arts",
        "educazione musicale"  => "Educazione musicale
Music Education",
        "educazione religiosa"  => "Educazione religiosa
Religious Education",
        "educazione motoria"  => "Educazione motoria
Physical Education",

        "Area linguistica - Italiano"   => "Area linguistica - Italiano
Literacy - Italian Language",
        "Area Linguistica - Inglese"  => "Area Linguistica - Inglese
Literacy - English Language",
        "Area linguistica - Francese"  => "Area linguistica - Francese
Literacy - French Language",
        "Area matematica"  => "Area matematica
Mathematics",
        "Educazione cosmica (Metodo Montessori)"  => "Educazione cosmica (Metodo Montessori)
Cosmic Education (Montessori Philosophy)",
        "Outdoor and Sustainability Education"  => "Outdoor and Sustainability Education",
        ""  => "",
        ""  => "",
        ""  => "",


    ];



    // Dizionario temporaneo
    $labels = [
        "pag_firme_1"    =>
"
Firma dei docenti/teachers’ signatures<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
<br>
Data/Date<br>
_______________________________________________________________________________________________<br>
<br>
<br>
Firma dell’autorità parentale/Signature of person exercising parental authority<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
<br>
Data/Date<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
_______________________________________________________________________________________________<br>
<br>
",

        "pag_firme_2"    =>
"Firma dell’autorità parentale / Signature of person exercising parental authority<br>
<br>
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<br><br>
<br>
Data / Date . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . ."
    ];

    if ($formato == 'A5') {
        $labels["pag_firme_2"] =
            "Firma dell’autorità parentale / Signature of person exercising parental authority<br>
<br>
. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .<br><br>
<br>
Data / Date . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .";
    }


    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }

    $firma_dirigente = $firma_docenti = '';
    switch ($studente['sezione']) {
        case 'AZZ':
        case 'AZZURRI':
            $firma_docenti = '<img src="immagini_scuola/cappelletti_sabrina.png" height="30" width="130"> &nbsp;&nbsp;&nbsp;'
                . '<img src="immagini_scuola/lilico_cathryn.png" height="30" width="130">';
            break;
        case 'VER':
        case 'VERDI':
            $firma_docenti = '<img src="immagini_scuola/pagani_anna.png" height="30" width="130"> &nbsp;&nbsp;&nbsp;'
                . '<img src="immagini_scuola/ulrike-mallasz.png" height="30" width="130"> &nbsp;&nbsp;&nbsp;'
                ;
            break;
        case 'ARANCIONI':
            $firma_docenti = '<img src="immagini_scuola/pontiggia-giorgia.png" height="30" width="130"> &nbsp;&nbsp;&nbsp;'
                . '<img src="immagini_scuola/isabelle-etter.png" height="30" width="130"> &nbsp;&nbsp;&nbsp;'
                ;
    }
    $firma_dirigente = '<img src="immagini_scuola/elisabetta-masini.png" height="30" width="130">';
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="pdf">
    $fnt = 'helvetica';
    $fd = ($formato == 'A4' ? 10 : 8);
    $y_pg = ($formato == 'A4' ? 30 : 25);
    $fd_1 = ($formato == 'A4' ? 14 : 9);
    $y_pg_1 = ($formato == 'A4' ? 70 : 40);
    $h = ($formato == 'A4' ? 30 : 15);

    // Image($file, $x='', $y='', $w=0, $h=0, $type='', $link='', $align='', $resize=false, $dpi=300, $palign='', $ismask=false, $imgmask=false, $border=0, $fitbox=false, $hidden=false, $fitonpage=false)
    $pdf->AddPage('P');
    $pdf->SetAutoPageBreak("off", 0);
    $pdf->SetFont($fnt, '', $fd_1+2);
    $img_bg = 'immagini_scuola/carta_intestata.jpg';
    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
    $pdf->SetY($y_pg_1);
    $pdf->writeHTMLCell(0, 0, '', '', "Scheda personale descrittiva<br><i>Progress Report</i>", 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Anno Scolastico<br><i>School Year</i><br>$anno_scolastico_attuale", 0, 1, false, true, 'C');
    $pdf->ln(19);
    $pdf->writeHTMLCell(0, 0, '', '', "{$studente['nome']} {$studente['cognome']}", 0, 1, false, true, 'C');
    $pdf->ln(19);
    $pdf->writeHTMLCell(0, 0, '', '', "Scuola dell`Infanzia<br><i>Early Years</i>", 0, 1, false, true, 'C');

//    $pdf->AddPage('P');
//    $pdf->SetFont($fnt, '', $fd);
//    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
//    $pdf->SetY($y_pg);
//    reset($arr_voti);
//    foreach ($arr_voti as $id_materia => $voto) {
//        $desc_materia = $arr_mat_to_tit[strtolower($voto['descrizione'])];
//        if ($desc_materia != '') {
//            $first_key = $id_materia;
//        }
//    }
//    echo "$first_key";
    foreach ($arr_voti as $id_materia => $voto) {
        $desc_materia = $arr_mat_to_tit[strtolower($voto['descrizione'])];
        if ( ! $desc_materia != '') {
            $desc_materia = $voto['descrizione'];
        }
//        if ($desc_materia != '')
//        {
            if ($first_key != $id_materia)
            {
                $pdf->AddPage('P');
                $pdf->SetFont($fnt, '', $fd);
                $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
                $pdf->SetY($y_pg);
            }

            $desc_materia = str_replace("/", "\n", $desc_materia);
            $pdf->SetFont($fnt, 'B', $fd+3);
            $pdf->SetFillColor(0, 92, 153);
            $pdf->SetTextColor(255, 255, 255);
            $pdf->MultiCell(0, 0, $desc_materia, 1, 'L', 1, 1, '', '', true, 1, $ishtml=false, true, 0);
            $pdf->SetFillColor(255, 255, 255);
            $pdf->SetTextColor(0, 0, 0);

            foreach ($voto['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) ) {
                    if (stripos($campo_libero['nome'],'Assenze giustificate') === false
                            and
                        stripos($campo_libero['nome'],'Assenze arbitrarie') === false
                            and
                        stripos($campo_libero['nome'],'Decisione di fine anno') === false
                            and
                        stripos($campo_libero['nome'],'Osservazioni generali') === false
                        )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
    //                    if ($value != '') {
                            $campo_libero['nome'] = str_replace(".", ".<br>&#x2022;   ", $campo_libero['nome']);
                            if (
                                    substr($campo_libero['nome'], -16) == ".<br>&#x2022;   "
                                ) {
                                $campo_libero['nome'] = substr($campo_libero['nome'], 0, -15);
                            }
                            $value = str_replace(["\r", "\n"], "<br>", $value);
                            $value = str_replace("<br><br>", "<br>", $value);
                            if ($campo_libero['nome'] != 'AREE DI SVILUPPO / Areas of Development') {
                            $pdf->SetFont($fnt, 'B', $fd+1);
                            $pdf->SetFillColor(153, 204, 255);
                                $pdf->MultiCell(0, 0, " &#x2022;   {$campo_libero['nome']}", 1, 'L', 1, 1, '', '', true, 1, $ishtml=true, true, 0);
                            $pdf->SetFont($fnt, '', $fd);
                            $pdf->SetFillColor(242, 242, 242);
//                            $pdf->MultiCell(0, 0, "Commento del docente", 1, 'L', 1, 1, '', '', true, 1, $ishtml=true, true, 0);
//                            $pdf->writeHTMLCell(0, 8, '', '', $campo_libero['nome'], 1, 1, false, true, 'L');

                            if (
                                    ($studente['id_studente'] ==1001635
                                    ||
                                    $studente['id_studente'] == 1001661)
                                    &&
                                    stripos($campo_libero['nome'],'lezioni collettive') !== false
                                )
                            {$pdf->SetFont($fnt, '', $fd-1);}

                            $prof_nomi = [];
                            foreach ($voto['nomi_professori'] as $prf) {
                                $prof_nomi[] .= $prf;
                            }
                            $prof_nomi = implode(',', $prof_nomi);
                            if (stripos($desc_materia,'Montessori')!==false && ($studente['sezione']=='AZZ' || $studente['sezione']=='VER')) {
                                $pdf->writeHTMLCell(0, 18, '', '', $value, 'TLR', 1, false, true, 'L');
                                if (stripos($campo_libero['nome'],'utilizzo del materiale')!==false) {
                                    $pdf->writeHTMLCell(0, 0, '', '', '('.$prof_nomi.')', 'BLR', 1, false, true, 'L');
                                }
                            } else {
                                $pdf->writeHTMLCell(0, $h, '', '', $value, 'TLR', 1, false, true, 'L');
                                $pdf->writeHTMLCell(0, 0, '', '', '('.$prof_nomi.')', 'BLR', 1, false, true, 'L');
                            }
                            }
    //                    }
                    }
                }
            }
//        }
    }

    // ordinamento diverso professori per classe 1SE
    if ($studente['classe'] == 1 && $studente['sezione'] == 'SE') {
        $prof_nths = array_keys($professori);

        $professori = array_slice($professori, 0, 1) +
                array( $prof_nths[2] => $professori[$prof_nths[2]] ) +
                array( $prof_nths[1] => $professori[$prof_nths[1]] ) +
                array_slice($professori, 2);
    }
    $stringa_docenti = '';
    foreach($professori as $prof) {
        $prof = estrai_utente($prof);
        if (stripos('utente', $prof['cognome'])===false && stripos('utente', $prof['nome'])===false
                && stripos('Terzaquarta', $prof['nome'])===false){
            $stringa_docenti[] = ucwords(strtolower("{$prof['cognome']} {$prof['nome']}"));
        }
    }
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Docenti titolari / Class Teachers: $firma_docenti", 0, 1, false, true, 'L');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Dirigente / School Headteacher: $firma_dirigente", 0, 1, false, true, 'L');


    $pdf->AddPage('P');
    $pdf->SetFont($fnt, '', $fd+2);
    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();
    $pdf->SetY($y_pg);
    $pdf->writeHTMLCell(0, 0, '', '', "Scheda personale descrittiva<br><i>Progress Report</i>", 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', "Anno Scolastico<br><i>School Year</i><br>$anno_scolastico_attuale", 0, 1, false, true, 'C');
    $pdf->ln(16);
    $pdf->writeHTMLCell(0, 0, '', '', "{$studente['nome']} {$studente['cognome']}", 0, 1, false, true, 'C');
    $pdf->ln(16);
    $pdf->writeHTMLCell(0, 0, '', '', "Scuola dell`Infanzia<br><i>Early Years</i>", 0, 1, false, true, 'C');
    $pdf->ln(8);
    $pdf->writeHTMLCell(0, 0, '', '', "Consegna: $data_att", 0, 1, false, true, 'C');
    if ($formato == 'A4') {
        $pdf->ln(28);
    }else{
        $pdf->ln(6);
    }
    $pdf->writeHTMLCell(0, 0, '', '', $labels['pag_firme_2'], 0, 1, false, true, 'L');
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'         => $id_classe,
    'data_day'          => $data_Day,
    'data_month'        => $data_Month,
    'data_year'         => $data_Year,
    'periodo_pagella'   => $periodo_pagella,
    'periodo'           => $periodo,
    'orientamento'      => $orientamento,
    'formato'           => $formato

];

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            $da_stampare = genera_stampa($pdf, $studente, $parametri_stampa);
            if ($da_stampare !== false) {

            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            $da_stampare = genera_stampa($pdf, $studente, $parametri_stampa);
            if ($da_stampare !== false) {
                $pdf->Output($file, "F");
            }
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            $da_stampare =genera_stampa($pdf, $studente, $parametri_stampa);
            if ($da_stampare !== false)
            {
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
