<?php

function estrai_professori($tipo = 'P', $chiave_id = 'NO', $limiti = [], $current_key = "")
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i professori dell'istituto">
    if ($tipo == 'P' || $tipo == 'A')
    {
        $where_tipo = " (tipo_utente = '{$tipo}' OR tipo_utente = 'M') ";
    }
    else
    {
        $where_tipo = " tipo_utente = '{$tipo}' ";
    }

    if (isset($limiti['indirizzi']) && count($limiti['indirizzi'] > 0)){
        $where_tipo .= " AND id_utente IN (SELECT id_professore
                                            FROM classi_prof_materie
                                            WHERE id_classe IN (SELECT id_classe
                                                                FROM classi_complete
                                                                WHERE id_indirizzo IN (" . implode(", ", $limiti['indirizzi']) . ")
                                                                )
                                            ) ";
    }

	$query = "SELECT * FROM utenti
              WHERE	{$where_tipo}
				AND	flag_canc = 0
			  ORDER BY cognome";
	$result = pgsql_query($query) or die ("Invalid $query");
	$numero = pg_num_rows($result);

    if($numero > 0)
	{
		//TODO: con php 5.2.x il trim é necessario perché con campi character fissi (questo é di 2) pg_fetch_* restituisce ad es. "0 " (con lo spazio in più)
		for($cont = 0; $cont < $numero; $cont++)
		{
            if($chiave_id == 'SI'){
                $id_docente = pg_fetch_result($result, $cont, "id_utente");
                $dati_professori[$id_docente]['id_utente'] 			= $id_docente;
                $dati_professori[$id_docente]['nome']				= decode(pg_fetch_result($result, $cont, "nome"));
                $dati_professori[$id_docente]['cognome'] 			= decode(pg_fetch_result($result, $cont, "cognome"));
                $dati_professori[$id_docente]['utente']				= decode(pg_fetch_result($result, $cont, "utente"));
                $dati_professori[$id_docente]['password_utente'] 		= pg_fetch_result($result, $cont, "password_utente");
                $dati_professori[$id_docente]['data_nascita']                   = pg_fetch_result($result, $cont, "data_nascita");
                $dati_professori[$id_docente]['provincia_nascita']              = pg_fetch_result($result, $cont, "provincia_nascita");
                $dati_professori[$id_docente]['tipo_professore'] 		= pg_fetch_result($result, $cont, "tipo_utente");
                $dati_professori[$id_docente]['data_nascita_tradotta']          = date('d/m/Y', $dati_professori[$id_docente]['data_nascita']);
                $dati_professori[$id_docente]['password_modificata']            = pg_fetch_result($result, $cont, "password_modificata");
                $dati_professori[$id_docente]['utente_registro'] 		= pg_fetch_result($result, $cont, "utente_registro");

                if ($current_key != ""){
                    $utente_couch = estrai_utente_couch($id_docente, 'U', $current_key);
                    $dati_professore[$id_docente]["utente"] = $utente_couch['user'];
                    $dati_professore[$id_docente]["password_utente"] = $utente_couch['password'];
                }
            } else {
                $dati_professori[$cont][0] 						 = pg_fetch_result($result, $cont, "id_utente");
                $dati_professori[$cont][1]						 = decode(pg_fetch_result($result, $cont, "nome"));
                $dati_professori[$cont][2] 						 = decode(pg_fetch_result($result, $cont, "cognome"));
                $dati_professori[$cont][3]						 = decode(pg_fetch_result($result, $cont, "utente"));
                $dati_professori[$cont][4] 						 = pg_fetch_result($result, $cont, "password_utente");
                $dati_professori[$cont][5] 						 = trim(pg_fetch_result($result, $cont, "privilegi"));
                $dati_professori[$cont]['id_utente'] 			 = $dati_professori[$cont][0];
                $dati_professori[$cont]['nome']					 = $dati_professori[$cont][1];
                $dati_professori[$cont]['cognome'] 				 = $dati_professori[$cont][2];
                $dati_professori[$cont]['utente']				 = $dati_professori[$cont][3];
                $dati_professori[$cont]['password_utente'] 		 = $dati_professori[$cont][4];
                $dati_professori[$cont]['data_nascita'] 		 = pg_fetch_result($result, $cont, "data_nascita");
                $dati_professori[$cont]['provincia_nascita']	 = pg_fetch_result($result, $cont, "provincia_nascita");
                $dati_professori[$cont]['tipo_professore'] 		 = pg_fetch_result($result, $cont, "tipo_utente");
                $dati_professori[$cont]['data_nascita_tradotta'] = date('d/m/Y', $dati_professori[$cont]['data_nascita']);
                $dati_professori[$cont]['password_modificata'] 	 = pg_fetch_result($result, $cont, "password_modificata");
                $dati_professori[$cont]['utente_registro'] 		 = pg_fetch_result($result, $cont, "utente_registro");

                if ($current_key != "") {
                    $utente_couch = estrai_utente_couch($dati_professori[$cont]['id_utente'], 'U', $current_key);
                    $dati_professore[$cont]["utente"] = $utente_couch['user'];
                    $dati_professore[$cont]["password_utente"] = $utente_couch['password'];
                }
            }
		}
	}

	return $dati_professori;
	//}}} </editor-fold>
}

function estrai_elenco_professori_select()
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre tutti i professori che insegnano in una classe">
    $professori = estrai_professori('P');

    if(is_array($professori))
    {
        foreach($professori as $professore)
        {
            $mat_professori[] = [
                'nome'   => $professore['cognome'] .' '. $professore['nome'],
                'valore' => $professore['id_utente']
            ];
        }

        return $mat_professori;
    }
    //}}} </editor-fold>
}

function estrai_dati_professore($id_professore, $tipo_orario = null, $current_key = "")
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i professori dell'istituto">
	if($id_professore > 0)
	{
		$query = "SELECT * FROM	utenti
                            WHERE id_utente = ".$id_professore."
                            AND	flag_canc = 0";

		$result = pgsql_query($query) or die ("Invalid $query");
		$numero = pg_num_rows($result);
        if($numero > 0)
		{
			//TODO: con php 5.2.x il trim é necessario perché con campi character fissi (questo é di 2) pg_fetch_* restituisce ad es. "0 " (con lo spazio in più)
			$dati_professore[0] 						  = pg_fetch_result($result, $cont, "id_utente");
			$dati_professore[1] 						  = decode(pg_fetch_result($result, $cont, "nome"));
			$dati_professore[2] 						  = decode(pg_fetch_result($result, $cont, "cognome"));
			$dati_professore[3] 						  = decode(pg_fetch_result($result, $cont, "utente"));
			$dati_professore[4] 						  = pg_fetch_result($result, $cont, "password_utente");
			$dati_professore[5] 						  = trim(pg_fetch_result($result, $cont, "privilegi"));
			$dati_professore["id_utente"] 				  = $dati_professore[0];
			$dati_professore["nome"] 					  = $dati_professore[1];
			$dati_professore["cognome"] 				  = $dati_professore[2];
			$dati_professore["utente"] 					  = $dati_professore[3];
			$dati_professore["utente_registro"] 		  = pg_fetch_result($result, $cont, "utente_registro");
			$dati_professore["password_utente"] 		  = $dati_professore[4];
			$dati_professore["privilegi"] 				  = $dati_professore[5];
			$dati_professore['data_nascita'] 			  = pg_fetch_result($result, $cont, "data_nascita");
			$dati_professore['provincia_nascita'] 		  = pg_fetch_result($result, $cont, "provincia_nascita");
			$dati_professore['tipo_professore'] 		  = pg_fetch_result($result, $cont, "tipo_utente");
			$dati_professore['data_nascita_tradotta'] 	  = date('d/m/Y',$dati_professore['data_nascita']);
			$dati_professore['password_modificata'] 	  = pg_fetch_result($result, $cont, "password_modificata");
			$dati_professore['vincolo_orario_scolastico'] = pg_fetch_result($result, $cont, "vincolo_orario_scolastico");
            $dati_professore['docente_sostegno']          = pg_fetch_result($result, $cont, "docente_sostegno");
            $dati_professore['lingua_interfaccia']        = pg_fetch_result($result, $cont, "lingua_interfaccia");
            $dati_professore['codice_fiscale']            = trim(pg_fetch_result($result, $cont, "codice_fiscale"));

            if ($current_key != ""){
                $utente_couch = estrai_utente_couch($dati_professore['id_utente'], 'U', $current_key);
                $dati_professore["utente"] = $utente_couch['user'];
                $dati_professore["password_utente"] = $utente_couch['password'];
            }
		}

		return $dati_professore;
	}
	else
	{
        if ($tipo_orario && $tipo_orario == 'orario_studenti') {
            return ["id_utente" => -1, 'cognome' => 'Nessun professore'];
        } else {
            return ["id_utente" => -1, 'cognome' => 'Professore non definito'];
        }
	}
	//}}} </editor-fold>
}

/**
 * TODO: cambiare la procedura per fare tutto in SQL usando md5(random()::text)
 *
 * @param type $id_utente
 * @param type $current_user
 */
function modifica_chiave_registro_utente($id_utente, $current_user)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per modificare la chiave dell'utente">
	$query = "SELECT utente_registro FROM utenti";

	$res = pgsql_query($query) or die ("Invalid $query");
	$num = pg_num_rows($res);

    $keys = [];

	if ($num > 0) {
        for ($c = 0; $c < $num; $c++) {
            $keys[] = pg_fetch_result($res, $c, "utente_registro");
        }
    }

    do {
		$newKey = md5(uniqid(rand(10,99),true)) . md5(uniqid(rand(10,99),true));
	} while(in_array($newKey, $keys));

    $query = "UPDATE utenti SET	utente_registro = '{$newKey}'
              WHERE id_utente = {$id_utente}";

	pgsql_query($query) or die ("Invalid $query");

    inserisci_log(["id_utente" => $id_utente], "utenti", $current_user, "INTERFACCIA", "MODIFICA");
	//}}} </editor-fold>
}

function resetta_chiave_professori($mat_utenti, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per resettare la chiave del professore">
    if (is_array($mat_utenti)) {
        foreach ($mat_utenti as $id_utente) {
            modifica_chiave_registro_utente($id_utente, $current_user);
        }
    }
    //}}} </editor-fold>
}

function esistenza_record_in_funzioni_professori($codice, $descrizione, $categoria, $ordinamento_categoria, $ordinamento, $parametro)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="estrazione del record interessato">
	if($categoria == 'REMOVE')
	{
		$query = "DELETE FROM elenco_funzioni_professori WHERE codice_funzione = '$codice'";
		pgsql_query($query);
	}
	else
	{
		$query = "SELECT * FROM elenco_funzioni_professori WHERE codice_funzione = '$codice'";
		$result = pgsql_query($query) or die ("Invalid $query");
		$numero = pg_num_rows($result);

		if($numero > 0)
		{
			$dati = pg_fetch_assoc($result, 0);

			if(	$dati['descrizione'] == $descrizione
				&& $dati['parametro_associato'] == $parametro
				&& $dati['categoria'] == $categoria
				&& $dati['ordinamento_categoria'] == $ordinamento_categoria
				&& $dati['ordinamento'] == $ordinamento
			)
			{
				$valore_record = "OK";
			}
			else
			{
				$valore_record = "UPDATE";
			}

			if($numero > 1)
			{
				for($cont = 1; $cont < $numero; $cont++)
				{
					$id_funzione_professore = pg_fetch_result($result,$cont,"id_funzione_professore");

					$query = "DELETE FROM elenco_funzioni_professori
                                WHERE id_funzione_professore = ".$id_funzione_professore;
					pgsql_query($query);
				}
			}
		}
		else
		{
			$valore_record = "NON_ESISTE";
		}

		//verifica l'esistenza del record e nel caso non esistesse lo inserisce
		if($valore_record == "NON_ESISTE")
		{
			$query = "INSERT INTO elenco_funzioni_professori (
							codice_funzione,
							descrizione_funzione,
							parametro_associato,
							categoria,
							ordinamento_categoria,ordinamento
						) VALUES (
							'" . $codice . "',
							'" . $descrizione . "',
							'" . $parametro . "',
							'" . $categoria . "',
							'" . $ordinamento_categoria . "',
							'" . $ordinamento . "'
						)";

            pgsql_query($query);
		}
		elseif($valore_record == "UPDATE")
		{
			$query = "UPDATE elenco_funzioni_professori
						SET	descrizione_funzione	= '" . $descrizione . "',
							parametro_associato		= '" . $parametro . "',
							categoria				= '" . $categoria . "',
							ordinamento_categoria	= '" . $ordinamento_categoria . "',
							ordinamento				= '" . $ordinamento . "'
						WHERE codice_funzione = '$codice'";

            pgsql_query($query);
		}
		else
		{
			$query = "";
			$result = "In elenco_funzioni_professori il record " . $codice . " esiste già";
		}
	}

	$commento = "query = " . $query . chr(13) . chr(10) . "    risultato = " . $result . chr(13) . chr(10);

    return $commento;
	//}}} </editor-fold>
}

function aggiorna_privilegi_professori($privilegio, $vincolo_orario_scolastico, $current_user)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="aggiornamento dei privilegi di tutti i professori">
	$query_update = "UPDATE	utenti SET
                        privilegi = '$privilegio',
						vincolo_orario_scolastico = '$vincolo_orario_scolastico'
					 WHERE tipo_utente = 'P'";

    $result = pgsql_query($query_update) or die("Invalid $query_update");

    inserisci_log(["tipo_utente" => "P"], "utenti", $current_user, "INTERFACCIA", "MODIFICA");

    return $result;
    //}}} </editor-fold>
}

function estrai_livello_abilitazione_funzioni_professore($id_professore, $flag_parametri = '')
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i livelli di abilitazione alle funzionalità dei singoli professori">
	$query_funzioni = "SELECT * FROM elenco_funzioni_professori
					   ORDER BY ordinamento_categoria, categoria, ordinamento, codice_funzione";

	$result_funzioni = pgsql_query($query_funzioni) or die ("Invalid $query_funzioni");
	$numero_funzioni = pg_num_rows($result_funzioni);

    $messaggistica = estrai_parametri_singoli('ABILITA_MESSAGGI_REGISTRO');

	if($numero_funzioni > 0)
	{
		$cont_spec = 0;

		for($cont_funz = 0; $cont_funz < $numero_funzioni; $cont_funz++)
		{
			$checked = true;

            // verifico, nel caso ci sia un optional, se sia abilitato o meno
            $optional_associato = pg_fetch_result($result_funzioni,$cont_funz,"optional_associato");
            if ($optional_associato != '')
            {
                $valore_optional = estrai_optionals_singoli($optional_associato, 'professore');

                if ($valore_optional == 0)
                {
                    $checked = false;
                }
            }

			if(is_array($flag_parametri))
			{
				$parametro_associato = pg_fetch_result($result_funzioni,$cont_funz,"parametro_associato");

				foreach($flag_parametri as $flag => $flag_value)
				{
					if(!
                        ($flag_value and $flag == $parametro_associato
						or !$flag_value and $flag != $parametro_associato)
                    )
					{
						$checked = false;
					}
				}
			}

			if($checked)
			{
				$elenco_funzioni[$cont_spec][0] = pg_fetch_result($result_funzioni,$cont_funz,"id_funzione_professore");
				$elenco_funzioni[$cont_spec][1] = pg_fetch_result($result_funzioni,$cont_funz,"codice_funzione");
				$elenco_funzioni[$cont_spec][2] = decode(pg_fetch_result($result_funzioni,$cont_funz,"descrizione_funzione"));
				$elenco_funzioni[$cont_spec]["id_funzione_professore"] = $elenco_funzioni[$cont_spec][0];
				$elenco_funzioni[$cont_spec]["codice_funzione"] = $elenco_funzioni[$cont_spec][1];
				$elenco_funzioni[$cont_spec]["descrizione_funzione"] = $elenco_funzioni[$cont_spec][2];

                if ($elenco_funzioni[$cont_spec]["codice_funzione"] == 'MESSAGGI REGISTRO' && $messaggistica == 'NO') {
                    $elenco_funzioni[$cont_spec]["descrizione_funzione"] .= '<br><span style="margin-left:24px">(i servizi sono ancora disabilitati per la scuola, contattare l\'assistenza)</span>';
                }

				$elenco_funzioni[$cont_spec]["categoria"] = pg_fetch_result($result_funzioni,$cont_funz,"categoria");
				$elenco_funzioni[$cont_spec]["ordinamento"] = pg_fetch_result($result_funzioni,$cont_funz,"ordinamento");
				$elenco_funzioni[$cont_spec]["ordinamento_categoria"] = pg_fetch_result($result_funzioni,$cont_funz,"ordinamento_categoria");

				if($id_professore > 0)
				{
					$query_permessi = "SELECT * FROM abbinamenti_funzioni_professori
										WHERE id_professore = " . $id_professore . "
											AND id_funzione = " . $elenco_funzioni[$cont_spec][0];
					$result_permessi = pgsql_query($query_permessi) or die ("Invalid $query_permessi");
					$numero_permessi = pg_num_rows($result_permessi);
				}
				else
				{
					$numero_permessi = 0;
				}

				if($numero_permessi > 0)
				{
					$elenco_funzioni[$cont_spec][3] = pg_fetch_result($result_permessi,0,"id_abb_prof_funz");
					$elenco_funzioni[$cont_spec][4] = pg_fetch_result($result_permessi,0,"livello_abilitazione");
					$elenco_funzioni[$cont_spec]["id_abb_prof_funz"] = $elenco_funzioni[$cont_spec][3];
					$elenco_funzioni[$cont_spec]["livello_abilitazione"] = $elenco_funzioni[$cont_spec][4];
				}
				else
				{
					$elenco_funzioni[$cont_spec][3] = "-1";
					$elenco_funzioni[$cont_spec][4] = "0";
					$elenco_funzioni[$cont_spec]["id_abb_prof_funz"] = "-1";
					$elenco_funzioni[$cont_spec]["livello_abilitazione"] = "0";
				}

				$cont_spec++;
			}
		}
	}

	return $elenco_funzioni;
	//}}} </editor-fold>
}

/**
 * Funzione per estrarre l'abilitazione di un docente ad una singola funzione
 *
 * @param int $id_professore
 * @param string $funzione
 * @return boolean $abilitato
 *
 */
function verifica_abilitazione_singola_funzione_professore($id_professore, $funzione)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre l'abilitazione ad una singola funzione del docente">
    $abilitato = false;

    if ($funzione != '')
    {
        $query = "SELECT livello_abilitazione
                    FROM abbinamenti_funzioni_professori
                    WHERE id_funzione = (
                        SELECT id_funzione_professore FROM elenco_funzioni_professori
                        WHERE codice_funzione = '{$funzione}') AND id_professore = {$id_professore}";

		$result = pgsql_query($query) or die ("Invalid $query");
        $numero = pg_num_rows($result);
        if ($numero > 0) {
            $abilitato = pg_fetch_result($result, 0, 'livello_abilitazione') == 1 ? true : false;
        }
    }

    return $abilitato;
    //}}} </editor-fold>
}

function estrai_elenco_funzioni($tipo_utente)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i livelli di abilitazione alle funzionalità dei singoli professori">
    switch ($tipo_utente) {
        case "A":
            $suffisso = "utenti";
            $campo = "utente";
            break;

        case "P":
            $suffisso = "professori";
            $campo = "professore";
            break;

        default:
            $suffisso = "professori";
            $campo = "professore";
            break;
    }
	$query_funzioni = "SELECT * FROM elenco_funzioni_{$suffisso}
					   ORDER BY ordinamento_categoria, categoria, ordinamento, codice_funzione";

	$result_funzioni = pgsql_query($query_funzioni) or die ("Invalid $query_funzioni");
	$numero_funzioni = pg_num_rows($result_funzioni);

    $messaggistica = estrai_parametri_singoli('ABILITA_MESSAGGI_REGISTRO');

	if($numero_funzioni > 0)
	{
		$cont_spec = 0;

		for($cont_funz = 0; $cont_funz < $numero_funzioni; $cont_funz++)
		{
			$checked = true;

			if($checked)
			{
				$elenco_funzioni[$cont_spec][0] = pg_fetch_result($result_funzioni,$cont_funz,"id_funzione_{$campo}");
				$elenco_funzioni[$cont_spec][1] = pg_fetch_result($result_funzioni,$cont_funz,"codice_funzione");
				$elenco_funzioni[$cont_spec][2] = decode(pg_fetch_result($result_funzioni,$cont_funz,"descrizione_funzione"));
                $elenco_funzioni[$cont_spec]["id_funzione_{$campo}"] = $elenco_funzioni[$cont_spec][0];
				$elenco_funzioni[$cont_spec]["codice_funzione"] = $elenco_funzioni[$cont_spec][1];
				$elenco_funzioni[$cont_spec]["descrizione_funzione"] = $elenco_funzioni[$cont_spec][2];
                $elenco_funzioni[$cont_spec]["descrizione_completa"] = $elenco_funzioni[$cont_spec][2];
                if ($elenco_funzioni[$cont_spec]["codice_funzione"] == 'MESSAGGI REGISTRO' && $messaggistica == 'NO') {
                    $elenco_funzioni[$cont_spec]["descrizione_funzione"] .= '<br><span style="margin-left:24px">(i servizi sono ancora disabilitati per la scuola, contattare l\'assistenza)</span>';
                }

				$elenco_funzioni[$cont_spec]["categoria"] = pg_fetch_result($result_funzioni,$cont_funz,"categoria");
				$elenco_funzioni[$cont_spec]["ordinamento"] = pg_fetch_result($result_funzioni,$cont_funz,"ordinamento");
				$elenco_funzioni[$cont_spec]["ordinamento_categoria"] = pg_fetch_result($result_funzioni,$cont_funz,"ordinamento_categoria");

                $cont_spec++;
			}
		}
	}

	return $elenco_funzioni;
	//}}} </editor-fold>
}

function aggiorna_abilitazione_funzioni_professore($id_professore, $elenco_funzioni, $current_user)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per aggiornare le funzionalità abilitate per i professori">
	for ($cont = 0; $cont < count($elenco_funzioni); $cont++)
	{
        if($elenco_funzioni[$cont]["id_abb_prof_funz"] == "-1")
        {
            $query = "INSERT INTO abbinamenti_funzioni_professori (
                            id_professore,
                            id_funzione,
                            livello_abilitazione
                        ) VALUES (
                            " . $id_professore . ",
                            " . $elenco_funzioni[$cont]["id_funzione_professore"] . ",
                            '" . $elenco_funzioni[$cont]["livello_abilitazione"] . "'
                        )";

            pgsql_query($query) or die ("Invalid $query");

            $query = "SELECT id_abb_prof_funz FROM abbinamenti_funzioni_professori
                      WHERE	id_professore = '" . $id_professore . "'
                        AND	id_funzione = '" . $elenco_funzioni[$cont]["id_funzione_professore"] . "'
                        AND	livello_abilitazione = '" . $elenco_funzioni[$cont]["livello_abilitazione"] . "'";

            $result = pgsql_query($query) or die ("Invalid $query");
            $numero = pg_num_rows($result);

            if($numero > 0)
            {
                $id_abbinamento = pg_fetch_result($result,0,"id_abb_prof_funz");
            }
        }

        if($elenco_funzioni[$cont]["id_abb_prof_funz"] > 0)
        {
            $query = "UPDATE abbinamenti_funzioni_professori
                        SET livello_abilitazione = '" . $elenco_funzioni[$cont]["livello_abilitazione"] . "'
                      WHERE id_abb_prof_funz = " .$elenco_funzioni[$cont]["id_abb_prof_funz"];

            $result = pgsql_query($query) or die ("Invalid $query");

            $query2 = "
                      UPDATE abbinamenti_funzioni_professori a
                        SET livello_abilitazione = '0'
                        WHERE a.id_funzione = (
                                SELECT id_funzione_professore
                                FROM elenco_funzioni_professori
                                WHERE codice_funzione = 'HOTSPOT REGISTRO'
                            )
                            AND (
                                SELECT COUNT(livello_abilitazione)
                                FROM abbinamenti_funzioni_professori b
                                WHERE b.id_professore = a.id_professore
                                    AND b.id_funzione IN (
                                        SELECT id_funzione_professore
                                        FROM elenco_funzioni_professori
                                        WHERE codice_funzione = 'MESSAGGI REGISTRO'
                                    )
                                    AND livello_abilitazione = '0'
                            ) > 0;
                      ";

            pgsql_query($query2) or die ("Invalid $query2");
        }
	}

	return $result;
	//}}} </editor-fold>
}

function resetta_password_professori($filtro, $prefisso, $suffisso, $current_user, $current_key)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per eliminare un professore">
	$mat_professori = estrai_professori('P');
	$mat_commissari = estrai_professori('E');

	$chiave_accesso = estrai_parametri_singoli("USA_CHIAVE_ACCESSO");

    $as = str_replace('/', '_', estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE'));
    $mastercom_id = estrai_parametri_singoli('MASTERCOM_ID');

	foreach($mat_professori as $professore)
	{
		if($professore['password_modificata'] != 'SI' or $filtro == 'SOVRASCRIVI_TUTTE')
		{
            $password_composta = $prefisso . $professore['utente'] . $suffisso;
            $nuova_password = MT\Utils\Pbkdf2::encode($prefisso . $professore['utente'] . $suffisso);
			$query = "UPDATE utenti
						SET password_utente = '" . $nuova_password . "',
							password_modificata = '--'
						WHERE id_utente = '" . $professore['id_utente'] . "'";

            pgsql_query($query) or die ("Invalid $query");

			inserisci_log(["id_utente" => $professore['id_utente']], "utenti", $current_user, "INTERFACCIA", "MODIFICA");

            $utente_couch = estrai_utente_couch($professore['id_utente'], 'U', $current_key);
            $id_couch = $utente_couch['_id'];

            if (strlen($id_couch) > 0) {
                $path = 'user/' . $id_couch . '/update_credentials';
                $parametri = [];
                $parametri['password'] = $password_composta;
                $utente_couch = nextapi_call($path, 'POST', $parametri, $current_key);
            }

            if ($chiave_accesso == 'SENZA_PWD')
            {
                $sql = "UPDATE utenti SET registro_no_cod = '".$prefisso.$professore['utente'].$suffisso."' WHERE id_utente = ".$professore['id_utente'];
                pgsql_query($sql);

            }

		}
	}

	foreach($mat_commissari as $professore)
	{
		if($professore['password_modificata'] != 'SI' or $filtro == 'SOVRASCRIVI_TUTTE')
		{
            $password_composta = $prefisso . $professore['utente'] . $suffisso;
            $nuova_password = MT\Utils\Pbkdf2::encode($prefisso . $professore['utente'] . $suffisso);
			$query = "UPDATE utenti
						SET	password_utente = '" . $nuova_password . "',
							password_modificata = 'NO'
						WHERE id_utente = '".$professore['id_utente']."'";

			pgsql_query($query) or die ("Invalid $query");

			inserisci_log(["id_utente" => $professore['id_utente']], "utenti", $current_user, "INTERFACCIA", "MODIFICA");

            $utente_couch = estrai_utente_couch($professore['id_utente'], 'U', $current_key);
            $id_couch = $utente_couch['_id'];

            if (strlen($id_couch) > 0) {
                $path = 'user/' . $id_couch . '/update_credentials';
                $parametri = [];
                $parametri['password'] = $password_composta;
                $utente_couch = nextapi_call($path, 'POST', $parametri, $current_key);
            }
		}
	}
    //}}} </editor-fold>
}

function controlla_firma_professore($id_classe, $id_materia, $id_utente, $data_inizio, $data_fine)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per verificare se un dato docente abbia gia effettuato l'appello in una certa classe, per una certa materia, in una certa fascia oraria">
	if($id_classe > 0 && $id_materia >= -1 && $id_utente > 0 && $data_inizio > 0 && $data_fine >= $data_inizio)
	{
		$query = "SELECT id_firma FROM firme_appello_docenti
				  WHERE id_professore = " . $id_utente . "
					AND	id_classe = " . $id_classe . "
					AND	id_materia = " . $id_materia . "
					AND data >= " . $data_inizio . "
					AND	data <= " . $data_fine . "
					AND	flag_canc = 0";

        $result = pgsql_query($query) or die ("Invalid $query");
		$cont = pg_num_rows($result);

		if ($cont == 1) {
            return "SI";
        } elseif ($cont > 1) {
            //Caso di firme multiple, da decidere se dare errore o accettarlo
            return "SI";
        } else {
            return "NO";
        }
    }
	else
	{
		return "KO";
	}
    //}}} </editor-fold>
}

/**
 * Funzione per inserire la firma di un professore
 *
 * @param type $id_classe
 * @param type $id_materia
 * @param type $id_utente
 * @param type $data_inizio
 * @param type $data_fine
 * @param type $current_user
 * @param type $data
 * @return string
 */
function inserisci_firma_professore($id_classe, $id_materia, $id_utente, $data_inizio, $data_fine,  $current_user, $data = null, $tipo_inserimento = 'INTERFACCIA')
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per inserire la firma di un professore">
	if(!($data > 0))
	{
		$data = time();
	}

	$firma_presente = controlla_firma_professore($id_classe, $id_materia, $id_utente, $data_inizio, $data_fine);

	if($firma_presente == 'NO')
	{
		$query = "INSERT INTO firme_appello_docenti (
                        id_professore,
                        id_classe,
                        id_materia,
                        data
                    ) VALUES (
                        ".$id_utente.",
                        ".$id_classe.",
                        ".$id_materia.",
                        ".$data."
                    )";

		pgsql_query($query) or die ("Invalid $query");

        $mat_oggetti = [
            "id_professore" => $id_utente,
            "id_classe"     => $id_classe,
            "id_materia"    => $id_materia,
            "data"          => $data
        ];
		inserisci_log($mat_oggetti, "firme_appello_docenti", $current_user, $tipo_inserimento, "INSERIMENTO");

		return 'SI';
	}
	else
	{
		return $firma_presente;
	}
	//}}} </editor-fold>
}

function ricerca_firme_professori($parametri_ricerca)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per verificare se un dato docente abbia gia effettuato l'appello in una certa classe, per una certa materia, in una certa fascia oraria">

	// parametri ricerca:
	// start_interval 		timestamp inizio ricerca
	// end_interval 		timestamp fine ricerca
	// search 				stringa elenco argomenti di ricerca separati da spazi (classe, sezione, nome, cognome etc.)
	// tipo_visualizzazione (settimane, settimana, giornaliera)
    // ricerca_docente      boolean per limitare la ricerca al solo nome e cognome utente

	if(is_array($parametri_ricerca))
	{
		if ($parametri_ricerca['start_interval'] > 0) {
            $start_interval = intval($parametri_ricerca['start_interval']);
        } else {
            $start_interval = 0;
        }
        if ($parametri_ricerca['end_interval'] > $parametri_ricerca['start_interval']) {
            $end_interval = intval($parametri_ricerca['end_interval']);
        } else {
            $end_interval = time();
        }

        $where = '';

		if(strlen($parametri_ricerca['search']) > 0)
		{
			$mat_search = explode(' ',$parametri_ricerca['search']);

			foreach ($mat_search as $search_token) {
                if (is_numeric($search_token)) {
                    $mat_search_nums[] = $search_token;
                } else {
                    $mat_search_strings[] = $search_token;
                }
            }

			$mat_where = null;

			if(is_array($mat_search_nums))
			{
				$where .= "	( ";

				foreach($mat_search_nums as $search_token)
				{
					$mat_where[] = " ( classi_complete.classe = '". $search_token . "' )";
				}

				$where .= implode(' or ',$mat_where);
				$where .= " ) AND ";
			}

			$mat_where = null;

			if(is_array($mat_search_strings))
			{
				$where .= " ( ";

				foreach($mat_search_strings as $search_token)
				{
                    if (!$parametri_ricerca['ricerca_docente']) {
                    // NON limito la ricerca al docente
                        $mat_where[] = " (
                                        classi_complete.sezione ILIKE '". $search_token . "%'
                                        OR materie.descrizione ILIKE '" . $search_token . "'
                                        OR utenti.cognome ILIKE '%" . $search_token . "%'
                                        OR utenti.nome ILIKE '%" . $search_token . "%'
                                        OR classi_complete.codice_indirizzi ILIKE '" . $search_token . "%'
                                        OR classi_complete.descrizione_indirizzi ILIKE '" . $search_token . "%'
                                    )";
                    } else {
                    // Limito la ricerca al docente
                        $mat_where[] = " (
                                        utenti.cognome ILIKE '%" . $search_token . "%'
                                        OR utenti.nome ILIKE '%" . $search_token . "%'
                                    )";

                    }
				}

				$where .= implode(' or ',$mat_where);
				$where .= " ) AND ";
			}
		}

		$query = "SELECT
						firme_appello_docenti.*,
						classi_complete.classe,
						classi_complete.sezione,
						classi_complete.codice_indirizzi,
						classi_complete.descrizione_indirizzi,
						materie.descrizione AS materia,
						utenti.cognome,
						utenti.nome
					FROM firme_appello_docenti
                    JOIN utenti ON (utenti.id_utente = firme_appello_docenti.id_professore)
                    JOIN classi_complete ON (classi_complete.id_classe = firme_appello_docenti.id_classe)
                    LEFT JOIN materie ON (materie.id_materia = firme_appello_docenti.id_materia)
					WHERE
						$where firme_appello_docenti.data >= {$start_interval}
                        AND firme_appello_docenti.data <= {$end_interval}
						AND firme_appello_docenti.flag_canc=0
					ORDER BY
						utenti.cognome ASC,
						utenti.nome ASC,
						firme_appello_docenti.data ASC";

		$result = pgsql_query($query) or die ("Invalid $query");
		$numero = pg_num_rows($result);

		if ($numero > 0) {
            for ($cont = 0; $cont < $numero; $cont++) {
                $firme[$cont] = pg_fetch_assoc($result, $cont);
                foreach ($firme[$cont] as $key => $value) {
                    $firme[$cont][$key] = decode($value);
                }
            }

            return $firme;
        } else {
            return null;
        }
    } else {
        return "KO";
    }
    //}}} </editor-fold>
}

function calendari_firme_professori($parametri_ricerca, $beginning_year = null)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Estrae calendario firme professore">
    $data_corrente = time();
    if (!$beginning_year) {
        $beginning_year = intval(date('Y',$data_corrente));
    }

	if($parametri_ricerca['data_corrente'] > 0)	{
		$data_corrente = intval($parametri_ricerca['data_corrente']);
        if(intval(date('m',$data_corrente)) < 9) {
        	$beginning_year = intval(date('Y',$data_corrente)) - 1;
        }
	}

	$data_inizio = mktime(0,0,0,9,1,$beginning_year);
	$data_fine = mktime(0,0,0,9,1,$beginning_year + 1);
	$anno_scolastico = $beginning_year."/".($beginning_year + 1);

	if(intval($data_inizio) > intval($parametri_ricerca['start_interval']))	{
		$parametri_ricerca['start_interval'] = $data_inizio;
	}

	if(intval($data_fine) < intval($parametri_ricerca['end_interval']) || intval($parametri_ricerca['end_interval']) <= intval($parametri_ricerca['start_interval'])){
		$parametri_ricerca['end_interval'] = $data_fine;
	}

    // Aggiunto parametro ricerca per limitare la ricerca solo ai docenti
    $parametri_ricerca['ricerca_docente'] = true;
	$lista_firme = ricerca_firme_professori($parametri_ricerca);

	//Struttura array finale [id_professore][mese][giorno][ora]

	if(is_array($lista_firme)) {
		if($parametri_ricerca['tipo_visualizzazione'] == 'settimane') {
			$mat_settimane = estrai_settimane($data_corrente,'scolastico');
			$mat_eventi = estrai_elenco_eventi_firme($data_inizio, $data_fine);
		} elseif($parametri_ricerca['tipo_visualizzazione'] == 'settimana' || $parametri_ricerca['tipo_visualizzazione'] == 'giornaliera') {
			$settimana = determina_intervallo_settimana_data_corrente($data_corrente);

			$mat_settimane[] = [
                'begin_ts'   => $settimana['data_inizio'],
                'begin_data' => date('d/m/Y', $settimana['data_inizio']),
                'end_ts'     => $settimana['data_fine'],
                'end_data'   => date('d/m/Y', $settimana['data_fine']),
                'valore'     => $settimana['data_inizio'],
                'nome'       => date('d', $settimana['data_inizio']) .' '. traduci_mese_in_lettere(date('m', $settimana['data_inizio'])) ." -- ". date('d', $settimana['data_fine']) .' ' . traduci_mese_in_lettere(date('m', $settimana['data_fine']))
            ];

			$mat_eventi = estrai_elenco_eventi_firme($settimana['data_inizio'], $settimana['data_fine']);
		}

		$mat_calendario = DrawGrid_festivita($anno_scolastico, 'caledario_festivita', 'scolastico');

		foreach($lista_firme as $firma)	{
			$griglia_firme[$firma['id_professore']]['nome'] = $firma['cognome'].' '.$firma['nome'];
			$griglia_firme[$firma['id_professore']]['calendario'][date('m', $firma['data'])][date('d', $firma['data'])][$firma['data']] = $firma;
			$griglia_firme[$firma['id_professore']]['settimane'][date('W', $firma['data'])][date('w', $firma['data'])][$firma['data']] = $firma;
		}

        if($parametri_ricerca['tipo_visualizzazione'] != 'mensile'){
            foreach($griglia_firme as $id_professore => $professore){
				$griglia_firme[$id_professore]['orario'] = estrai_report_orario_docente($id_professore,$data_corrente,$mat_settimane);
			}
		}

		$mat_result = [
			'calendario'=> $mat_calendario,
			'eventi' 	=> $mat_eventi,
			'settimane' => $mat_settimane,
			'firme'		=> $griglia_firme
		];

		return $mat_result;
	}else{
		return $lista_firme;
	}
	//}}} </editor-fold>
}

function verifica_presenza_prof($id_professore, $data_inizio, $data_fine, $dati_classe)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre il log docenti">
    $db = new \MT\Mastercom\Db;
    $db->name = 'logger';
    $db->setDatabaseParameters((array) $db);

    $res = $db->query("
        SELECT COUNT(id) AS count FROM activity
        WHERE app_name = 'registro-browser'
            AND event = 'R.UI.gotoMenu'
            AND datetime >= '" . date('c', $data_inizio) . "'
            AND datetime <= '" . date('c', $data_fine) . "'
            AND user_id = {$id_professore}
            AND context ILIKE '%[classe] => {$dati_classe['classe']}%'
            AND context ILIKE '%[sezione] => {$dati_classe['sezione']}%'
            AND context ILIKE '%[indirizzo] => {$dati_classe['codice']}%'
            AND context ILIKE '%[tipo] => select_abbinamento%'
    ");

    return $res['count'];
	//}}} </editor-fold>
}

function verifica_presenza_prof_firme($id_professore, $data_inizio, $data_fine, $id_classe)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre il log docenti">
	$query = "SELECT *,
                firme_appello_docenti.data_inserimento as data_inserimento_firma,
                firme_appello_docenti.chi_inserisce as chi_inserisce_firma
              FROM firme_appello_docenti
              INNER JOIN utenti ON (utenti.id_utente = firme_appello_docenti.id_professore)
              WHERE	data >= " . $data_inizio . "
                AND data <= " . $data_fine . "
				AND id_professore = ". intval($id_professore) . "
				AND id_classe = " . intval($id_classe) . "
			  ORDER BY data";

    $result = pgsql_query($query) or die ("Invalid $query");
	$numero = pg_num_rows($result);

	$dati_log = [];

	if($numero > 0)
	{
		for($cont = 0; $cont < $numero; $cont++)
		{
			$dati_log[$cont] = pg_fetch_assoc($result, $cont);
        }
	}

    return $dati_log;
    //}}} </editor-fold>
}

function estrai_tabellone_temporale_firme_classe_materia($id_classe, $id_materia, $data_inizio, $data_fine)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre firme classe materia per tabellonei">
	$query = "SELECT
                    firme_appello_docenti.*,
                    classi_complete.classe,
                    classi_complete.sezione,
                    classi_complete.codice_indirizzi,
                    classi_complete.descrizione_indirizzi,
                    materie.descrizione AS materia,
                    utenti.cognome,
                    utenti.nome
                FROM firme_appello_docenti
                JOIN utenti ON (utenti.id_utente = firme_appello_docenti.id_professore)
                JOIN classi_complete ON (classi_complete.id_classe = firme_appello_docenti.id_classe)
                JOIN materie ON (materie.id_materia = firme_appello_docenti.id_materia)
                WHERE classi_complete.id_classe = " . $id_classe . "
                    AND materie.id_materia = " . $id_materia . "
                    AND firme_appello_docenti.data >= " . intval($data_inizio) . "
                    AND firme_appello_docenti.data <= " . intval($data_fine) . "
                    AND firme_appello_docenti.flag_canc=0
                ORDER BY
                    utenti.cognome ASC,
                    utenti.nome ASC,
                    firme_appello_docenti.data ASC";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $firme = pg_fetch_assoc($result, $cont);

            foreach ($firme as $key => $value) {
                $firme[$key] = decode($value);
            }

            $dati[date('d/m/Y', $firme['data'])][] = $firme;
        }

        return $dati;
    } else {
        return null;
    }

    return $dati;
    //}}} </editor-fold>
}

function estrai_firma_professore($id_firma)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i dati della firma di un docente">
	if ($id_firma > 0) {
        $query = "SELECT * FROM firme_appello_docenti
				  WHERE	id_firma = " . $id_firma . "
                    AND	flag_canc = 0";

        $result = pgsql_query($query) or die("Invalid $query");
        $cont = pg_num_rows($result);

        if ($cont == 1) {
            $firma = pg_fetch_assoc($result, 0);

            foreach ($firma as $key => $value) {
                $firma[$key] = decode($value);
            }

            return $firma;
        } else {
            return "NO";
        }
    } else {
        return "KO";
    }
    //}}} </editor-fold>
}

/**
 * Estrae le firme dei docenti in base all'orario previsto
 *
 * @param array $filtro
 * @return array
 */
function estrai_firme(array $filtro = [])
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le firme">
    $where = empty($filtro) ? '' : "WHERE " . implode(" AND ", $filtro);
    $query = "SELECT * FROM statistiche.firme_docenti {$where}";

    $results = pgsql_query($query) or die("Invalid $query");
    $firme = [];

    if (pg_num_rows($results) > 0) {
        $firme = pg_fetch_all($results);
    }

    return $firme;
    //}}} </editor-fold>
}

function modifica_firma_professore($id_firma, $id_classe, $id_materia, $id_utente, $data, $current_user)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per inserire la firma di un professore">
	$query = "UPDATE firme_appello_docenti
                SET id_professore = ".$id_utente.",
                    id_classe     = ".$id_classe.",
                    id_materia    = ".$id_materia.",
                    data 		  = ".$data."
              WHERE id_firma = $id_firma";

    pgsql_query($query) or die ("Invalid $query");

    inserisci_log(["id_firma" => $id_firma], "firme_appello_docenti", $current_user, "INTERFACCIA", "MODIFICA");
    //}}} </editor-fold>
}

function elimina_firma_professore($id_firma,  $current_user)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per inserire la firma di un professore">
	$query = "UPDATE firme_appello_docenti SET flag_canc = " . time() . "
			  WHERE	id_firma = " . $id_firma;

    pgsql_query($query) or die ("Invalid $query");

    inserisci_log(["id_firma" => $id_firma], "firme_appello_docenti", $current_user, "INTERFACCIA", "ELIMINAZIONE");
    //}}} </editor-fold>
}

function cerca_professore($search)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="cerca i professori per classe, sezione, indirizzo, materia insegnata e cognome e nome, e li restituisce con le materie insegnate">
	$where = '';

    if(strlen($search) > 0)
	{
		/*{{{ */
		$mat_search = explode(' ', $search);

        foreach ($mat_search as $search_token) {
            if (is_numeric($search_token)) {
                $mat_search_nums[] = $search_token;
            } else {
                $mat_search_strings[] = $search_token;
            }
        }

        $mat_where = null;

        if(is_array($mat_search_nums))
		{
			$where .= " ( ";

			foreach($mat_search_nums as $search_token)
			{
				$mat_where[] = " ( classi_complete.classe = '". $search_token . "' ) ";
			}

            $where .= implode(' OR ',$mat_where);
			$where .= " ) AND ";
		}

		$mat_where = null;

		if(is_array($mat_search_strings))
		{
			$where .= " ( ";

			foreach($mat_search_strings as $search_token)
			{
				if (strlen($search_token) > 1) {
					$mat_where[] = " (
									classi_complete.sezione ILIKE '". $search_token . "%'
									OR materie.descrizione ILIKE '" . $search_token . "'
									OR utenti.cognome ILIKE '%" . $search_token . "%'
									OR utenti.nome ILIKE '%" . $search_token . "%'
									OR classi_complete.codice_indirizzi ILIKE '" . $search_token . "%'
									OR classi_complete.descrizione_indirizzi ILIKE '" . $search_token . "%'
								)";
				} elseif (strlen($search_token) > 0) {
					$mat_where[] = " (
									classi_complete.sezione ILIKE '". $search_token . "%'
									OR materie.descrizione ILIKE '" . $search_token . "'
									OR classi_complete.codice_indirizzi ILIKE '" . $search_token . "%'
									OR classi_complete.descrizione_indirizzi ILIKE '" . $search_token . "%'
								)";
				}
			}

			$where .= implode(' OR ',$mat_where);
			$where .= " ) AND ";
		}

		$query = "SELECT
						classi_prof_materie.id_professore,
						classi_complete.id_classe,
						classi_complete.id_indirizzo,
						classi_complete.classe,
						classi_complete.sezione,
						classi_complete.codice_indirizzi,
						classi_complete.descrizione_indirizzi,
						materie.descrizione AS materia,
						materie.id_materia,
						utenti.cognome,
						utenti.nome
					FROM classi_prof_materie
                    JOIN utenti ON (utenti.id_utente = classi_prof_materie.id_professore)
                    JOIN classi_complete ON (classi_complete.id_classe = classi_prof_materie.id_classe)
                    JOIN materie ON (materie.id_materia = classi_prof_materie.id_materia)
					WHERE
						$where utenti.flag_canc = 0
						AND materie.flag_canc = 0
						AND classi_prof_materie.flag_canc = 0
					ORDER BY
						utenti.cognome ASC,
						utenti.nome ASC,
						classi_complete.codice_indirizzi ASC,
						classi_complete.classe ASC,
						classi_complete.sezione ASC,
						materia ASC
                    ";

        $result = pgsql_query($query) or die ("Invalid $query");
		$numero = pg_num_rows($result);

		if($numero > 0)
		{
			for($cont = 0; $cont < $numero; $cont++)
			{
				$riga = pg_fetch_assoc($result, $cont);

                foreach($riga as $key => $value)
				{
					$riga[$key] = decode($value);
				}

                $risultato[$riga['id_professore']]['nome'] = $riga['cognome'].' '.$riga['nome'];
				$risultato[$riga['id_professore']]['classi_materie'][$riga['id_classe']][] = $riga;
			}
		}
		/*}}}*/
	}

    return $risultato;
    //}}} </editor-fold>
}

function controlla_presenza_professore_consiglio($id_classe, $periodo, $id_professore)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i dati della firma di un docente">
    if ($id_classe > 0 && $id_professore > 0) {
        $query = "SELECT * FROM presenze_consiglio
                  INNER JOIN utenti ON (presenze_consiglio.id_professore = utenti.id_utente)
                  WHERE id_professore = " . $id_professore . "
                        AND id_classe = " . $id_classe . "
                        AND periodo = '" . $periodo . "'
                        AND presenze_consiglio.flag_canc = 0
                        AND utenti.flag_canc = 0";

        $result = pgsql_query($query) or die("Invalid $query");
        $cont = pg_num_rows($result);

        if ($cont == 1) {
            $presenza = pg_fetch_assoc($result, 0);

            foreach ($presenza as $key => $value) {
                $presenza[$key] = decode($value);
            }
            return $presenza;
        } else {
            return $query;
        }
    } else {
        return "KO";
    }
    //}}} </editor-fold>
}

function sostituisci_docente_consiglio($id_classe, $periodo, $id_professore)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i dati dello stato presenza al consiglio di classe e l'eventuale sostituto">
    $sostituzione = [];

    $query = "SELECT p.id_professore,
                    dp.cognome as dp_cognome,
                    dp.nome as dp_nome,
                    p.id_classe,
                    p.periodo,
                    p.id_professore_sostituto,
                    ds.cognome ds_cognome,
                    ds.nome ds_nome
                FROM presenze_consiglio p
                INNER JOIN utenti dp ON p.id_professore = dp.id_utente AND dp.flag_canc = 0
                LEFT JOIN utenti ds ON p.id_professore_sostituto = ds.id_utente  AND ds.flag_canc = 0
                WHERE p.id_professore = {$id_professore}
                    AND id_classe = " . $id_classe . "
                    AND periodo = '" . $periodo . "'
                    AND p.flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");

    $cont = pg_num_rows($result);
    if ($cont == 1) {
        $presenza = pg_fetch_assoc($result, 0);

        switch ($presenza['id_professore_sostituto'])
        {
            case -1:
                $sostituzione['stato'] = 'A';
                break;
            case 0:
                $sostituzione['stato'] = 'P';
                break;
            default:
                $sostituzione['stato'] = 'S';
                $sostituzione['cognome'] = $presenza['ds_cognome'];
                $sostituzione['nome'] = $presenza['ds_nome'];
                break;
        }
    }
    return $sostituzione;
    //}}} </editor-fold>
}

function estrai_presenze_consiglio($id_classe, $periodo)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le presenze al consiglio di classe">
    $query = "SELECT p.id_professore,
                    dp.cognome as dp_cognome,
                    dp.nome as dp_nome,
                    p.id_classe,
                    p.periodo,
                    p.id_professore_sostituto,
                    ds.cognome ds_cognome,
                    ds.nome ds_nome
                FROM presenze_consiglio p
                INNER JOIN utenti dp ON p.id_professore = dp.id_utente AND dp.flag_canc = 0
                LEFT JOIN utenti ds ON p.id_professore_sostituto = ds.id_utente  AND ds.flag_canc = 0
                WHERE id_classe = " . $id_classe . "
                    AND periodo = '" . $periodo . "'
                    AND p.flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");

    return pg_fetch_all($result);
    //}}} </editor-fold>
}

function registra_presenza_professore_consiglio($id_classe, $periodo, $id_professore, $id_sostituto, $current_user)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i dati della firma di un docente">
    if ($id_classe > 0 && $id_professore > 0) {
        $query = "SELECT * FROM presenze_consiglio
                  INNER JOIN utenti ON (presenze_consiglio.id_professore = utenti.id_utente)
                  WHERE id_professore = " . $id_professore . "
                    AND id_classe = " . $id_classe . "
                    AND periodo = '" . $periodo . "'
                    AND presenze_consiglio.flag_canc = 0
                    AND utenti.flag_canc = 0";

        $result = pgsql_query($query) or die("Invalid $query");
        $cont = pg_num_rows($result);

        if ($cont > 0) {
            $presenza = pg_fetch_assoc($result, 0);
            $query = 'UPDATE presenze_consiglio SET id_professore_sostituto = ' . intval($id_sostituto) . '
                      WHERE id_professore = ' . $id_professore . '
                        AND id_classe = ' . $id_classe . '
                        AND periodo = \'' . $periodo . '\'
                        AND flag_canc = 0';

            $result = pgsql_query($query) or die("Invalid $query");

            $mat_oggetti = [
                "id_professore"           => $id_professore,
                "periodo"                 => $periodo,
                "id_classe"               => $id_classe,
                "id_professore_sostituto" => $id_sostituto
            ];
            inserisci_log($mat_oggetti, "presenze_consiglio", $current_user, "INTERFACCIA", "MODIFICA");

            return $presenza;
        } else {
            $query = 'INSERT INTO presenze_consiglio (
                            id_professore,
                            id_classe,
                            periodo,
                            id_professore_sostituto
                        ) VALUES (
                            ' . $id_professore . ',
                            ' . $id_classe . ',
                            \'' . $periodo . '\',
                            ' . $id_sostituto . '
                        )';

            $result = pgsql_query($query) or die("Invalid $query");

            $mat_oggetti = [
                "id_professore"           => $id_professore,
                "periodo"                 => $periodo,
                "id_classe"               => $id_classe,
                "id_professore_sostituto" => $id_sostituto
            ];
            inserisci_log($mat_oggetti, "presenze_consiglio", $current_user, "INTERFACCIA", "INSERIMENTO");
        }
    } else {
        return "KO";
    }
    //}}} </editor-fold>
}

/**
 * Funzione per estrarre tutti i docenti abbinati alle classi di un meccanografico
 * @param type $meccanografico
 * @return array
 */
function estrai_docenti_meccanografico($meccanografico)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i docenti abbinati alle classi di un meccanografico">
    $elenco_prof = [];

    $query = "SELECT id_utente,
                    cognome,
                    nome
                FROM utenti
                WHERE flag_canc = 0 AND
                    id_utente IN (
                        SELECT id_professore
                        FROM classi_prof_materie
                        WHERE flag_canc = 0 AND
                            id_classe IN (
                                SELECT id_classe
                                FROM classi_complete
                                WHERE codice_meccanografico = '{$meccanografico}'
                            )
                    ) ORDER BY cognome, nome";

    $result = pgsql_query($query) or die("Invalid $query");

    $elenco_prof = pg_fetch_all($result);

    return $elenco_prof;
    //}}} </editor-fold>
}

function estrai_firme_periodo($inizio, $fine, $id_classe = 0, $id_professore = 0, $ordinamento = '', $id_materia = 0)
{
	//{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i dati della firma di un docente">
    $elenco_firme = [];
    $where = '';

    $where .= $id_classe > 0 ? " AND id_classe = {$id_classe}" : "";
    $where .= $id_professore > 0 ? " AND id_professore = {$id_professore}" : "";
    $where .= $id_materia > 0 ? " AND f.id_materia = {$id_materia}" : "";

    switch ($ordinamento)
    {
        case '':
            $order = '';
            break;

        case 'data':
            $order = ' ORDER BY data';
            break;
    }

    $query = "SELECT f.*,
                    mf.descrizione as materia_firma,
                    mf.tipo_materia,
                    df.cognome || ' ' || df.nome as docente_firma,
                    f.data as data_firma,
                    f.data_inserimento as data_inserimento_firma
                FROM firme_appello_docenti f
                LEFT JOIN materie mf ON f.id_materia = mf.id_materia AND mf.flag_canc = 0
                LEFT JOIN utenti df ON f.id_professore = df.id_utente and df.flag_canc = 0
                WHERE f.data >= {$inizio}
                    AND f.data <= {$fine}
                    AND	f.flag_canc = 0
                    {$where}
                    {$order}
                ";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0)
    {
        $elenco_firme = pg_fetch_all($result);
    }

    return $elenco_firme;
    //}}} </editor-fold>
}

/**
 * Funzione che verifica se un docente è un docente di sostegno
 * @param type $id_professore
 * @return string
 */
function verifica_se_docente_sostegno($id_professore)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="verifica se professore è di sostegno">
	$query = "SELECT docente_sostegno
				FROM utenti
				WHERE id_utente = " . intval($id_professore) . "
					AND flag_canc = 0";
    $result = pgsql_query($query);
    $num_rows = pg_num_rows($result);
    if ($num_rows == 1) {
		$docente_sostegno = pg_fetch_result($result,0,'docente_sostegno');
		if ($docente_sostegno) {
			return 'SI';
		} else {
			return 'NO';
		}
	} else {
		return 'NON ESISTE';
	}
	//}}} </editor-fold>
}

/**
 * Funzione che verifica se uno studente è assegnato ad un certo professore per il sostegno
 * @param type $id_studente
 * @param type $id_professore
 * @return boolean
 */
function studente_assegnato_sostegno_docente($id_studente,$id_professore)
{
	//{{{ <editor-fold defaultstate="collapsed">
	$query = "select
					*
				from
					utenti,
					abbinamenti_studente_docente_sostegno
				where
					utenti.id_utente = ".intval($id_professore)."
					and
					utenti.id_utente = abbinamenti_studente_docente_sostegno.id_utente
					and
					abbinamenti_studente_docente_sostegno.id_studente = ".intval($id_studente)."
					and
					utenti.flag_canc=0
					and
					abbinamenti_studente_docente_sostegno.flag_canc=0";
    $result = pgsql_query($query);
    $num_rows = pg_num_rows($result);
    if ($num_rows > 0) {
		return true;
	} else {
		return false;
	}
	//}}} </editor-fold>
}

function verifica_studenti_sostegno_classe($id_professore,$id_classe)
{
	//{{{ <editor-fold defaultstate="collapsed">
	$risultato = [];
	$sostegno = verifica_se_docente_sostegno($id_professore);
	if ($sostegno == 'SI') 	{

		$query = "SELECT DISTINCT studenti_completi.id_studente
					FROM utenti
						INNER JOIN abbinamenti_studente_docente_sostegno
                            ON (utenti.id_utente = abbinamenti_studente_docente_sostegno.id_utente)
						INNER JOIN studenti_completi
                            ON (abbinamenti_studente_docente_sostegno.id_studente = studenti_completi.id_studente)
					WHERE utenti.id_utente = " . intval($id_professore) . "
						AND studenti_completi.id_classe = " . intval($id_classe) . "
						AND utenti.flag_canc = 0
						AND abbinamenti_studente_docente_sostegno.flag_canc = 0";
		$result = pgsql_query($query);
		$num_rows = pg_num_rows($result);
 		if ($num_rows > 0) {
			for ($cont = 0; $cont< $num_rows ; $cont++)	{
				$risultato[] = pg_fetch_result($result,$cont,'id_studente');
			}
		}
	}
	return $risultato;

	//}}} </editor-fold>
}


function inserisci_nomina_docente($id_docente, $anno_scolastico, $data_emissione, $data_inizio, $data_fine, $tipo, $ruolo, $materia, $classi, $ore_settimanali, $note, $current_user)
{
    //{{{ <editor-fold defaultstate="collapsed">
    $anno_scolastico = trim($anno_scolastico);

    if ($id_docente > 0)
    {
        if ($data_emissione == '')
        {
            $data_emissione = 0;
        }
        if ($data_inizio == '')
        {
            $data_inizio = 0;
        }
        if ($data_fine == '')
        {
            $data_fine = 0;
        }
        if ($ore_settimanali == '')
        {
            $ore_settimanali = 0;
        }

        $insert = "INSERT INTO nomine_docenti
                    (
                        id_docente,
                        anno_scolastico,
                        data_emissione,
                        data_inizio,
                        data_fine,
                        tipo,
                        ruolo,
                        materia,
                        classi,
                        ore_settimanali,
                        note,
                        chi_inserisce,
                        data_inserimento,
                        tipo_inserimento,
                        chi_modifica,
                        data_modifica,
                        tipo_modifica
                    )
                VALUES
                    (
                        " . $id_docente . ",
                        '" . encode($anno_scolastico) . "',
                        " . $data_emissione . ",
                        " . $data_inizio . ",
                        " . $data_fine . ",
                        '" . encode($tipo) . "',
                        '" . encode($ruolo) . "',
                        '" . encode($materia) . "',
                        '" . encode($classi) . "',
                        " . $ore_settimanali . ",
                        '" . encode($note) . "',
                        " . $current_user . ",
                        " . time() . ",
                        'INTERFACCIA',
                        " . $current_user . ",
                        " . time() . ",
                        'INTERFACCIA'
                    )
                RETURNING id_nomina
                    ";
        $result = pgsql_query($insert) or die("Invalid $insert");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $id_nomina = pg_fetch_result($result, 0, "id_nomina");
            return $id_nomina;
        }
        else
        {
            return false;
        }
    }
    else
    {
        return false;
    }
    //}}} </editor-fold>
}

function modifica_nomina_docente($id_nomina, $anno_scolastico, $data_emissione, $data_inizio, $data_fine, $tipo, $ruolo, $materia, $classi, $ore_settimanali, $note, $current_user)
{
    //{{{ <editor-fold defaultstate="collapsed">
    if ($id_nomina > 0)
    {
        if ($data_emissione == '')
        {
            $data_emissione = 0;
        }
        if ($data_inizio == '')
        {
            $data_inizio = 0;
        }
        if ($data_fine == '')
        {
            $data_fine = 0;
        }
        if ($ore_settimanali == '')
        {
            $ore_settimanali = 0;
        }

        $update = "UPDATE nomine_docenti
                    SET anno_scolastico = '" . encode($anno_scolastico) . "',
                        data_emissione = " . $data_emissione . ",
                        data_inizio = " . $data_inizio . ",
                        data_fine = " . $data_fine . ",
                        tipo = '" . encode($tipo) . "',
                        ruolo = '" . encode($ruolo) . "',
                        materia = '" . encode($materia) . "',
                        classi = '" . encode($classi) . "',
                        ore_settimanali = " . $ore_settimanali . ",
                        note = '" . encode($note) . "',
                        chi_modifica = " . $current_user . ",
                        data_modifica = " . time() . ",
                        tipo_modifica = 'INTERFACCIA'
                    WHERE id_nomina = " . $id_nomina;

        pgsql_query($update) or die("Invalid $update");
        return true;
    }
    else
    {
        return false;
    }
    //}}} </editor-fold>
}

function estrai_nomine_docente($id_docente, $filtri = [])
{
    //{{{ <editor-fold defaultstate="collapsed">
    $nomine = [];
    $filtro_sql = "";

    if (!empty($filtri))
    {
        foreach ($filtri as $filtro)
        {
            $filtro_sql .= " AND {$filtro}";
        }
    }

    if ($id_docente > 0)
    {
        $query = "SELECT *
                FROM nomine_docenti
                WHERE flag_canc = 0
                    AND id_docente = $id_docente
                    {$filtro_sql}
                ORDER BY anno_scolastico, data_emissione, data_inizio, data_fine
                ";
        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $nomine = pg_fetch_all($result);
            foreach ($nomine as $key => $nomina)
            {
                $nomine[$key]['anno_scolastico'] = decode($nomina['anno_scolastico']);
                $nomine[$key]['tipo'] = decode($nomina['tipo']);
                $nomine[$key]['ruolo'] = decode($nomina['ruolo']);
                $nomine[$key]['materia'] = decode($nomina['materia']);
                $nomine[$key]['classi'] = decode($nomina['classi']);
                $nomine[$key]['note'] = decode($nomina['note']);
                $nomine[$key]['data_emissione_tradotta'] = date("d/m/Y", $nomina['data_emissione']);
                $nomine[$key]['data_inizio_tradotta'] = date("d/m/Y", $nomina['data_inizio']);
                $nomine[$key]['data_fine_tradotta'] = date("d/m/Y", $nomina['data_fine']);
                $nomine[$key]['data_emissione_tradotta_iso'] = date("Y-m-d", $nomina['data_emissione']);
                $nomine[$key]['data_inizio_tradotta_iso'] = date("Y-m-d", $nomina['data_inizio']);
                $nomine[$key]['data_fine_tradotta_iso'] = date("Y-m-d", $nomina['data_fine']);
            }
        }
    }

    return $nomine;
    //}}} </editor-fold>
}

function estrai_nomina_docente($id_nomina)
{
    //{{{ <editor-fold defaultstate="collapsed">
    $nomina = [];

    if ($id_nomina > 0)
    {
        $query = "SELECT *
                    FROM nomine_docenti
                    WHERE flag_canc = 0
                        AND id_nomina = $id_nomina
                ";
        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $nomina = pg_fetch_all($result)[0];

            $nomina['anno_scolastico'] = decode($nomina['anno_scolastico']);
            $nomina['tipo'] = decode($nomina['tipo']);
            $nomina['ruolo'] = decode($nomina['ruolo']);
            $nomina['materia'] = decode($nomina['materia']);
            $nomina['classi'] = decode($nomina['classi']);
            $nomina['note'] = decode($nomina['note']);
            $nomina['data_emissione_tradotta'] = date("d/m/Y", $nomina['data_emissione']);
            $nomina['data_inizio_tradotta'] = date("d/m/Y", $nomina['data_inizio']);
            $nomina['data_fine_tradotta'] = date("d/m/Y", $nomina['data_fine']);
            $nomina['data_emissione_tradotta_iso'] = date("Y-m-d", $nomina['data_emissione']);
            $nomina['data_inizio_tradotta_iso'] = date("Y-m-d", $nomina['data_inizio']);
            $nomina['data_fine_tradotta_iso'] = date("Y-m-d", $nomina['data_fine']);
        }
    }

    return $nomina;
    //}}} </editor-fold>
}

function elimina_nomina($id_nomina, $current_user)
{
    //{{{ <editor-fold defaultstate="collapsed">
    if ($id_nomina > 0)
    {
        $delete = "UPDATE nomine_docenti
                    SET flag_canc = " . time() . ",
                        chi_modifica = " . $current_user . ",
                        data_modifica = " . time() . "
                    WHERE id_nomina = " . $id_nomina;
        pgsql_query($delete) or die("Invalid $delete");
    }
    //}}} </editor-fold>
}

function estrai_assenze_docente($id_docente, $filtri = [])
{
    //{{{ <editor-fold defaultstate="collapsed">
    $assenze = [];
    $filtro_sql = "";

    if (!empty($filtri))
    {
        foreach ($filtri as $filtro)
        {
            $filtro_sql .= " AND {$filtro}";
        }
    }

    if ($id_docente > 0)
    {
        $query = "  SELECT *
                    FROM assenze_docenti
                    WHERE flag_canc = 0
                        AND id_docente = $id_docente
                        {$filtro_sql}
                    ORDER BY anno_scolastico, data_inizio, data_fine
                ";
        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $assenze = pg_fetch_all($result);
            foreach ($assenze as $key => $assenza)
            {
                $assenze[$key]['anno_scolastico'] = decode($assenza['anno_scolastico']);
                $assenze[$key]['motivo'] = decode($assenza['motivo']);
                $assenze[$key]['data_inizio_tradotta'] = date("d/m/Y", $assenza['data_inizio']);
                $assenze[$key]['data_fine_tradotta'] = date("d/m/Y", $assenza['data_fine']);
                $assenze[$key]['data_inizio_tradotta_iso'] = date("Y-m-d", $assenza['data_inizio']);
                $assenze[$key]['data_fine_tradotta_iso'] = date("Y-m-d", $assenza['data_fine']);
            }
        }
    }

    return $assenze;
    //}}} </editor-fold>
}

function estrai_assenze_nomina($id_nomina)
{
    //{{{ <editor-fold defaultstate="collapsed">
    $nomina = estrai_nomina_docente($id_nomina);
    $assenze = [];
    $filtro_date = '';
    $filtro_anno_scolastico = '';

    if ($nomina['data_inizio'] > 0)
    {
        $filtro_date .= ' AND data_inizio >= ' . $nomina['data_inizio'];
    }
    if ($nomina['data_fine'] > 0)
    {
        $filtro_date .= ' AND data_fine <= ' . $nomina['data_fine'];
    }
    if ($nomina['anno_scolastico'] > 0)
    {
        $filtro_anno_scolastico .= " AND anno_scolastico = '" . $nomina['anno_scolastico'] . "'";
    }

    if (!empty($nomina))
    {
        $query = "  SELECT *
                    FROM assenze_docenti
                    WHERE flag_canc = 0
                        AND id_docente = " . $nomina['id_docente'] . "
                        " . $filtro_anno_scolastico . "
                        " . $filtro_date . "
                    ORDER BY anno_scolastico, data_inizio, data_fine
                ";
        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $assenze = pg_fetch_all($result);
            foreach ($assenze as $key => $assenza)
            {
                $assenze[$key]['anno_scolastico'] = decode($assenza['anno_scolastico']);
                $assenze[$key]['motivo'] = decode($assenza['motivo']);
                $assenze[$key]['data_inizio_tradotta'] = date("d/m/Y", $assenza['data_inizio']);
                $assenze[$key]['data_fine_tradotta'] = date("d/m/Y", $assenza['data_fine']);
                $assenze[$key]['data_inizio_tradotta_iso'] = date("Y-m-d", $assenza['data_inizio']);
                $assenze[$key]['data_fine_tradotta_iso'] = date("Y-m-d", $assenza['data_fine']);
            }
        }
    }

    return $assenze;
    //}}} </editor-fold>
}

function inserisci_assenza_docente($id_docente, $data_inizio, $data_fine, $motivo, $anno_scolastico, $current_user)
{
    //{{{ <editor-fold defaultstate="collapsed">
    $anno_scolastico = trim($anno_scolastico);
    if ($id_docente > 0)
    {
        if ($data_inizio == '')
        {
            $data_inizio = 0;
        }
        if ($data_fine == '')
        {
            $data_fine = 0;
        }

        $insert = "INSERT INTO assenze_docenti
                    (
                        id_docente,
                        anno_scolastico,
                        data_inizio,
                        data_fine,
                        motivo,
                        chi_inserisce,
                        data_inserimento,
                        tipo_inserimento,
                        chi_modifica,
                        data_modifica,
                        tipo_modifica
                    )
                VALUES
                    (
                        " . $id_docente . ",
                        '" . encode($anno_scolastico) . "',
                        " . $data_inizio . ",
                        " . $data_fine . ",
                        '" . encode($motivo) . "',
                        " . $current_user . ",
                        " . time() . ",
                        'INTERFACCIA',
                        " . $current_user . ",
                        " . time() . ",
                        'INTERFACCIA'
                    )
                RETURNING id_assenza
                    ";
        $result = pgsql_query($insert) or die("Invalid $insert");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $id_assenza = pg_fetch_result($result, 0, "id_assenza");
            return $id_assenza;
        }
        else
        {
            return false;
        }
    }
    else
    {
        return false;
    }
    //}}} </editor-fold>
}

function modifica_assenza_docente($id_assenza, $data_inizio, $data_fine, $motivo, $anno_scolastico, $current_user)
{
    //{{{ <editor-fold defaultstate="collapsed">
    if ($id_assenza > 0)
    {
        if ($data_inizio == '')
        {
            $data_inizio = 0;
        }
        if ($data_fine == '')
        {
            $data_fine = 0;
        }

        $update = "UPDATE assenze_docenti
                    SET anno_scolastico = '" . encode($anno_scolastico) . "',
                        data_inizio = " . $data_inizio . ",
                        data_fine = " . $data_fine . ",
                        motivo = '" . encode($motivo) . "',
                        chi_modifica = " . $current_user . ",
                        data_modifica = " . time() . ",
                        tipo_modifica = 'INTERFACCIA'
                    WHERE id_assenza = " . $id_assenza;
        pgsql_query($update) or die("Invalid $update");
        return true;
    }
    else
    {
        return false;
    }
    //}}} </editor-fold>
}

function elimina_assenza_docente($id_assenza, $current_user)
{
    //{{{ <editor-fold defaultstate="collapsed">
    if ($id_assenza > 0)
    {
        $delete = "UPDATE assenze_docenti
                    SET flag_canc = " . time() . ",
                        chi_modifica = " . $current_user . ",
                        data_modifica = " . time() . "
                    WHERE id_assenza = " . $id_assenza;
        pgsql_query($delete) or die("Invalid $delete");
    }
    //}}} </editor-fold>
}

function estrai_professori_indirizzi($array_id_indirizzi = [], $esclusioni = '')
{
    $where_indirizzi = "";
    if (!empty($array_id_indirizzi)){
        $where_indirizzi .= " AND cpm.id_professore IN (SELECT id_professore
                                                        FROM classi_prof_materie
                                                        WHERE classi_prof_materie.id_classe IN (SELECT id_classe
                                                                                                    FROM classi
                                                                                                    WHERE flag_canc = 0
                                                                                                        AND id_indirizzo IN (" . implode(', ', $array_id_indirizzi) . ")
                                                                                                    )
                                                        )";
    }

    $query = "
        SELECT id_utente as id_professore,
                cognome,
                nome,
                id_indirizzo,
                descrizione_indirizzi,
                id_codice_ministeriale
        FROM utenti, classi_prof_materie as cpm, classi_complete
        WHERE utenti.flag_canc = 0
            AND cpm.flag_canc = 0
            AND classi_complete.id_classe = cpm.id_classe
            AND cpm.id_professore = utenti.id_utente
            " . $where_indirizzi . "
        ORDER BY cognome, nome";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    $elenco_docenti = [];
    if ($numero > 0){
        $elenco_tmp = pg_fetch_all($result);

        foreach ($elenco_tmp as $professore){
            $elenco_docenti[$professore['id_professore']]['id_professore'] = $professore['id_professore'];
            $elenco_docenti[$professore['id_professore']]['nome'] = decode($professore['nome']);
            $elenco_docenti[$professore['id_professore']]['cognome'] = decode($professore['cognome']);
            if ($esclusioni == '' || ($esclusioni == 'fake' && $professore['id_codice_ministeriale'] !== '94')){
                $elenco_docenti[$professore['id_professore']]['indirizzi'][decode($professore['descrizione_indirizzi']) . '_' . $professore['id_indirizzo']]['id_indirizzo'] = $professore['id_indirizzo'];
                $elenco_docenti[$professore['id_professore']]['indirizzi'][decode($professore['descrizione_indirizzi']) . '_' . $professore['id_indirizzo']]['descrizione_indirizzi'] = decode($professore['descrizione_indirizzi']);
                $elenco_docenti[$professore['id_professore']]['indirizzi'][decode($professore['descrizione_indirizzi']) . '_' . $professore['id_indirizzo']]['id_codice_ministeriale'] = $professore['id_codice_ministeriale'];
                ksort($elenco_docenti[$professore['id_professore']]['indirizzi']);
            }
        }
    }

    return $elenco_docenti;
}

function estrai_firme_riepilogo_dirigente($data_inizio, $data_fine, $tipo = 0, $id_indirizzo = 0, $id_classe = 0, $id_docente = 0)
{
    $sql = "
        SELECT o.id_orario_classe,
            o.id_classe,
            cc.id_indirizzo,
            o.id_materia as id_materia_prevista,
            o.id_professore as id_professore_previsto,
            (SELECT classe||'ª '||sezione||' '||codice_indirizzi FROM classi_complete WHERE id_classe = o.id_classe) as classe,
            (SELECT descrizione FROM materie WHERE id_materia = o.id_materia) as materia_prevista,
            (SELECT cognome||' '||nome FROM utenti WHERE id_utente = o.id_professore) as docente_previsto,
            o.data_inizio,
            o.data_fine,
            to_timestamp(o.data_inizio) as inizio,
            to_timestamp(o.data_fine) as fine,
            to_char(to_timestamp(o.data_inizio), 'FMDD TMMon YYYY'::text)::character varying AS data,
            to_char(to_timestamp(o.data_inizio), 'FMHH24:MI'::text)::character varying ||' ➜ '|| to_char(to_timestamp(o.data_fine), 'FMHH24:MI'::text)::character varying AS orario,
            f.id_firma,
            f.id_materia as id_materia_firma,
            f.id_professore as id_professore_firma,
            (SELECT descrizione FROM materie WHERE id_materia = f.id_materia) as materia_firma,
            (SELECT cognome||' '||nome FROM utenti WHERE id_utente = f.id_professore) as docente_firma,
            to_char(to_timestamp(f.data), 'FMHH24:MI'::text)::character varying AS ora_firma,
            f.data_inserimento
        FROM orario_classi o
        LEFT JOIN classi_complete cc ON cc.id_classe = o.id_classe
        LEFT JOIN firme_appello_docenti f ON f.id_classe = o.id_classe AND f.data BETWEEN o.data_inizio AND o.data_fine AND f.flag_canc = 0
        WHERE o.data_inizio BETWEEN {$data_inizio} AND {$data_fine}
            AND o.flag_canc = 0
            AND to_timestamp(o.data_inizio)::date IN (SELECT to_timestamp(data)::date FROM festivita_scuola WHERE flag_canc = 0 AND tipo_giornata = 'A')
            --AND (date_part('doy'::text, to_timestamp(o.data_inizio::double precision)) IN ( SELECT date_part('doy'::text, to_timestamp(festivita_scuola.data::double precision)) AS date_part
            --    FROM festivita_scuola
            --    WHERE festivita_scuola.flag_canc = 0 AND festivita_scuola.tipo_giornata::text = 'A'::text))
    ";

    if ($id_indirizzo > 0) {
        $sql .= " AND o.id_classe IN (SELECT id_classe FROM classi_complete WHERE id_indirizzo = {$id_indirizzo}) ";
    }

    if ($id_classe > 0) {
        $sql .= " AND o.id_classe = {$id_classe} ";
    }

    switch ($tipo) {
        case 1:
            // Da firmare
            $sql .= " AND f.id_firma IS NULL ";

            if (date('Y-m-d', $data_fine) === date('Y-m-d')) {
                $sql .= " AND o.data_fine <= " . $data_fine . " ";
            }

            break;
        case 2:
            // Sostituzione
            $sql .= " AND f.id_firma IS NOT NULL "
                . " AND o.id_professore <> f.id_professore ";
            break;
        case 3:
            // Materia diversa
            $sql .= " AND f.id_firma IS NOT NULL "
                . " AND o.id_professore = f.id_professore "
                . " AND o.id_materia <> f.id_materia ";
            break;
        case 4:
            // Firmate
            $sql .= " AND f.id_firma IS NOT NULL ";
            break;
        default:
            // Tutte
            break;
    }

    if ($id_docente > 0) {
        $sql .= " AND f.id_professore = {$id_docente} ";
    }

    $sql .= " ORDER BY data_inizio, classe ";

    $result = pgsql_query($sql) or die("Invalid $sql");
    $numero = pg_num_rows($result);

    $elenco_firme = [];
    if ($numero > 0) {
        $elenco_firme = pg_fetch_all($result);

        $elenco_tmp = pg_fetch_all($result);
        // $elenco_tmp2 = [];
        //
        // pulisco le ore con piu' materie e piu' firme
        foreach ($elenco_tmp as $key => $value) {
            $arr_tmp = $value;
            $arr_tmp['materia_prevista'] = decode($value['materia_prevista']);
            $arr_tmp['docente_previsto'] = decode($value['docente_previsto']);
            $arr_tmp['materia_firma'] = decode($value['materia_firma']);
            $arr_tmp['docente_firma'] = decode($value['docente_firma']);

            $elenco_tmp2[$value['id_orario_classe']][] = $arr_tmp;
            $elenco_firme[$key] = $arr_tmp;
        }
        //
        // $ore_multi_materia_firme = array_filter($elenco_tmp2, function($array){
        //     if (count($array) > 1){
        //         return $array;
        //     }
        // });
        //
        // if (count($ore_multi_materia_firme) > 0){
        //     foreach ($ore_multi_materia_firme as $key => $ore) {
        //         foreach ($ore as $key2 => $ora){
        //             if ($ora['id_professore_previsto'] == $ora['id_professore_firma']){
        //                 foreach ($ore as $key3 => $ora2) {
        //                     if (!($ora['id_professore_firma'] == $ora2['id_professore_firma'] && $ora2['id_materia_firma'] == $ora['id_materia_firma'])) {
        //                         unset($ore_multi_materia_firme[$key][$key2][$key3]);
        //                         //$elenco_tmp2[[$key]] = $ore_multi_materia_firme[$key];
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // }
        //$elenco_firme = $elenco_tmp2;
        //return $ore_multi_materia_firme;
    }

    return $elenco_firme;
}

function aggiorna_credenziali_sidi_professore($utente, $password, $id_utente)
{
    $sql = "UPDATE utenti
            SET utente_sidi = '{$utente}', password_sidi = '{$password}'
            WHERE id_utente = {$id_utente}";
    $result = pgsql_query($sql) or die("Invalid $sql");

    return true;
}

function estrai_credenziali_sidi_professore($id_utente)
{
    $sql = "SELECT utente_sidi, password_sidi
            FROM utenti
            WHERE id_utente = {$id_utente}";
    $result = pgsql_query($sql) or die("Invalid $sql");

    $credenziali = pg_fetch_all($result)[0];

    return $credenziali;
}
