<script type="text/javascript">
    var materieDocenteClassi = JSON.parse('{$json_materie_docente_classi}');
    var lezioni_future = JSON.parse('{$lezioni_future_json}');
    var abb_mat_arg_nav = {};
    var elenco_arg_util_nav = {};
    var pr_didattiche = {};
    var abb_pr_didattiche = {};
    try {
        abb_mat_arg_nav = JSON.parse(jsonEscape('{$abb_mat_arg_nav_json}'));
        elenco_arg_util_nav = JSON.parse(jsonEscape('{$elenco_arg_util_nav_json}'));
    } catch (e) { }

    try {
        pr_didattiche = JSON.parse(jsonEscape('{$programmazioni_didattiche_json}'));
        abb_pr_didattiche = JSON.parse(jsonEscape('{$collegamenti_arg_mod_progr_json}'));
    } catch (e) { }

    {literal}
    $('#modal-open').on("change", function (e)
    {
        var body = $('#body')[0];

        if (this.value === 'SI')
        {
            body.style.overflow='hidden';
            $('#btn_menu_utente').css("pointer-events", "none");
            $('#btn_giorno_precedente').attr("disabled", true);
            $('#btn_giorno_successivo').attr("disabled", true);
            $('#btn_calendario').attr("disabled", true);
            $('#btn_giorno').attr("disabled", true);
            $('#btn_settimana').attr("disabled", true);
            $('#btn_mese').attr("disabled", true);
            $('#elenco_giorni').css('overflow','hidden');
        }
        else
        {
            body.style.overflow='visible';
            $('#btn_menu_utente').css("pointer-events", "auto");
            $('#btn_giorno_precedente').attr("disabled", false);
            $('#btn_giorno_successivo').attr("disabled", false);
            $('#btn_calendario').attr("disabled", false);
            $('#btn_giorno').attr("disabled", false);
            $('#btn_settimana').attr("disabled", false);
            $('#btn_mese').attr("disabled", false);
            $('#elenco_giorni').css('overflow','auto');
        }
    });

    function chiudiSfondoOscurato()
    {
        $('#sfondo_oscurato').hide();
        $('#nuova_nota_disciplinare').hide();
        $('#modifica_argomento_compito').hide();
        $('#cambio_materia_orario_docente').hide();
        $('.form_inserimento_compito_argomento').hide();
        $('#modal-open').val('NO').trigger('change');

        $('#id_materia_argomento_ministeriale').val(-1);
        $('#id_classe_argomento_ministeriale').val('');
        caricaProgrammazioniNavali(-1, -1, 'modifica');

        $('#id_materia_programmazione_didattica').val(-1);
        $('#id_classe_programmazione_didattica').val('');
        caricaProgrammazioniPrDidattica(-1, -1, 'modifica');
    }

    function carica_studenti(){
        var current_user = document.getElementById('current_user').value;
		var current_key = document.getElementById('current_key').value;
		var db_key = document.getElementById('db_key').value;
		var tipo_utente = document.getElementById('tipo_utente').value;

		var query = {
				current_user:  current_user,
				current_key: current_key,
				db_key: db_key,
				form_tipo_utente: tipo_utente,
				form_azione: 'jqx_carica_elenco_studenti'
			};
		$.ajax({ type: "POST",
             url: "ajax_professore.php",
             data: query,//no need to call JSON.stringify etc... jQ does this for you
             cache: false,
             success: function(response)
             {//check response: it's always good to check server output when developing...
             },
			 complete: function(response) {
				if (response.responseJSON) {
					var risultato = response.responseJSON;
					var sorgente_studenti =
					{
						datatype: "array",
						datafields: [
							{ name: 'id_studente' },
							{ name: 'nome' },
						],
						localdata: risultato,
					};
					var interfaccia_studenti = new $.jqx.dataAdapter(sorgente_studenti, {
						loadComplete: function (data) { },
						loadError: function (xhr, status, error) { }
					});

					$("#elencoStudenti").jqxDropDownList({ source: interfaccia_studenti, displayMember: "nome", valueMember: "id_studente", theme: 'Bootstrap', checkboxes: true, filterable: true, filterPlaceHolder: "Cerca...", width: 250, placeHolder: "Seleziona studenti:", searchMode: 'containsignorecase'  });
                    $("#elencoStudenti").on('checkChange', function (event) {
					if (event.args) {
						var items = $("#elencoStudenti").jqxDropDownList('getCheckedItems');
                        var ids = new Array();
                        for (i in items)
                        {
                            ids.push(items[i].value);
                        }

                        var testo_hidden = ids.join();
                        $("#id_studenti_nota").val(testo_hidden);
					}
				});

				} else { console.log(response.responseText); }
			 }
    	});
    }

    function settaDataOraNuovoCompito(ts_data, id_data_compito, id_ora_compito) {
        if (ts_data > 0)
        {
            //setto la data
            ts_data = parseInt(ts_data)*1000;
            let date = new Date(ts_data);

            let mese = date.getMonth()+1;
            let giorno = date.getDate();
            let ora = date.getHours();
            let minuti = date.getMinutes();

            let data_valida = date.getFullYear() + '-' + fillZero(mese) + '-' + fillZero(giorno);
            $('#'+id_data_compito).val(data_valida);

            let ora_valida = fillZero(ora)+':'+fillZero(minuti);
            $('#'+id_ora_compito+'_input').val(ora_valida);

            $('#'+id_data_compito).hide();
            $('#'+id_ora_compito).hide();
        }
        else
        {
            $('#'+id_data_compito).val('');
            $('#'+id_ora_compito+'_input').val('08:00');
            $('#'+id_data_compito).show();
            $('#'+id_ora_compito).show();
        }
    }

    function apriModificaArgomentoCompito(idArgomento, descrizioneMateria, fascia)
    {
        var current_user = document.getElementById('current_user').value;
		var current_key = document.getElementById('current_key').value;
		var db_key = document.getElementById('db_key').value;
		var tipo_utente = document.getElementById('tipo_utente').value;

		var query = {
				current_user:  current_user,
				current_key: current_key,
				db_key: db_key,
				form_tipo_utente: tipo_utente,
				id_argomento: idArgomento,
				form_azione: 'estrai_argomento'
			};
		$.ajax({ type: "POST",
             url: "ajax_professore.php",
             data: query,//no need to call JSON.stringify etc... jQ does this for you
             cache: false,
             success: function(response)
             {//check response: it's always good to check server output when developing...
             },
			 complete: function(response) {
                if (typeof response.responseJSON !== 'undefined')
                {
                    let argomento = response.responseJSON;
                    $('#id_argomento_selezionato').val(idArgomento);
                    $('#modifica_argomento_compito_materia').html(descrizioneMateria);

                    let idClasse = argomento['id_classe'];
                    let idMateria = argomento['id_materia'];

                    if (argomento['tipo'] === 'argomento'){
                        $('#modifica_argomento_tabella').show();
                        $('#modifica_compito_tabella').hide();
                    } else {
                        $('#modifica_argomento_tabella').hide();
                        $('#modifica_compito_tabella').show();

                        let cont = 1;
                        let oneSelected = false;
                        let html = '';

                        html += '<option value="0">{/literal}{mastercom_label}Data libera{/mastercom_label}{literal}</option>';
                        if (lezioni_future != null){
                            if (typeof lezioni_future[idClasse] !== 'undefined'){
                                if (typeof lezioni_future[idClasse][idMateria] !== 'undefined'){
                                    for (var key in lezioni_future[idClasse][idMateria])
                                    {
                                        let lezione = lezioni_future[idClasse][idMateria][key];
                                        html += '<option value="'+lezione['data_inizio']+'" ';
                                        if (parseInt(lezione['data_inizio']) === parseInt(argomento['data'])){
                                            html += ' selected ';
                                            oneSelected = true;
                                        }
                                        html += '>'+lezione['data_tradotta']+'</option>';
                                        if (cont === 10){
                                            break;
                                        }
                                        cont++;
                                    };
                                }
                            }
                        }

                        $('#modifica_compito_select_data').html(html);

                        $('#modifica_compito_select_data option:selected').change();

                        if (!oneSelected){
                            let dataCompito = new Date(argomento['data']*1000);
                            let dataTmp = dataCompito.getFullYear()+'-'+fillZero(dataCompito.getMonth()+1)+'-'+fillZero(dataCompito.getDate());
                            let oraTmp = fillZero(dataCompito.getHours())+':'+fillZero(dataCompito.getMinutes());
                            $('#modifica_compito_select_data option:first').attr('selected','selected');

                            $('#modifica_compito_data').val(dataTmp);
                            $('#modifica_compito_ora_input').val(oraTmp);
                        }
                    }

                    $('#modifica_argomento_fascia').html(fascia);
                    $('#modifica_argomento_modulo').val(argomento['modulo']);
                    $('#modifica_argomento_testo').val(argomento['descrizione']);
                    $('#modifica_argomento_note_riservate').val(argomento['note']);

                    $('#modifica_compito_assegnazioni').val(argomento['assegnazioni']);
                    $('#modifica_compito_note_riservate').val(argomento['note']);

                    //temporaneo fino a che' ci sara' un solo tag per argomento
                    if (argomento['tipo'] === 'argomento' && argomento.tag.length > 0){
                        var tag = argomento.tag[0];
                        $("#modifica_argomento_tag").val(tag.id_tag);
                    } else {
                        $("#modifica_argomento_tag").val("");
                    }

                    $('#id_materia_argomento_ministeriale').val(idMateria);
                    $('#id_classe_argomento_ministeriale').val(idClasse);
                    caricaProgrammazioniNavali(idClasse, idMateria, 'modifica');

                    $('#id_materia_programmazione_didattica').val(idMateria);
                    $('#id_classe_programmazione_didattica').val(idClasse);
                    caricaProgrammazioniPrDidattica(idClasse, idMateria, 'modifica');

                    if (argomento['id_argomento_ministeriale'] > 0){
                        var prm = $('#navali_programmazione_inserisci_arg_modifica');
                        var modm = $('#navali_modulo_inserisci_arg_modifica');
                        var argm = $('#navali_argomento_inserisci_arg_modifica');

                        prm.val(elenco_arg_util_nav[idClasse][idMateria][argomento['id_programmazione_ministeriale']][argomento['id_modulo_ministeriale']][argomento['id_argomento_ministeriale']]['idProgrammazSidi']).trigger('change');
                        modm.val(elenco_arg_util_nav[idClasse][idMateria][argomento['id_programmazione_ministeriale']][argomento['id_modulo_ministeriale']][argomento['id_argomento_ministeriale']]['idModulo']).trigger('change');
                        argm.val(elenco_arg_util_nav[idClasse][idMateria][argomento['id_programmazione_ministeriale']][argomento['id_modulo_ministeriale']][argomento['id_argomento_ministeriale']]['idArgomento']).trigger('change');
                    }

                    if (argomento['id_programmazione_argomenti'] > 0){
                        var prm = $('#prdidattica_programmazione_inserisci_arg_modifica');
                        var modm = $('#prdidattica_modulo_inserisci_arg_modifica');
                        var argm = $('#prdidattica_argomento_inserisci_arg_modifica');

                        prm.val(abb_pr_didattiche[argomento.id_programmazione_argomenti]['id_programmazione']).trigger('change');
                        modm.val(abb_pr_didattiche[argomento.id_programmazione_argomenti]['id_programmazione_moduli']).trigger('change');
                        argm.val(argomento.id_programmazione_argomenti).trigger('change');
                    }

                    apriChiudiPopup('modifica_argomento_compito', 'sfondo_oscurato', true);
                }
            }
        });
    }

    function verificaDataCompitoFutura(idData, idOra)
    {
        let data = $('#'+idData).val();
        let ora = $('#'+idOra).val();

        let timeAttuale = new Date();
        let dataAttuale = timeAttuale.getFullYear()+'-'+fillZero(timeAttuale.getMonth()+1)+'-'+fillZero(timeAttuale.getDate());
        let oraAttuale = fillZero(timeAttuale.getHours())+':'+fillZero(timeAttuale.getMinutes());

        if (data !== '' && ora !== ''){
            if (data > dataAttuale){
                return true;
            } else if (data === dataAttuale && ora > oraAttuale){
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    function fillZero(num)
    {
        num = parseInt(num);
        if (num < 10){
            num = '0'+num;
        }
        return String(num);
    }

    function caricaProgrammazioniNavali(idClasse, idMateria, postfisso){
        var pr = $('#navali_programmazione_inserisci_arg_'+postfisso);
        var mod = $('#navali_modulo_inserisci_arg_'+postfisso);
        var arg = $('#navali_argomento_inserisci_arg_'+postfisso);
        $('#tabelle_navali_'+postfisso).html("");

        pr.find('option').remove();
        mod.find('option').remove();
        mod.attr("disabled", true);
        arg.find('option').remove();
        arg.attr("disabled", true);

        if (abb_mat_arg_nav != null && typeof abb_mat_arg_nav[idClasse] != 'undefined' && typeof abb_mat_arg_nav[idClasse][idMateria] != 'undefined'){
            pr.append(new Option("- Programmazione -", "-1"));
            Object.keys(abb_mat_arg_nav[idClasse][idMateria]['programmazioni']).forEach(function (index){
                pr.append(new Option(abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][index]['descrProgrammazione'], abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][index]['idProgrammazSidi']));
            });
            pr.removeAttr('disabled');
        } else {
            pr.attr("disabled", true);
        }
    }

    function caricaModuliNavali(idProgrammazione, postfisso){
        var idClasse = $('#id_classe_argomento_ministeriale').val();
        var idMateria = $('#id_materia_argomento_ministeriale').val();
        var mod = $('#navali_modulo_inserisci_arg_'+postfisso);
        var arg = $('#navali_argomento_inserisci_arg_'+postfisso);
        $('#tabelle_navali_'+postfisso).html("");

        mod.find('option').remove();
        arg.find('option').remove();
        arg.attr("disabled", true);

        if (typeof abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione] != 'undefined'){
            mod.append(new Option("- Modulo -", "-1"));
            Object.keys(abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli']).forEach(function (index){
                mod.append(new Option(abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][index]['desModulo'], abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][index]['idModulo']));
            });
            mod.removeAttr('disabled');
        } else {
            mod.attr("disabled", true);
        }
    }

    function caricaArgomentiNavali(idModulo, postfisso){
        var idClasse = $('#id_classe_argomento_ministeriale').val();
        var idMateria = $('#id_materia_argomento_ministeriale').val();
        var idProgrammazione = $('#navali_programmazione_inserisci_arg_'+postfisso).val();
        var arg = $('#navali_argomento_inserisci_arg_'+postfisso);
        var tabelle = $('#tabelle_navali_' + postfisso);

        arg.find('option').remove();

        if (typeof abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo] != 'undefined'){
            arg.append(new Option("- Argomento -", "-1"));
            Object.keys(abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['argomenti']).forEach(function (index){
                arg.append(new Option(abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['argomenti'][index]['desArgomento'], abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['argomenti'][index]['idArgomento']));
            });
            arg.removeAttr('disabled');

            var html = "";

            if (abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['criteri_verifica'] !== null && abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['criteri_verifica'].length > 0){
                html += "<b>Criteri di verifica</b>";
                html += "<ul>";
                abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['criteri_verifica'].forEach(function(item){
                    html += "<li>" + item.desVerifica + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['competenze'] !== null && abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['competenze'].length > 0){
                html += "<b>Competenze</b>";
                html += "<ul>";
                abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['competenze'].forEach(function(item){
                    html += "<li>" + item.desCompetenza + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['conoscenze'] !== null && abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['conoscenze'].length > 0){
                html += "<b>Conoscenze</b>";
                html += "<ul>";
                abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['conoscenze'].forEach(function(item){
                    html += "<li>" + item.desConoscenza + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['abilita'] !== null && abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['abilita'].length > 0){
                html += "<b>Abilità</b>";
                html += "<ul>";
                abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['abilita'].forEach(function(item){
                    html += "<li>" + item.desAbilita + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['metodologie'] !== null && abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['metodologie'].length > 0){
                html += "<b>Metodologie</b>";
                html += "<ul>";
                abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['metodologie'].forEach(function(item){
                    html += "<li>" + item.desMetodologia + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['mezzi'] !== null && abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['mezzi'].length > 0){
                html += "<b>Mezzi</b>";
                html += "<ul>";
                abb_mat_arg_nav[idClasse][idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['mezzi'].forEach(function(item){
                    html += "<li>" + item.desMezzo + "</li>";
                });
                html += "</ul>";
            }

            tabelle.html(html);
        } else {
            arg.attr("disabled", true);
            tabelle.html("");
        }
    }

    function caricaProgrammazioniPrDidattica(idClasse, idMateria, postfisso){
        var pr = $('#prdidattica_programmazione_inserisci_arg_'+postfisso);
        var mod = $('#prdidattica_modulo_inserisci_arg_'+postfisso);
        var arg = $('#prdidattica_argomento_inserisci_arg_'+postfisso);
        $('#tabelle_prdidattica_'+postfisso).html("");

         pr.find('option').remove();
         mod.find('option').remove();
         arg.find('option').remove();

        if (pr_didattiche != null && typeof pr_didattiche[idClasse] != 'undefined' && typeof pr_didattiche[idClasse][idMateria] != 'undefined'){
            pr.append(new Option("- Programmazione -", "-1"));
            Object.keys(pr_didattiche[idClasse][idMateria]).forEach(function (index){
                pr.append(new Option(pr_didattiche[idClasse][idMateria][index]['programmazione']['descrizione_breve'], pr_didattiche[idClasse][idMateria][index]['programmazione']['id_programmazione']));
            });
            pr.removeAttr('disabled');
        } else {
            pr.attr("disabled", true);
            mod.attr("disabled", true);
            arg.attr("disabled", true);
        }
    }

    function caricaModuliPrDidattica(idProgrammazione, postfisso){
        var idMateria = $('#id_materia_programmazione_didattica').val();
        var idClasse = $('#id_classe_programmazione_didattica').val();
        var mod = $('#prdidattica_modulo_inserisci_arg_'+postfisso);
        var arg = $('#prdidattica_argomento_inserisci_arg_'+postfisso);
        $('#tabelle_prdidattica_'+postfisso).html("");

        mod.find('option').remove();
        arg.find('option').remove();

        if (typeof pr_didattiche[idClasse][idMateria][idProgrammazione] != 'undefined' && typeof pr_didattiche[idClasse][idMateria][idProgrammazione]['moduli'] != 'undefined'){
            mod.append(new Option("- Modulo -", "-1"));
            Object.keys(pr_didattiche[idClasse][idMateria][idProgrammazione]['moduli']).forEach(function (index){
                mod.append(new Option(pr_didattiche[idClasse][idMateria][idProgrammazione]['moduli'][index]['descrizione_breve'], pr_didattiche[idClasse][idMateria][idProgrammazione]['moduli'][index]['id_programmazione_moduli']));
            });
            mod.removeAttr('disabled');
        } else {
            mod.attr("disabled", true);
            arg.attr("disabled", true);
        }
    }

    function caricaArgomentiPrDidattica(idModulo, postfisso){
        var idMateria = $('#id_materia_programmazione_didattica').val();
        var idClasse = $('#id_classe_programmazione_didattica').val();
        var idProgrammazione = $('#prdidattica_programmazione_inserisci_arg_'+postfisso).val();
        var arg = $('#prdidattica_argomento_inserisci_arg_'+postfisso);
        var tabelle = $('#tabelle_prdidattica_'+postfisso);

        arg.find('option').remove();

        if (typeof pr_didattiche[idClasse][idMateria][idProgrammazione]['moduli'][idModulo] != 'undefined'){
            arg.append(new Option("- Argomento -", "-1"));
            Object.keys(pr_didattiche[idClasse][idMateria][idProgrammazione]['moduli'][idModulo]['argomenti']).forEach(function (index){
                arg.append(new Option(pr_didattiche[idClasse][idMateria][idProgrammazione]['moduli'][idModulo]['argomenti'][index]['descrizione_breve'], pr_didattiche[idClasse][idMateria][idProgrammazione]['moduli'][idModulo]['argomenti'][index]['id_programmazione_argomenti']));
            });
            arg.removeAttr('disabled');

            var html = ""; //per eventuale uso futuro

            tabelle.html(html);
        } else {
            arg.attr("disabled", true);
            tabelle.html("");
        }
    }


    function sistemaCalendario(id){
        var bounding = document.querySelector("#"+id).getBoundingClientRect();
        if (bounding.right > $(window).width())
        {
            document.getElementById(id).style.right = '-150px';
        }
    }

    window.addEventListener("orientationchange", function() {
        //if ($('#elenco_giorni').height() > $(window.innerWidth)[0] - $('#elenco_giorni').position().top - 10){
        //    $('#elenco_giorni').css('height', '');
            $('#elenco_giorni').delay(500).height($(window.innerWidth)[0] - $('#elenco_giorni').position().top - 15);
        //}
        {/literal}
            {if $is_mobile == 1}
                var altezzaSchermo = $(window.innerWidth)[0];
                var larghezzaSchermo = $(window.innerHeight)[0];

                if (altezzaSchermo > larghezzaSchermo)
                {
                    mostraMessaggioRuotaSchermo();
                }
            {/if}
        {literal}
    });


    $(document).ready( function(){
            $('#message').delay(5000).fadeOut();
            $('.tooltip').css('z-index', '1');

            if ($('#elenco_giorni').length)
            {
                if ($('#elenco_giorni').height() > $(window.innerHeight)[0] - $('#elenco_giorni').position().top - 10){
                    $('#elenco_giorni').height($(window.innerHeight)[0] - $('#elenco_giorni').position().top - 10);
                }
            }

            carica_studenti();

            //--- Calendario JQX
            $("#jqxCalendar").jqxCalendar({ width: '400px', height: '400px', enableTooltips: false, theme: 'Bootstrap', culture: 'it-IT', enableViews: false, columnHeaderHeight: 40, titleHeight: 40, showOtherMonthDays: false });
            $("#jqxCalendar").jqxCalendar('setDate', new Date());
            sistemaCalendario("jqxCalendar");
            $("#jqxCalendar").hide();
            $('#jqxCalendar').on('change', function (event)
            {
                var jsDate = new Date;
                jsDate = event.args.date;
                var type = event.args.type; // keyboard, mouse or null depending on how the date was selected.
                var input_hidden = $('#data_da_calendario');

                var dataIso = jsDate.getDate() + '/' + (jsDate.getMonth() + 1) + '/' + jsDate.getFullYear();
                input_hidden.val(dataIso);
                input_hidden[0].form.submit();
                $('#jqxGeneralLoader').jqxLoader('open');
            });
            //---
        });
    {/literal}

    function caricaMaterieDisponbibiliDocenteClasse(id_orario, id_classe){
        $('#id_ora_modifica_materia_orario').val(id_orario);

        var html = '<table>';
        Object.keys(materieDocenteClassi[id_classe]).forEach(function(index){
            html += '<tr>';
            html +=     '<td>'+materieDocenteClassi[id_classe][index]['descrizione']+'</td>';
            html +=     '<td>';
            html +=         '<button class="btn_flat testo_verde"';
            html +=             'onclick="$(\'#id_materia_modifica_materia_orario\').val('+materieDocenteClassi[id_classe][index]['id_materia']+');';
            html +=             'document.getElementById(\'form_container\').operazione.value = \'cambia_materia_in_orario\';';
            html +=             '$(\'#jqxGeneralModalLoader\').jqxLoader(\'open\');"';
            html +=             '>{mastercom_label}Scegli{/mastercom_label}</button>';
            html +=     '</td>';
            html += '</tr>';
        });
        html += '</table>';

        $('#elenco_materie_classe').html(html);
    }

    {if $is_mobile == 1}
        function creaMessaggioRuotaSchermo(){
            $('body').append('<div id="rotateScreen" align="center"></div>');
            $('#rotateScreen').css({
                "position": "fixed",
                "color": "white",
                "background-color": "#262626",
                "font-size": "20px",
                "border-radius": "15px",
                "padding": "8px 16px",
                "width": "50%",
                "left": "50%",
                "top": "50%",
                "transform": "translate(-50%, -50%)",
                "opacity": "0.9",
                "display": "none"
            });
            $('#rotateScreen').html('&#8634;<br>{mastercom_label}Ruotare lo schermo per una migliore visualizzazione{/mastercom_label}');
        }

        function mostraMessaggioRuotaSchermo()
        {
            $('#rotateScreen').fadeIn();
            $('#rotateScreen').delay(2000).fadeOut();
        }

        $(document).ready( function(){
            creaMessaggioRuotaSchermo();
            var altezzaSchermo = $(window.innerHeight)[0];
            var larghezzaSchermo = $(window.innerWidth)[0];

            if (altezzaSchermo > larghezzaSchermo)
            {
                mostraMessaggioRuotaSchermo();
            }
        });
    {/if}
</script>

<br>

<div id="sfondo_oscurato" style="width: 100%; height: 100%; top: 0; left: 0; position: fixed; background-color: #aaa; opacity: 0.5; z-index: 2; display: none;"
     onclick='chiudiSfondoOscurato();'></div>

{if $messaggio}
    <div id="message" class="messaggio_basso_scomparsa" style="font-size: 110%">
        {$messaggio}
    </div>
{/if}

<form method='post' action='{$SCRIPT_NAME}' id="form_container">
    <div class="div_titolo_scheda_generica sfondo_scuro" style="width: 98%;" align="center">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div align="left" class="ombra_testo" style="padding-left: 10px; font-size: 120%;">
                {mastercom_label}Registro docente{/mastercom_label}
            </div>
            <div>
                {* Calendario con informazioni su giorno Settimana Mese *}
                {if !isset($tipo_visualizzazione) || ($tipo_visualizzazione == 'giorno')}
                    {foreach $elenco_giorni as $giorno}
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno precedente *}
                        <input type="button"
                               class="btn_flat_bianco ombra_testo"
                               id="btn_giorno_precedente"
                               value="&#9001;"
                               title="{mastercom_label}Giorno precedente{/mastercom_label}"
                               style="font-size: 110%; padding: 8px;"
                               onclick="this.form.giorno_scelto.value='{$giorno['timestamp_giorno_precedente']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                               >
                        {/if}
                        {* Pulsante calendario con giorno corrente *}
                        <div style="position: relative; display: inline-block;">
                            <button type="button" class="btn_pieno" id="btn_calendario" style="background-color: #FAFAFA; color:#3F51B5; border-radius: 32px" title="{mastercom_label}Apri il calendario{/mastercom_label}" onclick="apriChiudiPopup('jqxCalendar', '', true, ['btn_calendario']);">
                                {$giorno['giorno_tradotto']} {$giorno['data']}
                            </button>
                            <div style="position: absolute; z-index: 1000;" id="jqxCalendar"></div>
                        </div>
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno successivo *}
                        <input
                            type="button"
                            class="btn_flat_bianco ombra_testo"
                            id="btn_giorno_successivo"
                            value="&#9002;"
                            title="{mastercom_label}Giorno successivo{/mastercom_label}"
                            style="font-size: 110%; padding: 8px;"
                            onclick="this.form.giorno_scelto.value='{$giorno['timestamp_giorno_successivo']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                            >
                        {/if}
                        <input type="hidden" name="giorno_scelto" value="{$giorno['timestamp']}">
                        <input type="hidden" name="data_da_calendario"  id="data_da_calendario" value="">
                    {/foreach}
                {elseif ($tipo_visualizzazione == 'settimana')}
                    {foreach $elenco_giorni as $giorno}
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno precedente *}
                        <input type="button"
                               class="btn_flat_bianco ombra_testo"
                               id="btn_giorno_precedente"
                               value="&#9001;"
                               title="{mastercom_label}Settimana precedente{/mastercom_label}"
                               style="font-size: 110%; padding: 8px;"
                               onclick="this.form.giorno_scelto.value='{$giorno['timestamp_settimana_precedente']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                               >
                        {/if}
                        {* Pulsante calendario con giorno corrente *}
                        <div style="position: relative; display: inline-block;">
                            <button type="button" class="btn_pieno" id="btn_calendario" style="background-color: #FAFAFA; color:#3F51B5; border-radius: 32px" title="{mastercom_label}Apri il calendario{/mastercom_label}" onclick="apriChiudiPopup('jqxCalendar', '', true, ['btn_calendario']);">
                                {$giorno['inizio_settimana']} - {$giorno['fine_settimana']}
                            </button>
                            <div style="position: absolute; z-index: 1000;" id="jqxCalendar"></div>
                        </div>
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno successivo *}
                        <input
                            type="button"
                            class="btn_flat_bianco ombra_testo"
                            id="btn_giorno_successivo"
                            value="&#9002;"
                            title="{mastercom_label}Settimana successiva{/mastercom_label}"
                            style="font-size: 110%; padding: 8px;"
                            onclick="this.form.giorno_scelto.value='{$giorno['timestamp_settimana_successiva']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                            >
                        {/if}
                        <input type="hidden" name="giorno_scelto" value="{$giorno['timestamp']}">
                        <input type="hidden" name="data_da_calendario"  id="data_da_calendario" value="">
                        {break}
                    {/foreach}
                {elseif ($tipo_visualizzazione == 'mese')}
                    {foreach $elenco_giorni as $giorno}
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno precedente *}
                        <input type="button"
                               class="btn_flat_bianco ombra_testo"
                               id="btn_giorno_precedente"
                               value="&#9001;"
                               title="{mastercom_label}Mese precedente{/mastercom_label}"
                               style="font-size: 110%; padding: 8px;"
                               onclick="this.form.giorno_scelto.value='{$giorno['timestamp_mese_precedente']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                               >
                        {/if}
                        {* Pulsante calendario con giorno corrente *}
                        <div style="position: relative; display: inline-block;">
                            <button type="button" class="btn_pieno" id="btn_calendario" style="background-color: #FAFAFA; color:#3F51B5; border-radius: 32px" value="{$giorno['intestazione_mese']}" title="{mastercom_label}Apri il calendario{/mastercom_label}" onclick="apriChiudiPopup('jqxCalendar', '', true, ['btn_calendario']);">
                                {$giorno['intestazione_mese']}
                            </button>
                            <div style="position: absolute; z-index: 1000;" id="jqxCalendar"></div>
                        </div>
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno successivo *}
                        <input
                            type="button"
                            class="btn_flat_bianco ombra_testo"
                            id="btn_giorno_successivo"
                            value="&#9002;"
                            title="{mastercom_label}Mese successivo{/mastercom_label}"
                            style="font-size: 110%; padding: 8px;"
                            onclick="this.form.giorno_scelto.value='{$giorno['timestamp_mese_successivo']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                            >
                        {/if}
                        <input type="hidden" name="giorno_scelto" value="{$giorno['timestamp']}">
                        <input type="hidden" name="data_da_calendario"  id="data_da_calendario" value="">
                        {break}
                    {/foreach}
                {/if}
            </div>
            <div class="ombra_testo">
                {* Scelta visualizzazione per Giorno Settimana Mese *}
                <button
                    type="button"
                    class="btn_flat_bianco ombra_testo"
                    id="btn_giorno"
                    title="{mastercom_label}Visualizzazione per giorno{/mastercom_label}"
                    style="font-size: 100%; padding-top: 0px; padding-bottom: 0px;{if $tipo_visualizzazione == 'giorno'}color: yellow;{/if}"
                    onclick="this.form.tipo_visualizzazione.value='giorno'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                    >{if $is_mobile === 1}
                        {mastercom_label}G{/mastercom_label}
                    {else}
                        {mastercom_label}Giorno{/mastercom_label}
                    {/if}</button>|
                <button
                    type="button"
                    class="btn_flat_bianco ombra_testo"
                    id="btn_settimana"
                    title="{mastercom_label}Visualizzazione per settimana{/mastercom_label}"
                    style="font-size: 100%; padding-top: 0px; padding-bottom: 0px;{if $tipo_visualizzazione == 'settimana'}color: yellow;{/if}"
                    onclick="this.form.tipo_visualizzazione.value='settimana'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                    >{if $is_mobile === 1}
                        {mastercom_label}S{/mastercom_label}
                    {else}
                        {mastercom_label}Settimana{/mastercom_label}
                    {/if}</button>|
                <button
                    type="button"
                    class="btn_flat_bianco ombra_testo"
                    id="btn_mese"
                    title="{mastercom_label}Visualizzazione per mese{/mastercom_label}"
                    style="font-size: 100%; padding-top: 0px; padding-bottom: 0px;{if $tipo_visualizzazione == 'mese'}color: yellow;{/if}"
                    onclick="this.form.tipo_visualizzazione.value='mese'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                    >{if $is_mobile === 1}
                        {mastercom_label}M{/mastercom_label}
                    {else}
                        {mastercom_label}Mese{/mastercom_label}
                    {/if}</button>
            </div>
            <div style="padding-right: 10px; width: 25%;">
                {* Scelta Professore *}
                Docente:
                <select name="id_professore" class="select" style="background-color: white; width: 80%;" onchange="this.form.submit();">
                    <option value="0">-</option>
                    {foreach $elenco_docenti as $docente}
                        <option value="{$docente['id_utente']}"
                                {if $id_professore == $docente['id_utente']}selected{/if}
                                >
                            {$docente['cognome']} {$docente['nome']}
                        </option>
                    {/foreach}
                </select>
            </div>
        </div>
    </div>
    <div class="div_corpo_scheda_generica" id="elenco_giorni" align="center" style="width: 98%; color: black; font-weight: normal; overflow: auto; -webkit-overflow-scrolling: touch;">
        {assign var="salva_firme" value='NO'}
        {foreach $elenco_giorni as $giorno}
            {* - INTESTAZIONE GIORNO - *}
            <div style="padding: 10px; display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <table style="border-collapse: collapse; border-left: 3px solid red;">
                        <tr>
                            <td rowspan="2" style="font-size: 60px; line-height: 60px; padding-left: 10px;">
                                {$giorno['giorno']}
                            </td>
                            <td>
                                <b>{$giorno['mese_tradotto']} {$giorno['anno']}</b>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" style="font-size: 18px; line-height: 18px;">
                                <b>{$giorno['giorno_tradotto']}</b>
                            </td>
                        </tr>
                    </table>
                </div>
                <div align="center">
                    {if $giorno['compleanni'] != '' && $compleanni_registro == 'SI'}
                        <img src="icone/cake.png" style="width: 30px;"/>
                        <span style="padding-left: 5px;"><b>{$giorno['compleanni']}</b></span>
                    {/if}
                </div>
                <div align="right">
                    {if $giorno['santi'] != '' && $santi_registro == 'SI'}
                        <table style="border-collapse: collapse; border-right: 3px solid #4d8e4d;">
                            <tr>
                                <td align="right" style="padding-right: 10px; height: 30px;">
                                    {mastercom_label}Santi del giorno{/mastercom_label}
                                </td>
                            </tr>
                            <tr>
                                <td align="right" style="padding-right: 10px; height: 30px;">
                                    <b><i>{$giorno['santi']}</i></b>
                                </td>
                            </tr>
                        </table>
                    {/if}
                </div>
            </div>
            <table align="center" style="width: 100%;">
                {if $id_professore > 0}
                    {if $giorno['ore']|@count > 0}
                        <tr width="100%" class="bordo_basso_generico titolo_colonna_tabella" style="opacity: 1;">
                            {* - Intestazione tabella giornata - *}
                            <td style="width: 110px; min-width: 110px;" class="padding_cella_generica bordo_destro_generico">
                                {mastercom_label}Ora{/mastercom_label}
                            </td>
                            <td style="width: 130px; min-width: 130px;" class="padding_cella_generica bordo_destro_generico">
                                {mastercom_label}Materia{/mastercom_label}
                            </td>
                            <td style="width: 140px; min-width: 140px;" class="padding_cella_generica bordo_destro_generico">
                                {mastercom_label}Assenze{/mastercom_label}
                            </td>
                            <td style="width: 20%; min-width: 140px;" class="padding_cella_generica bordo_destro_generico">
                                {mastercom_label}Argomenti{/mastercom_label}
                            </td>
                            <td style="width: 20%; min-width: 140px;" class="padding_cella_generica bordo_destro_generico">
                                {mastercom_label}Compiti{/mastercom_label}
                            </td>
                            {if $funzione_note == '1'}
                                <td style="width: 15%; min-width: 120px;" class="padding_cella_generica bordo_sinistro_generico">
                                    {mastercom_label}Note disc.{/mastercom_label}
                                </td>
                            {/if}
                        </tr>
                        {foreach $giorno['ore'] as $ora}
                            {* - Ciclo le fasce orarie - *}
                            <tr width="100%" {if $ora['ora_attuale'] == 'SI'}style="background-color: #d6ffd6"{/if}>
                                <td align="center" class="padding_cella_generica bordo_alto_generico bordo_destro_generico">
                                    {* fascia oraria *}
                                    <span style="cursor: pointer;"
                                            title="{mastercom_label}Vai al registro della classe{/mastercom_label}"
                                            onmouseover="this.style.opacity = '0.7';"
                                            onmouseout="this.style.opacity = '1';"
                                            onclick="document.getElementById('stato_secondario').value = 'registri_classe';
                                                    document.getElementById('id_classe').value = '{$ora['id_classe']}';
                                                    document.getElementById('form_container').submit();
                                                    $('#jqxGeneralLoader').jqxLoader('open');">
                                        <b>{$ora['classe']}</b>
                                    </span>
                                    <br>
                                    <span>{$ora['fascia']}</span>
                                </td>
                                <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico bordo_destro_generico">
                                    <table width="100%">
                                        {assign var="salva_firme" value='SI'}
                                        {foreach $ora['materie'] as $materia}
                                            {if $materia['stampa'] != 'NO'}
                                                <tr style="padding: 5px;">
                                                    <td style="padding-top: 8px; padding-bottom: 8px;">
                                                        <div style="min-width: 130px; overflow: auto;">
                                                            <table width="100%">
                                                                <tr>
                                                                    <td>
                                                                        <div>
                                                                            <b>{$materia['materia']}</b>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                {if $materia['id_professore'] !== $id_professore}
                                                                    <tr>
                                                                        <td>
                                                                            <i>{$materia['professore']}</i>
                                                                        </td>
                                                                    </tr>
                                                                {/if}
                                                            </table>
                                                        </div>
                                                        {if $materia['ora_del_docente'] == 'SI' && $materia['modifica_firma'] == 'SI' && $materia['firma'] == 'NO'}
                                                            <div align="center">
                                                                <div align="center"
                                                                    style="
                                                                        margin-top: 2px;
                                                                        padding: 1px 6px;
                                                                        background-color: #ff4949;
                                                                        color: white;
                                                                        width: max-content;
                                                                        height: 19px;
                                                                        font-weight: bold;
                                                                        border-radius: 10px;
                                                                        font-size: 90%;
                                                                        margin-left: auto;
                                                                        margin-right: auto;
                                                                        display: inline-block;
                                                                        ">
                                                                    <label class="noselect"
                                                                                style="display: block;
                                                                                padding-left: 15px;
                                                                                text-indent: -15px;
                                                                                margin-top: 3px;">
                                                                        <input type="checkbox"
                                                                                name="firme_da_inserire[]"
                                                                                title="{mastercom_label}Inserisci firma{/mastercom_label}"
                                                                                {if $materia['ora_come_sostegno'] == 'SI'}
                                                                                    value="{$materia['id_materia_firma_sostegno']}_{$ora['id_classe']}_{$ora['data_inizio']}_{$ora['data_fine']}"
                                                                                {else}
                                                                                    value="{$materia['id_materia']}_{$ora['id_classe']}_{$ora['data_inizio']}_{$ora['data_fine']}"
                                                                                {/if}

                                                                                style="width: 13px;
                                                                                    height: 13px;
                                                                                    padding: 0;
                                                                                    margin:0;
                                                                                    vertical-align: bottom;
                                                                                    position: relative;
                                                                                    top: -1px;
                                                                                    *overflow: hidden;
                                                                                "
                                                                                > {mastercom_label}Firma{/mastercom_label}
                                                                                {if $materia['ora_come_sostegno'] == 'SI'}
                                                                                    <span style='font-weight: normal;'>({mastercom_label}Sostegno{/mastercom_label})</span>
                                                                                {elseif $materia['ora_come_itp'] == 'SI'}
                                                                                    <span style='font-weight: normal;'>({mastercom_label}ITP{/mastercom_label})</span>
                                                                                {/if}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        {elseif $materia['ora_del_docente'] == 'SI' && $materia['id_professore_firma'] == $id_professore}
                                                            <div align="center">
                                                                <div class="noselect"
                                                                    style="
                                                                        margin-top: 2px;
                                                                        padding: 1px;
                                                                        background-color: #4d8e4d;
                                                                        color: white;
                                                                        width: 70px;
                                                                        height: 19px;
                                                                        line-height: 19px;
                                                                        font-weight: bold;
                                                                        border-radius: 10px;
                                                                        margin-right: 3px;
                                                                        font-size: 90%;
                                                                        text-align: center;
                                                                        margin-left: auto;
                                                                        margin-right: auto;
                                                                        display: inline-block;
                                                                        ">
                                                                    {mastercom_label}Firmata{/mastercom_label}
                                                                </div>
                                                                {if $materia['ora_cancellabile'] == 'SI'}
                                                                    <div style="display: inline-block;">
                                                                        <button  type="button"
                                                                                class="btn_flat_tondo testo_bianco ripples"
                                                                                title="{mastercom_label}Elimina firma{/mastercom_label}"
                                                                                style="background-color: #ff4949;
                                                                                    font-size: 90%;
                                                                                    padding: 3px 5px;
                                                                                "
                                                                                onclick="if (confirm('{mastercom_label}Eliminare la firma?{/mastercom_label}') === true){
                                                                                        document.getElementById('form_container').id_firma_selezionata.value='{$materia['id_firma']}';
                                                                                        document.getElementById('form_container').operazione.value='elimina_firma_corrente';
                                                                                        document.getElementById('form_container').submit();
                                                                                        $('#jqxGeneralLoader').jqxLoader('open');}
                                                                                        "
                                                                                >&#10005;</button>
                                                                    </div>
                                                                {/if}
                                                            </div>
                                                        {/if}
                                                    </td>
                                                </tr>
                                            {/if}
                                        {/foreach}
                                    </table>
                                </td>
                                <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico bordo_destro_generico">
                                    {* assenti *}
                                    {if isset($ora['assenze_studenti']['uditori'])}
                                        <div style="padding-top: 4px; padding-bottom: 4px;" class="testo_rosso">
                                            {mastercom_label}Studenti Uditori{/mastercom_label}
                                            <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 5px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                <b>{$ora['assenze_studenti']['uditori']|@count}</b>
                                                <span class="tooltiptext">
                                                    {foreach $ora['assenze_studenti']['uditori'] as $studente}
                                                        {$studente}<br>
                                                    {/foreach}
                                                </span>
                                            </div>
                                        </div>
                                    {/if}
                                    {if isset($ora['assenze_studenti']['intera_ora'])}
                                        <div style="padding-top: 4px; padding-bottom: 4px;">
                                            {mastercom_label}Assenti intera ora{/mastercom_label}
                                            <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 5px; color: black; border-radius: 20px; padding: 2px 5px;">
                                                <b>{$ora['assenze_studenti']['intera_ora']|@count}</b>
                                                <span class="tooltiptext">
                                                    {foreach $ora['assenze_studenti']['intera_ora'] as $studente}
                                                        {$studente}<br>
                                                    {/foreach}
                                                </span>
                                            </div>
                                        </div>
                                    {/if}
                                    {if isset($ora['assenze_studenti']['ritardo'])}
                                        <div style="padding-top: 4px; padding-bottom: 4px;">
                                            {mastercom_label}Entrati in ritardo{/mastercom_label}
                                            <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 16px; color: black; border-radius: 20px; padding: 2px 5px;">
                                                <b>{$ora['assenze_studenti']['ritardo']|@count}</b>
                                                <span class="tooltiptext">
                                                    {foreach $ora['assenze_studenti']['ritardo'] as $studente}
                                                        {$studente}<br>
                                                    {/foreach}
                                                </span>
                                            </div>
                                        </div>
                                    {/if}
                                    {if isset($ora['assenze_studenti']['uscita'])}
                                        <div style="padding-top: 4px; padding-bottom: 4px;">
                                            {mastercom_label}Usciti in anticipo{/mastercom_label}
                                            <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 14px; color: black; border-radius: 20px; padding: 2px 5px;">
                                                <b>{$ora['assenze_studenti']['uscita']|@count}</b>
                                                <span class="tooltiptext">
                                                    {foreach $ora['assenze_studenti']['uscita'] as $studente}
                                                        {$studente}<br>
                                                    {/foreach}
                                                </span>
                                            </div>
                                        </div>
                                    {/if}
                                    {if isset($ora['assenze_studenti']['didattica_distanza'])}
                                        <div style="padding-top: 4px; padding-bottom: 4px;">
                                            {mastercom_label}Assenti did. dist.{/mastercom_label}
                                            <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 14px; color: black; border-radius: 20px; padding: 2px 5px;">
                                                <b>{$ora['assenze_studenti']['didattica_distanza']|@count}</b>
                                                <span class="tooltiptext">
                                                    {foreach $ora['assenze_studenti']['didattica_distanza'] as $studente}
                                                        {$studente}<br>
                                                    {/foreach}
                                                </span>
                                            </div>
                                        </div>
                                    {/if}
                                    {if isset($ora['assenze_studenti']['didattica_distanza_entrata'])}
                                                <div style="padding-top: 4px; padding-bottom: 4px;">
                                                    {mastercom_label}Entrati did. dist.{/mastercom_label}
                                                    <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 14px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                        <b>{$ora['assenze_studenti']['didattica_distanza_entrata']|@count}</b>
                                                        <span class="tooltiptext">
                                                            {foreach $ora['assenze_studenti']['didattica_distanza_entrata'] as $studente}
                                                                {$studente}<br>
                                                            {/foreach}
                                                        </span>
                                                    </div>
                                                </div>
                                            {/if}
                                            {if isset($ora['assenze_studenti']['didattica_distanza_uscita'])}
                                                <div style="padding-top: 4px; padding-bottom: 4px;">
                                                    {mastercom_label}Usciti did. dist.{/mastercom_label}
                                                    <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 14px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                        <b>{$ora['assenze_studenti']['didattica_distanza_uscita']|@count}</b>
                                                        <span class="tooltiptext">
                                                            {foreach $ora['assenze_studenti']['didattica_distanza_uscita'] as $studente}
                                                                {$studente}<br>
                                                            {/foreach}
                                                        </span>
                                                    </div>
                                                </div>
                                            {/if}
                                </td>
                                <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico bordo_destro_generico">
                                    {* argomenti *}
                                    {if isset($ora['argomenti']) && $ora['argomenti']|@count > 0}
                                        {foreach $ora['argomenti'] as $argomento}
                                            <div width="100%" style="overflow: auto; padding-bottom: 10px;">
                                                {if $argomento['cancellabile'] == 'SI' && $funzione_argomenti_registro == '1'}
                                                    <div style="display: table-cell; padding-right: 6px;">
                                                        <button type="button"
                                                                class="btn_flat_tondo testo_bianco"
                                                                title="{mastercom_label}Elimina Argomento{/mastercom_label}"
                                                                style="background-color: #ff4949;
                                                                    font-size: 90%;
                                                                    padding: 3px 5px;
                                                                "
                                                                onmouseover="this.style.color='yellow';"
                                                                onmouseout="this.style.color='white';"
                                                                onclick="if (confirm('{mastercom_label}Eliminare l\'argomento?{/mastercom_label}') === true){
                                                                        document.getElementById('form_container').id_argomento_selezionato.value='{$argomento['id_argomento']}';
                                                                        document.getElementById('form_container').operazione.value='elimina_argomento';
                                                                        document.getElementById('form_container').submit();
                                                                        $('#jqxGeneralLoader').jqxLoader('open');}
                                                                        "
                                                            >&#10005;</button>
                                                            {if $argomento['modificabile'] == 'SI'}
                                                            <br>
                                                            <button type="button"
                                                                    class="btn_flat_tondo sfondo_azzurro testo_bianco ripples"
                                                                    title="{mastercom_label}Modifica Argomento{/mastercom_label}"
                                                                    style="font-size: 90%;
                                                                        padding: 3px 6px;
                                                                        margin-top: 3px;
                                                                    "
                                                                    onclick="apriModificaArgomentoCompito('{$argomento['id_argomento']}',
                                                                                                        '{$argomento['descrizione_materia']|replace:"'":"\'"}',
                                                                                                        '{$ora['fascia']}');
                                                                            ">&hellip;</button>
                                                            {/if}
                                                    </div>
                                                {/if}
                                                <div style="display: table-cell;">
                                                    {$argomento['modulo']|escape:"html"}: {$argomento['descrizione']|escape:"html"}
                                                    {if $argomento['note'] !== '' && $argomento['id_professore'] == $id_professore}
                                                        <br>
                                                        <span class="annotazione_leggera">{$argomento['note']}</span>
                                                    {/if}
                                                    {if $argomento['tag_display'] != '' && $argomento['id_professore'] == $id_professore}
                                                        <br>
                                                        <span class="annotazione_leggera">Tag: {$argomento['tag_display']}</span>
                                                    {/if}
                                                    {if $argomento['id_programmazione_argomenti'] > 0 && $argomento['id_professore'] == $id_professore}
                                                        <br>
                                                        <span class="annotazione_leggera">{mastercom_label}Progr.{/mastercom_label}: {$argomento['dettagli_programmazione']['programmazione_display']}</span>
                                                    {/if}
                                                    {if $argomento['id_modulo_ministeriale'] > 0}
                                                        <br>
                                                        <span class="annotazione_leggera">Arg. minist. navali: </span>
                                                        <br>
                                                        {$argomento['testo_argomento_ministeriale_composto']}
                                                    {/if}
                                                </div>
                                                <br><br>
                                            </div>
                                        {/foreach}
                                    {/if}
                                    {foreach $ora['materie'] as $materia}
                                        {if $materia['editabile'] == 'SI' && $funzione_argomenti_registro == '1'}
                                            <div width="100%" align="center" style="overflow: auto;">
                                                <button type="button"
                                                    class="btn_flat_indaco bordo_scuro btn_padding_ridotto ripples"
                                                    onclick="apriChiudiPopup('compito_argomento_firma_{$materia['id_firma']}', 'sfondo_oscurato', true);
                                                            $('#id_materia_argomento_ministeriale').val({$materia['id_materia']});
                                                            $('#id_classe_argomento_ministeriale').val({$ora['id_classe']});
                                                            $('#id_materia_programmazione_didattica').val({$materia['id_materia']});
                                                            $('#id_classe_programmazione_didattica').val({$ora['id_classe']});
                                                            caricaProgrammazioniNavali({$ora['id_classe']}, {$materia['id_materia']}, {$materia['id_firma']});
                                                            caricaProgrammazioniPrDidattica({$ora['id_classe']}, {$materia['id_materia']}, {$materia['id_firma']}); "
                                                    >{mastercom_label}Inserisci per{/mastercom_label} {$materia['materia']}</button>
                                            </div>
                                        {/if}
                                    {/foreach}
                                </td>
                                <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico bordo_destro_generico" style="overflow: auto;">
                                    {* compiti *}
                                    {if isset($ora['compiti']) && $ora['compiti']|@count > 0}
                                        {foreach $ora['compiti'] as $compito}
                                            <div width="100%" style="overflow: auto; padding-bottom: 10px;">
                                                {if $compito['cancellabile'] == 'SI' && $funzione_argomenti_registro == '1'}
                                                    <div style="display: table-cell; padding-right: 6px;">
                                                        <button type="button"
                                                                class="btn_flat_tondo testo_bianco"
                                                                title="{mastercom_label}Elimina Compito{/mastercom_label}"
                                                                style="background-color: #ff4949;
                                                                    font-size: 90%;
                                                                    padding: 3px 5px;
                                                                "
                                                                onmouseover="this.style.color='yellow';"
                                                                onmouseout="this.style.color='white';"
                                                                onclick="if (confirm('{mastercom_label}Eliminare il compito?{/mastercom_label}') === true){
                                                                        document.getElementById('form_container').id_argomento_selezionato.value='{$compito['id_argomento']}';
                                                                        document.getElementById('form_container').operazione.value='elimina_compito';
                                                                        document.getElementById('form_container').submit();
                                                                        $('#jqxGeneralLoader').jqxLoader('open');}
                                                                        "
                                                                        >&#10005;</button>
                                                        <br>
                                                        <button type="button"
                                                                class="btn_flat_tondo sfondo_azzurro testo_bianco ripples"
                                                                title="{mastercom_label}Modifica Compito{/mastercom_label}"
                                                                style="font-size: 90%;
                                                                    padding: 3px 6px;
                                                                    margin-top: 3px;
                                                                    {if $compito['modificabile'] !== 'SI'}opacity: 0.4;{/if}
                                                                "
                                                                {if $compito['modificabile'] == 'SI'}
                                                                onclick="apriModificaArgomentoCompito('{$compito['id_argomento']}',
                                                                                                    '{$compito['descrizione_materia']|replace:"'":"\'"}',
                                                                                                    '{$ora['fascia']}');"
                                                                {else}
                                                                onclick="alert('{mastercom_label}Non è possibile modificare un compito di una data passata{/mastercom_label}');"
                                                                {/if}
                                                                >&hellip;</button>
                                                    </div>
                                                {/if}
                                                <div style="display: table-cell;">
                                                    <span style="font-size: 85%;">
                                                    {mastercom_label}Docente{/mastercom_label}: {$compito['docente']} <br>
                                                    {mastercom_label}Materia{/mastercom_label}: {$compito['materia']} <br>
                                                    {mastercom_label}Inserito il{/mastercom_label}: {$compito['data_inserimento_tradotta']} </span><br>
                                                    {$compito['assegnazioni']|escape:"html"}
                                                    {if $compito['note'] !== '' && $compito['id_professore'] == $id_professore}
                                                        <br>
                                                        <span class="annotazione_leggera">{$compito['note']|escape:"html"}</span>
                                                    {/if}
                                                    <br><br>
                                                </div>
                                            </div>
                                        {/foreach}
                                    {/if}
                                    {foreach $ora['materie'] as $materia}
                                        {if $materia['editabile'] == 'SI' && $funzione_argomenti_registro == '1'}
                                            <div width="100%" align="center" style="overflow: auto;">
                                                <button type="button"
                                                    class="btn_flat_indaco bordo_scuro btn_padding_ridotto ripples"
                                                    onclick="apriChiudiPopup('compito_argomento_firma_{$materia['id_firma']}', 'sfondo_oscurato', true);
                                                            $('#id_materia_argomento_ministeriale').val({$materia['id_materia']});
                                                            $('#id_classe_argomento_ministeriale').val({$ora['id_classe']});
                                                            $('#id_materia_programmazione_didattica').val({$materia['id_materia']});
                                                            $('#id_classe_programmazione_didattica').val({$ora['id_classe']});
                                                            caricaProgrammazioniNavali({$ora['id_classe']}, {$materia['id_materia']}, {$materia['id_firma']});
                                                            caricaProgrammazioniPrDidattica({$ora['id_classe']}, {$materia['id_materia']}, {$materia['id_firma']});  "
                                                    >{mastercom_label}Inserisci per{/mastercom_label} {$materia['materia']}</button>
                                            </div>
                                        {/if}
                                    {/foreach}
                                </td>
                                <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico">
                                    {* note *}
                                    {if isset($ora['note']) && $ora['note']|@count > 0}
                                        {foreach $ora['note'] as $nota}
                                            <div style="padding-top: 4px; padding-bottom: 4px;">
                                                {$nota['testo_ufficiale']}
                                                <br>
                                                ({mastercom_label}studenti{/mastercom_label}:
                                                <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="color: black; border-radius: 20px; padding: 2px 5px;">
                                                    <b>{$nota['elenco_soggetti']|@count}</b>
                                                    <span class="tooltiptext" style="right: 100%;">
                                                        {foreach $nota['elenco_soggetti'] as $key => $studente}
                                                            {$studente}{if $nota['elenco_id_soggetti'][$key]|in_array:$id_studenti_sostegno}{$param_simbolo_studente_sostegno}{/if}<br>
                                                        {/foreach}
                                                    </span>
                                                </div>
                                                    )
                                            </div>
                                        {/foreach}
                                    {/if}

                                    <div width="100%" align="center">
                                        <input type="button"
                                            value="{mastercom_label}Inserisci nota{/mastercom_label}"
                                            title="{mastercom_label}Inserisci nota disciplinare{/mastercom_label}"
                                            class="btn_flat_indaco bordo_scuro btn_padding_ridotto ripples"
                                            onclick="document.getElementById('data_nota_disciplinare').value='{$ora['data_inserimento_nota']}';
                                                        document.getElementById('ora_nota_disciplinare').value='{$ora['ora_inserimento_nota']}';
                                                        apriChiudiPopup('nuova_nota_disciplinare', 'sfondo_oscurato', true);"
                                            >
                                    </div>
                                </td>
                            </tr>
                        {/foreach}
                    {else}
                        <tr width="100%" class="bordo_basso_generico titolo_colonna_tabella" style="opacity: 1; border: none;">
                            {* - Intestazione tabella giornata - *}
                            <td colspan="7" style="padding: 10px 0px;" class="padding_cella_generica">
                                {mastercom_label}Non ci sono ore in questa giornata{/mastercom_label}
                            </td>
                        </tr>
                    {/if}
                    </table>
                    <div style="height: 30px;"></div>

                    {if $giorno['ore']|@count > 0 && $funzione_argomenti_registro == '1'}
                        {foreach $giorno['ore'] as $ora}
                            {foreach $ora['materie'] as $materia}
                                {if $materia['editabile'] == 'SI'}
                                    {*form inserimento compiti e argomenti*}
                                    <div  class="div_scheda_generica form_inserimento_compito_argomento"
                                        id="compito_argomento_firma_{$materia['id_firma']}"
                                        style="position: fixed;
                                            width: 60%;
                                            {*height: 60%;*}
                                            left: 50%;
                                            top: 50%;
                                            max-height: 80%;
                                                overflow-y: auto;
                                            transform: translate(-50%, -50%);
                                            display: none;
                                            z-index: 3;"
                                    >
                                        <h1>{$ora['classe']} - {$materia['materia']}</h1>
                                        <div style="height: 10px; font-size: 90%;" class='annotazione_leggera'>{mastercom_label}(puoi inserire un argomento, un compito o entrambi){/mastercom_label}</div>
                                        <table width="95%" align="center" style="border-left: 3px solid #007fff;">
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <b>{mastercom_label}Inserisci argomento{/mastercom_label}</b>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    {mastercom_label}Ora{/mastercom_label}: <b>{$ora['fascia']}</b>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <select class="select" name="argomenti[{$materia['id_firma']}][id_argomento_duplica]" onchange="(this.value == '-1') ? $('#duplica_argomento{$materia['id_firma']}').show() : $('#duplica_argomento{$materia['id_firma']}').hide();">
                                                        <option value="-1" selected>{mastercom_label}Nuovo argomento{/mastercom_label}</option>
                                                        {foreach $giorno[$ora['id_classe']]['lista_argomenti'][$materia['id_materia']] as $argomento_da_duplicare}
                                                        <option value="{$argomento_da_duplicare['id_argomento']}">{mastercom_label}Duplica{/mastercom_label} "{$argomento_da_duplicare['modulo']|@escape}"</option>
                                                        {/foreach}
                                                    </select>
                                                </td>
                                            </tr>
                                            <tbody id="duplica_argomento{$materia['id_firma']}">
                                                <tr>
                                                    <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                        {mastercom_label}Modulo{/mastercom_label}: <input type="text" name="argomenti[{$materia['id_firma']}][modulo]" placeholder="{mastercom_label}Modulo{/mastercom_label}" style="width: 50%;">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                        <textarea name="argomenti[{$materia['id_firma']}][testo]"
                                                                rows="3"
                                                                style="width: 80%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                                                placeholder="{mastercom_label}Argomento{/mastercom_label}.."></textarea>
                                                        <input type="hidden"
                                                            name="argomenti[{$materia['id_firma']}][id_materia]"
                                                            value="{$materia['id_materia']}">
                                                        <input type="hidden"
                                                            name="argomenti[{$materia['id_firma']}][data]"
                                                            value="{$ora['data_inizio']}">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                        <textarea name="argomenti[{$materia['id_firma']}][note_riservate]"
                                                            rows="1"
                                                            style="width: 90%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                                            placeholder="{mastercom_label}Eventuali note riservate{/mastercom_label}.."></textarea>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                        {mastercom_label}Tag{/mastercom_label}:
                                                        <select class="select" name="argomenti[{$materia['id_firma']}][id_tag]" placeholder="{mastercom_label}Tag{/mastercom_label}">
                                                            <option value = "">- {mastercom_label}Nessun tag{/mastercom_label} -</option>
                                                            {foreach $elenco_tag as $tag}
                                                                <option value="{$tag['id_tag']}">{$tag['descrizione']}</option>
                                                            {/foreach}
                                                        </select>
                                                    </td>
                                                </tr>
                                                {if $programmazione_didattica == 'SI'}
                                                        <tr>
                                                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                                <div class="bold">{mastercom_label}Argomento didattico collegato{/mastercom_label}</div>
                                                                <select id="prdidattica_programmazione_inserisci_arg_{$materia['id_firma']}" class="select" disabled placeholder="{mastercom_label}Programmazione{/mastercom_label}" onchange="caricaModuliPrDidattica(this.value, {$materia['id_firma']});"></select>
                                                                &rarr; <select id="prdidattica_modulo_inserisci_arg_{$materia['id_firma']}" class="select" disabled placeholder="{mastercom_label}Modulo{/mastercom_label}" onchange="caricaArgomentiPrDidattica(this.value, {$materia['id_firma']});"></select>
                                                                &rarr; <select id="prdidattica_argomento_inserisci_arg_{$materia['id_firma']}" class="select" name="argomenti[{$materia['id_firma']}][id_argomento_prdidattica]" disabled placeholder="{mastercom_label}Argomento{/mastercom_label}"></select>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;" id="tabelle_prdidattica_{$materia['id_firma']}"></td>
                                                        </tr>
                                                    {/if}
                                                {if $navale[$ora['id_classe']] == 'SI'}
                                                    <tr>
                                                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                            <div class="bold">{mastercom_label}Argomento SIDI collegato (navali){/mastercom_label}</div>
                                                            <select id="navali_programmazione_inserisci_arg_{$materia['id_firma']}" class="select" name="argomenti[{$materia['id_firma']}][id_programmazione_ministeriale]" disabled placeholder="{mastercom_label}Programmazione{/mastercom_label}" onchange="caricaModuliNavali(this.value, {$materia['id_firma']});"></select>
                                                            &rarr; <select id="navali_modulo_inserisci_arg_{$materia['id_firma']}" class="select" name="argomenti[{$materia['id_firma']}][id_modulo_ministeriale]" disabled placeholder="{mastercom_label}Modulo{/mastercom_label}" onchange="caricaArgomentiNavali(this.value, {$materia['id_firma']});"></select>
                                                            &rarr; <select id="navali_argomento_inserisci_arg_{$materia['id_firma']}" class="select" name="argomenti[{$materia['id_firma']}][id_argomento_ministeriale]" disabled placeholder="{mastercom_label}Argomento{/mastercom_label}"></select>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;" id="tabelle_navali_{$materia['id_firma']}"></td>
                                                    </tr>
                                                {/if}
                                            </tbody>
                                        </table>
                                        <div style="height: 10px;"></div>
                                        <table width="95%" align="center" style="border-left: 3px solid #ff9400;">
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <b>{mastercom_label}Inserisci compito{/mastercom_label}</b>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="min-height: 35px; padding-left: 15px;">
                                                    {mastercom_label}Data{/mastercom_label}:
                                                    <select class="select" onchange="settaDataOraNuovoCompito(this.value, 'data_compito_{$ora['id_classe']}_{$materia['id_materia']}_{$materia['id_firma']}', 'ora_compito_{$ora['id_classe']}_{$materia['id_materia']}_{$materia['id_firma']}');">
                                                        <option value="0">{mastercom_label}Data libera{/mastercom_label}</option>
                                                        {assign var='cont' value=1}
                                                        {foreach $lezioni_future[$ora['id_classe']][$materia['id_materia']] as $lezione}
                                                            <option value="{$lezione['data_inizio']}">{$lezione['data_tradotta']}</option>
                                                            {if $cont == 10}
                                                                {break}
                                                            {/if}
                                                            {assign var='cont' value=$cont+1}
                                                        {/foreach}
                                                    </select>
                                                    </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="min-height: 35px; padding-left: 15px;">
                                                    <input type="date"
                                                        name="compiti[{$materia['id_firma']}][data_compito]"
                                                        id="data_compito_{$ora['id_classe']}_{$materia['id_materia']}_{$materia['id_firma']}">
                                                    <span id="ora_compito_{$ora['id_classe']}_{$materia['id_materia']}_{$materia['id_firma']}">
                                                    &emsp;{mastercom_label}Ora{/mastercom_label}:
                                                    <input type="time"
                                                        id="ora_compito_{$ora['id_classe']}_{$materia['id_materia']}_{$materia['id_firma']}_input"
                                                        name="compiti[{$materia['id_firma']}][ora_compito]"
                                                        value="08:00">
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <textarea name="compiti[{$materia['id_firma']}][assegnazioni]"
                                                            rows="3"
                                                            style="width: 80%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                                            placeholder="{mastercom_label}Assegnazioni{/mastercom_label}.."></textarea>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <textarea name="compiti[{$materia['id_firma']}][note_riservate]"
                                                        rows="1"
                                                        style="width: 90%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                                        placeholder="{mastercom_label}Eventuali note riservate{/mastercom_label}.."></textarea>
                                                </td>
                                            </tr>
                                        </table>
                                        <div style="height: 10px;"></div>
                                        <div align="center" style="padding: 10px;">
                                            <input type="hidden"
                                                name="compiti[{$materia['id_firma']}][id_materia]"
                                                value="{$materia['id_materia']}">
                                            <input type="hidden"
                                                name="compiti[{$materia['id_firma']}][data]"
                                                value="{$ora['data_inizio']}">
                                        </div>
                                        <div align="center" style="padding: 10px;">
                                            <button type="button"
                                                class="btn_flat_indaco"
                                                    onclick="apriChiudiPopup('compito_argomento_firma_{$materia['id_firma']}', 'sfondo_oscurato', true);"
                                                    >{mastercom_label}Annulla{/mastercom_label}</button>
                                            <button type="button"
                                                class="btn_pieno sfondo_scuro"
                                                onclick="if (verificaDataCompitoFutura('data_compito_{$ora['id_classe']}_{$materia['id_materia']}_{$materia['id_firma']}', 'ora_compito_{$ora['id_classe']}_{$materia['id_materia']}_{$materia['id_firma']}_input') === true){
                                                                document.getElementById('form_container').operazione.value = 'inserisci_argomento_compito';
                                                                document.getElementById('form_container').id_firma_selezionata.value='{$materia['id_firma']}';
                                                                document.getElementById('form_container').id_classe_selezionata.value='{$ora['id_classe']}';
                                                                document.getElementById('form_container').submit();
                                                                $('#jqxGeneralLoader').jqxLoader('open');
                                                            } else {
                                                                alert('{mastercom_label}Il compito deve avere una data futura{/mastercom_label}');}"
                                                    >{mastercom_label}Salva{/mastercom_label}</button>
                                        </div>
                                    </div>
                                {/if}
                            {/foreach}
                        {/foreach}
                    {/if}
                {else}
                    <tr width="100%" class="bordo_basso_generico titolo_colonna_tabella" style="opacity: 1; border: none;">
                        <td colspan="7" style="padding: 10px 0px;" class="padding_cella_generica">
                            Seleziona un docente
                        </td>
                    </tr>
                    </table>
                {/if}
            {/foreach}

            {*form modifica compiti e argomenti*}
            <div  class="div_scheda_generica form_modifica_compito_argomento"
                id="modifica_argomento_compito"
                style="position: fixed;
                      width: 60%;
                      max-height: 80%;
                      overflow-y: auto;
                      {*height: 60%;*}
                      left: 50%;
                      top: 50%;
                      transform: translate(-50%, -50%);
                      display: none;
                      z-index: 3;"
            >
                <h1 id='modifica_argomento_compito_materia'></h1>
                <div style="height: 10px;"></div>
                <table width="95%" align="center" style="border-left: 3px solid #007fff;" id='modifica_argomento_tabella'>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                            <b>{mastercom_label}Modifica argomento{/mastercom_label}</b>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                            {mastercom_label}Ora{/mastercom_label}: <b id='modifica_argomento_fascia'></b>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                            {mastercom_label}Modulo{/mastercom_label}: <input type="text" id='modifica_argomento_modulo' name='modifica_argomento_compito[modulo]' placeholder="{mastercom_label}Modulo{/mastercom_label}" style="width: 50%;">
                        </td>
                    </tr>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                            <textarea id='modifica_argomento_testo'
                                      name='modifica_argomento_compito[testo]'
                                      rows="3"
                                      style="width: 80%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                      placeholder="{mastercom_label}Argomento{/mastercom_label}.."></textarea>
                        </td>
                    </tr>
                    </tr>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                            <textarea id='modifica_argomento_note_riservate'
                                      name='modifica_argomento_compito[note_riservate]'
                                    rows="1"
                                    style="width: 90%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                    placeholder="{mastercom_label}Eventuali note riservate{/mastercom_label}.."></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                            {mastercom_label}Tag{/mastercom_label}:
                            <select class="select" id="modifica_argomento_tag" name="modifica_argomento_compito[id_tag]" placeholder="{mastercom_label}Tag{/mastercom_label}">
                                <option value = "">- {mastercom_label}Nessun tag{/mastercom_label} -</option>
                                {foreach $elenco_tag as $tag}
                                    <option value="{$tag['id_tag']}">{$tag['descrizione']}</option>
                                {/foreach}
                            </select>
                        </td>
                    </tr>
                    {if $programmazione_didattica == 'SI'}
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                <div class="bold">{mastercom_label}Argomento didattico collegato{/mastercom_label}</div>
                                <input type="hidden" id="id_materia_programmazione_didattica">
                                <input type="hidden" id="id_classe_programmazione_didattica">
                                <select id="prdidattica_programmazione_inserisci_arg_modifica" class="select" disabled placeholder="{mastercom_label}Programmazione{/mastercom_label}" onchange="caricaModuliPrDidattica(this.value, 'modifica');"></select>
                                &rarr; <select id="prdidattica_modulo_inserisci_arg_modifica" class="select" disabled placeholder="{mastercom_label}Modulo{/mastercom_label}" onchange="caricaArgomentiPrDidattica(this.value, 'modifica');"></select>
                                &rarr; <select id="prdidattica_argomento_inserisci_arg_modifica" class="select" name="modifica_argomento_compito[id_argomento_prdidattica]" disabled placeholder="{mastercom_label}Argomento{/mastercom_label}"></select>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;" id="tabelle_prdidattica_modifica"></td>
                        </tr>
                    {/if}
                    {if $navale['navale'] == 'SI'}
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                <div class="bold">{mastercom_label}Argomento SIDI collegato (navali){/mastercom_label}</div>
                                <input type="hidden" id="id_materia_argomento_ministeriale">
                                <input type="hidden" id="id_classe_argomento_ministeriale">
                                <select id="navali_programmazione_inserisci_arg_modifica" class="select" name="modifica_argomento_compito[id_programmazione_ministeriale]" disabled placeholder="{mastercom_label}Programmazione{/mastercom_label}" onchange="caricaModuliNavali(this.value, 'modifica');"></select>
                                &rarr; <select id="navali_modulo_inserisci_arg_modifica" class="select" name="modifica_argomento_compito[id_modulo_ministeriale]" disabled placeholder="{mastercom_label}Modulo{/mastercom_label}" onchange="caricaArgomentiNavali(this.value, 'modifica');"></select>
                                &rarr; <select id="navali_argomento_inserisci_arg_modifica" class="select" name="modifica_argomento_compito[id_argomento_ministeriale]" disabled placeholder="{mastercom_label}Argomento{/mastercom_label}"></select>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;" id="tabelle_navali_modifica"></td>
                        </tr>
                    {/if}
                </table>
                <table width="95%" align="center" style="border-left: 3px solid #ff9400;" id='modifica_compito_tabella'>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                            <b>{mastercom_label}Modifica compito{/mastercom_label}</b>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="min-height: 35px; padding-left: 15px;">
                            {mastercom_label}Data{/mastercom_label}:
                            <select class="select" id="modifica_compito_select_data" onchange="settaDataOraNuovoCompito(this.value, 'modifica_compito_data', 'modifica_compito_ora');">
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="min-height: 35px; padding-left: 15px;">
                            <input type="date"
                                   name="modifica_argomento_compito[data]"
                                   id="modifica_compito_data">
                            <span id="modifica_compito_ora">
                            &emsp;{mastercom_label}Ora{/mastercom_label}:
                            <input type="time"
                                   id="modifica_compito_ora_input"
                                   name="modifica_argomento_compito[ora]">
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                            <textarea name="modifica_argomento_compito[assegnazioni]"
                                      id="modifica_compito_assegnazioni"
                                      rows="3"
                                      style="width: 80%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                      placeholder="{mastercom_label}Assegnazioni{/mastercom_label}.."></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                            <textarea name="modifica_argomento_compito[note_riservate]"
                                    id="modifica_compito_note_riservate"
                                    rows="1"
                                    style="width: 90%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                    placeholder="{mastercom_label}Eventuali note riservate{/mastercom_label}.."></textarea>
                        </td>
                    </tr>
                </table>
                <div style="height: 10px;"></div>
                <div align="center" style="padding: 10px;">
                    <button type="button"
                           class="btn_flat_indaco"
                            onclick="apriChiudiPopup('modifica_argomento_compito', 'sfondo_oscurato', true);"
                            >{mastercom_label}Annulla{/mastercom_label}</button>
                    <button type="button"
                           class="btn_pieno sfondo_scuro ripples"
                           onclick="if (verificaDataCompitoFutura('modifica_compito_data', 'modifica_compito_ora_input') === true){
                                        document.getElementById('form_container').operazione.value = 'modifica_argomento_compito';
                                        document.getElementById('form_container').submit();
                                        $('#jqxGeneralLoader').jqxLoader('open');
                                    } else {
                                        alert('{mastercom_label}Il compito deve avere una data futura{/mastercom_label}');}"
                            >{mastercom_label}Salva{/mastercom_label}</button>
                </div>
            </div>

            {*form inserimento nota disciplinare*}
            <div  class="div_scheda_generica"
                    id="nuova_nota_disciplinare"
                    style="position: fixed;
                          width: 50%;
                          min-width: 500px;
                          left: 50%;
                          top: 50%;
                          transform: translate(-50%, -50%);
                          display: none;
                          z-index: 3;
                          overflow-y: auto;"
                >
                <div align="center" style="width: 100%; height: 100%;">
                    <table width="95%" align="center">
                        <tr>
                            <td align="center" style="padding: 15px 10px 20px 10px; font-size: 120%;">
                                <b>{mastercom_label}INSERISCI NOTA{/mastercom_label}</b><br>
                            </td>
                        </tr>
                        <tr>
                            <td align="center">
                                <div width="100%" style="display: flex; align-items: center; justify-content: space-around; flex-wrap: wrap;">
                                    <div class='padding_cella_generica'>
                                        {mastercom_label}Data{/mastercom_label}<br>
                                        <input type="date"
                                               name="data_nota_disciplinare"
                                               id="data_nota_disciplinare"
                                               value=""
                                               style="border: 0.5px solid #ccc; padding: 2px;">
                                    </div>
                                    <div class='padding_cella_generica'>
                                        {mastercom_label}Ora{/mastercom_label}<br>
                                        <input type="time"
                                               name="ora_nota_disciplinare"
                                               id="ora_nota_disciplinare"
                                               value=""
                                               style="border: 0.5px solid #ccc; padding: 2px;">
                                    </div>
                                    <div>
                                        {mastercom_label}Studenti{/mastercom_label}<div id='elencoStudenti'></div>
                                        <input type="hidden" name="id_studenti_nota" id="id_studenti_nota">
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" class='padding_cella_generica'>
                                <div width="100%" style="display: flex; align-items: center; justify-content: space-around; flex-wrap: wrap;">
                                    <div class="padding_cella_generica">
                                        {mastercom_label}Valori predefiniti (opzionali){/mastercom_label}<br>
                                        <select class="select" onchange="$('#testo_nota_disciplinare').val($(this).val());">
                                            <option value="">-</option>
                                            {foreach $elenco_valori_note as $valore}
                                                <option value="{$valore['descrizione']}">{$valore['descrizione']}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                    <div class="padding_cella_generica">
                                        {mastercom_label}Tipo{/mastercom_label}<br>
                                        <select class="select" name="id_tag_nota_disciplinare">
                                            {foreach $elenco_tag_note as $tag}
                                                <option value="{$tag['id_tag']}">{$tag['descrizione']}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" class='padding_cella_generica'>
                                <textarea name="testo_nota_disciplinare"
                                            id="testo_nota_disciplinare"
                                            rows="10"
                                            style="width: 95%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                            placeholder="{mastercom_label}Inserisci il testo della nota{/mastercom_label}.."
                                            ></textarea>
                            </td>
                        </tr>
                    </table>
                    <div class="padding_cella_generica">
                        <br>
                        <button type="button"
                                class="btn_flat_indaco"
                                onclick="apriChiudiPopup('nuova_nota_disciplinare', 'sfondo_oscurato', true);"
                                >{mastercom_label}Chiudi{/mastercom_label}</button>
                        <button type="button"
                                class="btn_pieno sfondo_scuro"
                                onclick="document.getElementById('form_container').operazione.value = 'inserisci_nota_disciplinare';
                                        document.getElementById('form_container').submit();
                                        $('#jqxGeneralLoader').jqxLoader('open');"
                                >{mastercom_label}Salva{/mastercom_label}</button>
                    </div>
                </div>
            </div>

            {*form cambio materia docente*}
            {if $funzione_inserisci_orario_registro == '1'}
                <div  class="div_scheda_generica"
                        id="cambio_materia_orario_docente"
                        style="position: fixed;
                              width: auto;
                              min-width: 500px;
                              left: 50%;
                              top: 50%;
                              transform: translate(-50%, -50%);
                              display: none;
                              z-index: 3;
                              overflow-y: auto;"
                    >
                    <div align="center" style="width: 100%; height: 100%;">
                        <table width="95%" align="center">
                            <tr>
                                <td align="center" style="padding: 15px 10px 20px 10px; font-size: 120%;">
                                    <b>{mastercom_label}CAMBIA MATERIA IN ORARIO{/mastercom_label}</b><br>
                                    <input type='hidden' name='modifica_materia_orario[id_ora]' id='id_ora_modifica_materia_orario'>
                                    <input type='hidden' name='modifica_materia_orario[id_materia]' id='id_materia_modifica_materia_orario'>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" class='padding_cella_generica'>
                                    <div id="elenco_materie_classe"></div>
                                </td>
                            </tr>
                        </table>
                        <div class="padding_cella_generica">
                            <br>
                            <button type="button"
                                   class="btn_flat_indaco"
                                    onclick="apriChiudiPopup('cambio_materia_orario_docente', 'sfondo_oscurato', true);"
                                    >{mastercom_label}Chiudi{/mastercom_label}</button>
                        </div>
                    </div>
                </div>
            {/if}

            {if $salva_firme == 'SI'}
                <div align="center" style="padding: 20px 0px;" class="padding_cella_generica">
                    <button type="button"
                           class="btn_pieno sfondo_scuro"
                           style="margin-left:10px"
                            onclick="document.getElementById('form_container').operazione.value = 'salva_firme';
                                    document.getElementById('form_container').submit();
                                    $('#jqxGeneralLoader').jqxLoader('open');"
                            >{mastercom_label}Salva Firme Selezionate{/mastercom_label}</button>
                </div>
            {/if}
    </div>
    <input type='hidden' name='form_stato' value='{$form_stato}'>
    <input type='hidden' name='stato_principale' id='stato_principale' value='{$stato_principale}'>
    <input type='hidden' name='stato_secondario' id='stato_secondario' value='{$stato_secondario}'>
    <input type='hidden' name='tipo_visualizzazione' value='{$tipo_visualizzazione}'>
    <input type='hidden' name='operazione' value=''>
    <input type='hidden' name='id_ora_selezionata' value=''>
    <input type='hidden' name='id_firma_selezionata' value=''>
    <input type='hidden' name='id_argomento_selezionato' id='id_argomento_selezionato' value=''>
    <input type='hidden' name='id_classe_selezionata' value=''>
    <input type='hidden' name='id_classe' id='id_classe' value=''>
    <input type='hidden' name='current_user' id='current_user' value='{$current_user}'>
    <input type='hidden' name='current_key' id='current_key' value='{$current_key}'>
    <input type='hidden' id='db_key' value='{$db_key}'>
    <input type='hidden' id='tipo_utente' value='{$form_stato}'>
</form>