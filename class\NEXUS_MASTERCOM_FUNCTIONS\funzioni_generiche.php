<?php

/**
 * Funzioni di uso comune
 */
function encode($field) {
    //{{{ <editor-fold defaultstate="collapsed">
    // chr(26) = CTRL-Z
    $field = str_replace(
            ["\\", "\'", "'", chr(26), '"'], ['&#92;', '&#039;', '&#039;', " ", "&quot;"], $field
    );

    //$field = mb_convert_encoding($field, "HTML-ENTITIES", "UTF-8, ISO-8859-1, ISO-8859-5");
    $field = mb_convert_encoding($field, "HTML-ENTITIES", mb_detect_encoding($field, "UTF-8, ISO-8859-1, ISO-8859-5, ISO-8859-15", true));

    $result = preg_replace('~^(&([#a-zA-Z0-9]);)~', htmlentities('${1}', ENT_QUOTES), $field);

    return $result;
    //}}} </editor-fold>
}

function decode($field) {
    //{{{ <editor-fold defaultstate="collapsed">
    if (!is_array($field) && strlen($field) > 0) {
        return mb_convert_encoding($field, "UTF-8", "HTML-ENTITIES");
    } else {
        return $field;
    }
    //}}} </editor-fold>
}

function safe_encode($field) {
    //{{{ <editor-fold defaultstate="collapsed">
    $trans = get_html_translation_table(HTML_ENTITIES, ENT_QUOTES);

    return strtr($field, $trans);
    //}}} </editor-fold>
}

function safe_decode($field) {
    //{{{ <editor-fold defaultstate="collapsed">
    $trans = get_html_translation_table(HTML_ENTITIES, ENT_QUOTES);
    $trans = array_flip($trans);
    $value = strtr($field, $trans);

    return $value;
    //}}} </editor-fold>
}

function check_compatible_browser() {
    //{{{ <editor-fold defaultstate="collapsed">
    $user_agent = $_SERVER['HTTP_USER_AGENT'];
    if (
            stripos($user_agent, 'Firefox') === false
            and
            stripos($user_agent, 'Shiretoko') === false
    ) {
        //echo "<!--* $user_agent *--!>";
        //return 'n';
    } else {
        //return 'y';
    }
    //}}} </editor-fold>
}

function safestrtotime($strInput) {
    //{{{ <editor-fold defaultstate="collapsed" desc="sostituto di mktime che fa andare anche i numeri inferiori al 1970">
    $array_data = explode("/", $strInput);
    $iVal = -1;

    for ($i = 1900; $i <= 1969; $i++) {
        # Check for this year string in date
        $strYear = (string) $i;
        if (!(strpos($strInput, $strYear) === false)) {
            $replYear = $strYear;
            $yearSkew = 1970 - $i;
            $strInput = str_replace($strYear, "1970", $strInput);
        }
    }

    $iVal = strtotime($strInput);

    if ($yearSkew > 0) {
        $numSecs = (60 * 60 * 24 * 365 * $yearSkew);
        $iVal = $iVal - $numSecs;
        $numLeapYears = 0;        # Work out number of leap years in period
        for ($j = $replYear; $j <= 1969; $j++) {
            $thisYear = $j;
            $isLeapYear = false;
            # Is div by 4?
            if (($thisYear % 4) == 0) {
                $isLeapYear = true;
            }
            # Is div by 100?
            if (($thisYear % 100) == 0) {
                $isLeapYear = false;
            }
            # Is div by 1000?
            if (($thisYear % 1000) == 0) {
                $isLeapYear = true;
            }
            if ($isLeapYear == true) {
                $numLeapYears++;
            }
        }
        if (
                (intval($array_data[0]) > 2) &&
                ((intval($array_data[2]) % 4) == 0)
        ) {
            $numLeapYears--;
        }
        $iVal = $iVal - (60 * 60 * 24 * $numLeapYears);
    }
    $iVal += 7261;

    return $iVal;
    //}}} </editor-fold>
}

function setta_variabile_sessione($id_utente, $nome_parametro, $valore_parametro) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Registro una variabile nella sessione corrente">
    global $db_key, $db_official;

    $id_utente = (int) $id_utente;
    $nome_parametro = safe_encode($nome_parametro);

    $db_key_temp = $db_key;
    $db_key = $db_official;
    $valore_array = $valore_parametro;
    //rimesso il safe_encode perchè non va importazione orario, quindi capire nel caso come gestirlo
    if (is_array($valore_parametro)) {
        $valore_parametro = safe_encode(serialize($valore_parametro));
    } elseif (strlen($valore_parametro) > 0) {
        $valore_parametro = safe_encode(serialize(str_replace('\\', '/', $valore_parametro)));
    }

        file_put_contents("/tmp/valore_parametro", print_r($valore_array,true));

    $query = "SELECT id_sessione FROM sessioni
              WHERE	id_utente = '{$id_utente}'
                AND	parametro = '{$nome_parametro}'";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        if (is_null($valore_parametro)) {
            $query_int = "DELETE FROM sessioni
						  WHERE id_sessione = '" . pg_fetch_result($result, 0, "id_sessione") . "'";
        } else {
            $query_int = "UPDATE sessioni SET
                            valore = '{$valore_parametro}'
                          WHERE	id_sessione = '" . pg_fetch_result($result, 0, "id_sessione") . "'";
        }

        pgsql_query($query_int) or die("Invalid $query_int");
    } else {
        if (!is_null($valore_parametro)) {
            $query_int = "INSERT INTO sessioni (
								id_utente,
								parametro,
								valore
							) VALUES (
								" . $id_utente . ",
								'" . $nome_parametro . "',
								'" . $valore_parametro . "'
							)";
            pgsql_query($query_int) or die("Invalid $query_int");
        }
    }
//    if($valore_array['idProgrammazSidi'] == 18979)
//    {
        file_put_contents("/tmp/query_int", print_r($query_int,true));
//    }
    $db_key = $db_key_temp;
    //}}} </editor-fold>
}

function leggi_variabile_sessione($id_utente, $nome_parametro) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Recupero il valore di una variabile dalla sessione corrente">
    $id_utente = (int) $id_utente;
    $nome_parametro = encode($nome_parametro);

    global $db_key, $db_official;

    $db_key_temp = $db_key;
    $db_key = $db_official;

    $query = "SELECT valore	FROM sessioni
              WHERE	id_utente = {$id_utente}
                AND	parametro = '{$nome_parametro}'";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $valore_parametro = pg_fetch_result($result, 0, "valore");

        $valore_parametro = unserialize(safe_decode($valore_parametro));

        $db_key = $db_key_temp;

        return $valore_parametro;
    } else {
        $db_key = $db_key_temp;

        return null;
    }
    //}}} </editor-fold>
}

function svuota_sessione($id_utente) {
    //{{{ <editor-fold defaultstate="collapsed" desc="svuoto la sessione corrente">
    global $db_key, $db_official;

    $id_utente = (int) $id_utente;

    $db_key_temp = $db_key;
    $db_key = $db_official;

    $query = "DELETE FROM sessioni WHERE id_utente = {$id_utente}";

    pgsql_query($query) or die("Invalid $query");
    $db_key = $db_key_temp;

    return null;
    //}}} </editor-fold>
}

/**
 * Valuta se un'elemento è l'ultimo di un'array, per uso in un foreach
 *
 * @param type $array
 * @param string $key
 * @return boolean
 */
function last(&$array, $key) {
    end($array);

    return $key === key($array);
}

function sksort(&$array, $subkey = "id", $sort_ascending = false) {
    //{{{ <editor-fold defaultstate="collapsed" desc="funzione per ordinare un array secondo una chiave interna">
    /*
      esempio:
      $info = array(
      "peter" => array(
      "age" => 21,
      "gender" => "male"
      ),
      "john"  => array(
      "age" => 19,
      "gender" => "male"
      ),
      "mary" => array(
      "age" => 20,
      "gender" => "female"
      )
      );
      sksort($info, "age"); -> ordinamento decrescente
      sksort($info, "age", true);  -> ordinamento crescente
     */
    if (count($array)) {
        $temp_array[key($array)] = array_shift($array);
    }

    foreach ($array as $key => $val) {
        $offset = 0;
        $found = false;

        foreach ($temp_array as $tmp_key => $tmp_val) {
            if (!$found and strtolower($val[$subkey]) > strtolower($tmp_val[$subkey])) {
                $temp_array = array_merge(
                        (array) array_slice($temp_array, 0, $offset), [$key => $val], array_slice($temp_array, $offset)
                );
                $found = true;
            }
            $offset++;
        }

        if (!$found) {
            $temp_array = array_merge($temp_array, [$key => $val]);
        }
    }

    if ($sort_ascending) {
        $array = array_reverse($temp_array);
    } else {
        $array = $temp_array;
    }
    //}}} </editor-fold>
}

function ordina_array($array, $nome_campo1, $nome_campo2 = '', $nome_campo3 = '') {
    //{{{ <editor-fold defaultstate="collapsed" desc="funzione per ordinare un array bidimensionale di righe">
    $array_finale = [];

    while (count($array) > 0) {
        $valore1 = $valore2 = $valore3 = '##@@##';

        foreach ($array as $key => $row) {
            //{{{ <editor-fold defaultstate="collapsed" desc="trovo il record ordinato dal più piccolo al più grande in base ai campi definiti">
            if ($valore1 == '##@@##') {
                $valore1 = $row[$nome_campo1];
                $valore2 = $row[$nome_campo2];
                $valore3 = $row[$nome_campo3];
                $elemento = $key;
            } elseif (strtoupper($valore1) > strtoupper($row[$nome_campo1])) {
                $valore1 = $row[$nome_campo1];
                $valore2 = $row[$nome_campo2];
                $valore3 = $row[$nome_campo3];
                $elemento = $key;
            } elseif (strtoupper($valore1) == strtoupper($row[$nome_campo1])) {
                if ($nome_campo2 != '') {
                    if ($valore2 == '##@@##') {
                        $valore2 = $row[$nome_campo2];
                        $valore3 = $row[$nome_campo3];
                        $elemento = $key;
                    } elseif (strtoupper($valore2) > strtoupper($row[$nome_campo2])) {
                        $valore2 = $row[$nome_campo2];
                        $valore3 = $row[$nome_campo3];
                        $elemento = $key;
                    } elseif (strtoupper($valore2) == strtoupper($row[$nome_campo2])) {
                        if ($nome_campo3 != '') {
                            if ($valore3 == '##@@##') {
                                $valore3 = $row[$nome_campo3];
                                $elemento = $key;
                            } elseif (strtoupper($valore3) > strtoupper($row[$nome_campo3])) {
                                $valore3 = $row[$nome_campo3];
                                $elemento = $key;
                            } elseif (strtoupper($valore3) == strtoupper($row[$nome_campo3])) {
                                $valore3 = $row[$nome_campo3];
                                $elemento = $key;
                            }
                        } else {
                            $valore2 = $row[$nome_campo2];
                            $elemento = $key;
                        }
                    }
                } else {
                    $valore1 = $row[$nome_campo1];
                    $elemento = $key;
                }
            }
            //}}} </editor-fold>
        }

        $array_temp = [];

        foreach ($array as $key => $row) {
            if ($key == $elemento) {
                $array_finale[] = $row;
            } else {
                $array_temp[] = $row;
            }
        }

        $array = $array_temp;
    }

    return $array_finale;
    //}}} </editor-fold>
}

function sort_by_nome($a,$b)
{
	//Usata nella sezione include_tabellone_pagelle per i nomi degli insegnanti
    return strcmp($a["nome"], $b["nome"]);
}

function sort_by_cognome($a,$b)
{
	//Usata nella sezione comunicazioni per i nomi degli studenti
    return strcmp($a["nome_completo"], $b["nome_completo"]);
}

function sort_by_credito_residuo($a,$b)
{
	//Usata nella sezione mense per i crediti mensa residui degli studenti
    if ($a['credito_residuo'] == $b['credito_residuo'])
    {
        return 0;
    }
    return ($a['credito_residuo'] < $b['credito_residuo']) ? -1 : 1;
}

function sort_by_codice($a, $b) {
    return strcmp($a["codice"], $b["codice"]);
}

function secondi_a_tempo($secondi) {

    $sec_minuto = 60;
    $sec_ora  	= 3600;
    $sec_giorno = 86400;


    $giorni = floor($secondi / $sec_giorno);
	$secondi = $secondi % $sec_giorno;
	$ore = floor($secondi / $sec_ora);
	$secondi = $secondi % $sec_ora;
	$minuti = floor($secondi / $sec_minuto);
	$secondi = ceil($secondi % $sec_minuto);

	$testo = '';

	if (intval($giorni) > 1)
    {
		$testo .= $giorni . " giorni ";
	}
    elseif (intval($giorni) == 1)
    {
		$testo .= $giorni . " giorno ";
	}

	if (intval($ore) > 1)
    {
		$testo .= $ore . " ore ";
	}
    elseif (intval($ore) == 1)
    {
		$testo .= $ore . " ora ";
	}

	if (intval($minuti) > 1)
    {
		$testo .= $minuti. " minuti ";
	}
    elseif (intval($minuti) == 1)
    {
		$testo .= $minuti . " minuto ";
	}

	if (intval($secondi) > 1)
    {
		$testo .= $secondi.  " secondi ";
	}
    elseif (intval($secondi) == 1)
    {
		$testo .= $secondi. " secondo ";
	}

    $obj = array(
		't' => $testo,
        'd' => (int) $giorni,
        'h' => (int) $ore,
        'm' => (int) $minuti,
        's' => (int) $secondi,
    );
    return $obj;
}

function isMobile() {
    // ritorna true se trova che il client e' un dispositivo mobile, altrimenti false
    return preg_match("/(android|webos|avantgo|iphone|ipad|ipod|blackberry|iemobile|bolt|boost|cricket|docomo|fone|hiptop|mini|opera mini|kitkat|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i", $_SERVER["HTTP_USER_AGENT"]);
}

function formatBytes($size, $precision = 2)
{
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');

    return round(pow(1024, $base - floor($base)), $precision) . $suffixes[floor($base)];
}

/*
 * Funzione per recuperare l'id_parente partendo dal suo utente
 * Utilizzata per le operazioni effettuate dai genitori da app e sito genitori
 */
function aggiorna_comunicazione_password($id, $tipo_utente, $time, $current_user)
{
 	//{{{ <editor-fold defaultstate="collapsed" desc="Aggiorna la data di comunicazione della password di un utente, studente o genitore">
    // U = Utente
    // G = Genitore
    // S = Studente
    if ($id > 0)
    {
        switch ($tipo_utente)
        {
            case 'U':
                $tabella = 'utenti';
                $id_sql = 'id_utente';
                $cosa = "COMUNICATE CREDENZIALI ALL'UTENTE CON ID {$id}";
                break;
            case 'G':
                $tabella = 'parenti';
                $id_sql = 'id_parente';
                $cosa = "COMUNICATE CREDENZIALI AL PARENTE CON ID {$id}";
                break;
            case 'S':
                $tabella = 'studenti';
                $id_sql = 'id_studente';
                $cosa = "COMUNICATE CREDENZIALI ALLO STUDENTE CON ID {$id}";
                break;
        }
        $query = "UPDATE {$tabella}
                    SET data_comunicazione_password = {$time}
                    WHERE {$id_sql} = {$id}
                    ";

        pgsql_query($query);

        inserisci_log_storico($current_user, 'GESTIONE_COMUNICAZIONI', $cosa);

        return true;
    }
    else
    {
        return false;
    }
    //}}} </editor-fold>
}

function estrai_dati_flussi_sidi($tipo){
    $dati = [];

    switch ($tipo) {
        case 'navali':
            $dati = [
                "codici_indirizzi"          =>  ['ITCI', 'ITCN', 'ITAI'],
                "classi"                    =>  [3, 4, 5],
                "codici_materie"            =>  ['I011', 'I028', 'I040', 'I043', 'I070', 'I128', 'I158', 'I159', 'I243', 'I251', 'I555', 'I666', '9999', 'I173'],
                "nome_variabile_sessione"   =>  'abbinamenti_sidi_navali'
            ];
            break;
        default:
            break;
    }

    return $dati;
}

function prepara_json_per_templates(&$element, $index)
{
    $element = str_replace('"', '\"', $element);
}

/*
 * funzione per ordinare ricorsivamente una struttura
 * $cmp_function : funzione usata per comparare i dati
 * $check_rec_data : nome campo da checkare per la ricorsione
 */
function recursive_uasort(&$array, $cmp_function, $check_rec_data)
{
    foreach ($array as $id => $dati)
    {
        if (is_array($dati[$check_rec_data]))
        {
            recursive_uasort($array[$id][$check_rec_data], $cmp_function, $check_rec_data);
        }
    }

    uasort($array, $cmp_function);
}

function GUID()
{
    if (function_exists('com_create_guid') === true) {
        return trim(com_create_guid(), '{}');
    }

    return sprintf('%04X%04X-%04X-%04X-%04X-%04X%04X%04X', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(16384, 20479), mt_rand(32768, 49151), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535));
}

/*
La funzione verifica che la sessione non sia stata ricaricata, in quel caso vuota la variabile $operazione per bloccare le operazioni sul registro.
Funziona solo per utenti
*/
function verifica_sessione($current_user, $token, &$operazione){
    $old_token = leggi_variabile_sessione($current_user, 'SESSION_TOKEN');

    if ($old_token >= $token){
        $operazione = "";
    } else {
        setta_variabile_sessione($current_user, 'SESSION_TOKEN', $token);
    }
}

/*
La funzione verifica se una password è stata cambiata confrontando quella in chiaro con quella codificata
*/
function password_cambiata($password_chiaro, $password_codificata){
    if (
        password_verify($password_chiaro, $password_codificata)
        ||
        MT\Utils\Pbkdf2::isValid($password_chiaro, $password_codificata)
    ) {
        return false;
    } else {
        return true;
    }
}