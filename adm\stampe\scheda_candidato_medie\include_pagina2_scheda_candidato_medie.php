<?php

$mat_anno = explode('/',$anno_scolastico);
$anno_inizio = $mat_anno[0];

$prova_italiano = "Prova scelta n. " . $studente['tipo_voto_esame_medie_italiano'];
switch ($studente['tipo_voto_esame_medie_italiano'])
{
    case '1':
        $prova_italiano = "traccia 1";
        break;
    case '2':
        $prova_italiano = "traccia 2";
        break;
    case '3':
        $prova_italiano = "traccia 3";
        break;
    case '4':
        $prova_italiano = "testo argomentativo";
        break;
    case '5':
        $prova_italiano = "testo espositivo (relazione)";
        break;
    case '6':
        $prova_italiano = "testo narrativo o descrittivo (lettera,diario)";
        break;
    case '7':
        $prova_italiano = "traccia di comprensione e sintesi di un testo letterario, divulgativo, scientifico";
        break;
    default :
        break;
}


$prova_inglese = [];
$tipi_esame_ing[0] = $studente['tipo_voto_esame_medie_inglese'];
if(intval($anno_inizio >= 2022)) {
    $tipi_esame_ing = explode(',', $studente['tipo_voto_esame_medie_inglese']);
}
foreach ($tipi_esame_ing as $tipo) {
    switch ($tipo)
    {
        case '1':
            $prova_inglese[] = "lettera";
            break;
        case '2':
            $prova_inglese[] = "comprensione testo";
            break;
        case '3':
            $prova_inglese[] = "dialogo";
            break;
        case '4':
            $prova_inglese[] = "questionario a risposta chiusa o aperta";
            break;
        case '5':
            $prova_inglese[] = "completamento di un testo";
            break;
        case '6':
            $prova_inglese[] = "riordino, riscrittura o trasformazione di un testo";
            break;
        case '7':
            $prova_inglese[] = "elaborazione di un dialogo";
            break;
        case '8':
            $prova_inglese[] = "elaborazione di una lettera o mail personale";
            break;
        case '9':
            $prova_inglese[] = "sintesi di un testo";
            break;
        case '10':
            $prova_inglese[] = "prova combinata";
            break;
        default :
            break;
    }
}
if (empty($prova_inglese)) {
    $prova_inglese = 'prova n ...';
} else {
    $prova_inglese = implode(', ', $prova_inglese);
}


$prova_lingua_straniera = [];
$tipi_esame_sec_lin[0] = $studente['tipo_voto_esame_medie_seconda_lingua'];
if(intval($anno_inizio >= 2022)) {
    $tipi_esame_sec_lin = explode(', ', $studente['tipo_voto_esame_medie_seconda_lingua']);
}
foreach ($tipi_esame_sec_lin as $tipo) {
    switch ($tipo)
    {
        case '1':
            $prova_lingua_straniera[] = "lettera";
            break;
        case '2':
            $prova_lingua_straniera[] = "comprensione testo";
            break;
        case '3':
            $prova_lingua_straniera[] = "dialogo";
            break;
        case '4':
            $prova_lingua_straniera[] = "questionario a risposta chiusa o aperta";
            break;
        case '5':
            $prova_lingua_straniera[] = "completamento di un testo";
            break;
        case '6':
            $prova_lingua_straniera[] = "riordino, riscrittura o trasformazione di un testo";
            break;
        case '7':
            $prova_lingua_straniera[] = "elaborazione di un dialogo";
            break;
        case '8':
            $prova_lingua_straniera[] = "elaborazione di una lettera o mail personale";
            break;
        case '9':
            $prova_lingua_straniera[] = "sintesi di un testo";
            break;
        case '10':
            $prova_lingua_straniera[] = "prova combinata";
            break;
        default :
            break;
    }
}
if (empty($prova_lingua_straniera)) {
    $prova_lingua_straniera = 'prova n ...';
} else {
    $prova_lingua_straniera = implode(', ', $prova_lingua_straniera);
}

if (
        false &&
        $anno_scolastico != '2020/2021'
        && $nome_scuola != 'OSDB'
    )
{
    if($studente['giudizio_prove_scritte_scuole_medie'] != '')
    {
        $pdf->SetFont('helvetica', 'B', 15);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "GIUDIZI SULLE PROVE SCRITTE", 0, 1, 'C');
        $pdf->Ln(30);
        //italiano
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "ITALIANO (" . $prova_italiano . ")", 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto prova " . $studente['voto_esame_medie_italiano'], 0, 1, 'L');
        //matematica
        $pdf->Ln(10);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "MATEMATICA (traccia n. " . $studente['numero_quesiti_esame_medie_matematica'] . ")", 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto prova " . $studente['voto_esame_medie_matematica'], 0, 1, 'L');
        //seconda lingua straniera
        $pdf->Ln(10);
        $pdf->SetFont('helvetica', 'B', 12);
        if ($anno_scolastico != '2021/2022')
        {
            if(intval($anno_inizio >= 2017))
            {
                $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Lingue Straniera:  INGLESE / FRANCESE / TEDESCO / SPAGNOLO (" . $prova_lingua_straniera . ")", 0, 1, 'L');
            }
            else
            {
                $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Lingua Straniera:  FRANCESE / TEDESCO / SPAGNOLO (" . $prova_lingua_straniera . ")" , 0, 1, 'L');
            }
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto prova " . $studente['voto_esame_medie_seconda_lingua'], 0, 1, 'L');
            if(intval($anno_inizio < 2017))
            {
                //lingua straniera inglese
                $pdf->Ln(10);
                $pdf->SetFont('helvetica', 'B', 12);
                $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Lingua Straniera:  INGLESE ", 0, 1, 'L');
                $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto prova " . $studente['voto_esame_medie_inglese'], 0, 1, 'L');
                 //invalsi
                $pdf->Ln(10);
                $pdf->SetFont('helvetica', 'B', 12);
                $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Prova Nazionale INVALSI", 0, 1, 'L');
                $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto finale: " . $studente['voto_esame_medie_invalsi_finale'], 0, 1, 'L');
            }
        }
        //giudizio prove scritte
        $pdf->Ln(10);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, 'GIUDIZIO PROVE SCRITTE', 0, 1, 'C');
        $pdf->Ln(10);
        $pdf->SetFont('helvetica', '', 8);
        $pdf->MultiCell(150, $altezza_cella, $studente['giudizio_prove_scritte_scuole_medie'], 0, 'L');

    }
    else
    {

        $pdf->SetFont('helvetica', 'B', 15);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "GIUDIZI SULLE PROVE SCRITTE", 0, 1, 'C');
        $pdf->Ln(30);
        //italiano
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "ITALIANO ($prova_italiano)", 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto prova " . $studente['voto_esame_medie_italiano'], 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 12);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Giudizio prova:   (Vedi scheda allegata alla prova) ", 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
        //matematica
        $pdf->Ln(10);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "MATEMATICA (traccia n. " . $studente['numero_quesiti_esame_medie_matematica'] . ")" , 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto prova " . $studente['voto_esame_medie_matematica'], 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 12);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Giudizio prova:   (Vedi scheda allegata alla prova) ", 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');

        //seconda lingua straniera
        $pdf->Ln(10);
        $pdf->SetFont('helvetica', 'B', 12);
        if ($anno_scolastico != '2021/2022')
        {
            if(intval($anno_inizio >= 2017))
            {
               $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Lingua Straniera:  INGLESE / FRANCESE / TEDESCO / SPAGNOLO (prova num " . $studente['tipo_voto_esame_medie_seconda_lingua'] . ")" , 0, 1, 'L');
            }
            else
            {
               $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Lingua Straniera:  FRANCESE / TEDESCO / SPAGNOLO (prova num " . $studente['tipo_voto_esame_medie_seconda_lingua'] . ")" , 0, 1, 'L');
            }

            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto prova " . $studente['voto_esame_medie_seconda_lingua'], 0, 1, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Giudizio prova:   (Vedi scheda allegata alla prova) ", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
        }
        if(intval($anno_inizio < 2017))
        {
            //lingua straniera inglese
            $pdf->Ln(10);
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Lingua Straniera:  INGLESE (prova num " . $studente['tipo_voto_esame_medie_inglese'] . ")", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto prova " . $studente['voto_esame_medie_inglese'], 0, 1, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Giudizio prova:   (Vedi scheda allegata alla prova) ", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');

            //invalsi
            $pdf->Ln(10);
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Prova Nazionale INVALSI", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Voto finale: " . $studente['voto_esame_medie_invalsi_finale'], 0, 1, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "Giudizio prova:   (Vedi scheda allegata alla prova) ", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "___________________________________________________________________________________________________________ ", 0, 1, 'L');
        }
    }
}
else
{ //20/21

    $pdf->SetX(10);
    $txt_voto_ammissione = "Il candidato è stato ammesso all'esame con il seguente <b>voto di ammissione: {$studente['voto_ammissione_medie']}</b>";

    $pdf->SetFont('helvetica', '', 12);
    $pdf->writeHTMLCell($larghezza_max_celle, 0, '', '', $txt_voto_ammissione, 0, 1);
    $pdf->Ln(2);
    if (
            true
//            $nome_scuola == 'OSDB'
        )
    {
        $voti_pagellina = estrai_voti_pagellina_studente_multi_classe($id_classe, 29, $studente['id_studente']);
        $stampa_val = '';
        foreach ($voti_pagellina as $voto_pagellina)
        {
            if (is_array($voto_pagellina['campi_liberi']))
            {
                foreach ($voto_pagellina['campi_liberi'] as $campo_libero)
                {
                    if (strpos(strtoupper($campo_libero['nome']), 'GIUDIZIO SCRUTINIO') !== false)
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $stampa_val .= $value;
                        }
                    }
                }
            }

        }
    //    $pdf->SetX($x_base);
        $pdf->MultiCell($larghezza_max_celle, $altezza_celle, $stampa_val . "\n" , 0, 'J', $fill=0, $ln=1, $x='', $y='', $reseth=true, $stretch=0, $ishtml=true, $autopadding=true, $maxh=0);

        $pdf->Ln(6);
//        if ($stampa_pag2_giudizio_si_no != 'NO') {
//            if ($stampa_pag2_giudizio == 'RIGHE') {
//                $pdf->CellFitScale($wp, 10, 'Giudizio finale: ', 0, 1);
//                $pdf->CellFitScale($wp, 10, '________________________________________________________________________________', 0, 1);
//                $pdf->CellFitScale($wp, 10, '________________________________________________________________________________', 0, 1);
//                $pdf->CellFitScale($wp, 10, '________________________________________________________________________________', 0, 1);
//            } else {
//                $pdf->MultiCell($larghezza_max_celle, $altezza_celle, "<b>Giudizio finale: </b>".$studente["giudizio_descrittivo_finale_esame_terza_media"] . "\n" , 0, 'J', $fill=0, $ln=1, $x='', $y='', $reseth=true, $stretch=0, $ishtml=true, $autopadding=true, $maxh=0);
//            }
//        }
    }

    $pdf->ln(15);
    if (($studente['consiglio_orientativo_trentino'] != '') && ($studente['consiglio_orientativo_trentino'] != 0))
    {
        $pdf->writeHTMLCell($larghezza_max_celle, 0, '', '', "Il consiglio di classe ha formulato inoltre il seguente <b>consiglio orientativo</b> sulle scelte successive:", 0, 1);
        // Estrazione consiglio orientativo
        $valori_consiglio_orientativo = estrai_valori_consiglio_orientativo_trentino($studente['consiglio_orientativo_trentino']);
        foreach ($valori_consiglio_orientativo as $valore)
        {
            $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, '-'.$valore['descrizione'], 0, 1, 'L');
        }
    }

    if($studente['id_consiglio_orientativo'] > 0)
    {
        $param_consigli = [];
        $elenco_consigli_orientativi = nextapi_call('/school/structure/consiglio_orientativo', 'GET', $param_consigli, $current_key);
        $consigli_orientativi_array = [];
        foreach($elenco_consigli_orientativi as $consiglio){
            $consigli_orientativi_array[$consiglio['id_consiglio_orientativo_template']] = $consiglio;
        }
        $descrizione_consiglio_orientativo = $consigli_orientativi_array[$studente['id_consiglio_orientativo']]['descrizione'];
        $pdf->writeHTMLCell($larghezza_max_celle, 0, '', '', "Il consiglio di classe ha formulato inoltre il seguente <b>consiglio orientativo</b> sulle scelte successive:", 0, 1);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, '-'.$descrizione_consiglio_orientativo, 0, 1, 'L');
    }


    if (
            true
//            $nome_scuola == 'OSDB'
        )
    {
//        $pdf->Ln(2);
//        $pdf->writeHTMLCell($larghezza_max_celle, 0, '', '', "Eventuale integrazione consiglio orientativo: ", 0, 1);

        $pdf->ln(25);
        $pdf->SetFont('helvetica', 'B', 15);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "PROVE D'ESAME", 0, 1, 'C');

        $pdf->ln(5);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->CellFitScale($larghezza_max_celle, $altezza_celle, "PROVE SCRITTE", 0, 1, 'L');
        $pdf->Ln(10);

        if(intval($anno_inizio >= 2022))
        {
            //italiano
            $pdf->SetFont('helvetica', '', 12);
//            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "ITALIANO ($prova_italiano)", 0, 1);
            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "ITALIANO ", 0, 1);
            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "<b>VALUTAZIONE: </b> ".$studente['voto_esame_medie_italiano'], 0, 1);
            if ($studente['giudizio_prove_scritte_scuole_medie'] != '') {
        //        $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', 'Giudizio: '.$studente['giudizio_prove_scritte_scuole_medie'], 0, 1);
                $pdf->MultiCell($larghezza_max_celle, $altezza_cella, 'Giudizio: '.$studente['giudizio_prove_scritte_scuole_medie'], 0, 'L');
            }
            //matematica
            $pdf->Ln(10);
//            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "MATEMATICA (Prova scelta n. " . $studente['numero_quesiti_esame_medie_matematica'] . ")", 0, 1);
            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "MATEMATICA ", 0, 1);
            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "<b>VALUTAZIONE: </b> ".$studente['voto_esame_medie_matematica'], 0, 1);
            if ($studente['giudizio_prove_scritte_mat_scuole_medie'] != '') {
        //        $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', 'Giudizio: '.$studente['giudizio_prove_scritte_mat_scuole_medie'], 0, 1);
                $pdf->MultiCell($larghezza_max_celle, $altezza_cella, 'Giudizio: '.$studente['giudizio_prove_scritte_mat_scuole_medie'], 0, 'L');
            }
            // inglese
            $pdf->Ln(10);

            if ($nome_scuola == 'OSDB')
            {
                $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "INGLESE ", 0, 1);
                $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "<b>VALUTAZIONE: </b> ".$studente['voto_esame_medie_inglese'], 0, 1);
                $pdf->MultiCell($larghezza_max_celle, $altezza_cella, 'Giudizio Inglese: '.$studente['giudizio_prove_scritte_ing_scuole_medie'], 0, 'L');
            }
            else
            {
                if($valle_aosta_abilitata == "SI"){
                    $pdf->SetFont('helvetica', '', 12);
                    $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "LINGUE STRANIERE", 0, 1);
                    $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "<b>VALUTAZIONE: </b> ", 0, 1);
                    $pdf->Ln(2);
                    $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, 20, '', "LINGUA INGLESE: ".$studente['voto_esame_medie_inglese'], 0, 1);
                    $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, 20, '', "LINGUA FRANCESE: ".$studente['voto_esame_medie_seconda_lingua'], 0, 1);
                    $pdf->Ln(2);
                    $pdf->MultiCell($larghezza_max_celle, $altezza_cella, 'Giudizio Inglese e Francese: '.$studente['giudizio_prove_scritte_ing_scuole_medie'], 0, 'L');
                }else{
                    $pdf->SetFont('helvetica', '', 12);
                    $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "LINGUE STRANIERE", 0, 1);
                    $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "<b>VALUTAZIONE: </b> ".$studente['voto_esame_medie_inglese'], 0, 1);
                    $pdf->Ln(2);
        //            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, 20, '', "INGLESE ($prova_inglese)", 0, 1);
        //            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, 20, '', "LINGUE STRANIERE ($prova_lingua_straniera)", 0, 1);
                    $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, 20, '', "INGLESE ", 0, 1);
                    $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, 20, '', "SECONDA LINGUA COMUNITARIA ", 0, 1);
                    $pdf->Ln(2);
                    if ($studente['giudizio_prove_scritte_ing_scuole_medie'] != '') {
                    $pdf->MultiCell($larghezza_max_celle, $altezza_cella, 'Giudizio Inglese e Lingua straniera: '.$studente['giudizio_prove_scritte_ing_scuole_medie'], 0, 'L');
                    }
                }
            }
        }
        else {
            //italiano
            $pdf->SetFont('helvetica', '', 12);
            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "ITALIANO ($prova_italiano)", 0, 1);
            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "<b>VALUTAZIONE: </b> ".$studente['voto_esame_medie_italiano'], 0, 1);
            //matematica
            $pdf->Ln(10);
            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "MATEMATICA (Prova scelta n. " . $studente['numero_quesiti_esame_medie_matematica'] . ")", 0, 1);
            $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', "<b>VALUTAZIONE: </b> ".$studente['voto_esame_medie_matematica'], 0, 1);

            $pdf->Ln(5);
    //        $pdf->writeHTMLCell($larghezza_max_celle, $altezza_celle, '', '', $studente['giudizio_prove_scritte_scuole_medie'], 0, 1);
            $pdf->MultiCell($larghezza_max_celle, $altezza_cella, $studente['giudizio_prove_scritte_scuole_medie'], 0, 'L');
        }


    }
}
