:root {

  /* Colori */
  --mastercom-dark-blue: #163253;
  --mastercom-light-blue: #2b9cef;
  --mastercom-yellow: #ecc509;
  --mastercom-pale-yellow: #fee888;

  --google-classroom-orange: #eda406;
  --google-classroom-yellow: #f5ba14;
  --google-classroom-green: #20a464;
  --google-classroom-lightgreen: #60be90;
  --google-classroom-orange-op20: #eda40633;
  --google-classroom-yellow-op20: #f5ba1433;
  --google-classroom-green-op20: #20a46433;
  --google-classroom-lightgreen-op20: #60be9033;

  /* Neumorphism */
  --nm: 6px 6px 16px 0 rgba(0, 0, 0, 0.2),
  -6px -6px 16px 0 rgba(255, 255, 255, 1);

  --nm-inset:inset 4px 4px 8px 0 rgba(0, 0, 0, 0.2),
  inset -4px -4px 8px 0 rgba(255, 255, 255, 0.8);
}

body {
    background-color : #AAAAAA;
    font-family : Verdana, Geneva, Arial, Helvetica, sans-serif;
    font-size: 15px;
    font-weight: bold;
    height: 100%;
}
pre {
    color : #CC0000;
}
table {
    border-collapse: collapse;
    font-size: medium;
    font-weight: normal;
}
form {
    margin-bottom: 0;
}
option {
    font-size: 10pt !important;
    color: black;
}
.stile_div {
    background-color:#DDDDFF;
    width:100%;
    height:100%;
    overflow: scroll;
}
h1 { color: #000000; font-size: 115%; font-weight: bold; text-align: center}
h2 { color: #000000; font-size: 100%; font-weight: bold; text-align: center}
h3 { color: #000000; font-size: 85%; font-weight: bold; text-align: center}
h4 { color: #000000; font-size: 80%; font-weight: bold; text-align: center}
h5 { color: #000000; font-size: 75%; font-weight: bold; text-align: center}
h6 { color: #000000; font-size: 70%; font-weight: bold; text-align: center}
td { color: #000000; font-size: 90%}
.area_class { cursor: pointer }
.nobordoimage { cursor:pointer}
.nobordotab {
    font-size: 10px;
    color: #000000;
    background-color: #DDDDFF;
    padding: 0px;
    border-width: 0px;
    border-spacing: 0px;
    border-style: solid;
    border-color: black;
}
.bordotab {
    font-size: 10px;
    color: #000000;
    background-color: #DDDDFF;
    padding: 0px;
    border-width: 1px;
    border-spacing: 1px;
    border-style: solid;
    border-color: #222;
    border-radius:3px;
    -moz-border-radius:3px;
    -webkit-border-radius:3px;
}
.bordotab {
	font-size: 10px;
	font-weight: bold;
	color: #000000;
}
.classetd {
    font-size: 10px;
    color: #000000;
    background-color: #BBBBFF;
}
.classetd_rossa {
    font-size: 10px;
    color: #000000;
    background-color: #FF0000;
}
.classetd_gialla {
    font-size: 10px;
    color: #000000;
    background-color: #FFFF00;
}
.classetd_verde {
    font-size: 10px;
    color: #000000;
    background-color: #00FF00;
}
.classetd_grigia {
    font-size: 10px;
    color: #000000;
    background-color: #AAAAAA;
}
.classetd a {
    font-size: 10px;
    color: #000099;
    display: block;
    position: relative;
    text-decoration: none;
}
.classetd a:hover {
    font-size: 10px;
    color: #CC0000;
    background-color: #CCFFFF;
    text-decoration: none;
    cursor: pointer;
}
.classetd_domenica {
    font-size: 10px;
    color: #CCCCCC;
    background-color: #CCCCCC;
}
.classesubmit {
    font-size: 10px;
    color: #0000ff;
    background-color: #BBBBFF;
}
.classesubmit_giust {
    font-size: 10px;
    color: #009900;
    background-color: #BBBBFF;
}
.classesubmit_non_giust {
    font-size: 10px;
    color: #CC0000;
    background-color: #BBBBFF;
}
.classesubmit_scritto,
.classesubmit_orale,
.classesubmit_pratico,
.classesubmit_unico {
    font-size: 10px;
    background-color: #BBBBFF;
}
.classesubmit_orale {
    color: #A59143;
}
.classesubmit_pratico {
    color: #0000AA;
}
.classesubmit_unico {
    color: #000000;
}
.classeselect_base {
    font-size: 10px;
    color: #000000;
    background-color: #FFFFFF;
}
.classeselect_small {
    font-size: 6px;
    color: #000000;
    background-color: #FFFFFF;
}
.anylinkcss {
    position:absolute;
    visibility: hidden;
    border:1px solid black;
    border-bottom-width: 1px;
    font-size: 8px;
    line-height: 15px;
    z-index: 1;
    background-color: #BBBBFF;
}
.anylinkcss a {
    width: 100%;
    display: block;
    text-indent: 3px;
    text-decoration: none;
    font-weight: bold;
    text-indent: 5px;
}
.anylinkcss a:hover {
    background-color: black;
    color: white;
    cursor: pointer;
}
.classe_td_in_div a {
    font-size: 8px;
    cursor: pointer;
}
.classe_td_in_div a:hover, a:link, a:visited, a:active {
    font-size: 8px;
    cursor: pointer;
}
.classe_td_fill a {
    font-size: 12px;
    color: #000000;
    cursor: pointer;
}
.classe_td_fill a:hover, a:link, a:visited, a:active {
    font-size: 12px;
    color: #000000;
    cursor: pointer;
}
.classe_td_empty a {
    font-size: 12px;
    color: #0000ff;
    cursor: pointer;
}
.classe_td_empty a:hover, a:link, a:visited, a:active {
    font-size: 12px;
    color: #0000ff;
    cursor: pointer;
}

/*{{{ Classi calendario */
.calendar_par_middle {
    left: 5%;
    width:100%;
}
table.calendar_table {
    table-layout: fixed;
    margin-left: auto;
    margin-right: auto;
    width: 80%;
}
COL.month_col {
    border-style: none;
    border-width: 0px;
    width: 70px;
    text-align: left;
    font-size: 10pt;
    font-weight: normal;
}
COL.calendar_col {
    width: 25px;
}
td.content_cell {
    border-style: solid;
    border-color: #050505;
    border-top-width: 0px;
    border-left-width: 0px;
    border-bottom-width: 0px;
    border-right-width: 1px;
    text-align: center;
    font-size: 10pt;
    font-weight: normal;
}
td.content_cell_not_existing {
    background-color:#CCCCCC;
    border-style: solid;
    border-color: #050505;
    border-top-width: 0px;
    border-left-width: 0px;
    border-bottom-width: 0px;
    border-right-width: 1px;
    text-align: center;
    font-size: 10pt;
    font-weight: normal;
}
a.cell_link {
    font-size: 10pt;
    color:#0000FF;
}
a.cell_redlink,
a.cell_redlink_base {
    font-size: 10pt;
    color:#ff0000;
}
a.cell_greenlink {
    font-size: 10pt;
    color:#00FF00;
}
.mypopup_int_table {
    border: 10px;
    font-size: 10pt;
    width: 100%;
}
.bgcolor_header {
    background-color: #aaaaff;
}
.bgcolor_sunday_dark,
.bgcolor_sunday_normal {
    background-color: #aaaaaa;
}
.bgcolor_dark {
    background-color: #d8e8e6;
}
.bgcolor_normal {
    background-color: #ffffff;
}
.calendar_col {
    border-style: solid;
    border-color: #050505;
    border-top-width: 0px;
    border-left-width: 0px;
    border-bottom-width: 0px;
    border-right-width: 1px;
    text-align: center;
}
/*}}}*/

/*{{{ Menu Popup */
.mypopup {
    position:absolute;
    visibility: hidden;
    border:1px solid #aaaaff;
    border-bottom-width: 1px;
    font:8px Verdana;
    line-height: 18px;
    z-index: 5;
    background-color: #ffffff;
    width: 250px;
}

.mypopup a {
    width: 100%;
    display: block;
    text-indent: 3px;
    text-decoration: none;
    font-weight: bold;
    text-indent: 5px;
}

.mypopup a:hover {
    background-color: #d8e8e6;
    color: black;
}

.link_hc_rosso {
    color: #FF0000;
    font-weight: 700;
    font-size: 14px;
}
.link_hc_normal {
    color: #000000;
    font-weight: 700;
    font-size: 12px;
}
.link_rosso {
    color: #FF0000;
    font-weight: 700;
    font-size: 14px;
}
.link_normal {
    color: #000000;
    font-weight: 700;
    font-size: 12px;
}
.link_small {
    color: #000000;
    font-weight: 700;
    font-size: 12px;
}
.link_ie_hc_rosso {
    color: #FF0000;
    font-weight: 700;
    font-size: 14px;
}
.link_ie_hc_normal {
    color: #000000;
    font-weight: 700;
    font-size: 12px;
}
.link_ie_rosso {
    color: #FF0000;
    font-weight: 700;
    font-size: 14px;
}
.link_ie_normal {
    color: #000000;
    font-weight: 700;
    font-size: 12px;
}
/*}}}*/

/*{{{ tabellone generico javascript */
.div_tabellone_main {
    background-color: #8888ff;
    border: 1px solid #000000;
    position: fixed;
    top: 1px;
    bottom: 1px;
    left: 1px;
    right: 1px;
    z-index: 2;
    overflow:auto;
    font-weight: bold;
}
.div_tabellone_header {
    background-color: #8888ff;
    position: absolute;
}
.div_tabellone_top {
    background-color: #ddddff;
    border-top: 2px solid #000000;
    border-left: 2px solid #000000;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    text-align: center;
    position: absolute;
}
.div_tabellone_int_riga_clear {
    background-color: #ddddff;
    border-top: 1px solid #000000;
    border-left: 2px solid #000000;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    overflow: hidden;
    position: absolute;
}
.div_tabellone_int_riga_dark {
    background-color: #aaaaff;
    border-top: 1px solid #000000;
    border-left: 2px solid #000000;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    overflow: hidden;
    position: absolute;
}
.div_tabellone_int_colonna {
    background-color: #ddddff;
    border-left: 0px hidden;
    border-top: 1px solid #000000;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    text-align: center;
    position: absolute;
}
.div_tabellone_cella_clear {
    background-color: #ddddff;
    border-top: 1px solid #000000;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_tabellone_cella_dark {
    background-color: #aaaaff;
    border-top: 1px solid #000000;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_tabellone_cella_arancio {
    background-color: #ffbd82;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_tabellone_cella_blu {
    background-color: #82b1ff;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_tabellone_cella_verde {
    background-color: #9fff82;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_tabellone_cella_select {
    background-color: #ffffff;
}
.td_header_popup {
    background-color: #bbbbff;
    cursor: move;
    font-size: 14px;
    font-weight: bold;
    z-index: 45;
}
.td_normal_popup {
    background-color: #ffffff;
    z-index: 45;
}

/*{{{ Popup tabellone */
.popup_tabellone {
    visibility: hidden;
    position:absolute;
    border:1px solid #aaaaff;
    border-bottom-width: 1px;
    font: 8px Verdana;
    line-height: 18px;
    z-index: 50;
}
.popup_tabellone_shadow {
    position:absolute;
    top:5px;
    left:5px;
    bottom:-5px;
    right:-5px;
    border:1px solid #aaaaff;
    border-bottom-width: 1px;
    z-index: -5;
    border-top: 1px hidden;
    border-left: 1px hidden;
    border-right: 1px hidden;
    border-bottom: 1px hidden;
    background-color: #000000;
    filter: alpha(opacity=30);
    opacity: 0.3;
}
.popup_tabellone a {
    width: 100%;
    display: block;
    text-indent: 3px;
    text-decoration: none;
    font-weight: bold;
    text-indent: 5px;
}
.popup_tabellone a:hover {
    background-color: #d8e8e6;
    color: black;
}
/*}}}*/

/*}}}*/


/*{{{ tabellone generico javascript per ie */
.div_ie_tabellone_main {
    background-color: #aaaaff;
    border: 1px solid #000000;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    z-index: 10;
    overflow:auto;
    font-weight: bold;
}
.div_ie_tabellone_header {
    background-color: #8888ff;
    position: absolute;
}
.div_ie_tabellone_top {
    background-color: #ddddff;
    border-top: 1px solid #000000;
    border-left: 1px solid #000000;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    text-align: center;
    position: absolute;
}
.div_ie_tabellone_int_riga_clear {
    background-color: #ddddff;
    border-top: 0px hidden;
    border-left: 1px solid #000000;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    overflow: hidden;
    position: absolute;
}
.div_ie_tabellone_int_riga_dark {
    background-color: #aaaaff;
    border-top: 0px hidden;
    border-left: 1px solid #000000;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    overflow: hidden;
    position: absolute;
}
.div_ie_tabellone_int_colonna {
    background-color: #ddddff;
    border-left: 0px hidden;
    border-top: 1px solid #000000;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    text-align: center;
    position: absolute;
}
.div_ie_tabellone_cella_clear {
    background-color: #ddddff;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_ie_tabellone_cella_dark {
    background-color: #aaaaff;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_ie_tabellone_cella_arancio {
    background-color: #ffbd82;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_ie_tabellone_cella_blu {
    background-color: #82b1ff;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_ie_tabellone_cella_verde {
    background-color: #9fff82;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_ie_tabellone_cella_select {
    background-color: #ffffff;
}
.td_ie_header_popup {
    background-color: #bbbbff;
    cursor: move;
    font-size: 14px;
    font-weight: bold;
    z-index: 45;
}
.td_ie_normal_popup {
    background-color: #ffffff;
    z-index: 45;
}

/*{{{ Popup tabellone */
.popup_ie_tabellone {
    visibility: hidden;
    position:absolute;
    display: none;
    height:110%;
    border:1px solid #aaaaff;
    border-bottom-width: 1px;
    font: 8px Verdana;
    line-height: 18px;
    z-index: 50;
}
.popup_ie_tabellone_shadow {
    position:absolute;
    top:5px;
    left:5px;
    width:100%;
    height:100%;
    border:1px solid #aaaaff;
    border-bottom-width: 1px;
    z-index: -5;
    border-top: 1px hidden;
    border-left: 1px hidden;
    border-right: 1px hidden;
    border-bottom: 1px hidden;
    background-color: #000000;
    filter: alpha(opacity=30);
    opacity: 0.3;
}
.popup_ie_tabellone a {
    width: 100%;
    display: block;
    text-indent: 3px;
    text-decoration: none;
    font-weight: bold;
    text-indent: 5px;
}
.popup_ie_tabellone a:hover {
    background-color: #d8e8e6;
    color: black;
}
/*}}}*/

/*}}}*/

/*{{{ tabellone generico javascript HC */
.div_hc_tabellone_main {
    background-color: #ffffff;
    border: 1px solid #000000;
    position: fixed;
    top: 1px;
    bottom: 1px;
    left: 1px;
    right: 1px;
    z-index: 2;
    overflow:auto;
    font-weight: bold;
}
.div_hc_tabellone_header {
    background-color: #ffffff;
    position: absolute;
}
.div_hc_tabellone_top {
    background-color: #ffffff;
    border-top: 1px solid #000000;
    border-left: 2px solid #000000;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    text-align: center;
    position: absolute;
}
.div_hc_tabellone_int_riga_clear {
    background-color: #ffffff;
    border-top: 1px solid #000000;
    border-left: 2px solid #000000;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    overflow: hidden;
    position: absolute;
}
.div_hc_tabellone_int_riga_dark {
    background-color: #ffffff;
    border-top: 1px solid #000000;
    border-left: 2px solid #000000;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    overflow: hidden;
    position: absolute;
}
.div_hc_tabellone_int_colonna {
    background-color: #ffffff;
    border-left: 0px hidden;
    border-top: 1px solid #000000;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    text-align: center;
    position: absolute;
}
.div_hc_tabellone_cella_clear {
    background-color: #ffffff;
    border-top: 1px solid #000000;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    font-size: 11px;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
}
.div_hc_tabellone_cella_clear a {
    font-size: 12px;
    font-weight: bold;
}
.div_hc_tabellone_cella_dark {
    background-color: #dddddd;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_hc_tabellone_cella_arancio {
    background-color: #ffffff;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_hc_tabellone_cella_blu {
    background-color: #ffffff;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_hc_tabellone_cella_verde {
    background-color: #ffffff;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 2px solid #000000;
    border-bottom: 2px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_hc_tabellone_cella_select {
    background-color: #ffffff;
}
.td_hc_header_popup {
    background-color: #ffffff;
    cursor: move;
    font-size: 14px;
    font-weight: bold;
    z-index: 45;
}
.td_hc_normal_popup {
    background-color: #ffffff;
    z-index: 45;
}

/*{{{ Popup tabellone */
.popup_hc_tabellone {
    visibility: hidden;
    position:absolute;
    border:1px solid #aaaaff;
    font: 8px Verdana;
    line-height: 18px;
    z-index: 50;
}
.popup_hc_tabellone_shadow {
    position:absolute;
    top:5px;
    left:5px;
    bottom:-5px;
    right:-5px;
    border:1px solid #aaaaff;
    z-index: -5;
    border-top: 1px hidden;
    border-left: 1px hidden;
    border-right: 1px hidden;
    border-bottom: 1px hidden;
    background-color: #000000;
    filter: alpha(opacity=30);
    opacity: 0.3;
}
.popup_hc_tabellone a {
    width: 100%;
    display: block;
    text-indent: 3px;
    text-decoration: none;
    font-weight: bold;
    text-indent: 5px;
}
.popup_hc_tabellone a:hover {
    background-color: #d8e8e6;
    color: black;
}
/*}}}*/

/*}}}*/

/*{{{ tabellone generico javascript per ie */
.div_ie_hc_tabellone_main {
    background-color: #ffffff;
    border: 1px solid #000000;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    z-index: 10;
    overflow:auto;
    font-weight: bold;
}
.div_ie_hc_tabellone_header {
    background-color: #ffffff;
    position: absolute;
}
.div_ie_hc_tabellone_top {
    background-color: #ffffff;
    border-top: 1px solid #000000;
    border-left: 1px solid #000000;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    text-align: center;
    position: absolute;
}
.div_ie_hc_tabellone_int_riga_clear,
.div_ie_hc_tabellone_int_riga_dark {
    background-color: #ffffff;
    border-top: 0px hidden;
    border-left: 1px solid #000000;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    position: absolute;
}
.div_ie_hc_tabellone_int_colonna {
    background-color: #ffffff;
    border-left: 0px hidden;
    border-top: 1px solid #000000;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    text-align: center;
    position: absolute;
}
.div_ie_hc_tabellone_cella_clear,
.div_ie_hc_tabellone_cella_dark,
.div_ie_hc_tabellone_cella_arancio,
.div_ie_hc_tabellone_cella_blu,
.div_ie_hc_tabellone_cella_verde {
    background-color: #ffffff;
    border-top: 0px hidden;
    border-left: 0px hidden;
    border-right: 1px solid #000000;
    border-bottom: 1px solid #000000;
    position: absolute;
    text-align: center;
    cursor: pointer;
}
.div_ie_hc_tabellone_cella_select {
    background-color: #ffffff;
}
.td_ie_hc_header_popup {
    background-color: #ffffff;
    cursor: move;
    font-size: 14px;
    font-weight: bold;
    z-index: 45;
}
.td_ie_hc_normal_popup {
    background-color: #ffffff;
    z-index: 45;
}

/*{{{ Popup tabellone */
.popup_ie_hc_tabellone {
    visibility: hidden;
    position:absolute;
    display: none;
    height:110%;
    border:1px solid #ffffff;
    border-bottom-width: 1px;
    font: 8px Verdana;
    line-height: 18px;
    z-index: 50;
}
.popup_ie_hc_tabellone_shadow {
    position:absolute;
    top:5px;
    left:5px;
    width:100%;
    height:100%;
    border:1px solid #aaaaff;
    border-bottom-width: 1px;
    z-index: -5;
    border-top: 1px hidden;
    border-left: 1px hidden;
    border-right: 1px hidden;
    border-bottom: 1px hidden;
    background-color: #000000;
    filter: alpha(opacity=30);
    opacity: 0.3;
}
.popup_ie_hc_tabellone a {
    width: 100%;
    display: block;
    text-indent: 3px;
    text-decoration: none;
    font-weight: bold;
    text-indent: 5px;
}
.popup_ie_hc_tabellone a:hover {
    background-color: #d8e8e6;
    color: black;
}
/*}}}*/

/*}}}*/

.classe_div_agenda a {
    font-size: 12px;
    font-weight: bold;
    color: #000000;
    cursor: pointer;
}
.classe_div_agenda a:hover {
    font-size: 12px;
    font-weight: bold;
    color: #ff0000;
    cursor: pointer;
}
.classe_div_agenda a:link, a:visited, a:active {
    font-size: 12px;
    font-weight: bold;
    color: #000000;
    cursor: pointer;
}
.classe_img_puntatore {
    cursor: pointer;
}

*#cont-eventi-categorie {
    position: absolute;
    top: 140px;
    left: 30px;
    right: 30px;
    height: 100%;
    bottom: 30px;
    overflow: auto;
}

*#cont-eventi-generale {
    position: absolute;
    visibility: hidden;
    top: 0px;
    left: 0px;
    height: 100%;
    width: 100%;
    z-index: 10;
}

#cont-eventi-anno {
    box-shadow: 10px 5px 12px rgba(0, 0, 0, 0.6);
}

/* celle editabili */
.nedit {
    border:none;
    background:none;
    margin:0;
    padding-left:5px;
    width: 100%;
}
.edit {
    margin:0;
    width:100%;
    padding-left:5px;
}

/* Anomalie voti tabellone */

.tab_ie_hc_table {

}

.tab_hc_table {

}

.tab_ie_table {
    background-color: #ddddff;
    border-radius: 8px;
    padding:5px;
}

.tab_table {
    background-color: #ddddff;
    border-radius: 8px;
    padding:5px;
}

.tab_ie_hc_header {

}

.tab_hc_header {

}

.tab_ie_header {

}

.tab_header {

}

.tab_ie_hc_student_row {
    font-size: 14px;
    border-bottom: 1px solid black;
}

.tab_hc_student_row {
    font-size: 14px;
    border-bottom: 1px solid black;
}

.tab_ie_student_row {
    background-color: #8888ff;
    font-size: 14px;
    border-bottom: 1px solid #8888ff;
}

.tab_student_row {
    background-color: #8888ff;
    font-size: 14px;
    border-bottom: 1px solid #8888ff;
}

.tab_ie_hc_row {
    font-size: 14px;
}

.tab_hc_row {
    font-size: 14px;
}

.tab_ie_row {
    font-size: 14px;
}

.tab_row {
    font-size: 14px;
}


/* Banner libretti */
div.banner-libretti {
    white-space: nowrap;
    overflow: hidden;
    background-image: url(../images/banner_libretti.png);
    background-repeat: no-repeat;
    width: 800px;
    height: 200px;
    margin-top: 20px;
    padding-top: 145px;
    margin-bottom: -145px;
}
div.banner-libretti > div.banner-libretti-text {
    white-space: nowrap;
    display: inline;
    width: auto;
    font-size: 26px;
    margin-top: 100px;
}

/* Notifiche Messenger */
#messenger-notify {
    background-color: #b11;
    border: 2px solid #fff;
    border-radius: 19px !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    color: white;
    float: left;
    font-size: 10px;
    font-weight: bold;
    left: -15px;
    margin: 0 0 0 10px;
    padding: 0 7px;
    position: relative;
    top: -32px;
}

.messenger-notify-2 {
    background-color: #b11;
    border-radius: 19px !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    color: white;
    font-size: 11px;
    font-weight: bold;
    padding: 3px 6px;
    display: none;
    position: absolute;
    margin-top: 10px;
    margin-left: 25px;
}

.ps-header {
    background-color:#8FE38D;
    width: 100%;
    color: #000;
    font-size: 14px;
}


/* Modifiche nuova grafica */
/* ----------------------- */

/* Stili Specifici */

.login_area {
    border-radius: 8px;
    box-shadow: 10px 10px 5px #888888;
    padding: 20px;
}

.area_smart_message {
    background-color:#bbbbff;
    padding:0px;
    color:#FF0000;
    font-size: 16px;
    font-weight: bold;
    box-shadow: 10px 10px 5px #888888;
    border-radius: 8px;
}

/* TITOLI E TESTATE */

/* size=5 */
.titolo_principale {
    font-weight: bold;
    font-size: 24px;
}

/* size=4 */
.titolo_testo {
    padding:5px;
    font-size:18px;
}

/* size=3 */
.sottotitolo_testo {
    padding:5px;
    font-size:16px;
}

.sottotitolo_testo_bold {
    padding:5px;
    font-size:16px;
    font-weight: bold;
}

.titolo_funzione {
    text-align: center;
    border-radius: 8px;
    padding:5px;
    font-size:18px;
    font-weight: bold;
}

.sottotitolo_funzione {
    padding:5px;
    font-size:16px;
    font-weight: bold;
}

.colore_testo_notifiche {
    font-weight: bold;
    color: #3322FF;
}

a.email_link:link {
    color:#000000;
    font-size:16px;
}

a.permessi {

}

/* Testi colorati */

.testo_verde {
    color: #22bb22;
}

.testo_rosso {
    color: #ff0000;
}

.testo_giallo {
    color: #ffe719;

}
/* SFONDI */

.sfondo_trova_filtri_tag {
    background-color: #ffbbbb;
    border-collapse: collapse;
}

.sfondo_trova_filtri_aggiuntivi {
    background-color: #FFE6BB;
    border-collapse: collapse;
}

.sfondo_trova_campi_multipli {
    background-color: #D5FFBB;
    border-collapse: collapse;
}

.sfondo_trova_classi {
    background-color: #BBD8FF;
    border-collapse: collapse;
}

.sfondo_classi {
    background-color: #BBD8FF;
    border-collapse: collapse;
}

.sfondo_adozione_libri {
    background-color:#75BD77;
    border-collapse: collapse;
}

.sfondo_contrasto_generico {
    background-color: #bbbbff;
    border-collapse: separate;
}

.sfondo_contrasto_generico_leggero {
    background-color: #bbbafc70;
    border-collapse: separate;
}

.sfondo_scuro_generico {
    background-color: #888EFA;
    border-collapse: separate;
}

.sfondo_base_generico {
    background-color: #ddddff;
    border-collapse: separate;
}

.sfondo_base_certificati {
    background-color:#E3DA8D;
}

.sfondo_contrasto_certificati {
    background-color:#CBC273;
}

.sfondo_contrasto_materie {
    background-color:#8FE38D;
}

.sfondo_base_materie {
    background-color:#C5E5C4;
    padding: 5px;
}

.sfondo_contrasto_simboli {
    background-color:#8FE38D;
}

.sfondo_base_simboli {
    background-color:#C5E5C4;
    padding: 5px;
}

.sfondo_setup_utenti {
    background-color:#9DC5FC;
}

.sfondo_riquadro_generico {
   background-color:#CCCCCC;
}

.sfondo_base_commissione {
   background-color:#FFECD1;
}

.sfondo_contrasto_commissione {
   background-color:#FFC266;
}

.sfondo_orario_festivo {
   background-color:#aaaaaa;
}

.sfondo_orario_vuoto {
    background-color:#999;
}

.sfondo_orario_lezione {
    background-color:#74ad5a;
}

.sfondo_orario_assente {
   background-color:#bb0000;
}

.sfondo_orario_simil {
   background-color:#00bbbb;
}

.sfondo_orario_4meno {
   background-color:#bbbb00;
}

.sfondo_orario_4piu {
   background-color:#00bb00;
}

.sfondo_sottolineatura {
   background-color:#FFEF99;
}

/* ORARIO */

.elementi_singola_lezione {
    background-color: Transparent;
    background-repeat: no-repeat;
    border: none;
    cursor:pointer;
    overflow: hidden;
    outline:none;
    width:100%
}

/* CONTENITORI */

.area_contenitore_principale {
    border-radius: 12px;
    padding: 5px;
    box-shadow: 10px 10px 5px #888888;
    border-collapse: collapse;
}

.bordo_tabelle_separate {
    border-collapse: separate;
}

.bordo_generico_rilievo {
    border-radius: 8px;
    padding: 3px;
    box-shadow: 10px 10px 5px #888888;
}

.bordo_superiore_rilievo {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding:5px;
    box-shadow: 10px 10px 5px #888888;
    border-collapse: separate;
}

.bordo_inferiore_rilievo {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    padding:5px;
    box-shadow: 10px 10px 5px #888888;
    border-collapse: separate;
}

.bordo_generico_piatto {
    border-radius: 8px;
    padding: 3px;
}

.bordo_tabella_generico {
    border: 2px solid #bbbbff;
    border-collapse: separate;
}

.contenitore_generico {
    border-radius: 8px;
    padding: 5px;
    border-collapse: separate;
}

.divisore_basso {
    border-bottom: 1px solid #bbbbff;
    padding: 5px;
}

.divisore_scuro {
    border-bottom: 1px solid #888EFA;;
}

.divisore_alto {
    border-top: 1px solid #bbbbff;
    padding: 5px;
}

.divisore_template_orario {
    border-bottom: 3px solid #bbbbff;
    padding: 5px;
}

.divisore_studenti {
    padding: 2px;
}

.contenitore_classeA13 {
    padding: 5px;
}

.padding_cella_generica {
    padding:5px;
}

.divisori_laterali {
    border-color: #888EFA !important;
    border-style: solid !important;
    border-width: 0px 1px 0px 1px !important;
}

/* Stili Area Setup #CC5E00 */
.setup_area_titolo {
    background-color: #BBD8FF;
    border-radius: 8px;
    padding:5px;
    color: #000;
    font-weight: bold;
    font-size: 20px;
}

.setup_contenitore {
    background-color: #BBD8FF;
    border-radius: 8px;
    padding: 5px;
}

.setup_titolo {
    color: #000;
    font-weight: bold;
    font-size: 14px;
}

/* Stili Area Amministrazione #CC5E00 */
.amministrazione_contenitore {
    background-color: #99EBC2;
    border-radius: 8px;
    padding: 5px;
}

.amministrazione_titolo {
    color: #000;
    font-weight: bold;
    font-size: 14px;
}

/* Stili Area Stampa #1D8EB6 */
.stampe_area_titolo {
    background-color: #BBD8FF;
    border-radius: 8px;
    padding:5px;
    font-weight: bold;
    font-size: 20px;
}

.stampe_contenitore {
    background-color: #BBD8FF;
    border-radius: 8px;
    padding: 5px;
}

.stampe_titolo {
    font-weight: bold;
    font-size: 14px;
}

/* Stili Studente */

.studente_colonne {
    text-align: center;
    font-weight: bold;
    background-color: #bbbbff;
    padding:5px;
}

.studente_sottotitolo {
    font-weight:bold;
    font-size: 16px;
}

.studente_sfondo_anagrafica {
    background-color: #8de3e2;
}

.studente_sfondo_stato_scol {
    background-color: #FFE6BB;
}

.studente_sfondo_cv_chiaro {
    background-color: #FFF8B1;
}

.studente_sfondo_cv_scuro {
    background-color: #DDD690;
}

.studente_sfondo_contatti {
   background-color: #BBD8FF;
}

.studente_sfondo_parente_insert {
    background-color: #c5e5c4;
}

.studente_sfondo_parente_chiaro {
    background-color: #9ff49e;
}

.studente_sfondo_parente_scuro {
    background-color: #7ed27c;
}

.studente_sfondo_abbinamenti {
    background-color: #E3DA8D;
}

.studente_sfondo_documenti_chiaro{
    background-color: #8FDC8E;
}

.studente_sfondo_documenti_scuro {
    background-color: #66AA65;
}

.studente_sfondo_varie {
    background-color: #a8b75d;
}

.studente_sfondo_mediche {
    background-color: #b495c2;
}

.studente_sfondo_personalizzati {
    background-color: #c295ac;
}

.download_file a {
    font-size: 10px;
    color: hotpink;
}

/* ESAMI DI STATO */

.es_titolo_colonna {
    padding:2px;
    font-size:16px;
    font-weight: bold;
}
.es_titolo_colonna_2 {
    padding:3px;
    font-size:16px;
    font-weight: bold;
}

.es_sfondo_ruoli_commissari {
    background-color: #c5e5c4;
}

.es_sfondo_scuro_ruoli_commissari {
    background-color: #B1CEB0;
    border-collapse: separate;
}
.es_sfondo_verbali {
    background-color: #8de3e2;
}

.es_sfondo_cal_base {
    background-color: #FFE6BB;
}

.es_sfondo_cal_scuro {
    background-color: #ff9227;
}

.es_sfondo_orali_chiaro {
    background-color: #ffac5a;
}

.es_sfondo_orali_scuro {
    background-color: #ff9e3f;
}

.es_sfondo_esami_chiaro {
    background-color: #7ec0e1;
}

.es_sfondo_esami_scuro {
    background-color: #6ea0e1;
}

.es_sfondo_scheda_candidato {
    background-color: #d79d8c;
}

.es_sfondo_stampe {
    background-color: #EEE7A0;
}

.es_sfondo_registro {
    background-color: #BBD8FF;
}

.es_sfondo_pubblicazione {
    background-color: #a8b75d;
}

/* TABELLONE PAGELLINE */

.sfondo_tabellone_chiaro {
    background-color: #7EC0E1;
}

.sfondo_tabellone_medio {
    background-color: #5AA0E1;
}

.sfondo_tabellone_scuro {
    background-color: #6EA0E1;
}

/* MANUTENZIONE PARENTI */

.parente_Attivo {
    color: #000;
}

.parente_Eliminato {
    color: #970000;
}

/* INTERFACCIA MENSE */

.mense_icone {
    text-align: right;
    vertical-align:middle;
}

.highlight:hover {
    background-color: #E2DBA6 !important;
}

/* ICONA STAMPA B1 */

.b1_icona_torna_su {
    position: fixed;
    bottom:20px;
    right: 2%;
    z-index: 100;
}

/* Interfaccia permessi */

.btn_dow {
    width: 100px;
    height: 30px;
    font-weight: bold;
}

.btn_week {
    width: 200px;
    height: 30px;
    font-weight: bold;
}

/* Manutenzione 2 */

.div_descrizione {
    width: 98%;
    padding: 8px 0px 8px 0px;
    min-height: 50px;
    border-radius: 2px 2px 0px 0px;
    background-color: white;
    margin-top: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.btn_script {
    width: 98%;
    height: 32px;
    font-weight: bold;
    outline:none;
    border: none;
    background-color: #3F51B5;
    border-radius: 0px 0px 2px 2px;
    color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    transition: all 0.3s cubic-bezier(.25,.8,.25,1);
    cursor: pointer;
}

.btn_manutenzione {
    font-weight: bold;
    outline: 0;
    border: 0;
    background-color: #3F51B5;
    border-radius: 32px;
    color: white;
    transition: all 0.3s cubic-bezier(.25,.8,.25,1);
    cursor: pointer;
    text-align: center;
    vertical-align: middle;
    padding: 6px 16px;
}

/* Sostegno */
.bordi_alti_scheda {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.bordi_bassi_scheda {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.div_scheda_generica {
    width: 98%;
    margin: auto;
    background-color: #FAFAFA;
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    transition: all 0.3s cubic-bezier(.25,.8,.25,1);
    border-radius: 5px 5px 5px 5px;
    overflow: hidden;
    box-sizing: border-box;
}

/*------tendenzialmente da usare insieme cosi' che i bordi si completino------*/
.div_corpo_scheda_generica {
    width: 98%;
    margin: auto;
    background-color: #FAFAFA;
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    transition: all 0.3s cubic-bezier(.25,.8,.25,1);
    border-radius: 0px 0px 5px 5px;
    box-sizing: border-box;
}

.div_titolo_scheda_generica {
    width: 98%;
    margin: auto;
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    transition: all 0.3s cubic-bezier(.25,.8,.25,1);
    border-radius: 5px 5px 0px 0px;
    padding: 14px 0px 14px 0px;
}
/*------------*/
.scheda_informativa {
    padding: 10px;
    border-radius: 5px;
    box-sizing: border-box;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    overflow: hidden;
}

.scheda_interna {
    padding: 10px;
    border-radius: 5px;
}

.div_testo_titolo_scheda_sx {
    padding: 4px 8px;
    text-align: left;
    font-weight: bold;
    font-size: 110%;
    overflow: hidden;
}

.padding_regular {
    padding: 8px 16px;
}

.btn_pieno {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    border-radius: 5px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    color: #fff;
    font-weight: bold;
    outline: 0;
}

.btn_pieno_tondo {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    border-radius: 32px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    color: #fff;
    font-weight: bold;
    outline: 0;
}

.btn_pieno_tondo:disabled {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    border-radius: 32px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    color: #fff;
    font-weight: bold;
    outline: 0;
    opacity: 0.5;
}

.btn_flat_indaco {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    border-radius: 5px;
    background-color: transparent;
    color:#1A237E;
    font-weight: bold;
    outline: 0;
}

.btn_flat_bianco {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    border-radius: 5px;
    background-color: transparent;
    color:white;
    font-weight: bold;
    outline: 0;
}

.btn_flat {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    border-radius: 5px;
    background-color: transparent;
    font-weight: bold;
    outline: 0;
}

.btn_flat_tondo {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    border-radius: 32px;
    background-color: transparent;
    font-weight: bold;
    outline: 0;
}

.btn_flat_new {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    border-radius: 5px;
    background-color: transparent;
    font-weight: bold;
    outline: 0;
}

.btn_close {
    position: absolute;
    color: gray;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    top: 5px;
    right: 5px;
    border-radius: 50%;
    font-weight: bold;
    outline: 0;
    font-size: 1.5em;
    background-color: transparent;
}

.btn_indietro_tab_comp {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    background-color: #c5d5c5;
    border-radius: 5px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    color: #000;
    font-weight: bold;
    outline: 0;
}

.btn_salva_tab_comp {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    background-color: rgba(34, 124, 94, 0.8);
    border-radius: 2px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    color: #FFF;
    font-weight: bold;
    outline: 0;
    width: 90px;
    height: 32px;
    text-shadow: 0 1px 2px rgb(0 0 0 / 65%);
}

.btn_salva_indietro_tab_comp {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    background-color: rgba(35, 133, 149, 0.8);
    border-radius: 2px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    color: #FFF;
    font-weight: bold;
    outline: 0;
    /* width: 230px; */
    height: 32px;
    text-shadow: 0 1px 2px rgb(0 0 0 / 65%);
}

.btn_annulla_tab_comp {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    background-color: rgba(255, 0, 0, 0.8);
    border-radius: 2px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    color: #FFF;
    font-weight: bold;
    outline: 0;
    width: 90px;
    height: 32px;
    text-shadow: 0 1px 2px rgb(0 0 0 / 65%);
}

.btn_stato_scrut_tab_comp {
    padding: 4px 12px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    background-color: #FFF;
    border-radius: 30px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    color: #000;
    font-weight: bold;
    outline: 0;
}

.mc-icon,
.mc-icon-2 {
    color: #FAFAFA;
    text-align: center;
    vertical-align: middle;
    border: 0;
    background-color: transparent;
    font-weight: bold;
    outline: 0;
    max-width: 100px;
    border-radius: 8px;
    padding: 5px;
    text-align: center;
    margin: 8px;
    overflow: hidden;
}

.mc-icon-img i,
.mc-icon-img-2 i,
.mc-icon-img-small i,
.mc-icon-img-2-small i {
    border-radius: 50%;
}

.mc-icon-img i,
.mc-icon-img-2 i {
    padding: 8px;
}

.mc-icon-img-small i,
.mc-icon-img-2-small i {
    padding: 4px;
}

.mc-icon-img-2 i {
    position: relative;
    top: 4px;
    margin-top: -4px;
    font-size: 110%;
}

.mc-icon-text {
    padding: 3px;
    border-radius: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    /* text-shadow: 0 1px 2px rgba(0,0,0,.75); */
    text-shadow: 0 1px 3px rgba(0,0,0,.99);
}

.mc-icon-text-2 {
    padding: 8px;
    border-radius: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    /* text-shadow: 0 1px 2px rgba(0,0,0,.75); */
    text-shadow: 0 1px 3px rgba(0,0,0,.99);
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
}

.mc-icon:hover,
.mc-icon-2:hover {
    cursor: pointer;
}
.mc-icon:hover i,
.mc-icon-2:hover i {
    color: rgb(255, 234, 97);
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
}

.mc-icon-2:hover>.mc-icon-text-2 {
    box-shadow: 0 1px 4px rgba(0, 0, 0, .8);
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
}

.mc-icon-shadow {
    box-shadow: 0 1px 3px rgba(0, 0, 0, .6);
}

.mc-icon-shadow:hover {
    box-shadow: 0 1px 3px rgba(0, 0, 0, .8);
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
}

.ellipses {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.btn_padding_ridotto {
    padding: 3px 8px;
}

.bordo_basso_generico {
    border-bottom: 1px solid #bababa;
}

.bordo_alto_generico {
    border-top: 1px solid #bababa;
}

.bordo_sinistro_generico {
    border-left: 1px solid #bababa;
}

.bordo_destro_generico {
    border-right: 1px solid #bababa;
}

.bordi_generici {
    border: 1px solid #bababa;
}

.titolo_colonna_tabella {
    font-weight: bold;
    color: black;
    text-align: center;
    opacity: 0.87;
}

.tabella_padding_celle td {
    padding: 3px;
}

.input_text {
    padding: 3px 6px;
    text-align: center;
    border:1px solid #bababa;
}

.select {
	font-family: inherit;
	background-color: transparent;
	padding: 3px 3px 3px 0;
	border-radius: 0;
	border: 1px solid rgba(0,0,0, 0.12);
    cursor: pointer;
}

.messaggio_basso_scomparsa {
    z-index: 20;
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    border: 0;
    border-radius: 32px;
    left: 50%;
    transform: translateX(-49%);
    bottom: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .6);
    color: #fff;
    background-color: #121212;
    font-weight: bold;
    outline: 0;
    position: fixed;
}

.sfondo_natale {
    /* background-color: #b53f3f; */
    background-color: #cc211f;
    color: white;
}

.sfondo_scuro {
    background-color: #3F51B5;
    color: white;
}

.sfondo_bianco {
    background-color: #FAFAFA;
    color: black;
}

.testo_scuro {
    color: #3F51B5;
}

.bordo_scuro {
    border: 1px solid #3F51B5;
}

.sfondo_grigio_chiaro {
    background-color: #E0E0E0;
}

.testo_grigio_chiaro {
    color: #E0E0E0;
}

.bordo_grigio_chiaro {
    border: 1px solid #E0E0E0;
}

.sfondo_grigio_scuro {
    background-color: #9E9E9E;
}

.testo_grigio_scuro {
    color: #9E9E9E;
}

.bordo_grigio_scuro {
    border: 1px solid #9E9E9E;
}

.testo_bianco {
    color: #FAFAFA;
}

.bordo_bianco {
    border: 1px solid #FAFAFA;
}

.testo_nero {
    color: black;
}

.bordo_nero {
    border: 1px solid black;
}

.sfondo_verde {
    background-color: #227c5e;
}

.sfondo_verde_op20 {
    background-color: rgb(34, 124, 94, 0.20);
}

.testo_verde {
    color: #227c5e;
}

.testo_blu {
    color: #226ebb;
}

.bordo_verde {
    border: 1px solid #227c5e;
}

.bordo_blu {
    border: 1px solid #226ebb;
}

.sfondo_verde_chiaro {
    background-color: #81c784;
}

.sfondo_verde_chiaro_op20 {
    background-color: rgb(129, 199, 132, 0.20);
}

.testo_verde_chiaro {
    color: #81c784;
}

.bordo_verde_chiaro {
    border: 1px solid #81c784;
}

.sfondo_rosso {
    background-color: #FF0000;
}

.sfondo_rosso_op20 {
    background-color: rgb(255, 0, 0, 0.20);
}

.testo_rosso {
    color: #FF0000;
}

.bordo_rosso {
    border: 1px solid #d50000;
}

.sfondo_rosso_scuro {
    background-color: #d50000;
}

.sfondo_rosso_scuro_op20 {
    background-color: rgb(213, 0, 0, 0.20);
}

.testo_rosso_scuro {
    color: #d50000;
}

.bordo_rosso_scuro {
    border: 1px solid #d50000;
}

.sfondo_ambra {
    background-color: rgb(255, 171, 0);
}

.sfondo_ambra_op20 {
    background-color: rgba(255, 171, 0, 0.20);
}

.testo_ambra {
    color: rgb(255, 171, 0);
}

.bordo_ambra {
    border: 1px solid rgb(255, 171, 0);
}

.sfondo_arancio {
    background-color: rgb(255, 122, 0);
}

.sfondo_arancio_op20 {
    background-color: rgba(255, 122, 0, 0.20);
}

.testo_arancio {
    color: rgb(255, 122, 0);
}

.bordo_arancio {
    border: 1px solid rgb(255, 122, 0);
}

.sfondo_arancio_scuro {
    background-color: rgb(255, 86, 34);
}

.sfondo_arancio_scuro_op20 {
    background-color: rgba(255, 86, 34, 0.20);
}

.testo_arancio_scuro {
    color: rgb(255, 86, 34);
}

.bordo_arancio_scuro {
    border: 1px solid rgb(255, 86, 34);
}

.sfondo_azzurro {
    background-color: rgb(33, 148, 243);
}

.sfondo_azzurro_op20 {
    background-color: rgba(33, 148, 243, 0.20);
}

.testo_azzurro {
    color: rgb(33, 148, 243);
}

.bordo_azzurro {
    border: 1px solid rgb(33, 148, 243);
}

.sfondo_viola {
    background-color: rgb(120, 40, 200);
}

.sfondo_viola_op20 {
    background-color: rgba(120, 40, 200, 0.20);
}

.testo_viola {
    color: rgb(120, 40, 200);
}

.bordo_viola {
    border: 1px solid rgb(120, 40, 200);
}

.sfondo_verde_old {
    background-color: rgb(68, 98, 28);
}

.sfondo_verde_old_op20 {
    background-color: rgba(68, 98, 28, 0.20);
}

.testo_verde_old {
    color: rgb(68, 98, 28);
}

.bordo_verde_old {
    border: 1px solid rgb(68, 98, 28);
}

.sfondo_grigioblu_old {
    background-color: rgb(76, 106, 132);
}

.sfondo_grigioblu_old_op20 {
    background-color: rgba(76, 106, 132, 0.20);
}

.testo_grigioblu_old {
    color: rgb(76, 106, 132);
}

.bordo_grigioblu_old {
    border: 1px solid rgb(76, 106, 132);
}

.sfondo_azzurro_old {
    background-color: rgb(29, 141, 182);
}

.sfondo_azzurro_old_op20 {
    background-color: rgba(29, 141, 182, 0.20);
}

.testo_azzurro_old {
    color: rgb(29, 141, 182);
}

.bordo_azzurro_old {
    border: 1px solid rgb(29, 141, 182);
}

.sfondo_viola_old {
    background-color: rgb(60, 47, 103);
}

.sfondo_viola_old_op20 {
    background-color: rgba(60, 47, 103, 0.20);
}

.testo_viola_old {
    color: rgb(60, 47, 103);
}

.bordo_viola_old {
    border: 1px solid rgb(60, 47, 103);
}

.sfondo_rosso_old {
    background-color: rgb(209, 26, 46);
}

.sfondo_rosso_old_op20 {
    background-color: rgba(209, 26, 46, 0.20);
}

.testo_rosso_old {
    color: rgb(209, 26, 46);
}

.bordo_rosso_old {
    border: 1px solid rgb(209, 26, 46);
}

.sfondo_arancio_old {
    background-color: rgb(204, 94, 0);
}

.sfondo_arancio_old_op20 {
    background-color: rgba(204, 94, 0, 0.20);
}

.testo_arancio_old {
    color: rgb(204, 94, 0);
}

.bordo_arancio_old {
    border: 1px solid rgb(204, 94, 0);
}

.sfondo_dark_cyan {
    background-color: rgb(0, 150, 136);
}

.sfondo_dark_cyan_op20 {
    background-color: rgba(0, 150, 136, 0.20);
}

.testo_dark_cyan {
    color: rgb(0, 150, 136);
}

.bordo_dark_cyan {
    border: 1px solid rgb(0, 150, 136);
}

.sfondo_bbb {
    background-color: rgb(10, 40, 70);
}

.sfondo_bbb_op20 {
    background-color: rgba(10, 40, 70, 0.20);
}

.testo_bbb {
    color: rgb(10, 40, 70);
}

.bordo_bbb {
    border: 1px solid rgb(10, 40, 70);
}

.sfondo_master_dark_blue {
    background-color: var(--mastercom-dark-blue);
}

.testo_master_dark_blue {
    color: var(--mastercom-dark-blue);
}

.bordo_master_dark_blue {
    border: 1px solid var(--mastercom-dark-blue);
}

.sfondo_master_light_blue {
    background-color: var(--mastercom-light-blue);
}

.testo_master_light_blue {
    color: var(--mastercom-light-blue);
}

.bordo_master_light_blue {
    border: 1px solid var(--mastercom-light-blue);
}

.sfondo_master_yellow{
    background-color: var(--mastercom-yellow);
}

.testo_master_yellow {
    color: var(--mastercom-yellow);
}

.bordo_master_yellow {
    border: 1px solid var(--mastercom-yellow);
}

.sfondo_master_pale_yellow{
    background-color: var(--mastercom-pale-yellow);
}

.testo_master_pale_yellow {
    color: var(--mastercom-pale-yellow);
}

.bordo_master_pale_yellow {
    border: 1px solid var(--mastercom-pale-yellow);
}

.testo_grigio {
    color: #374454;
}

.bordo_grigio {
    border: 1px solid #374454;
}

.ombra_testo {
    text-shadow: 0 1px 2px rgba(0,0,0,.65);
}

.ombra_testo_chiara {
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.65);
}

/* Classi per allarmi pagelle */
.allarmi_pagelle_table {
    width: 80%;
    max-height: 80%;
    background-color: white;
    border: 1px solid black;
}

.allarmi_pagelle_header {
    font-size:x-large;
    font-weight: bold;
}

.allarmi_pagelle_riga_anomalia {
    margin-left: 20px;
}


/* NUOVA SEZIONE DIRIGENTE */

.navbar {
    overflow: hidden;
    font-weight: bold;
}

.navbar a {
    float: left;
    font-size: 16px;
    color: white;
    text-align: center;
    padding: 14px 16px;
    text-decoration: none;
    cursor: pointer;
}

.dropdown {
    float: left;
    overflow: hidden;
}

.dropdown .dropbtn {
    font-size: 16px;
    border: none;
    outline: none;
    color: white;
    padding: 14px 16px;
    background-color: inherit;
    font-family: inherit;
    font-weight: bold;
    margin: 0;
    border-radius: 5px;
}

.navbar.sfondo_scuro a:hover, .sfondo_scuro.dropdown:hover {
    background-color: #616eb5;
}

.navbar.sfondo_natale a:hover, .sfondo_natale.dropdown:hover {
    /* background-color: #d45353; */
    background-color: #eeba5b;
}

.navbar.sfondo_verde a:hover, .sfondo_verde.dropdown:hover {
    background-color: #5d9381;
}

.dropdown-content {
    display: none;
    position: absolute;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 500;
    border: 1px solid lightgray;
    border-radius: 5px;
}

.dropdown-content2 {
    display: none;
    position: absolute;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 500;
    border: 1px solid lightgray;
    border-radius: 5px;
}

.dropdown-content a {
    float: none;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
    cursor: pointer;
    border-radius: 5px;
    color: black;
    font-weight: normal;
    position: relative;
}

.dropdown-content.sfondo_scuro a:hover {
    background-color: #616eb5;
}

.dropdown-content.sfondo_bianco.sel_blu a:hover {
    background-color: #eaecfc;
}

.dropdown-content.sfondo_bianco.sel_verde a:hover {
    background-color: #deede3;
}

.dropdown-content.sfondo_verde a:hover {
    background-color: #5d9381;
}

.desktop.dropdown:hover .desktop.dropdown-content {
    display: block;
}

.evidenzia:hover {
    background-color: #EEEEEE;
}

.evidenzia_dark:hover {
    background-color: #6b777c;
}

.evidenzia_menu_option:hover {
    background-color: #EEEEEE;
    border-radius: 5px;
    color: black;
}

.menu_option {
    padding: 4px 8px;
    color: #2b2b2b;
    font-weight: normal;
}

.annotazione_leggera {
    color:#595959;
}

.accendi:hover {
    color: yellow;
}

.font-normal {
    font-weight: normal;
}

.bold {
    font-weight: bold;
}

.italic {
    font-style: italic;
}

.pointer {
    cursor: pointer;
}

.cursor_default {
    cursor: default;
}

.evento_agenda {
    border-radius: 6px;
    padding: 10px;
    line-height: 1em;
    cursor: default;
}

th {
    color: black;
}

/* SIDENAV */
topnav {
    position: fixed;
    top: 0;
    height: 55px;
    color: white;
    z-index: 2;
}

nav.content {
    /* margin-top: 50px; */
}

.sidenav {
  height: 100%;
  width: 250px;
  position: fixed;
  z-index: 30;
  top: 0;
  left: 0;
  overflow-x: hidden;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  -webkit-transform: translate3d(0,0,0);
}

.sidenav a, .dropdown-btn {
  padding: 6px 8px 6px 16px;
  text-decoration: none;
  font-size: 20px;
  display: block;
  border: none;
  background: none;
  width:100%;
  text-align: left;
  cursor: pointer;
  outline: none;
  font-weight: normal;
  box-sizing: border-box;
  border-top-right-radius: 30px;
  border-bottom-right-radius: 30px;
}

/* On mouse-over */
.sidenav a:hover, .dropdown-btn:hover {
  background-color: #a0a8e8;
}

.active {
    background-color: #3F51B5;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,.65);
}

.active_natale {
    background-color: #227c5e;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,.65);
}

.dropdown-container {
  display: none;
  background-color: #EEEEEE;
  /* box-shadow: inset 0px 8px 16px 0px rgba(0,0,0,0.2); */
}

@media screen and (max-height: 450px) {
  .sidenav {padding-top: 15px;}
  .sidenav a {font-size: 18px;}
}

.new_file {
    display: inline-block;
    font-size: 10px;
    margin-left: 10px;
    background-color: #b11;
    border-radius: 20px;
    color: white;
    font-weight: bold;
    padding: 3px 6px;
}

.noselect {
  -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
     -khtml-user-select: none; /* Konqueror HTML */
       -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* Internet Explorer/Edge */
            user-select: none; /* Non-prefixed version, currently
                                  supported by Chrome and Opera */
}

.zoomX4:hover {
  -ms-transform: scale(4); /* IE 9 */
  -webkit-transform: scale(4); /* Safari 3-8 */
  transform: scale(4);
  transition: transform .2s;
}

@font-face {
  font-family: Roboto;
  src: url(../fonts/Roboto-Regular.ttf);
}
@font-face {
  font-family: Roboto;
  src: url(../fonts/Roboto-Bold.ttf);
  font-weight: bold;
}

.ripples {
    position: relative;
    overflow: hidden;
}

.ripples::before {
    content: '';
    opacity: 0;
    transform: scale(3);
    transition: all .8s;

    background: rgba(255, 255, 255, .5);
    border-radius: 50%;
    position: absolute;
    width: 100%;
    height: 100%;
}
.ripples:active::before {
    opacity: 1;
    transform: scale(0);
    transition: none;
}

.notify-badge {
    background-color: red;
    border-radius: 19px !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    color: white;
    min-width: 10px;
    height: 10px;
    padding: 1px;
    font-size: 9px;
    font-weight: bold;
    display: none;
    position: absolute;
    top: -6px;
    right: -6px;
}

.nowrap {
    white-space: nowrap;
}

.inline {
    display: inline-block;
}

input[type=date], input[type=time] {
    font-family: inherit;
}

.jqx-loader-modal
{
    z-index: 9999;
}

.valign-top {
    vertical-align: top;
}

.valign-middle {
    vertical-align: middle;
}

.valign-bottom {
    vertical-align: bottom;
}

.valign-baseline {
    vertical-align: baseline;
}

.img-rotate-90deg {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}

.table-hover tbody tr:hover {
    background-color:#f5f5f5;
}

select:invalid {
    color: gray;
}

.underline {
    text-decoration: underline;
}


/* Tooltip container */
.tooltip {
  position: relative;
  display: inline-block;
  z-index: 10;
}

/* Tooltip text */
.tooltip .tooltiptext {
  visibility: hidden;
  /*width: 120px;*/
  background-color: #555;
  color: #fff;
  text-align: left;
  padding: 5px 0;
  border-radius: 6px;
  font-size: 13px;

  /* Position the tooltip text */
  position: absolute;
  z-index: 9999; /* Make sure tooltip is above all other elements */
  bottom: 125%;
  left: 0%;
  margin-left: -190px;

  white-space: normal;

  /* Fade in tooltip */
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
}

/* Tooltip arrow
.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  right: 10%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #555 transparent transparent transparent;
}*/

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext:not(.tooltiptext-novisible) {
    visibility: visible;
    opacity: 1;
}

.tooltip-portal {
    background-color: #555;
    color: #fff;
    text-align: left;
    border-radius: 6px;
    padding: 5px;
    font-size: 13px;
    white-space: normal;
    /* Opzionale: aggiungi ombreggiatura e transizione */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    transition: opacity 0.3s;
}

.right {
    float: right;
}

.left {
    float: left;
}

/* Generic css */
.padding-none {
    padding: 0;
}

.padding3 {
    padding: 3px;
}

.padding6 {
    padding: 6px;
}

.padding8 {
    padding: 8px;
}

.padding16 {
    padding: 16px;
}

.margin0 {
    margin: 0px;
}

.margin2 {
    margin: 2px;
}

.margin6 {
    margin: 6px;
}

.margin8 {
    margin: 8px;
}
.margin16 {
    margin: 16px;
}

.margin-left8 {
    margin-left: 8px;
}

.margin-left4 {
    margin-left: 4px;
}

.margin-left16 {
    margin-left: 16px;
}

.margin-left32 {
    margin-left: 32px;
}

.margin-right8 {
    margin-right: 8px;
}

.margin-right16 {
    margin-right: 16px;
}

.margin-right32 {
    margin-right: 32px;
}

.margin-top8 {
    margin-top: 8px;
}

.margin-top10 {
    margin-top: 10px;
}

.margin-top16 {
    margin-top: 16px;
}

.margin-bottom8 {
    margin-bottom: 8px;
}

.margin-bottom16 {
    margin-bottom: 16px;
}

.margin-btn-small-jqx-table {
    margin: 6px 2px;
}

.font70p {
    font-size: 70%;
}

.font90p {
    font-size: 90%;
}

.font105p {
    font-size: 105%;
}

.font110p {
    font-size: 110%;
}

.font120p {
    font-size: 120%;
}

.bordrad5 {
    border-radius: 5px;
}

.smaller {
    font-size: smaller;
}
.padding-left-8 {
    padding-left: 8px;
}
.padding-bottom-4 {
    padding-bottom: 4px;
}
.padding-bottom-2 {
    padding-bottom: 2px;
}
.padding-bottom-10 {
    padding-bottom: 10px;
}
.vertical-center {
    display: flex;
    align-items: center;
}
.horizontal-center {
    justify-content: center;
}
.horizontal-right {
    justify-content: flex-end;
}
.height-20 {
    height: 20px;
}
.height-25 {
    height: 25px;
}
.height-28 {
    height: 28px;
}
.min-height-28 {
    min-height: 28px;
}

.centered_modal {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    overflow-y: auto;
}

.ombra_lingua_registro {
    box-shadow: 1px 1px 12px 0px rgb(0 0 0 / 30%)
}

.margin-top-bottom-4 {
    margin: 4px 0;
}

.container-flex {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: start;
    align-content: start;
}
.item-flex {
    flex: 0 0 50%;
    min-width: 450px;
}
.item-flex:hover {
    background-color: ghostwhite;
}

.hover_opacity:hover {
    opacity: 0.88;
}

.row-container-first-div-left {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}
.row-container-first-div-left > div:first-child {
    margin-right: auto;
}

.content-block {
	display: block;
    overflow: auto;
	width: 100%;
}

.sfondo_grigio_leggero {
    background-color: #e5e5e53d;
    border-collapse: collapse;
}

.input-mc {
    font-family: inherit;
    background-color: #dedede1c;
    padding: 1px 1px 1px 0;
    border-radius: 2px;
    border: 1px solid rgba(0,0,0, 0.12);
    cursor: pointer;
}
.padding-left-3 {
    padding-left: 3px;
}
.message-success {
    background-color: darkgreen;
}
.message-failure {
    background-color: red;
}

.coverAll {
    z-index: 1000;
    background-color: #CCC;
    position: absolute;
    top: 0px;
    left: 0px;
    min-width: 100%;
    min-height: 100%;
}

.vertical-text-bt{
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    -webkit-writing-mode: vertical-rl;
    writing-mode: vertical-rl;
}

.view-tabellone-competenze {
    width: 100%;
    max-width: 99vw;
    margin: auto;
}

.wrapper-tabellone-competenze {
    position: relative;
    overflow: auto;
    height: 85vh;
}

.view-tabellone-competenze thead td {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1;
    max-height: 200px;
    box-shadow: 0px -1px 3px 0px #8f8f8f;
    box-shadow: 0px 1px 3px 0px #8f8f8f;
}

.view-tabellone-competenze tbody td:first-child {
    position: -webkit-sticky;
    position: sticky;
    left: 0;
    min-width: 20vw;
    max-width: 20vw;
    box-shadow: 1px 0px 3px 0px #8f8f8f;
}

.view-tabellone-competenze thead td:first-child {
    left: 0;
    z-index: 2;
    min-width: 20vw;
    max-width: 20vw;
    box-shadow: 0px 1px 3px 0px #8f8f8f;
}

.tabellone-competenze-int-proposte{
    background-color: #769583;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .65);
}

.tabellone-competenze-col1-proposte{
    background-color: #c5d5c5;
    color: black;
}

.tabellone-competenze-int-aperto{
    background-color: #439b5a;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .65);
}

.tabellone-competenze-col1-aperto{
    background-color: #c2dfbc;
    color: black;
}

.tabellone-competenze-int-chiuso{
    /* background-color: #cfac2e; */
    background-color: #Ffa500;
    color: black;
}

.tabellone-competenze-col1-chiuso{
    /* background-color: #e7e2c1; */
    background-color: #Fff860;
    color: black;
}

.tabellone-competenze-int-pubblicato{
    background-color: #ef4f4f;
    color: black;
}

.tabellone-competenze-col1-pubblicato{
    background-color: #efbbbb;
    color: black;
}

.popup-tabellone-competenze{
    position: absolute;
    display: none;
    z-index: 3;
    min-width: 300px;
    max-width: 300px;
    padding: 6px;
    background-color: #3A3A3A;
    color: #FAFAFA;
    border-radius: 5px;
    box-shadow: grey 2px 2px 8px 0px;
}

.btn_elimina_x {
    padding: 6px 16px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 0;
    border-radius: 5px;
    background-color: transparent;
    font-weight: bold;
    outline: 0;
    color: #ff0000;
    border: 1px solid #d50000;

}
.btn_elimina_x:hover {
    background-color: #ff0000;
    color: whitesmoke;
}

.background-red-33-hov {
    background-color: rgba(255, 0, 0, 0.33);
}
.background-red-33-hov:hover {
    background-color: rgba(255, 0, 0, 0.8) !important;
}
.background-yellow-33-hov {
    background-color: rgba(255, 171, 0, 0.33); 
}
.background-yellow-33-hov:hover {
    background-color: rgba(255, 171, 0, 0.8) !important;
}
.btn_elimina_x_selected {
    background-color: #ff0000;
    color: whitesmoke;
}
.center {
	text-align: center!important
}
.width-80px {
    width: 80px;
}
.width-40px {
    width: 40px;
}
.btn_flat:disabled {
    outline: 0;
    cursor: not-allowed;
}
.btn_flat_new:disabled {    
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}
.input_modified_data {
    background-color: #ffde2161;
}

/* Modern styling for text inputs and text areas */
.form-input {
    width: 100%;
    padding: 10px;
    margin: 8px 0;
    border: 1px solid #ccc;
    border-radius: 4px;
    /* font-size: 1.1em; */
    font-family: inherit;
    box-sizing: border-box;
    background-color: #fff;
}

/* Nuovo stile per evidenziare i prompt cliccabili */
.clickable-prompt {
    padding: 4px;
    cursor: pointer;
    background-color: #f9f9f9;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: background-color 0.3s, border-color 0.3s;
}
.clickable-prompt:hover {
    background-color: #e6f2ff;
    border-color: var(--mastercom-light-blue);
}

@media screen and (min-width:1425px) {
    .item-flex {flex: 0 0 33%;}
}

@media screen and (max-width: 992px) {
    .container-flex {justify-content: center;}
    .item-flex {flex: 0 0 100%;}
}

.switch-container {
    padding-top: 4px;
}
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}
.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}
input:checked + .slider {
    background-color: #2196F3;
}
input:checked + .slider:before {
    transform: translateX(26px);
}
.slider.round {
    border-radius: 24px;
}
.slider.round:before {
    border-radius: 50%;
}

.parametro-container {
    display: flex;
    align-items: center; /* vertical centering */
    margin-bottom: 10px; /* optional: space between rows */
}

.parametro-label {
    flex: 0 0 50%;
    text-align: right;
    padding-right: 10px;
    box-sizing: border-box;
}

.parametro-input {
    flex: 1;
    text-align: left;
}

.span_status {
    padding: 5px 10px;
    font-size: 13px;
    border: none;
    border-radius: 4px;
    margin: 2px;
    color: white;
    width: 100px;
    min-width: fit-content;
    display: inline-block;
    box-sizing: border-box;
    font-weight: bold;
    text-align: center;
    cursor: default;
}

.btn-waiting { background-color: #6c757d; }        /* Gray */
.btn-in-progress { background-color: #007bff; }    /* Blue */
.btn-completed { background-color: #17a2b8; }      /* Teal */
.btn-error { background-color: #dc3545; }          /* Red */

.sfondo_verde_light {
    background-color: #28a745;
}
.sfondo_arancio_new { 
    background-color: #fd7e14; 
}   

.btn_responsive_padding {
}

@media screen and (max-width: 1357px) {
    .btn_responsive_padding {
        padding-left: 3px !important;
        padding-right: 3px !important;
    }
}
table.grid-container {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    font-family: Arial, sans-serif;
    background-color: #fff;
    border: 1px solid #ccc;
    font-size: 14px;
}

thead.grid-header {
    background-color: #f0f0f0;
}

.grid-header-cell {
    text-align: left;
    padding: 8px 12px;
    font-weight: bold;
    border-bottom: 2px solid #ccc;
}

.grid-row:nth-child(even) {
    background-color: #f9f9f9;
}

.grid-cell {
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    vertical-align: baseline;
}

.btn_disabled {   
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

.scheda_flex_responsive {
    display: flex;
    flex-direction: row; 
    justify-content: space-between;
}
@media screen and (max-width: 1500px) {
    .scheda_flex_responsive {
        flex-direction: column;
    }
}