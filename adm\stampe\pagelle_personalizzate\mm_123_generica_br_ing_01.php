<?php
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'mm_123_generica_br_ing_01', 'Pagella Scuola Secondaria di I Grado', 1, 'br-ing', 2);

 */

$orientamento = 'P';
$formato = 'A4';

if ($periodo == 27) {
    $periodo_pagella = 'intermedia';
}
elseif ($periodo == 29) {
    $periodo_pagella = 'finale';
}
else {
    $periodo_pagella = "pagellina" . ($periodo-20);
}

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">

    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $id_classe = $parametri_stampa['id_classe'];
    $periodo = $parametri_stampa['periodo'];
    $data_attestato = "{$parametri_stampa['data_day']}/{$parametri_stampa['data_month']}/{$parametri_stampa['data_year']}";

    $id_studente = $studente['id_studente'];
    $dati_studente = estrai_dati_studente((int) $id_studente);
    $dati_classe = estrai_classe((int) $id_classe);
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);

    // Estrazione voti
    $voti_pagella_p1 = estrai_voti_pagellina_studente_multi_classe($id_classe, 21, $studente['id_studente']);
    $voti_pagella_p2 = estrai_voti_pagellina_studente_multi_classe($id_classe, 22, $studente['id_studente']);
    $voti_pagella_q1 = estrai_voti_pagellina_studente_multi_classe($id_classe, 27, $studente['id_studente']);
    $voti_pagella_q2 = estrai_voti_pagellina_studente_multi_classe($id_classe, 29, $studente['id_studente']);

    if ($periodo == 21) {
        $voti_pagella_p2 = $voti_pagella_q1 = $voti_pagella_q2 = [];
    }
    if ($periodo == 27) {
        $voti_pagella_q2 = $voti_pagella_p2 = [];
    }
    if ($periodo == 22) {
        $voti_pagella_q2 = [];
    }

    $arr_voti = $condotta = $religione = [];
    $voto_condotta_finale = '';
    $resoconto = $competenze = $osservazione = [];
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            && !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA', 'RELIGIONE', 'SOSTEGNO'])

            )
        {
            $arr_voti[$id_materia]['p1'] = $voti_pagella_p1[$id_materia];
            $arr_voti[$id_materia]['p2'] = $voti_pagella_p2[$id_materia];
            $arr_voti[$id_materia]['q1'] = $voti_pagella_q1[$id_materia];
            $arr_voti[$id_materia]['q2'] = $voti_pagella_q2[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV'
            && (
                    (
                    $studente['esonero_religione'] == 0 && $materia['tipo_materia'] == 'RELIGIONE'
                    ||
                    $studente['esonero_religione'] == 1 && $materia['tipo_materia'] == 'ALTERNATIVA'
                    )
                )
            )
        {
            $arr_voti[$id_materia]['p1'] = $voti_pagella_p1[$id_materia];
            $arr_voti[$id_materia]['p2'] = $voti_pagella_p2[$id_materia];
            $arr_voti[$id_materia]['q1'] = $voti_pagella_q1[$id_materia];
            $arr_voti[$id_materia]['q2'] = $voti_pagella_q2[$id_materia];
        }

        // condotta
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {
            $condotta[$id_materia]['p1'] = $voti_pagella_p1[$id_materia];
            $condotta[$id_materia]['p2'] = $voti_pagella_p2[$id_materia];
            $condotta[$id_materia]['q1'] = $voti_pagella_q1[$id_materia];
            $condotta[$id_materia]['q2'] = $voti_pagella_q2[$id_materia];

            foreach ($voti_pagella_q2[$id_materia]['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voti_pagella_q2[$id_materia]['voto_pagellina'])
                {
                    //$p1 = $significato['codice'].' | '.decode($significato['valore']);
                    $voto_condotta_finale = decode($significato['valore']);
                }
            }

            foreach ($voti_pagella_p1[$id_materia]['campi_liberi'] as $campo_libero)
            {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0)
                {
                    if ( stripos($campo_libero['nome'], 'resoconto didattico') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $resoconto['p1'] = $value;
                        }
                    }
                    elseif ( stripos($campo_libero['nome'], 'area competenze') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $competenze['p1'] = $value;
                        }
                    }
                    elseif ( stripos($campo_libero['nome'], 'osservazione') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        $liv = estrai_valore_campo_libero_selezionato($campo_libero, 'codice');
                        if ($value !== '') {
                            $osservazione['p1']['desc_liv'] = $liv;
                            $osservazione['p1']['liv'] = $value;
                        }
                    }
                }
            }
            foreach ($voti_pagella_p2[$id_materia]['campi_liberi'] as $campo_libero)
            {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0)
                {
                    if ( stripos($campo_libero['nome'], 'resoconto didattico') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $resoconto['p2'] = $value;
                        }
                    }
                    elseif ( stripos($campo_libero['nome'], 'area competenze') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $competenze['p2'] = $value;
                        }
                    }
                    elseif ( stripos($campo_libero['nome'], 'osservazione') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        $liv = estrai_valore_campo_libero_selezionato($campo_libero, 'codice');
                        if ($value !== '') {
                            $osservazione['p2']['desc_liv'] = $liv;
                            $osservazione['p2']['liv'] = $value;
                        }
                    }
                }
            }
            foreach ($voti_pagella_q1[$id_materia]['campi_liberi'] as $campo_libero)
            {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0)
                {
                    if ( stripos($campo_libero['nome'], 'resoconto didattico') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $resoconto['q1'] = $value;
                        }
                    }
                    elseif ( stripos($campo_libero['nome'], 'area competenze') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $competenze['q1'] = $value;
                        }
                    }
                    elseif ( stripos($campo_libero['nome'], 'osservazione') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        $liv = estrai_valore_campo_libero_selezionato($campo_libero, 'codice');
                        if ($value !== '') {
                            $osservazione['q1']['desc_liv'] = $liv;
                            $osservazione['q1']['liv'] = $value;
                        }
                    }
                }
            }
            foreach ($voti_pagella_q2[$id_materia]['campi_liberi'] as $campo_libero)
            {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0)
                {
                    if ( stripos($campo_libero['nome'], 'resoconto didattico') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $resoconto['q2'] = $value;
                        }
                    }
                    elseif ( stripos($campo_libero['nome'], 'area competenze') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $competenze['q2'] = $value;
                        }
                    }
                    elseif ( stripos($campo_libero['nome'], 'osservazione') !== false )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        $liv = estrai_valore_campo_libero_selezionato($campo_libero, 'codice');
                        if ($value !== '') {
                            $osservazione['q2']['desc_liv'] = $liv;
                            $osservazione['q2']['liv'] = $value;
                        }
                    }
                }
            }
        }
    }


    // Dizionario temporaneo
    $labels = [
        "nato"  => "NAT||max_oa|| IL:",
        "iscritto"  => "ISCRITT||max_oa|| ALLA CLASSE:"
    ];


    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }

    $esito = $labels['attestato_esito'];


    $footer_html =
            "br-ingMeAPP S.r.l. Sede Legale: CASTELVENERE (BN) - Via San Tommaso, snc c.a.p. 82037;<br>
Sede Operativa: CASERTA (CE) - Via Cilea, 24 c.a.p. 81100;<br>
C.F./PARTITA IVA: 01663490629, REA: BN-138448, Capitale Sociale: € 30.000,00";
    $firma_dirigente = "<br><br><br><br><br>___________________________";
    $firma_dirigente = '<img src="immagini_scuola/firma_dirigente.jpg" width="130">';
    //}}} </editor-fold>

    $f = 'helvetica';
    $fd = 10;

    //{{{ <editor-fold defaultstate="collapsed" desc="pdf pagella voti">
    $pdf->AddPage('P');
    $pdf->SetAutoPageBreak('off', 1);
    $pdf->SetFont($f, '', $fd);

    inserisci_intestazione_pdf($pdf, $id_classe);
    $pdf->SetXY(25, 70);
    $pdf->SetFont($f, 'B', $fd+4);
    $pdf->CellFitScale(0, 0, "SCHEDA SCUOLA", 0, 1, 'L', 0);
    $pdf->ln(15);
    $pdf->SetFont($f, '', $fd+2);
    $pdf->writeHTMLCell(0, 0, 25, '', "ISTITUZIONE SCOLASTICA: br-ing - Scuola Secondaria di Primo Grado Paritaria Bilingue", 0, 1, false, true, 'L');
    $pdf->ln(7);
    $pdf->writeHTMLCell(0, 0, 25, '', "DENOMINAZIONE: ".'<font color="blue">b</font><font color="red">r</font><font color="green">-i</font><font color="red">n</font><font color="blue">g</font>*', 0, 1, false, true, 'L');
    $pdf->ln(7);
    $pdf->writeHTMLCell(0, 0, 25, '', "CODICE MECCANOGRAFICO: <b>{$studente['codice_meccanografico']}</b>", 0, 1, false, true, 'L');

    $pdf->ln(38);
    $pdf->SetTextColor(0,133,201);
    $pdf->SetFont($f, 'B', $fd+2);
    $pdf->writeHTMLCell(0, 0, 25, '', "SCHEDA ALUNNO", 0, 1, false, true, 'L');
    $pdf->SetFont($f, '', $fd+1);
    $pdf->ln(12);
    $pdf->writeHTMLCell(65, 0, 25, '', "Anno Scolastico:", 0, 0, false, true, 'L');
    $pdf->SetFont($f, 'B', $fd+1);
    $pdf->writeHTMLCell(0, 0, '', '', $anno_scolastico_attuale, 0, 1, false, true, 'L');
    $pdf->ln(5);
    $pdf->SetFont($f, '', $fd+1);
    $pdf->writeHTMLCell(65, 0, 25, '', "COGNOME e NOME:", 0, 0, false, true, 'L');
    $pdf->SetFont($f, 'B', $fd+1);
    $pdf->writeHTMLCell(0, 0, '', '', "{$studente['cognome']} {$studente['nome']}", 0, 1, false, true, 'L');
    $pdf->ln(5);
    $pdf->SetFont($f, '', $fd+1);
    $pdf->writeHTMLCell(65, 0, 25, '', $labels['nato'], 0, 0, false, true, 'L');
    $pdf->SetFont($f, 'B', $fd+1);
    $pdf->writeHTMLCell(0, 0, '', '', date('d/m/Y', $studente['data_nascita']), 0, 1, false, true, 'L');
    $pdf->ln(5);
    $pdf->SetFont($f, '', $fd+1);
    $pdf->writeHTMLCell(65, 0, 25, '', $labels['iscritto'], 0, 0, false, true, 'L');
    $pdf->SetFont($f, 'B', $fd+1);
    $pdf->writeHTMLCell(0, 0, '', '', "{$studente['classe']}^ Media - ".($studente['classe']+5)."th Grade", 0, 1, false, true, 'L');
    $pdf->SetFont($f, '', $fd-2);
    $pdf->writeHTMLCell(0, 0, 25, 280, $footer_html, 0, 0, false, true, 'L');

    $fd = 9;
    $pdf->AddPage('P');
    $pdf->SetTextColor(0,0,0);
    $pdf->SetFont($f, '', $fd);
    inserisci_intestazione_pdf($pdf, $id_classe);
    $pdf->SetY(55);
//border-color: #0085C9;
    $tbl = '<table cellpadding="2" align="center">'
            . '<tr>'
                . '<td width="31%"  style="border: 0.1px solid #0085C9;" bgcolor="blue"><b><font color="white">AREA DIDATTICA</font></b></td>'
                . '<td width="17%"  style="border: 0.1px solid #0085C9;" bgcolor="#A8A8A8"><b><font color="white">1° Bimestre<br>Mark | Indicator</font></b></td>'
                . '<td width="17%"  style="border: 0.1px solid #0085C9;" bgcolor="#505050"><b><font color="white">1° Quad.(2° Bim.)<br>Mark | Indicator</font></b></td>'
                . '<td width="1%"  bgcolor="#E8E8E8"></td>'
                . '<td width="17%"  style="border: 0.1px solid #0085C9;" bgcolor="#BA55D3"><b><font color="white">3° Bimestre<br>Mark | Indicator</font></b></td>'
                . '<td width="17%"  style="border: 0.1px solid #0085C9;" bgcolor="purple"><b><font color="white">2° Quad.(4° Bim.)<br>Mark | Indicator</font></b></td>'
            . '</tr>';
    foreach ($arr_voti as $id_materia => $mat_voti) {
        $desc = $p1 = $p2 = $q1 = $q2 = '';

        $desc = $mat_voti['p1']['descrizione'];

        $p1 = $mat_voti['p1']['voto_pagellina'];
        foreach ($mat_voti['p1']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $mat_voti['p1']['voto_pagellina'])
            {
                //$p1 = $significato['codice'].' | '.decode($significato['valore']);
                $p1 = decode($significato['valore']);
            }
        }
        $p2 = $mat_voti['p2']['voto_pagellina'];
        foreach ($mat_voti['p2']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $mat_voti['p2']['voto_pagellina'])
            {
                //$p2 = $significato['codice'].' | '.decode($significato['valore']);
                $p2 = decode($significato['valore']);
            }
        }
        $q1 = $mat_voti['q1']['voto_pagellina'];
        foreach ($mat_voti['q1']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $mat_voti['q1']['voto_pagellina'])
            {
                //$q1 = $significato['codice'].' | '.decode($significato['valore']);
                $q1 = decode($significato['valore']);
            }
        }
        $q2 = $mat_voti['q2']['voto_pagellina'];
        foreach ($mat_voti['q2']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $mat_voti['q2']['voto_pagellina'])
            {
                //$q2 = $significato['codice'].' | '.decode($significato['valore']);
                $q2 = decode($significato['valore']);
            }
        }
        $tbl .=
            '<tr>'
                . '<td  style="border: 0.1px solid #0085C9;">'.$desc.'</td>'
                . '<td  style="border: 0.1px solid #0085C9;">'.$p1.'</td>'
                . '<td  style="border: 0.1px solid #0085C9;">'.$q1.'</td>'
                . '<td bgcolor="#E8E8E8"></td>'
                . '<td  style="border: 0.1px solid #0085C9;">'.$p2.'</td>'
                . '<td  style="border: 0.1px solid #0085C9;">'.$q2.'</td>'
            . '</tr>';
    }
    $tbl .=
            '<tr  style="line-height: 15%;" bgcolor="#E8E8E8">'
                . '<td >'.'</td>'
                . '<td >'.'</td>'
                . '<td >'.'</td>'
                . '<td ></td>'
                . '<td >'.'</td>'
                . '<td >'.'</td>'
            . '</tr>
            <tr>'
                . '<td  style="border: 0.1px solid #0085C9;" bgcolor="blue"><b><font color="white">RESOCONTO DIDATTICO</font></b></td>'
                . '<td  style="border: 0.1px solid #0085C9;"><b>'.$resoconto['p1'].'</b></td>'
                . '<td  style="border: 0.1px solid #0085C9;"><b>'.$resoconto['q1'].'</b></td>'
                . '<td  bgcolor="#E8E8E8"></td>'
                . '<td  style="border: 0.1px solid #0085C9;"><b>'.$resoconto['p2'].'</b></td>'
                . '<td  style="border: 0.1px solid #0085C9;"><b>'.$resoconto['q2'].'</b></td>'
            . '</tr>';
    $tbl .=  '</table>';
    $pdf->writeHTMLCell(0, 0, '', '', $tbl, 0, 1, false, true, 'C');
    $pdf->ln(6);
    $tbl_comp = '<table cellpadding="2" align="center">'
            . '<tr>'
                . '<td style="border: 0.1px solid #0085C9;" width="31%" rowspan="5" bgcolor="red"><b><font color="white">AREA COMPETENZE</font></b></td>'
                . '<td style="border: 0.1px solid #0085C9;" width="17%" bgcolor="#ffcccb"><b>1° Bimestre</b></td>'
                . '<td style="border: 0.1px solid #0085C9;" width="17%" >'.$competenze['p1'].'</td>'
            . '</tr>'
            . '<tr>'
                . '<td style="border: 0.1px solid #0085C9;" bgcolor="#ffcccb"><b>1° Quad.(2° Bim.)</b></td>'
                . '<td style="border: 0.1px solid #0085C9;" >'.$competenze['q1'].'</td>'
            . '</tr>'
            . '<tr style="line-height: 15%;"bgcolor="#E8E8E8">'
                . '<td ></td>'
                . '<td ></td>'
            . '</tr>'
            . '<tr>'
                . '<td style="border: 0.1px solid #0085C9;" bgcolor="#ffcccb"><b>3° Bimestre</b></td>'
                . '<td style="border: 0.1px solid #0085C9;" >'.$competenze['p2'].'</td>'
            . '</tr>'
            . '<tr>'
                . '<td style="border: 0.1px solid #0085C9;" bgcolor="#ffcccb"><b>2° Quad.(4° Bim.)</b></td>'
                . '<td style="border: 0.1px solid #0085C9;" >'.$competenze['q2'].'</td>'
            . '</tr>'
            . '</table>';
    $pdf->writeHTMLCell(0, 0, '', '', $tbl_comp, 0, 1, false, true, 'C');
    $pdf->ln(6);
    $tbl_osservazioni = '<table cellpadding="1" align="center">'
            . '<tr>'
                . '<td  style="border: 0.1px solid #0085C9;" width="18%" rowspan="5" bgcolor="green"><b><font color="white">OSSERVAZIONE<br>COMPLESSIVA</font></b></td>'
                . '<td style="border: 0.1px solid #0085C9;" width="15%" bgcolor="#c7ffc8"><b>1° Bimestre</b></td>'
                . '<td style="border: 0.1px solid #0085C9;" width="15%">'.$osservazione['p1']['liv'].'</td>'
                . '<td style="border: 0.1px solid #0085C9;" width="52%" align="left">'.$osservazione['p1']['desc_liv'].'</td>'
            . '</tr>'
            . '<tr>'
                . '<td style="border: 0.1px solid #0085C9;" bgcolor="#c7ffc8"><b>1° Quad.(2° Bim.)</b></td>'
                . '<td style="border: 0.1px solid #0085C9;" >'.$osservazione['q1']['liv'].'</td>'
                . '<td style="border: 0.1px solid #0085C9;" align="left">'.$osservazione['q1']['desc_liv'].'</td>'
            . '</tr>'
            . '<tr style="line-height: 15%;" bgcolor="#E8E8E8">'
                . '<td ></td>'
                . '<td ></td>'
                . '<td ></td>'
            . '</tr>'
            . '<tr>'
                . '<td style="border: 0.1px solid #0085C9;"  bgcolor="#c7ffc8"><b>3° Bimestre</b></td>'
                . '<td style="border: 0.1px solid #0085C9;" >'.$osservazione['p2']['liv'].'</td>'
                . '<td style="border: 0.1px solid #0085C9;" align="left">'.$osservazione['p2']['desc_liv'].'</td>'
            . '</tr>'
            . '<tr>'
                . '<td style="border: 0.1px solid #0085C9;"  bgcolor="#c7ffc8"><b>2° Quad.(4° Bim.)</b></td>'
                . '<td style="border: 0.1px solid #0085C9;" >'.$osservazione['q2']['liv'].'</td>'
                . '<td style="border: 0.1px solid #0085C9;" align="left">'.$osservazione['q2']['desc_liv'].'</td>'
            . '</tr>'
            . '</table>';
    $pdf->writeHTMLCell(0, 0, '', '', $tbl_osservazioni, 0, 1, false, true, 'C');
    $pdf->ln(4);

    if ($periodo == 29) {
        $tbl_cond = '<table cellpadding="2" align="center">'
            . '<tr>'
                . '<td style="border: 0.1px solid #0085C9;" width="31%" rowspan="5" bgcolor="#595959;"><b><font color="white">CONDOTTA</font></b></td>'
                . '<td style="border: 0.1px solid #0085C9;" width="17%" bgcolor="#b3b3b3"><b>2° Quad.(4° Bim.)</b></td>'
                . '<td style="border: 0.1px solid #0085C9;" width="17%" >'
                . $voto_condotta_finale
                . '</td>'
            . '</tr>'
        . '</table>';
        $pdf->writeHTMLCell(0, 0, '', '', $tbl_cond, 0, 1, false, true, 'C');
        $pdf->ln(4);
    }


    $tbl_firme =
        '<table>'
        . '<tr>'
            . '<td width="33%">Data Scrutinio<br><b>'.$data_attestato.'</b></td>'
            . '<td width="33%">Firma Genitore o Tutore legale<br><br><br>___________________________<br><br><br>___________________________</td>'
            . '<td width="33%">Coordinatore delle attività didattiche ed<br>educative<br>'.$firma_dirigente.'</td>'
        . '</tr>'
    . '</table>';
    $tbl_firme =
        '<table>'
        . '<tr>'
            . '<td width="25%">Data Scrutinio<br><b>'.$data_attestato.'</b></td>'
            . '<td width="30%">Firma Genitore o Tutore legale<br><br><br>___________________________<br><br><br>___________________________</td>'
            . '<td width="45%" align="center">Coordinatore delle attività didattiche ed educative<br><i>Serena Pulcino</i><br><br>Supervisore delle attività metacognitive e Global Perspectives<br><i>Daniele Della Rosa</i><br><br><small>Firma autografa sostituita a mezzo stampa ai sensi dell’art. 3, comma 2, del D.Lgs. 12 febbraio 1993, n. 39</small></td>'
        . '</tr>'
    . '</table>';
    $pdf->writeHTMLCell(0, 0, '', '', $tbl_firme, 0, 1, false, true, 'L');

    $pdf->SetTextColor(0,133,201);
    $pdf->SetFont($f, '', $fd-2);
    $pdf->writeHTMLCell(0, 0, '', 280, $footer_html, 0, 0, false, true, 'L');
    //}}} </editor-fold>
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'         => $id_classe,
    'data_day'          => $data_Day,
    'data_month'        => $data_Month,
    'data_year'         => $data_Year,
    'orientamento'      => $orientamento,
    'formato_pagella'   => $formato_pagella,
    'periodo'           => $periodo

];

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
