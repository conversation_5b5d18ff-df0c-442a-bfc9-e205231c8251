<?php

/*
 * Tipo: Pagella Fine Anno Scuola Secondaria di I Grado OSDB-mi
 * Nome: mm_123_finale_A3_osdb_mi_01
 * Richiesta da: OSDB-mi
 * Data: 22/05/2020
 *
 * Materie particolari:
 *
 */
/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'mm_123_finale_A3_osdb_mi_01', 'Pagella Fine Anno Scuola Secondaria di I Grado', 1, 'OSDB-mi', 2);
 */

$stampa_personalizzata = 'SI';
$orientamento = 'L';
$formato = 'A3';
$periodo_pagella = 'finale';

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'                     => $id_classe,
    'periodo_pagella'               => $periodo_pagella,
    'orientamento'                  => $orientamento,

    'data_attestato_day'            => $data_attestato_Day,
    'data_attestato_month'          => $data_attestato_Month,
    'data_attestato_year'           => $data_attestato_Year,

    'data_attestato_IQ_day'            => $data_attestato_IQ_Day,
    'data_attestato_IQ_month'          => $data_attestato_IQ_Month,
    'data_attestato_IQ_year'           => $data_attestato_IQ_Year,

    'num_prot'                      => $num_prot,
    'stampa_attestato'              => $stampa_attestato,
    'current_key'              => $current_key
];

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    $orientamento = $parametri_stampa['orientamento'];
    $id_classe = $parametri_stampa['id_classe'];
    $current_key = $parametri_stampa['current_key'];
    $classe = $studente['classe'];
    $sezione = $studente['sezione'];

    $num_prot = $parametri_stampa['num_prot'];
    $stampa_attestato = $parametri_stampa['stampa_attestato'];

    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $scuola_provincia = estrai_provincia($studente['provincia_comuni'])['nome'];
    $scuola_tipo = explode('Paritaria', $studente['descrizione_sedi'])[0] . ' Paritaria';
    $scuola_nome = explode('Paritaria', $studente['descrizione_sedi'])[1];

    $data_attestato_day = $parametri_stampa['data_attestato_day'];
    $data_attestato_month = $parametri_stampa['data_attestato_month'];
    $data_attestato_year = $parametri_stampa['data_attestato_year'];

    $data_attestato_IQ_day = $parametri_stampa['data_attestato_IQ_day'];
    $data_attestato_IQ_month = $parametri_stampa['data_attestato_IQ_month'];
    $data_attestato_IQ_year = $parametri_stampa['data_attestato_IQ_year'];

    $stampa_pai = [];

    switch($data_attestato_month) {
        case 1: $mese_ita = 'Gennaio';break;
        case 2: $mese_ita = 'Febbraio';break;
        case 3: $mese_ita = 'Marzo';break;
        case 4: $mese_ita = 'Aprile';break;
        case 5: $mese_ita = 'Maggio';break;
        case 6: $mese_ita = 'Giugno';break;
        case 7: $mese_ita = 'Luglio';break;
        case 8: $mese_ita = 'Agosto';break;
        case 9: $mese_ita = 'Settembre';break;
        case 10: $mese_ita = 'Ottobre';break;
        case 11: $mese_ita = 'Novembre';break;
        case 12: $mese_ita = 'Dicembre';break;
    }
    $data_stampa_attestato = $data_attestato_day . ' ' . $mese_ita . ' ' . $data_attestato_year;

    $id_studente = $studente['id_studente'];
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .=  ' (' . $stato['descrizione'] . ')';
            $provincia_nascita = ' ';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }
    $classe_tradotta = '';
    switch ($studente['classe']) {
        case 1: $classe_tradotta = 'I';break;
        case 2: $classe_tradotta = 'II';break;
        case 3: $classe_tradotta = 'III';break;
    }

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    // Estrazione voti
    $voti_pagellina_primo_trimestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 27, $studente['id_studente']);
    $voti_pagella_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, 29, $studente['id_studente']);

    global $id_lab_opzionale;
    $arr_voti_finale = [];
    $arr_voti_finale_opt = [];
    $giudizi = [];
    $materie_opts = [];
    $materie_opts_set = false;
    $consiglio = '';
    $lingue_straniere = '';
    $comunicazioni_famiglia = '';

    $scrutinato = false;
    $monteore_finale = 0;
    $assenze_finale = 0;
    $comportamento = [];
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $monteore_finale += $voti_pagella_finale[$id_materia]['monteore_totale'];
        $assenze_finale += $voti_pagella_finale[$id_materia]['ore_assenza'];

        // Materie normali e attività opzionali
        if ($materia['in_media_pagelle'] != 'NV' && $materia['in_media_pagelle'] != 'NO' &&
            ( !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA', 'SOSTEGNO', 'OPZIONALE', 'RELIGIONE']) )
            )
        {
            if (strpos($materia['descrizione'], 'GIUDIZIO') === false)
            {
                $arr_voti_finale[$id_materia]['id_materia'] = $id_materia;
                $arr_voti_finale[$id_materia]['descrizione'] =  strtoupper( $materia['descrizione'] ) ;
                $arr_voti_finale[$id_materia]['primo_quadrimestre'] = $voti_pagellina_primo_trimestre[$id_materia];
                $arr_voti_finale[$id_materia]['finale'] = $voti_pagella_finale[$id_materia];
            }

            if (strpos(strtolower($materia['tipologia_aggregamento']), 'lingue straniere') !== false)
            {
                $lingue_straniere .= ' '.strtoupper( $materia['descrizione']).',';
            }

            foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero)
            {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0)
                {
                    if ( (strpos(strtoupper($campo_libero['nome']), 'PIANO DI APPRENDIMENTO INDIVIDUALIZZATO') !== false) )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {

                            $stampa_pai[$id_materia]['pai'] .= $value;
                            $stampa_pai[$id_materia]['materia'] = $materia['descrizione'];
                        }
                    }

                    if ( (strpos(strtoupper($campo_libero['nome']), 'STRATEGIE DI RECUPERO') !== false) )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $stampa_pai[$id_materia]['strategie'] .= $value;
                            $stampa_pai[$id_materia]['materia'] = $materia['descrizione'];
                            $stampa_pai[$id_materia]['strategie_nome'] = $campo_libero['nome'];
                        }
                    }
                }


            }


        }

        // CONDOTTA, GIUDIZI TRIMESTRE e FINALE
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA')
        {
            $arr_voti_finale[$id_materia]['id_materia'] = $id_materia;
            $arr_voti_finale[$id_materia]['descrizione'] =  strtoupper( $materia['descrizione'] ) ;
            $arr_voti_finale[$id_materia]['primo_quadrimestre'] = $voti_pagellina_primo_trimestre[$id_materia];
            $arr_voti_finale[$id_materia]['finale'] = $voti_pagella_finale[$id_materia];

            $comportamento[$id_materia]['id_materia'] = $id_materia;
            $comportamento[$id_materia]['descrizione'] =  strtoupper( $materia['descrizione'] ) ;
            $comportamento[$id_materia]['primo_quadrimestre'] = $voti_pagellina_primo_trimestre[$id_materia];
            $comportamento[$id_materia]['finale'] = $voti_pagella_finale[$id_materia];

            $giudizi['periodo_primo'] = '';
            $giudizi['periodo_finale'] = '';
            foreach ($voti_pagellina_primo_trimestre[$id_materia]['campi_liberi'] as $campo_libero)
            {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0)
                {

                    if ( (strpos(strtoupper($campo_libero['nome']), 'GIUDIZIO') !== false) )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                                $giudizi['periodo_primo'] .= $value . "\n";
                            }
                    }
                }

                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    if  (strpos(strtoupper($campo_libero['nome']), 'VALUTAZIONE DEL COMPORTAMENTO') !== false)
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '')
                        {
                            $giudizi['valutazione_primo_periodo'] = $value;
                        }
                    }
                }

            }
            foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero)
            {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0)
                {
                    if (
                            (strpos(strtoupper($campo_libero['nome']), 'GIUDIZIO') !== false)
//                            &&
//                            (strpos(strtoupper($campo_libero['nome']), 'GIUDIZIO DI IDONEIT') === false)
                        )
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                                $giudizi['periodo_finale'] .= $value . "\n";
                            }
                    }

                    if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                        if  (strpos(strtoupper($campo_libero['nome']), 'VALUTAZIONE DEL COMPORTAMENTO') !== false)
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value !== '')
                            {
                                $giudizi['valutazione_finale_periodo'] = $value;
                            }
                        }
                    }


                    if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                        if  (strpos(strtoupper($campo_libero['nome']), 'CONSIGLIO ORIENTATIVO') !== false)
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value !== '')
                            {
                                $consiglio .= $value;
                            }
                        }
                    }


                    if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                        if  (strpos(strtoupper($campo_libero['nome']), 'COMUNICAZIONI ALLA FAMIGLIA') !== false)
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value !== '')
                            {
                                $comunicazioni_famiglia .= $value;
                            }
                        }
                    }
                }
            }
        }
        // scrutinio
        if ($voti_pagella_finale[$id_materia]['voto_pagellina'] > 0 && $voti_pagella_finale[$id_materia]['voto_pagellina'] != '')
        {
            $scrutinato = true;
        }
    }
    $arr_voti_tot = $arr_voti_finale;

    // Validazione anno
    if ($monteore_finale > 0)
    {
        $perc_assenza = round($assenze_finale / $monteore_finale, 2) * 100;
        if ($perc_assenza < 25)
        {
            $validazione_anno = "SI";
        }
        else
        {
            $validazione_anno = ($scrutinato) ? "DEROGA" : "NO";
        }
    }
    else
    {
        $validazione_anno = "SI";
    }

    //{{{ <editor-fold defaultstate="collapsed" desc="dizionario">
    // Dizionario temporaneo
    $labels = [
        "giudizio_intermedio_1_quad"=> "VALUTAZIONE TRIMESTRE",
        "giudizio_finale"           => "VALUTAZIONE FINALE",

        "equipe_pedagogica"         => "I docenti dell’équipe pedagogica<br>".
                                       "________________________________________<br>".
                                       "________________________________________<br>".
                                       "________________________________________<br>".
                                       "________________________________________<br>".
                                       "________________________________________<br>",

        "dirigente"                 => "Il Dirigente scolastico\n{$studente['nome_dirigente']}",
        "firma_genitore"            => "Firma di uno dei genitori o di chi ne fa le veci",
        "titolo"                    => "Ministero dell’Istruzione, dell’Università e della Ricerca",
        "scuola_tipo"               => "Istituzione scolastica",
        "scuola_nome"               => "denominazione",
        "scuola_comune"             => "Comune",
        "scuola_via"                => "Via/Piazza &nbsp;&nbsp;&nbsp;&nbsp; n°",
        "scuola_provincia"          => "Provincia",

        "titolo_scheda"             => "DOCUMENTO DI VALUTAZIONE",
        "anno_scolastico"           => "Anno scolastico ",

        "alunno"                    => "dell’alunn||min_oa|| ",
        "alunno_1"                  => "L'alunn||min_oa|| ",
        "cf"                        => "C.F. ",
        "nascita_luogo"             => "Nat||min_oa|| a ",
        "nascita_data"              => "il ",
        "iscritto"                  => "Iscritt||min_oa|| alla classe ",
        "sezione"                   => " Sezione Unica",

        "attestato_titolo"          => "ATTESTATO",
        "attestato_desc"            => "Visti gli atti d’ufficio e vista la valutazione dell’équipe pedagogica si attesta che",
        "attestato_ammissione_1"    => "l’alunn||min_oa|| ha frequentato la classe ",
        "attestato_ammissione_2"    => "l’alunn||min_oa|| è stat||min_oa|| ",
        "timbro"                    => "Timbro<br>della scuola",

        "valutazioni"               => "VALUTAZIONI PERIODICHE",
        "val_periodo_primo"         => "1° trimestre",
        "val_periodo_finale"        => "Finale",
        "val_periodo_finale_lab"    => "Finale - LAB.:",

        "materie_opt"               => "Insegnamenti facoltativi opzionali",
        "attivita_facoltativa"      => "ATTIVITÀ/INSEGNAMENTO OPZIONALE: ",
        "attivita_facoltative_opt"  => "ATTIVITÀ/INSEGNAMENTO OPZIONALE/FACOLTATIVO: ",

        "comportamento"             => "COMPORTAMENTO",
        "giudizio_sintetico"        => "Giudizio sintetico:",

                "attestato_att"     => "si attesta che l'alunn||min_oa|| è ",
                "attestato_att2_terze"     => "ammess||min_oa|| all'Esame di Stato conclusivo del primo ciclo di istruzione",


        "att_1" => "Ai fini della validità dell’anno scolastico e dell’ammissione allo scrutinio finale, l’alunn||min_oa||:",


        "attestato_desc"            => "Visti gli atti d'ufficio e accertato che l'alunno, ai fini della validità dell'anno scolastico(c.1, art 5, D.Lgs. del 13/04/17, n.62),[X] ha [ ] non ha frequentato le lezioni e le attività didattiche per almeno i 3/4 dell'orario personale previsto [ ] non ha frequentato per almeno tre quarti dell'orario annuale, ma ha usufruito della deroga.",
        "consiglio"                 => "Vista la valutazione del Consiglio di Classe, si attesta che",

        "note"                      => "(1) Firma autografa sostituita a mezzo stampa secondo le indicazioni dell'art. 3 del Dlgs 39/1993.
(2) La valutazione del comportamento delle alunne e degli alunni viene espressa, per tutto il primo ciclo, mediante un giudizio sintetico che fa riferimento allo
sviluppo delle competenze di cittadinanza e, per quanto attiene alla scuola secondaria di primo grado, allo Statuto delle studentesse e degli studenti e al Patto
di corresponsabilità approvato dall’Istituzione Scolastica (Nota MIUR 1865 del 10/10/2017).
(3) ammesso/a( ovvero non ammesso/a) alla classe successiva oppure ammesso/a ( ovvero non ammesso/a) all'esame di Stato.",


        "txt_pai"           => "al termine dell&rsquo;A.S. $anno_scolastico_attuale presenta gravi/diverse/alcune carenze nel conseguimento di alcuni obiettivi previsti dalla programmazione annuale in una o pi&ugrave; materie di studio e in presenza di valutazioni inferiori a sei decimi. Il consiglio di classe predispone un piano di apprendimento individualizzato in cui sono indicati, per ciascuna disciplina, gli obiettivi di apprendimento da conseguire, ai fini della proficua prosecuzione del processo di apprendimento nella classe successiva, nonch&eacute; specifiche strategie per il miglioramento dei livelli di apprendimento.",
        "txt_pai_post"      => "L’alunn||min_oa|| è tenut||min_oa|| a impegnarsi nello svolgimento degli esercizi assegnati dagli/dall’insegnante per il periodo estivo.<br><br>A decorrere dal 1°settembre come indicato dal D.L. n°11 del 16 maggio 2020, l’alunn||min_oa|| seguirà attività di recupero relative al piano di integrazione degli apprendimenti, nonché al piano di apprendimento individualizzato."
    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
        $studente['esito'] = str_replace('Licenziato', 'Licenziata', $studente['esito']);
        $studente['esito'] = str_replace('licenziato', 'licenziata', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k=>$label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }


    if($studente['classe']== '3')
    {
        if($studente['esito_terza_media'] == 'SI')
        {
            $esito_medie = "l'alunn||min_oa|| è stat||min_oa|| ammess||min_oa|| all'esame di stato conclusivo del primo ciclo d'istruzione";
        }
        else
        {
            $esito_medie = "l'alunn||min_oa|| non è stat||min_oa|| ammess||min_oa|| all'esame di stato conclusivo del primo ciclo d'istruzione";
        }
    }
    elseif($studente['classe']== '2')
    {
        if($studente['esito_seconda_media'] == 'SI')
        {
            $esito_medie = "l'alunn||min_oa|| è stat||min_oa|| ammess||min_oa|| alla classe successiva";
        }
        else
        {
            $esito_medie = "l'alunn||min_oa|| non è stat||min_oa|| ammess||min_oa|| alla classe successiva";
        }
    }
    elseif($studente['classe']== '1')
    {
        if($studente['esito_prima_media'] == 'SI')
        {
            $esito_medie = "l'alunn||min_oa|| è stat||min_oa|| ammess||min_oa|| alla classe successiva";
        }
        else
        {
            $esito_medie = "l'alunn||min_oa|| non è stat||min_oa|| ammess||min_oa|| alla classe successiva";
        }
    }
    else
    {
        $esito_medie = '';
    }
    $esito_medie = str_replace("||min_oa||", $min_oa, $esito_medie);
    $esito_medie = str_replace("||min_eessa||", $min_eessa, $esito_medie);
    $esito_medie = str_replace("||max_oa||", $max_oa, $esito_medie);
    $esito_medie = str_replace("||max_eessa||", $max_eessa, $esito_medie);


    //}}} </editor-fold>
    //}}} </editor-fold>
//    switch ($validazione_anno) {
//        case 'SI':
//            $scrutinio_testo =  'ha frequentato almeno tre quarti dell’orario annuale.';
//            break;
//        case 'DEROGA':
//            $scrutinio_testo =  'non ha frequentato almeno tre quarti dell’orario annuale, ma ha usufruito delle deroga.';
//            break;
//        case 'NO':
//            $scrutinio_testo = 'non ha frequentato almeno tre quarti dell’orario annuale.';
//            break;
//    }
//    $scrutinio_testo =  'ha frequentato almeno tre quarti dell’orario annuale.';
//
//    echo $studente['esito'];
//
//    if ((int)$classe == 3) {
//        if ( stripos( $studente['esito'], 'NON' ) !== false ) {
//            $attestato_esito = $labels['attestato_att'] . 'Non ' . $labels['attestato_att2_terze'];
//        }
//        else {
//            $attestato_esito = $labels['attestato_att'] . $labels['attestato_att2_terze']; // promosso
//        }
//    }
//    else {
//        $attestato_esito = $labels['attestato_att'] . $studente['esito']; // 1 e 2, ammesso e non
//    }

    // PDF parametri
    $font = 'helvetica';
    $font_dim = 10;
    $h_header = 10;
    $img_logo = '';
    if (file_exists('immagini_scuola/logo_poloni.png'))        {
        $img_logo = 'immagini_scuola/logo_poloni.png';
    }
    $header = '<table>
            <tr>
                <td width="32%">' . $studente['cognome'] . '</td>
                <td width="32%">' . $studente['nome'] . '</td>
                <td width="25%">' . $studente['codice_fiscale'] . '</td>
                <td width="11%" align="right">' . $anno_scolastico_attuale . '</td>
            </tr>
        </table>';


    $pdf->AddPage($orientamento, 'A3');

    $pdf->SetAutoPageBreak("off", 1);
    $y_start = $pdf->getY();
    $page_width = $pdf->getPageWidth();

    $page_dims = $pdf->getPageDimensions();
    $m_sinistro = $page_dims['lm'];
    $m_top = $page_dims['tm'];
    $m_destro = $page_dims['rm'];

    $half_page = $page_width / 2;
    $w_page_parts = $half_page - $m_destro - $m_sinistro;
    $x_start2 = $half_page + $m_sinistro;

    // ######   PAGINA 1   ######
    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1: Informazioni Pagella, Giudizi, Firme">
    // header
//    $pdf->SetFont($font, '', $font_dim);
//    $pdf->writeHTMLCell($w_page_parts, $h_header, '', $m_top, $header, 'B');
    $pdf->SetFont($font, '', $font_dim);


    // giudizio intermedio I Quad
    $pdf->SetFont($font, 'B', $font_dim + 2);
    $y_st = $pdf->GetY();
    $pdf->Cell($w_page_parts, $font_dim+1, $labels['giudizio_intermedio_1_quad'], 0, 1, 'C');
    $pdf->SetFont($font, '', $font_dim);
    $pdf->setCellPaddings(2);
    $pdf->MultiCell($w_page_parts, 0, $giudizi['periodo_primo'], 0, 'J', false, 1);
    $pdf->ln(4);
    $firmeval = '<table>
            <tr>
                <td align="left" width="40%" style="border-bottom-width: 0.1px;">IL GENITORE O CHI NE FA LE VECI</td>
                <td width="20%"></td>
                <td align="right" width="40%" style="border-bottom-width: 0.1px;">IL COORDINATORE DIDATTICO (1)<br>'.$studente['nome_dirigente'].'<br><br></td>
            </tr>
            <tr>
                <td>'.$studente['descrizione_comuni'] . ', li ' . $data_attestato_IQ_day . '/' . $data_attestato_IQ_month . '/' . $data_attestato_IQ_year.'</td>
            </tr>
        </table>';
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $firmeval);
    $pdf->writeHTMLCell($w_page_parts, $pdf->GetY()-$y_st+28, '', $y_st, '', 1, 1, false);

    $pdf->setCellPaddings(1);
    $pdf->Ln(7);

    // giudizio finale
    $pdf->SetFont($font, 'B', $font_dim + 2);
    $y_st = $pdf->GetY();
//    $pdf->SetX($x_start2);
    $pdf->Cell($w_page_parts, $font_dim+1, $labels['giudizio_finale'], 0, 1, 'C');
    $pdf->SetFont($font, '', $font_dim);
    $pdf->setCellPaddings(2);
//    $pdf->SetX($x_start2);
    $pdf->MultiCell($w_page_parts, 0, $giudizi['periodo_finale'], 0, 'J', false, 1);
    $pdf->ln(4);
    $firmeval = '<table>
            <tr>
                <td align="left" width="40%" style="border-bottom-width: 0.1px;">IL GENITORE O CHI NE FA LE VECI</td>
                <td width="20%"></td>
                <td align="right" width="40%" style="border-bottom-width: 0.1px;">IL COORDINATORE DIDATTICO (1)<br>'.$studente['nome_dirigente'].'<br><br></td>
            </tr>
            <tr>
                <td>'.$studente['descrizione_comuni'] . ', li ' . $data_attestato_day . '/' . $data_attestato_month . '/' . $data_attestato_year.'</td>
            </tr>
        </table>';
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $firmeval);
    $pdf->writeHTMLCell($w_page_parts, $pdf->GetY()-$y_st+28, '', $y_st, '', 1, 1, false);
    $pdf->setCellPaddings(1);

    if ((int)$classe == 3) {
        $tbl_amm = '<table align="center">
                <tr>
                    <td><b>VOTO DI AMMISSIONE</b><br>Voto (in cifre e in lettere)</td>
                    <td>'.$studente['voto_ammissione_medie'].'/10</td>
                    <td>'.strtoupper(traduci_numero_in_lettere($studente['voto_ammissione_medie'])) .'/decimi</td>
                </tr>
            </table>
            ';
        $pdf->Ln(3);
        $pdf->writeHTMLCell($w_page_parts, 15, '', '', $tbl_amm, 1, 1);
        $pdf->Ln(2);
    }
    else {
        $pdf->Ln(14);
    }
    $pdf->SetFont($font, 'B', $font_dim+2);
    $pdf->Cell($w_page_parts, 0, 'ATTESTATO', 0, 1, 'C');
    $pdf->SetFont($font, '', $font_dim);
    $pdf->Ln(6);
    $pdf->MultiCell($w_page_parts, 0, $labels['attestato_desc']."\n", 0, 'J');
    $pdf->Ln(6);
    $pdf->SetFont($font, 'B', $font_dim);
    $pdf->Cell($w_page_parts, 0, $labels['consiglio'], 0, 1);
    $pdf->Ln(4);
    $pdf->SetFont($font, '', $font_dim+2);
    $pdf->Cell($w_page_parts, 0, $labels['attestato_ammissione_1'].$studente['classe'].' '.$studente['sezione'], 0, 1, 'C');
    $pdf->Ln(2);
    $pdf->Cell($w_page_parts, 0, $esito_medie, 0, 1, 'C');
    $pdf->ln(6);

    $pdf->SetFont($font, '', $font_dim);
    $firme = '<table>
            <tr>
                <td width="40%">'.$studente['descrizione_comuni'] . ', li ' . $data_attestato_day . '/' . $data_attestato_month . '/' . $data_attestato_year.'</td>
                <td width="60%" align="right">_____________________________</td>
            </tr>
                <td width="40%"></td>
                <td width="60%" align="right">IL COORDINATORE DIDATTICO (1)<br>'.$studente['nome_dirigente'].'</td>
            </tr>
        </table>';
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $firme);
    $pdf->ln(18);

    $pdf->SetFont($font, '', $font_dim-3);
    $pdf->MultiCell($w_page_parts, 0, $labels['note'], 0, 'L');
    $pdf->SetFont($font, '', $font_dim);

    $pdf->setCellHeightRatio( 1.25 );

    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1, Parte 2: Informazioni Pagella">
    //{{{ <editor-fold defaultstate="collapsed" desc="Intestazione">
    $pdf->Image('immagini_scuola/logo_repubblica.jpg', $x_start2 + ($w_page_parts/2) - 10, $m_top+$h_header+15, 20, 20, 'JPG', false);
    $pdf->setY($m_top+$h_header+15+17);
    $pdf->Ln(6);
    $pdf->SetFont($font, '', $font_dim + 5);
    $pdf->SetX($x_start2);
    $pdf->MultiCell($w_page_parts, 0, $labels['titolo'], 0, 'C');
    $tbl_scuola = '
        <table align="center">
            <tr>
                <td><b>'.$studente['descrizione_indirizzi'].'</b></td>
            </tr>
            <tr>
                <td>'.$studente['descrizione_indirizzi'].'<b> '.$studente['descrizione_scuola'].'</b></td>
            </tr>
            <tr>
                <td>'.$studente['codice_meccanografico'].'</td>
            </tr>
            <tr>
                <td>'.'VIALE MATTEOTTI, 425'.'</td>
            </tr>
            <tr>
                <td>'.$studente['descrizione_indirizzi'].'</td>
            </tr>
            <tr>
                <td>'.$studente['cap_comuni'].' '.$studente['descrizione_comuni'].' ('.$studente['provincia_comuni'].')</td>
            </tr>
        </table>';
    $pdf->Ln(12);
    $pdf->SetCellHeightRatio(1.45);
    $pdf->SetFont($font, '', $font_dim + 2);
    $pdf->SetX($x_start2);
    $pdf->writeHTMLCell($w_page_parts, 45, '', '', $tbl_scuola, 1, 1);
    $pdf->SetCellHeightRatio(1.25);
    $pdf->Ln(12);
    $pdf->SetFont($font, 'B', $font_dim + 7);
    $pdf->SetX($x_start2);
    $pdf->CellFitScale(0, 0, $labels['titolo_scheda'], 0, 1, 'C');
    $pdf->Ln(3);
    $pdf->SetFont($font, 'B', $font_dim + 4);
    $pdf->SetX($x_start2);
    $pdf->Cell(0, 0, $labels['anno_scolastico'] . $anno_scolastico_attuale, 0, 1, 'C');
    $pdf->Ln(12);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Anagrafica">
    $larghezza_max_cella = $w_page_parts;
	$altezza_cella = 7;
    $pdf->SetCellHeightRatio(1.7);


	$pdf->SetX($x_start2);
    $pdf->SetCellPadding( 1 );
    $pdf->SetFont($font, 'B', $font_dim + 4);
	$pdf->CellFitScale($larghezza_max_cella, 0, 'Dati anagrafici dello studente', 'TRL', 1, 'C');
    $pdf->SetFont($font, '', $font_dim);
    $pdf->SetFont($font, '', $font_dim);

	$pdf->SetX($x_start2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['cognome'], 'L', 0, 'L');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['nome'], 0, 0, 'L');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['codice_fiscale'], 'R', 1, 'L');

	$pdf->SetX($x_start2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella-2, 'COGNOME', 'L', 0, 'L');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella-2, 'NOME', 0, 0, 'L');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella-2, 'CODICE FISCALE', 'R', 1, 'L');

    $luogo_nascita = ($studente['descrizione_nascita'] == 'COMUNE ESTERO' || !$studente['descrizione_nascita']) ? $studente['citta_nascita_straniera'] : $studente['descrizione_nascita'];
	$pdf->SetX($x_start2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['data_nascita_ext'], 'L', 0, 'L');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $luogo_nascita, 0, 0, 'L');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, str_replace(array('(', ')'), '', $studente['provincia_nascita_da_comune']), 'R', 1, 'L');

	$pdf->SetX($x_start2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella-2, 'DATA DI NASCITA', 'LB', 0, 'L');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella-2, 'COMUNE DI NASCITA', 'B', 0, 'L');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella-2, 'PROV. O STATO ESTERO', 'RB', 1, 'L');

    $pdf->setCellHeightRatio( 1.25 );
    $pdf->SetCellPadding(0 );

    $pdf->ln(8);
    $pdf->SetFont($font, 'B', $font_dim+2);
	$pdf->SetX($x_start2);
	$pdf->CellFitScale($w_page_parts, 8, "Iscritto alla classe: ".$studente['classe'].$studente['sezione'], 1, 0, 'C');
    $pdf->SetFont($font, '', $font_dim-1);
    $pdf->ln(20);
    $firme = '<table>
            <tr>
                <td width="50%">'.$studente['descrizione_comuni'] . ', li ' . $data_attestato_day . '/' . $data_attestato_month . '/' . $data_attestato_year.'</td>
                <td width="50%" align="right">_____________________________</td>
            </tr>
                <td width="50%"></td>
                <td width="50%" align="right">IL COORDINATORE DIDATTICO (1)<br>'.$studente['nome_dirigente'].'</td>
            </tr>
        </table>';
	$pdf->SetX($x_start2);
    $pdf->writeHTML($firme);



    //}}} </editor-fold>
   //}}} </editor-fold>
    //}}} </editor-fold>

    // ######   PAGINA 2   ######
    $pdf->AddPage($orientamento, 'A3');
    // header
    $pdf->writeHTMLCell($w_page_parts, $h_header, '', $m_top, $header, 'B');
    $pdf->writeHTMLCell($w_page_parts, $h_header, $x_start2, $m_top, $header, 'B');

    $pdf->SetY($m_top + $h_header);
    $font_dim += 1;
    $pdf->setCellPaddings(3);
    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2: Voti">
    //{{{ <editor-fold defaultstate="collapsed" desc="Voti">
    $pdf->SetFont($font, 'B', $font_dim+5);
    $pdf->MultiCell($w_page_parts, 0, $labels['valutazioni'], 'TB', 'C', false, 0);
    $pdf->SetX($x_start2);
    $pdf->MultiCell($w_page_parts, 0, $labels['valutazioni'], 'TB', 'C', false, 1);


    // ordina voti in base al nome della materia
//    $arr_voti_tot = ordina_array($arr_voti_tot, 'descrizione');
//    $arr_voti_tot += $comportamento;

    $pdf->Ln(9);
    $y_start_voti = $pdf->GetY();
    $new_pg = false;
    $count_materie = 0;
    $pdf->setCellHeightRatio( 1.5 );
    // materie
    foreach ($arr_voti_tot as $id_materia_voti)
    {
        $count_materie++;
        $voto_tradotto_IQ = '';
        $voto_opt = '';

        // significati voti per la traduzione
        foreach ($id_materia_voti['primo_quadrimestre']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $id_materia_voti['primo_quadrimestre']['voto_pagellina']) {
                $voto_tradotto_IQ = $id_materia_voti['primo_quadrimestre']['voto_pagellina'].'/10'.'  &nbsp;  '.ucwords(strtolower($significato['valore_pagella']));
            }
        }

        foreach ($id_materia_voti['finale']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $id_materia_voti['finale']['voto_pagellina']) {
                $voto_opt = $id_materia_voti['finale']['voto_pagellina'].'/10'.'  &nbsp;  '. ucwords(strtolower($significato['valore_pagella']));
            }
        }
        // fine significati voti

        $cella_descrizione_materia = $id_materia_voti['descrizione'];

        if ($count_materie<9) {
            $pdf->SetFont($font, 'B', $font_dim);
            $pdf->CellFitScale($w_page_parts*0.34, 20, $cella_descrizione_materia, 'TBL', 0, 'C');
            $pdf->SetFont($font, '', $font_dim);
            if ( strpos(strtolower($cella_descrizione_materia),'religione')!==false || strpos(strtolower($cella_descrizione_materia),'comportamento')!==false) { // religione o comportamento
                $pdf->writeHTMLCell($w_page_parts*0.33, 20, '', '', $labels['val_periodo_primo'] . '<br><b>' . $voto_tradotto_IQ .'</b>', 'TB', 0, false, true, 'C');
                $pdf->writeHTMLCell($w_page_parts*0.33, 20, '', '', $labels['val_periodo_finale'] . '<br><b>' . $voto_opt .'</b>', 'TRB', 1, false, true, 'C');
            }
            else {
                $pdf->writeHTMLCell($w_page_parts*0.33, 20, '', '', $labels['val_periodo_primo'] . '<br><b>' . $voto_tradotto_IQ .'/decimi</b>', 'TB', 0, false, true, 'C');
                $pdf->writeHTMLCell($w_page_parts*0.33, 20, '', '', $labels['val_periodo_finale'] . '<br><b>' . $voto_opt .'/decimi</b>', 'TRB', 1, false, true, 'C');
            }
            $pdf->Ln(7);
        }
        else {
            if (!$new_pg) {
                $pdf->SetY($y_start_voti);
                $new_pg = true;
            }
            $pdf->SetFont($font, 'B', $font_dim);
            $pdf->SetX($x_start2);
            $pdf->Cell($w_page_parts*0.34, 20, $cella_descrizione_materia, 'TBL', 0, 'C');
            $pdf->SetFont($font, '', $font_dim);
            if ( strpos(strtolower($cella_descrizione_materia),'religione')!==false || strpos(strtolower($cella_descrizione_materia),'comportamento')!==false) { // religione o comportamento
                $pdf->writeHTMLCell($w_page_parts*0.33, 20, '', '', $labels['val_periodo_primo'] . '<br><b>' . $voto_tradotto_IQ .'</b>', 'TB', 0, false, true, 'C');
                $pdf->writeHTMLCell($w_page_parts*0.33, 20, '', '', $labels['val_periodo_finale'] . '<br><b>' . $voto_opt .'</b>', 'TRB', 1, false, true, 'C');
            }
            else {
                $pdf->writeHTMLCell($w_page_parts*0.33, 20, '', '', $labels['val_periodo_primo'] . '<br><b>' . $voto_tradotto_IQ .'/decimi</b>', 'TB', 0, false, true, 'C');
                $pdf->writeHTMLCell($w_page_parts*0.33, 20, '', '', $labels['val_periodo_finale'] . '<br><b>' . $voto_opt .'/decimi</b>', 'TRB', 1, false, true, 'C');
            }
            $pdf->Ln(8);
        }
    }
    $pdf->Ln(2);

    $pdf->setCellHeightRatio( 1.25 );
    $pdf->SetFont($font, '', $font_dim-1);

    $pdf->SetY(180);
    $y_st = $pdf->GetY();
    $pdf->SetX($x_start2);
    $pdf->CellFitScale(29, 0, 'COMPORTAMENTO','B',0);
    $pdf->CellFitScale(($w_page_parts-29)/2, 0, 'Trimestre','B',0, 'C');
    $pdf->CellFitScale(($w_page_parts-29)/2, 0, 'Finale','B',1, 'C');
    $pdf->SetX($x_start2);
    $pdf->ln(2);
    $pdf->SetX($x_start2);
    $pdf->Cell(27, 0, 'Giudizio(2)',0,0);
    $pdf->MultiCell(($w_page_parts-28)/2, 0, $giudizi['valutazione_primo_periodo']."\n", 0, 'J', false, 0);
    $pdf->SetX($pdf->GetX()+1);
    $pdf->MultiCell(($w_page_parts-30)/2, 0, $giudizi['valutazione_finale_periodo']."\n", 0, 'J', false, 0);

    $pdf->writeHTMLCell($w_page_parts, 80, $x_start2, $y_st-8, '', 1, 1, false);

    //}}} </editor-fold>
    //}}} </editor-fold>
    //}}} </editor-fold>
    if ($classe == 3 && $stampa_attestato == 'SI'
            &&
        strpos( strtoupper($studente['esito']), 'NON' ) === false
            &&
            (
//        strpos( strtoupper($studente['esito']), 'AMMESS' ) !== false
//                ||
        strpos( strtoupper($studente['esito']), 'LICENZIAT' ) !== false
            )
            ) {

        $pdf->AddPage('P', 'A4');
        inserisci_intestazione_pdf($pdf, (int) $id_classe, 50, 0);
        $pdf->SetFont($font, 'B', 14);
        $pdf->ln(5);

        $pdf->setCellHeightRatio( 1.50 );
        $pdf->MultiCell(0, 0, "ATTESTATO DI SUPERAMENTO DELL'ESAME DI STATO
CONCLUSIVO DEL PRIMO CICLO DI ISTRUZIONE
Anno scolastico $anno_scolastico_attuale", 0, 'C', false, 1);
        $pdf->setCellHeightRatio( 1.25 );

        $pdf->ln(8);
        $pdf->SetFont($font, '', 10);
        $pdf->Cell(0,0, "Registro Cert. N. $num_prot", 0, 1);
        $pdf->ln(8);
        $pdf->Cell(0,0, "Visti gli atti d'ufficio si attesta che", 0, 1, 'C');
        $pdf->ln(15);
        $pdf->writeHTMLCell(0, 0, '', '', "{$labels['alunno_1']} <b>{$studente['cognome']} {$studente['nome']}</b> della classe {$classe}{$sezione},", 0, 1, false, true, 'L');
        $pdf->ln(5);
        $desc_nascita = $provincia_nascita != ' ' ?  "$luogo_nascita ($provincia_nascita)": $luogo_nascita;
        $pdf->Cell(0, 0, "{$labels['nascita_luogo']} $desc_nascita il {$studente['data_nascita_ext']}", 0, 1);
        $pdf->ln(8);

        $pdf->MultiCell(0, 0, "nella sessione unica dell'anno scolastico $anno_scolastico_attuale ha sostenuto presso questa Scuola Secondaria di Primo Grado, in qualità di candidato interno, l'Esame di Stato conclusivo del I ciclo di Istruzione con risultato positivo conseguendo la seguente valutazione:", 0, 'L', false, 1);
        $pdf->ln(7);
        $pdf->SetFont($font, 'B', 12);
        $pdf->Cell(0,0, traduci_numero_in_lettere($studente['giudizio_sintetico_esame_terza_media'])."/decimi", 0, 1, 'C');
        $pdf->SetFont($font, '', 10);
        $pdf->ln(8);
        $lingue_straniere = rtrim($lingue_straniere, ',');
        $pdf->writeHTMLCell(0, 0, '', '', "Lingua Straniera studiata: $lingue_straniere.", 0, 1, false, true, 'L');
        $pdf->ln(8);
        
        $anno_inizio = explode('/', $anno_scolastico_attuale)[0];
        $anno_fine = explode('/', $anno_scolastico_attuale)[1];
        $nuovo_cons = "";
        $param_consigli = [
            "anno_scolastico" => "{$anno_inizio}_{$anno_fine}"
        ];
        $elenco_consigli = nextapi_call('/school/structure/consiglio_orientativo', 'GET', $param_consigli, $current_key);
        $arr_nuovi_cons = [];
        foreach ($elenco_consigli as $consiglio){
            if ($consiglio['id_consiglio_orientativo_template'] == $studente['id_consiglio_orientativo'] ||
                $consiglio['id_consiglio_orientativo_template'] == $studente['id_consiglio_orientativo2'] ||
                $consiglio['id_consiglio_orientativo_template'] == $studente['id_consiglio_orientativo3']) {
                    $arr_nuovi_cons[] = $consiglio['descrizione'];
                }
        }
        if (!empty($arr_nuovi_cons)) {
            $nuovo_cons = implode(', ', $arr_nuovi_cons);
        }
        $pdf->writeHTMLCell(0, 0, '', '', "Consiglio Orientativo: $nuovo_cons.", 0, 1, false, true, 'L');
//        $pdf->ln(8);
//        $pdf->writeHTMLCell(0, 0, '', '', "Eventuale integrazione del consiglio orientativo: @", 0, 1, false, true, 'L');
        $pdf->ln(15);
        $pdf->Cell(0,0, "Si rilascia il presente certificato in carta libera su richiesta dell'interessato/a per gli usi consentiti dalla Legge.", 0, 1);
        $pdf->ln(12);
        $pdf->SetFont($font, 'I', 8);
        $pdf->MultiCell(0,0, "Ai sensi della Direttiva N. 14/2011 del Ministero della Pubblica Amministrazione e della Semplificazione si precisa che il presente certificato non può essere prodotto agli organi della pubblica amministrazione o ai privati gestori di pubblici servizi.", 0, 'L', false, 1);
        $pdf->SetFont($font, '', 10);

        $pdf->ln(13);

        $firme = '<table>
                <tr>
                    <td width="55%" align="left">'.$studente['descrizione_comuni'] . ', li ' . $data_attestato_day . '/' . $data_attestato_month . '/' . $data_attestato_year.'</td>
                    <td width="45%" align="center">IL COORDINATORE DIDATTICO<br>'.$studente['nome_dirigente'].'<br></td>
                </tr>
                    <td width="55%"></td>
                    <td width="45%" align="center">_________________________________<br><small>Firma autografa sostituita a mezzo stampa secondo le indicazioni<br>dell\'art. 3 del Dlgs 39/1993.</small></td>
                </tr>
            </table>';
        $pdf->writeHTMLCell(0, 0, '', '', $firme, 0, 0, false, true, 'C');

        $pdf->SetXY(10, $pdf->getPageHeight()-21);
        if (file_exists('immagini_scuola/marelli_footer.gif')) {
            $pdf->Image('immagini_scuola/marelli_footer.gif', '','', 185,15, 'GIF', '', 'C');
        }

    }

    if ($classe<3 && !empty($stampa_pai))
    {
        $pdf->AddPage('P', 'A4');
        inserisci_intestazione_pdf($pdf, (int) $id_classe, 35, 0);

        $pdf->ln(3);
        $pdf->SetFont($font, 'B', $font_dim + 5);
        $pdf->CellFitScale(0, 0, "PIANO DI APPRENDIMENTO INDIVIDUALIZZATO", 0, 1, 'C');
        $pdf->SetFont($font, '', 9);
        $pdf->ln(3);

        $pdf->SetFont($font, 'B', 9);
        $pdf->writeHTMLCell(120, 0, '', '', "{$labels['alunno_1']} {$studente['cognome']} {$studente['nome']}", 0, 0, false, true, 'L');
        $pdf->writeHTMLCell(0, 0, '', '', "classe {$classe} sez. {$sezione}", 0, 1, false, true, 'R');
        $pdf->SetFont($font, '', 9);
        $pdf->ln(5);

        $pdf->writeHTMLCell(0, 0, '', '', $labels['txt_pai'], 0, 1, false, true, 'L');
        $pdf->ln(5);

        foreach ($stampa_pai as $dati_pai) {
            $pdf->SetFont($font, 'BI', 9);
            $pdf->writeHTMLCell(0, 0, '', '', $dati_pai['materia'], 0, 1, false, true, 'L');
            $pdf->SetFont($font, '', 8);
            $pdf->writeHTMLCell(0, 0, '', '', $dati_pai['pai'], 0, 1, false, true, 'L');
            $pdf->ln(2);
        }

        $pdf->ln(5);
        foreach ($stampa_pai as $dati_pai) {
            $pdf->SetFont($font, 'BI', 9);
            $pdf->writeHTMLCell(0, 0, '', '', $dati_pai['strategie_nome'], 0, 1, false, true, 'L');
            $pdf->SetFont($font, '', 8);
            $pdf->writeHTMLCell(0, 0, '', '', $dati_pai['strategie'], 0, 1, false, true, 'L');
            $pdf->ln(2);
        }

        $pdf->SetFont($font, '', 9);
        $pdf->ln(5);
        $pdf->writeHTMLCell(0, 0, '', '', $labels['txt_pai_post'], 0, 1, false, true, 'L');
        if ($comunicazioni_famiglia != '') {
            $pdf->ln(7);
            $pdf->writeHTMLCell(0, 0, '', '', "<b>Comunicazioni alla famiglia</b><br>$comunicazioni_famiglia", 0, 1, false, true, 'L');
        }
        $pdf->ln(10);
        $pdf->writeHTMLCell(0, 0, '', '', $firme, 0, 0, false, true, 'C');

        $pdf->SetXY(10, $pdf->getPageHeight()-21);
        if (file_exists('immagini_scuola/marelli_footer.gif')) {
            $pdf->Image('immagini_scuola/marelli_footer.gif', '','', 185,15, 'GIF', '', 'C');
        }
    }
}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
