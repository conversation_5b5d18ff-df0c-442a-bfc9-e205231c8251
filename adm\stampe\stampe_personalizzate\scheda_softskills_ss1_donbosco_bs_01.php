<?php

/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G', 'scheda_softskills_ss1_donbosco_bs_01', 'Scheda soft skills prima liceo', 1, 'Scheda soft skills prima liceo', 10);
 */


$orientamento = 'P';
$formato = 'A4';

if ($periodo ==7) {
    $periodo_pagella = "intermedia" ;
}
elseif ($periodo ==8) {
    $periodo_pagella = "pagellinast" ;
}
elseif ($periodo ==9) {
    $periodo_pagella = "finale" ;
}
else {
    $periodo_pagella = "pagellina" . $periodo;
}


function genera_stampa(&$pdf, $studente, $parametri_stampa)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Sezione dedicata ai parametri, al dizionario e alle impostazioni specifiche dei campi liberi">
    $id_classe = $parametri_stampa['id_classe'];
    $data_st = $parametri_stampa['data_day'].'/'.$parametri_stampa['data_month'].'/'.$parametri_stampa['data_year'];
    $periodo_st = $parametri_stampa['periodo'];
    $current_key = $parametri_stampa['current_key'];
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $stato_nascita .=   $stato['descrizione'] ;
        }
    }
    else
    {
        $stato_nascita = 'ITALIA';
        $luogo_nascita = $studente['descrizione_nascita'] . ' (' .$studente['provincia_nascita_da_comune']. ')';
    }

    $id_studente = $studente['id_studente'];
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_pagella = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo_st, $id_studente);
    $arr_comp = [];
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

//        if ($materia['in_media_pagelle'] != 'NV') {
            foreach ($voti_pagella[$id_materia]['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    $cmpname = $campo_libero['nome'];

                    if (stripos($cmpname, 'SKILLS') !== false && $value != '') {
                        if (stripos($cmpname, 'EFFICACIA PERSONALE') !== false) {
                            $arr_comp['personale'] = $value;
                        }
                        if (stripos($cmpname, 'SERVIZIO E REALIZZAZIONE') !== false) {
                            $arr_comp['servizio'] = $value;
                        }
                        if (stripos($cmpname, 'INFLUENZA E DI IMPATTO') !== false) {
                            $arr_comp['influenza'] = $value;
                        }
                        if (stripos($cmpname, 'COGNITIVE E RELAZIONALI') !== false) {
                            $arr_comp['cognitive'] = $value;
                        }
                    }
                }
            }
//        }
    }
    
    // Dizionario
    $labels = [
        "p1_titolo"           => "CERTIFICAZIONE delle COMPETENZE DI BASE<br>acquisite nel percorso formativo offerto dal nostro Istituto",

        "p1_studente"         => "che lo student||min_eessa|| {$studente['cognome']} {$studente['nome']}",
        "p1_nascita"          => "nat||min_oa|| il ".date('d/m/Y', $studente['data_nascita'])." a $luogo_nascita Stato $stato_nascita",
        "p1_iscritto"         => "iscritt||min_oa|| presso questo Istituto nella classe {$studente['classe']} sez. {$studente['sezione']}",
        "p1_indirizzo"        => "indirizzo di studio: {$studente['descrizione_indirizzi']}",
        "p1_periodo"          => "nell'anno scolastico $anno_scolastico_attuale",

        "legenda"             =>
            "<b>(1)</b> livelli relativi all'acquisizione delle competenze di ciascun asse:<br><br>
<b>A – Avanzato</b><br>
L’alunno/a svolge compiti e risolve problemi complessi, mostrando padronanza nell’uso delle conoscenze e delle abilità; propone e sostiene le proprie opinioni e assume in modo responsabile decisioni consapevoli.
<br><br>
<b>B – Intermedio</b><br>
L’alunno/a svolge compiti e risolve problemi in situazioni nuove, compie scelte consapevoli, mostrando di saper utilizzare le conoscenze e le abilità acquisite.
<br><br>
<b>C – Base</b><br>
L’alunno/a svolge compiti semplici anche in situazioni nuove, mostrando di possedere conoscenze e abilità fondamentali e di saper applicare basilari regole e procedure apprese.
<br><br>
<b>D – Iniziale</b><br>
L’alunno/a, se opportunamente guidato/a, svolge compiti semplici in situazioni note.",

    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    $classe_desc = traduci_classe_in_lettere($studente['classe']);
    if ($periodo_st==7) {
        $periodo_desc = "PRIMO";
    } else {
        $periodo_desc = "SECONDO";
    }
    $f = 'helvetica';
    $fd = 8;
    $pdf->AddPage('P', 'A4');
    $pdf->SetAutoPageBreak("off", 0);
    $pdf->SetFont($f, '', $fd);

    inserisci_intestazione_pdf($pdf, $id_classe);
    $pdf->ln(5);
    $pdf->SetFont($f, 'B', $fd);
    $pdf->CellFitScale(0, 0, "CERTIFICATO COMPETENZE SKILLS LAB - CLASSE $classe_desc LICEO SCIENTIFICO “DON BOSCO”", 0, 1, 'C');
    $pdf->ln(1);
    $pdf->CellFitScale(0, 0, "ANNO SCOLASTICO: $anno_scolastico_attuale - $periodo_desc QUADRIMESTRE", 0, 1, 'C');
    $pdf->ln(1);
    $pdf->CellFitScale(0, 0, "Studente: {$studente['cognome']} {$studente['nome']}", 0, 1, 'C');
    $pdf->SetFont($f, '', $fd);
    $tbl_comp =
'<table border="0.1px" cellpadding="2">'
.   '<tr align="center">'
        . '<td width="80%"><b>SKILLS</b></td>'
        . '<td width="20%"><b>LIVELLO*</b></td>'
.   '</tr>'
.   '<tr>
        <td>
            <b>A. SKILLS DI EFFICACIA PERSONALE</b><br>
            Aspetti della maturità di un individuo rispetto a sé stesso, agli altri ed alle attività da esso eseguite
            <b><br>&nbsp;&nbsp;&nbsp;1. Autonomia</b><br>Capacità di svolgere i compiti assegnati senza il bisogno di una costante supervisione facendo ricorso alle proprie risorse.
            <b><br>&nbsp;&nbsp;&nbsp;2. Fiducia in sé stessi</b><br>È la consapevolezza del proprio valore, delle proprie capacità e delle proprie idee al di là delle opinioni degli altri.
            <b><br>&nbsp;&nbsp;&nbsp;3. Flessibilità/Adattabilità</b><br>Sapersi adattare a contesti lavorativi mutevoli, essere aperti alle novità e disponibili a collaborare con persone con punti di vista anche diversi dal proprio.
            <b><br>&nbsp;&nbsp;&nbsp;4. Resistenza allo stress</b><br>Capacità di reagire positivamente alla pressione lavorativa mantenendo il controllo, rimanendo focalizzati sulle priorità e di non trasferire su altri le proprie eventuali tensioni.
            <b><br>&nbsp;&nbsp;&nbsp;5. Apprendere in maniera continuativa</b><br>È la capacità di riconoscere le proprie lacune ed aree di miglioramento, attivandosi per acquisire e migliorare sempre più le proprie conoscenze e competenze.
        </td>
        <td align="center" style="font-size: 130%;">
            '.$arr_comp['personale'].'
        </td>
    </tr>
    <tr>
        <td>
            <b>B. SKILLS DI INFLUENZA E DI IMPATTO</b><br>
            Capacità di un individuo di esercitare un\'influenza o un impatto sugli altri.
            <b><br>&nbsp;&nbsp;&nbsp;6. Precisione/Attenzione ai dettagli</b><br>È l’attitudine ad essere accurati, diligenti ed attenti a ciò che si fa, curandone i particolari ed i dettagli verso il risultato finale.
            <b><br>&nbsp;&nbsp;&nbsp;7. Leadership</b><br>L’innata capacità di condurre, motivare e trascinare gli altri verso mete e obiettivi ambiziosi, creando consenso e fiducia.
        </td>
        <td align="center" style="font-size: 130%;">
            '.$arr_comp['influenza'].'
        </td>
    </tr>
    <tr>
        <td>
            <b>C. SKILLS DI SERVIZIO E REALIZZAZIONE</b><br>
            Capacità di raggiungimento degli obiettivi prefissati comprendendo, gestendo ed applicando strategie in grado di risolvere problemi di varia natura.
            <b><br>&nbsp;&nbsp;&nbsp;8. Conseguire obiettivi</b><br>È l’impegno, la capacità, la determinazione che si mette nel conseguire gli obiettivi assegnati e, se possibile, superarli.
            <b><br>&nbsp;&nbsp;&nbsp;9. Essere intraprendente/Spirito d’iniziativa</b><br>Capacità di sviluppare idee e saperle organizzare in progetti per i quali si persegue la realizzazione, correndo anche rischi per riuscirci.
            <b><br>&nbsp;&nbsp;&nbsp;10. Capacità di pianificare ed organizzare</b><br>Capacità di realizzare idee, identificando obiettivi e priorità e, tenendo conto del tempo a disposizione, pianificarne il processo, organizzandone le risorse.
            <b><br>&nbsp;&nbsp;&nbsp;11. Problem Solving</b><br>È un approccio al lavoro che, identificandone le priorità e le criticità, permette di individuare le possibili migliori soluzioni ai problemi.
        </td>
        <td align="center" style="font-size: 130%;">
            '.$arr_comp['servizio'].'
        </td>
    </tr>
    <tr>
        <td>
            <b>D. SKILLS COGNITIVE E RELAZIONALI</b><br>
            Capacità di relazionarsi con altre persone, capacità di elaborare informazioni che favoriscano l’interazione con gli altri sia all\'interno che all\'esterno del contesto lavorativo.
            <b><br>&nbsp;&nbsp;&nbsp;12. Gestire le informazioni</b><br>Abilità nell’acquisire, organizzare e riformulare efficacemente dati e conoscenze provenienti da fonti diverse, verso un obiettivo definito.
            <b><br>&nbsp;&nbsp;&nbsp;13. Capacità comunicativa</b><br>Capacità di trasmettere e condividere in modo chiaro e sintetico idee ed informazioni con tutti i propri interlocutori, di ascoltarli e di confrontarsi con loro efficacemente.
            <b><br>&nbsp;&nbsp;&nbsp;14. Team work</b><br>Disponibilità a lavorare e collaborare con gli altri, avendo il desiderio di costruire relazioni positive tese al raggiungimento del compito assegnato.
        </td>
        <td align="center" style="font-size: 130%;">
            '.$arr_comp['cognitive'].'
        </td>
    </tr>
</table>';
    $tbl_comp .= '</table>';
    $pdf->ln(3);
    $pdf->writeHTMLCell(0, 0, '', '', $tbl_comp, 0, 1);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', "* A: Avanzato – B: Intermedio – C: Base – D: Iniziale",0, 1);
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, '', '', ucwords(strtolower($studente['descrizione_comuni'])) . ", $data_st",0, 1);
    $pdf->ln(5);
    $pdf->writeHTMLCell(0, 0, '', '', "<b>Il Coordinatore didattico – {$studente['nome_dirigente']}</b><br><small>(La firma è omessa ai sensi dell’Art. 3, D.to Lgs. 12/02/1993, n. 39)</small>", 0, 1, false, true, 'R');
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'       => $id_classe,
    'data_day'        => $data_Day,
    'data_month'      => $data_Month,
    'data_year'       => $data_Year,
    'periodo'         => $periodo,
    'orientamento'    => $orientamento,
    'current_key'     => $current_key
];


switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Scheda Soft Skills ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Scheda Soft Skills ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
