<?php

/* TO DO per il 18/19:
 * - FATTO: Aggiornare la data scrutini (data + un anno)
 * - FATTO: Aggiornare il periodo pagella in uso (nessun periodo) per evitare problemi con le rilevazioni
 * - Gestire gli hotspot dei giudizi sospesi (studenti NON in studenti completi i cui mac address di conseguenza vengono cancellati durante il cambio anno)
 *
 *  */
################### Creazione Copia Database per cambio anno #########################
require __DIR__ . '/../configs/mastercom.php';
require MC_PATH . '/functions/common.php';
require MC_PATH . '/class/NEXTAPI.inc.php';
require MC_PATH . "/class/NEXUS_MASTERCOM.class.php";
require MC_PATH . "/common/dbconnect.php";

$utente_api = 'nexus';
$password_api = '100modipernonENTRARE';

function setta_parametri_ca($current_user, $anno)
{
    // Aggiorno le date di inizio/fine lezioni e scrutini (per evitare che non vengano aggiornate e diano problemi)
    $parametri_anno_scolastico = [
        'DATA_INIZIO_SECONDO_QUADRIMESTRE',
        'DATA_INIZIO_TERZO_TRIMESTRE',
        'DATA_INIZIO_CALCOLO_SCRUTINI',
        'DATA_INIZIO_CALCOLO_GIUDIZI_SOSPESI',
    ];
    $date_anno_scolastico = estrai_parametri_multipli($parametri_anno_scolastico);

    aggiorna_parametri_singoli('DATA_INIZIO_SECONDO_QUADRIMESTRE', strtotime("+1 years", $date_anno_scolastico['DATA_INIZIO_SECONDO_QUADRIMESTRE']), $current_user);
    aggiorna_parametri_singoli('DATA_INIZIO_TERZO_TRIMESTRE', strtotime("+1 years", $date_anno_scolastico['DATA_INIZIO_TERZO_TRIMESTRE']), $current_user);
    aggiorna_parametri_singoli('DATA_INIZIO_CALCOLO_SCRUTINI', strtotime("+1 years", $date_anno_scolastico['DATA_INIZIO_CALCOLO_SCRUTINI']), $current_user);
    aggiorna_parametri_singoli('DATA_INIZIO_CALCOLO_GIUDIZI_SOSPESI', strtotime("+1 years", $date_anno_scolastico['DATA_INIZIO_CALCOLO_GIUDIZI_SOSPESI']), $current_user);

    // Imposto il periodo pagella in uso a Nessuna pagella per evitare problemi con la rilevazione
    aggiorna_parametri_singoli('PERIODO_PAGELLA_IN_USO', '0', $current_user);

    // Aggiorno la data di Inizio e Fine lezioni sulla base dei dati
    // estratti dal Calendario Regionale
    pgsql_query("UPDATE parametri
                    SET valore = (
                    SELECT min(data)
                    FROM festivita_scuola
                    WHERE tipo_giornata = 'A'
                        AND anno_scolastico = '{$anno}/".($anno + 1)."'
                )
                WHERE nome = 'DATA_INIZIO_LEZIONI'
                    AND 1 = (
                        SELECT count(*) FROM parametri
                        WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'
                            AND valore = '{$anno}/".($anno + 1)."'
                    )");

    pgsql_query("UPDATE parametri
                    SET valore = (
                    SELECT max(data)
                    FROM festivita_scuola
                    WHERE tipo_giornata = 'A'
                        AND anno_scolastico = '{$anno}/".($anno + 1)."'
                )
                WHERE nome = 'DATA_FINE_LEZIONI'
                    AND 1 = (
                        SELECT count(*) FROM parametri
                        WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'
                            AND valore = '{$anno}/".($anno + 1)."'
                    )");

    // vuoto blocco scrutini
    pgsql_query("UPDATE classi
        SET blocco_scrutini = ''");

    aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'NO', $current_user);
    aggiorna_parametri_singoli('END_CAMBIO_ANNO', '0', $current_user);
    aggiorna_parametri_singoli('START_CAMBIO_ANNO', '0', $current_user);
    genera_numeri_registro($current_user);
    aggiorna_parametri_singoli('RIGENERAZIONE_NUMERI_REGISTRO_WIZARD', 0, $current_user);
}

foreach ($dbname as $key => $value) {
    if (strpos($value["nome"], 'mastercom') !== false) {
        if ($value["nome"] >= $db_official) {
            $db_key = $value["nome"];

            $anno_scolastico_db_origine = str_replace('/', '_', estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE'));

            $mat_anno = explode('/', estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE'));

            $anno = $mat_anno[1];

            $time = time();

            if (preg_match('/20\d{2}/', $anno) !== false)
            {
                $vecchio_db = "mastercom_" . ($anno - 1) . "_" . $anno;
                $nuovo_db = "mastercom_" . $anno . "_" . ($anno + 1);

                // $found_nuovo = false;

                // if (array_key_exists($nuovo_db, $dbname))
                // {
                //     $found_nuovo = ${"idcon" . $nuovo_db} != false ? true : false;
                // }

                // $found_vecchio = false;

                // if (array_key_exists($vecchio_db, $dbname))
                // {
                //     $found_vecchio = ${"idcon" . $vecchio_db} != false ? true : false;
                // }

                $found_nuovo = array_key_exists($nuovo_db, $dbname);
                $found_vecchio = array_key_exists($vecchio_db, $dbname);

                $schedule = estrai_parametri_singoli('SCHEDULE_CAMBIO_ANNO');

                if ($schedule == '1')
                {
                    $finalizza_cambio_anno = estrai_parametri_singoli('FINALIZZA_CAMBIO_ANNO');

                    //token di autenticazione
                    $parametri = [
                        "username"   =>  $utente_api,
                        "password"  =>  $password_api
                    ];
                    $current_key = chiamata_api('localhost/next-api/v1/login', $parametri);
                    file_put_contents('/tmp/cambio_anno_current_key', print_r($current_key, true));
                    $utente_nexus = estrai_utente_da_nome("nexus");
                    $current_user = $utente_nexus["id_utente"];

                    aggiorna_parametri_singoli('SCHEDULE_CAMBIO_ANNO', '0', $current_user);
                    aggiorna_parametri_singoli('ERRORI_CAMBIO_ANNO', '', $current_user);
                    aggiorna_parametri_singoli('START_CAMBIO_ANNO', time(), $current_user);
                    aggiorna_parametri_singoli('END_CAMBIO_ANNO', '0', $current_user);
                    aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'SI', $current_user);
                    pgsql_query("COPY classi_studenti_succ TO '/var/lib/postgresql/classi_studenti_succ_{$anno_scolastico_db_origine}.copy'");
                    pgsql_query("COPY indirizzi_succ TO '/var/lib/postgresql/indirizzi_succ_{$anno_scolastico_db_origine}.copy'");
                    pgsql_query("COPY classi_succ TO '/var/lib/postgresql/classi_succ_{$anno_scolastico_db_origine}.copy'");
                    pgsql_query("COPY classi_prof_materie_succ TO '/var/lib/postgresql/classi_prof_materie_succ_{$anno_scolastico_db_origine}.copy'");
                    pgsql_query("COPY classi_materie_curricolo_succ TO '/var/lib/postgresql/classi_materie_curricolo_succ_{$anno_scolastico_db_origine}.copy'");

                    // Elimino indirizzi e classi cancellate
                    pgsql_query("DELETE FROM indirizzi_succ WHERE flag_canc > 0");
                    pgsql_query("DELETE FROM classi_succ WHERE flag_canc > 0");

                    $checkClassiSucc = "SELECT id_classe, id_indirizzo FROM classi_succ WHERE id_indirizzo NOT IN (SELECT id_indirizzo FROM indirizzi_succ)";
                    $resulClassiSucc = pgsql_query($checkClassiSucc);
                    $numClassiSucc = pg_num_rows($resulClassiSucc);

                    if ($numClassiSucc > 0)
                    {
                        pgsql_query("DELETE FROM classi_succ WHERE id_indirizzo NOT IN (SELECT id_indirizzo FROM indirizzi_succ)");
                    }

                    $checkClassiStudentiSucc = "SELECT id_studente FROM classi_studenti_succ WHERE id_classe NOT IN (SELECT id_classe FROM classi_succ)";
                    $resulClassiStudentiSucc = pgsql_query($checkClassiStudentiSucc);
                    $numClassiStudentiSucc = pg_num_rows($resulClassiStudentiSucc);

                    if ($numClassiStudentiSucc > 0)
                    {
                        pgsql_query("DELETE FROM classi_studenti_succ WHERE id_classe NOT IN (SELECT id_classe FROM classi_succ)");
                    }

                    $checkCpmSucc = "SELECT id_classe, id_professore FROM classi_prof_materie_succ WHERE id_classe NOT IN (SELECT id_classe FROM classi_succ)";
                    $resulCpmSucc = pgsql_query($checkCpmSucc);
                    $numCpmSucc = pg_num_rows($resulCpmSucc);

                    if ($numCpmSucc > 0)
                    {
                        pgsql_query("DELETE FROM classi_prof_materie_succ WHERE id_classe NOT IN (SELECT id_classe FROM classi_succ)");
                    }

                    $checkCpmDoppi = "SELECT id_professore, id_materia, id_classe, count(id_professore)
                                        FROM classi_prof_materie_succ
                                        GROUP BY id_professore, id_classe, id_materia, to_timestamp(data_inserimento)
                                        HAVING count(id_professore) > 1 ";
                    $resulCpmDoppi = pgsql_query($checkCpmDoppi);
                    $numCpmDoppi = pg_num_rows($resulCpmDoppi);

                    if ($numCpmDoppi > 0)
                    {
                        $cpmDoppi = pg_fetch_all($resulCpmDoppi);
                        file_put_contents('/tmp/cambio_anno_cpmDoppi', print_r($cpmDoppi, true), FILE_APPEND);
                        $deleteCpmDoppi = "DELETE FROM classi_prof_materie_succ
                                            WHERE ctid NOT IN (
                                                SELECT MIN(ctid)
                                                FROM classi_prof_materie_succ
                                                GROUP BY id_professore, id_classe, id_materia, to_timestamp(data_inserimento)
                                            ); ";
                        $resul_deleteCpmDoppi = pgsql_query($deleteCpmDoppi);
                    }

                    if ($found_nuovo == false)
                    {
                        //shell_exec("sudo -u postgres createdb " . $nuovo_db);
                        $command_crea_db = "sudo -u postgres createdb " . $nuovo_db;
                        $output_crea_db = array();
                        $returnValue_crea_db = 0;

                        // Esegue il comando e cattura l'output e il valore di ritorno
                        exec($command_crea_db . " 2>&1", $output_crea_db, $returnValue_crea_db);

                        // Verifica se ci sono errori
                        if ($returnValue_crea_db !== 0) {
                            $error_crea_db = implode("\n", $output_crea_db);
                            // Gestisci l'errore come desideri
                            file_put_contents('/tmp/cambio_anno_crea_db_errori', print_r($error_crea_db,true));
                            aggiorna_parametri_singoli('SCHEDULE_CAMBIO_ANNO', '0', $current_user);
                            aggiorna_parametri_singoli('ERRORI_CAMBIO_ANNO', '', $current_user);
                            aggiorna_parametri_singoli('START_CAMBIO_ANNO', '0', $current_user);
                            aggiorna_parametri_singoli('END_CAMBIO_ANNO', '0', $current_user);
                            exit();
                        }


                        $handle_mastercom = fopen(MC_CONF, "a");

                        $contenuto = "\n@@" . trim($nuovo_db) .
                                        "##" . trim($nuovo_db) .
                                        "##" . trim($dbname[$db_official]["hostname"]) .
                                        "##" . trim($dbname[$db_official]["username"]) .
                                        "##" . trim($dbname[$db_official]["password"]) .
                                        "##" . trim($dbname[$db_official]["port"]);

                        fwrite($handle_mastercom, $contenuto);
                        fclose($handle_mastercom);

                        $dbname[$nuovo_db]['nome'] = $nuovo_db;
                        $dbname[$nuovo_db]['hostname'] = $dbname[$db_official]["hostname"];
                        $dbname[$nuovo_db]['username'] = $dbname[$db_official]["username"];
                        $dbname[$nuovo_db]['password'] = $dbname[$db_official]["password"];
                        $dbname[$nuovo_db]['port'] = $dbname[$db_official]["port"];

                        ${"idcon" . $nuovo_db} = pg_connect("host=" . $dbname[$nuovo_db]["hostname"] . " port=" . $dbname[$nuovo_db]["port"] . " user=" . $dbname[$nuovo_db]["username"] . " password=" . $dbname[$nuovo_db]["password"] . " dbname=" . $dbname[$nuovo_db]["nome"]);

                        //shell_exec("sudo -u postgres pg_dump --clean {$vecchio_db} | sudo -u postgres psql " . $nuovo_db);
                        $command = "sudo -u postgres pg_dump --clean ".  $vecchio_db . " | sudo -u postgres psql " . $nuovo_db;
                        $output = array();
                        $returnValue = 0;

                        // Esegue il comando e cattura l'output e il valore di ritorno
                        exec($command . " 2>&1", $output, $returnValue);

                        // Verifica se ci sono errori
                        if ($returnValue !== 0) {
                            $error = implode("\n", $output);
                            // Gestisci l'errore come desideri
                            file_put_contents('/tmp/cambio_anno_log_clean_db_errori', print_r($error,true));
                            file_put_contents('/tmp/cambio_anno_log_clean_db_errori', print_r($contenuto,true), FILE_APPEND);
                            aggiorna_parametri_singoli('SCHEDULE_CAMBIO_ANNO', '0', $current_user);
                            aggiorna_parametri_singoli('ERRORI_CAMBIO_ANNO', '', $current_user);
                            aggiorna_parametri_singoli('START_CAMBIO_ANNO', '0', $current_user);
                            aggiorna_parametri_singoli('END_CAMBIO_ANNO', '0', $current_user);
                            exit();
                            //valutare se qui cancellare il db appena creato se ha dato errore
                        }


                        // Disabilitazione sincronizzazione Messenger per db attuale
                        pgsql_query("SELECT * FROM messenger_set_sync('DISABLE')");

                        // Mi sposto di database
                        $db_key = $nuovo_db;

                        aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'SI', $current_user);
                        aggiorna_parametri_singoli('ANNO_SCOLASTICO_ATTUALE', $anno . "/" . ($anno + 1), $current_user);
                        // Disabilitazione sincronizzazione Messenger per nuovo db
                        pgsql_query("SELECT * FROM messenger_set_sync('DISABLE')");

                        //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati mensa studente prima della pulizia per avere i conti dei consolidamenti">
                        $elenco_studenti_tmp = estrai_studenti_istituto();
                        $elenco_studenti = [];

                        foreach ($elenco_studenti_tmp as $studente)
                        {
                            if ($studente['servizio_mensa'] == 1)
                            {
                                $studente['situazione_mensa'] = estrai_situazione_mensa_studente($studente['id_studente'], $current_key);
                                $elenco_studenti[$studente['id_studente']] = $studente;
                            }
                        }
                        //}}} </editor-fold>

                        //{{{ <editor-fold defaultstate="collapsed" desc="array tabelle da copiare">
                        $mat_tabelle_da_copiare = [
                                'indirizzi',
                                'classi',
                                'classi_studenti',
                                'classi_prof_materie',
                                'classi_materie_curricolo'
                            ];

                        foreach ($mat_tabelle_da_copiare as $tabella)
                        {
                            $query = "TRUNCATE TABLE {$tabella} CASCADE";
                            pgsql_query($query);

                            $fieldname = [];
                            $query = "SELECT * FROM " . $tabella;
                            $result = pgsql_query($query);
                            $i = pg_num_fields($result);

                            for ($j = 0; $j < $i; $j++)
                            {
                                $fieldname[] = pg_field_name($result, $j);
                            }

                            sort($fieldname);
                            $campi_1 = implode($fieldname, ',');

                            $fieldname = [];
                            $query = "SELECT * FROM {$tabella}_succ";
                            $result = pgsql_query($query);
                            $i = pg_num_fields($result);

                            for ($j = 0; $j < $i; $j++)
                            {
                                $fieldname[] = pg_field_name($result, $j);
                            }

                            sort($fieldname);
                            $campi_2 = implode($fieldname, ',');

                            $query = "INSERT INTO " . $tabella . " (" . $campi_1 . ") SELECT " . $campi_2 . " FROM " . $tabella . "_succ";
                            pgsql_query($query);
                        }

                        $query = "SELECT setval('indirizzi_id_seq',(SELECT max(id_indirizzo) FROM indirizzi) + 1)";
                        pgsql_query($query) or die("Invalid $query");

                        $query = "SELECT setval('classi_id_seq',(SELECT max(id_classe) FROM classi) + 1)";
                        pgsql_query($query) or die("Invalid $query");
                        //}}} </editor-fold>

                        // Disabilitazione trigger su Voti per lo schema Statistiche
                        pgsql_query("ALTER TABLE public.voti DISABLE TRIGGER statistiche_voti_delete");
                        pgsql_query("ALTER TABLE public.voti DISABLE TRIGGER statistiche_voti_insert");
                        pgsql_query("ALTER TABLE public.voti DISABLE TRIGGER statistiche_voti_truncate");
                        pgsql_query("ALTER TABLE public.voti DISABLE TRIGGER statistiche_voti_update");



                        //{{{ <editor-fold defaultstate="collapsed" desc="array tabelle da cancellare">
                        $tabelle_da_cancellare = [
                                'abbinamenti_note_studenti',
                                'abbinamenti_studenti_utenti',
                                'abbinamenti_studenti_valori',
                                'allarmi',
                                'annotazioni_agenda',
                                'annotazioni_studenti',
                                'argomenti',
                                'assenze',
                                'assenze_didattica_distanza',
                                'associazione_libri_classe_materia',
                                'barcode_generici',
                                'campi_certificati',
                                'classi_materie_curricolo_succ',
                                'classi_prof_materie_succ',
                                'classi_studenti_succ',
                                'classi_succ',
                                'commissari',
                                'commissioni',
                                'compiti_studenti_fatti',
                                'comunicazioni',
                                'crs_spedizioni',
                                'crs_spedizioni_studenti',
                                'dati_giornalieri_bambini_asilo',
                                'email',
                                'email_destinatari',
                                'eventi',
                                'eventi_studenti',
                                'firme_appello_docenti',
                                'fuori_aula',
                                'indirizzi_succ',
                                'libri_adottati',
                                'log_assenze',
                                'log_email',
                                'log_gate',
                                'log_operazioni_quaderno',
                                'log_operazioni_registro',
                                'log_sms',
                                'log_storico',
                                'log_videomeeting_bbb',
                                'materia_riferimento_studenti_corsi',
                                'messaggi_allegati',
                                'messaggi_destinatari',
                                'messaggi_destinatari_gruppi',
                                'note_disciplinari',
                                'notifiche',
                                'novita_sitoapp',
                                'orario',
                                'orario_classi',
                                'orario_dettaglio',
                                'orario_personale',
                                'orario_studenti',
                                'pagelline',
                                'periodi_studenti_didattica_distanza',
                                'permessi_studenti',
                                'piani_studio',
                                'prenotazioni_colloqui',
                                'prenotazioni_risorse',
                                'presenze_consiglio',
                                'presenze_corsi',
                                'presenze_emergenza',
                                'presenze_personale',
                                'presenze_videomeeting_bbb',
                                'programmazione_argomenti',
                                'programmazione_moduli',
                                'programmazioni',
                                'programmazioni_abbinamenti',
                                'riepilogo_presenze_corsi',
                                'servizio_giornalieri_adesioni',
                                'sms',
                                'spedizioni_dump',
                                'stato_dispositivi',
                                'template_orario',
                                'tutor_colloqui',
                                'tutor_firme_colloqui',
                                'valori_campi_liberi',
                                'videomeeting',
                                'videomeeting_bbb',
                                'voti',
                                'voti_pagelline',

                                // Statistiche
                                'statistiche.ag_indirizzi_classi',
                                'statistiche.ag_indirizzi_classi_materie',
                                'statistiche.ag_indirizzi_classi_materie_studenti',
                                'statistiche.assenze',
                                'statistiche.notifiche',
                                'statistiche.ritardi',
                                'statistiche.voti',
                                'statistiche.voti_quantita',
                            ];

                        foreach ($tabelle_da_cancellare as $tabella)
                        {
                            pgsql_query("TRUNCATE TABLE {$tabella} RESTART IDENTITY");
                        }

                //        $query = "TRUNCATE TABLE " . implode(",", $tabelle_da_cancellare) . " RESTART IDENTITY";
                //        pgsql_query($query);

                        /**
                         * Pulizia tabelle mense
                         */
                        $tabelle_mense = [
                                'mensa_aggiornamenti',
                                'mensa_pasti_studenti',
                                'mensa_pasti_utenti',
                                'mensa_periodi_studenti',
                                'mensa_periodi_utenti',
                                'mensa_prenotazioni_studenti',
                                'mensa_prenotazioni_utenti'
                            ];

                        foreach ($tabelle_mense as $tabella)
                        {
                            pgsql_query("DELETE FROM {$tabella} WHERE 1 = (
                                            SELECT count(*) FROM parametri
                                            WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'
                                            AND valore = '{$anno}/" . ($anno + 1) . "'
                                        )");
                        }

                        //}}} </editor-fold>

                        // Pulizia vecchi dati già eliminati
                        pgsql_query("SELECT * FROM mc_clean_database(NOW())");

                        // Riabilitazione trigger su Voti per lo schema Statistiche
                //        pgsql_query("ALTER TABLE public.voti ENABLE TRIGGER statistiche_voti_delete");
                //        pgsql_query("ALTER TABLE public.voti ENABLE TRIGGER statistiche_voti_insert");
                //        pgsql_query("ALTER TABLE public.voti ENABLE TRIGGER statistiche_voti_truncate");
                //        pgsql_query("ALTER TABLE public.voti ENABLE TRIGGER statistiche_voti_update");

                        //{{{ <editor-fold defaultstate="collapsed" desc="Procedure risistemazione dati">
                        // $query = "UPDATE parenti_studenti
                        //             SET flag_canc = " . $time . "
                        //             WHERE id_studente NOT IN (
                        //                 SELECT DISTINCT id_studente FROM studenti WHERE flag_canc = 0
                        //             )";
                        // pgsql_query($query);

                        // $query = "UPDATE parenti
                        //             SET flag_canc = " . $time . "
                        //             WHERE id_parente NOT IN (SELECT DISTINCT id_parente FROM parenti_studenti WHERE flag_canc = 0)";
                        // pgsql_query($query);

                        //Svuoto i campi dei rappresentanti
                        $query = "UPDATE studenti SET rappresentante = DEFAULT ";
                        pgsql_query($query);
                        $query = "UPDATE parenti SET rappresentante = DEFAULT ";
                        pgsql_query($query);
                        $query = "UPDATE parenti_studenti SET rappresentante = DEFAULT ";
                        pgsql_query($query);
                        $query = "UPDATE utenti SET rappresentante = DEFAULT ";
                        pgsql_query($query);

                        //Svuoto i dati degli esami
                        $query = "UPDATE studenti
                                    SET voto_primo_scritto = DEFAULT,
                                    voto_secondo_scritto = DEFAULT,
                                    voto_terzo_scritto = DEFAULT,
                                    voto_orale = DEFAULT,
                                    voto_bonus = DEFAULT,
                                    materia_secondo_scr = DEFAULT,
                                    ulteriori_specif_diploma = DEFAULT,
                                    numero_diploma = DEFAULT,
                                    voto_ammissione = DEFAULT,
                                    differenza_punteggio = DEFAULT,
                                    voto_qualifica = DEFAULT,
                                    voto_esame_sc1_qual = DEFAULT,
                                    voto_esame_sc2_qual = DEFAULT,
                                    voto_esame_or_qual = DEFAULT,
                                    stato_privatista = DEFAULT,
                                    ammesso_esame_qualifica = DEFAULT,
                                    ammesso_esame_quinta = DEFAULT,
                                    giudizio_ammissione_quinta = DEFAULT,
                                    stato_licenza_maestro = DEFAULT,
                                    giudizio_esame_sc1_qual = DEFAULT,
                                    giudizio_esame_sc2_qual = DEFAULT,
                                    giudizio_esame_or_qual = DEFAULT,
                                    giudizio_complessivo_esame_qual = DEFAULT,
                                    data_orale = DEFAULT,
                                    tipo_primo_scritto = DEFAULT,
                                    tipo_secondo_scritto = DEFAULT,
                                    tipo_terzo_scritto = DEFAULT,
                                    unanimita_primo_scritto = DEFAULT,
                                    unanimita_secondo_scritto = DEFAULT,
                                    unanimita_terzo_scritto = DEFAULT,
                                    argomento_scelto_orale = DEFAULT,
                                    area_disc_1_orale = DEFAULT,
                                    area_disc_2_orale = DEFAULT,
                                    disc_elaborati_orale = DEFAULT,
                                    unanimita_voto_finale = DEFAULT,
                                    ordine_esame_orale = DEFAULT,
                                    esito_terza_media = DEFAULT,
                                    presente_esame_quinta = DEFAULT,
                                    num_volte_iscritto_classe_attuale = DEFAULT";
                        pgsql_query($query);

                        aggiorna_parametri_singoli('PASSAGGIO_A_STILE_ORARIO_TEMPLATE_PIU_ECCEZIONI', 'SI', $current_user);
                        //}}} </editor-fold>

                //        $anno_per_visualizzazione_materie = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
                //        $exec = 'php /var/www-source/mastercom/scripts/crea_materie_ministeriali.php '.$anno.'/'.($anno + 1);
                //        shell_exec($exec);

                        setta_parametri_ca($current_user, $anno);

                        // Torno nel database vecchio per togliere i blocchi
                        $db_key = $vecchio_db;

                        if ($finalizza_cambio_anno == 'NO') {
                            $sync_years_users = nextapi_call('admin/sync_years_users', 'POST', [], $current_key);

                            aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'NO', $current_user);
                            aggiorna_parametri_singoli('END_CAMBIO_ANNO', time(), $current_user);
                        }
                        pgsql_query("SELECT * FROM messenger_set_sync('ENABLE')");

                        // Ritorno nel database nuovo
                        $db_key = $nuovo_db;

                        // Allineo gli abbinamenti dei docenti di sostegno nel nuovo anno
                        allinea_sostegno_classi_prof_materie();

                        if ($nuovo_db != $db_official) {
                            //file_put_contents(MC_DBMAIN_CONF, $nuovo_db);
                            $exec_parametri = 'php /var/www-source/mastercom/scripts/database_parametri_sesto.php';
                            shell_exec($exec_parametri);
                            //genero il calendario delle festivita
                            crea_calendario_festivita($current_user);
                            $exec_anni = 'php /var/www-source/mastercom/scripts/calcola_anni_studente.php';
                            //shell_exec($exec_anni);
                        }
                    } else {
                        // caso in cui il nuovo db c'e' gia'

                        $mat_tabelle_da_copiare = [
                            'indirizzi',
                            'classi',
                            'classi_studenti',
                            'classi_prof_materie',
                            'classi_materie_curricolo'
                        ];

                        //se si aggiunge una tabella ricordarsi di inserire sotto anche l'aggiornamento della sequence realtiva
                        $mat_tabelle_da_copiare_no_succ = [
                            'materie',
                            'tag',
                            'adozioni_aie',
                            'template_word',
                            'funzioni_periodiche_esecuzioni',
                            'funzioni_periodiche',
                            'marketplace',
                            'marketplace_studenti_acquisti',
                            'elenco_stampe_personalizzate',
                            'satispay',
                            'abbinamenti_template_word_pagelle_classi'
                        ];

                        foreach ($mat_tabelle_da_copiare_no_succ as $tabella) {
                            pgsql_query("COPY {$tabella} TO '/var/lib/postgresql/{$tabella}_{$anno_scolastico_db_origine}.copy'");
                        }

                        // misposto nel nuovo db
                        $db_key = $nuovo_db;

                        aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'SI', $current_user);

                        //  - deflag studenti e parenti abbinati
                        //  - reimporta sempre tutte le _succ ritirando su i file e rilancio i cambi tabella
                        //
                        //


                        foreach ($mat_tabelle_da_copiare as $tabella) {
                            $query = "DROP TABLE {$tabella}_succ_ca";
                            pgsql_query($query);

                            $query = "CREATE TABLE {$tabella}_succ_ca AS TABLE {$tabella}_succ WITH NO DATA";
                            pgsql_query($query);

                            // $query = "TRUNCATE TABLE {$tabella}_succ CASCADE";
                            // pgsql_query($query);

                            $query = "TRUNCATE TABLE {$tabella} CASCADE";
                            pgsql_query($query);

                            //tiro su la _succ dal file .copy
                            // shell_exec(" \i /var/lib/postgresql/{$tabella}_succ.copy");
                            pgsql_query("COPY {$tabella}_succ_ca FROM '/var/lib/postgresql/{$tabella}_succ_{$anno_scolastico_db_origine}.copy'");

                            $fieldname = [];
                            $query = "SELECT * FROM " . $tabella;
                            $result = pgsql_query($query);
                            $i = pg_num_fields($result);

                            for ($j = 0; $j < $i; $j++) {
                                $fieldname[] = pg_field_name($result, $j);
                            }

                            sort($fieldname);
                            $campi_1 = implode($fieldname, ',');

                            $fieldname = [];
                            $query = "SELECT * FROM {$tabella}_succ_ca";
                            $result = pgsql_query($query);
                            $i = pg_num_fields($result);

                            for ($j = 0; $j < $i; $j++) {
                                $fieldname[] = pg_field_name($result, $j);
                            }

                            sort($fieldname);
                            $campi_2 = implode($fieldname, ',');

                            $query = "INSERT INTO " . $tabella . " (" . $campi_1 . ") SELECT " . $campi_2 . " FROM " . $tabella . "_succ_ca";
                            pgsql_query($query);
                        }

                        foreach ($mat_tabelle_da_copiare_no_succ as $tabella) {
                            $query = "TRUNCATE TABLE {$tabella} CASCADE";
                            pgsql_query($query);

                            //tiro su la tabella dal file .copy
                            // shell_exec(" \i /var/lib/postgresql/{$tabella}.copy");
                            pgsql_query("COPY {$tabella} FROM '/var/lib/postgresql/{$tabella}_{$anno_scolastico_db_origine}.copy'");
                        }

                        $query = "SELECT setval('materie_id_seq',(SELECT max(id_materia) FROM materie) + 1)";
                        pgsql_query($query) or die("Invalid $query");

                        $query = "SELECT setval('tag_id_seq',(SELECT max(id_tag) FROM tag) + 1)";
                        pgsql_query($query) or die("Invalid $query");

                        $query = "SELECT setval('adozioni_aie_id_seq',(SELECT max(id_adozione_aie) FROM adozioni_aie) + 1)";
                        pgsql_query($query) or die("Invalid $query");

                        $query = "SELECT setval('template_word_id_seq',(SELECT max(id_template) FROM template_word) + 1)";
                        pgsql_query($query) or die("Invalid $query");

                        $query = "SELECT setval('funzioni_periodiche_esecuzioni_id_seq',(SELECT max(id_esecuzione) FROM funzioni_periodiche_esecuzioni) + 1)";
                        pgsql_query($query) or die("Invalid $query");

                        $query = "SELECT setval('funzioni_periodiche_id_seq',(SELECT max(id_funzione_periodica) FROM funzioni_periodiche) + 1)";
                        pgsql_query($query) or die("Invalid $query");

                        $query = "SELECT setval('marketplace_id_seq',(SELECT max(id_marketplace) FROM marketplace) + 1)";
                        pgsql_query($query) or die("Invalid $query");


                        $query = "SELECT setval('indirizzi_id_seq',(SELECT max(id_indirizzo) FROM indirizzi) + 1)";
                        pgsql_query($query) or die("Invalid $query");

                        $query = "SELECT setval('classi_id_seq',(SELECT max(id_classe) FROM classi) + 1)";
                        pgsql_query($query) or die("Invalid $query");

                        // trovo tutti gli studenti, parenti, storia_studenti e parenti studenti che mancano nel nuov anno per andare a pescare nel vecchio db quelli da aggiungere
                        $query = "SELECT id_studente FROM studenti";
                        $res = pgsql_query($query) or die("Invalid $query");
                        $id_studenti_anno_nuovo = pg_fetch_all($res);

                        $query = "SELECT id_parente FROM parenti";
                        $res = pgsql_query($query) or die("Invalid $query");
                        $id_parenti_anno_nuovo = pg_fetch_all($res);

                        $query = "SELECT id_storia_studenti FROM storia_studenti";
                        $res = pgsql_query($query) or die("Invalid $query");
                        $id_storia_studenti_anno_nuovo = pg_fetch_all($res);


                        // mi sposto nel vecchio db
                        $db_key = $vecchio_db;

                        aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'SI', $current_user);

                        // estraggo i nuovi parenti, studenti, storia_studenti e parenti_studenti
                        $query = "SELECT id_studente FROM studenti WHERE id_studente NOT IN (" . implode(', ',array_column($id_studenti_anno_nuovo, "id_studente")) . ") ";
                        $res = pgsql_query($query) or die("Invalid $query");
                        $nuovi_id_studenti = pg_fetch_all($res);
                        pgsql_query("COPY (SELECT * FROM studenti WHERE id_studente NOT IN (" . implode(', ',array_column($id_studenti_anno_nuovo, "id_studente")) . ")) TO '/var/lib/postgresql/studenti_nuovi_{$anno_scolastico_db_origine}.copy'");

                        $query = "SELECT id_parente FROM parenti WHERE id_parente NOT IN (" . implode(', ',array_column($id_parenti_anno_nuovo, "id_parente")) . ") ";
                        $res = pgsql_query($query) or die("Invalid $query");
                        $nuovi_id_parenti = pg_fetch_all($res);
                        pgsql_query("COPY (SELECT * FROM parenti WHERE id_parente NOT IN (" . implode(', ',array_column($id_parenti_anno_nuovo, "id_parente")) . ")) TO '/var/lib/postgresql/parenti_nuovi_{$anno_scolastico_db_origine}.copy'");

                        $query = "SELECT id_storia_studenti FROM storia_studenti WHERE id_storia_studenti NOT IN (" . implode(', ',array_column($id_storia_studenti_anno_nuovo, "id_storia_studenti")) . ") ";
                        $res = pgsql_query($query) or die("Invalid $query");
                        $nuovi_id_storia_studenti = pg_fetch_all($res);
                        pgsql_query("COPY (SELECT * FROM storia_studenti WHERE id_storia_studenti NOT IN (" . implode(', ',array_column($id_storia_studenti_anno_nuovo, "id_storia_studenti")) . ")) TO '/var/lib/postgresql/storia_studenti_nuovi_{$anno_scolastico_db_origine}.copy'");

                        $and = [];
                        if (isset($nuovi_id_studenti[0]['id_studente'])){
                            $nuovi_id_studenti_string = " id_studente IN (" . implode(", ", array_column($nuovi_id_studenti, 'id_studente')) . ") ";
                            $and[] = $nuovi_id_studenti_string;
                        }

                        if (isset($nuovi_id_parenti[0]['id_parente'])){
                            $nuovi_id_parenti_string = " id_parente IN (" . implode(", ", array_column($nuovi_id_parenti, 'id_parente')) . ") ";
                            $and[] = $nuovi_id_parenti_string;
                        }

                        if (count($and) > 0){
                            $query = "SELECT * FROM parenti_studenti WHERE " . implode($and, ' OR ');
                            pgsql_query("COPY (SELECT * FROM parenti_studenti WHERE " . implode($and, ' OR ') . ") TO '/var/lib/postgresql/parenti_studenti_nuovi_{$anno_scolastico_db_origine}.copy'");
                        }

                        // mi risposto nel nuovo db e tiro su i nuovi dati
                        $db_key = $nuovo_db;

                        // shell_exec(" \i /var/lib/postgresql/studenti_nuovi.copy");
                        // shell_exec(" \i /var/lib/postgresql/parenti_nuovi.copy");
                        // shell_exec(" \i /var/lib/postgresql/storia_studenti_nuovi.copy");
                        pgsql_query("COPY studenti FROM '/var/lib/postgresql/studenti_nuovi_{$anno_scolastico_db_origine}.copy'");
                        pgsql_query("COPY parenti FROM '/var/lib/postgresql/parenti_nuovi_{$anno_scolastico_db_origine}.copy'");
                        pgsql_query("COPY storia_studenti FROM '/var/lib/postgresql/storia_studenti_nuovi_{$anno_scolastico_db_origine}.copy'");
                        if (count($and) > 0) {
                            // shell_exec(" \i /var/lib/postgresql/parenti_studenti_nuovi.copy");
                            pgsql_query("COPY parenti_studenti FROM '/var/lib/postgresql/parenti_studenti_nuovi_{$anno_scolastico_db_origine}.copy'");
                        }

                        // decancello studenti e parenti che ora hanno un abbinamento
                        // DA NON FARE ALTRIMENTI RISCHIA DI DECANCELLARE PARENTI CHE ERANO STATI VOLUTAMENTE CANCELLATI
                        // $query = "UPDATE studenti
                        //             SET flag_canc = 0
                        //             WHERE flag_canc > 0
                        //                 AND id_studente IN (
                        //                 SELECT DISTINCT id_studente FROM classi_studenti
                        //             )";
                        // pgsql_query($query);

                        // $query = "UPDATE parenti_studenti
                        //             SET flag_canc = 0
                        //             WHERE flag_canc > 0
                        //                 AND id_studente IN (
                        //                 SELECT DISTINCT id_studente FROM studenti WHERE flag_canc = 0
                        //             )";
                        // pgsql_query($query);

                        // $query = "UPDATE parenti
                        //             SET flag_canc = 0
                        //             WHERE flag_canc > 0
                        //                 AND id_parente IN (SELECT DISTINCT id_parente FROM parenti_studenti WHERE flag_canc = 0)";
                        // pgsql_query($query);

                        // pulisco i servizi marketplace che sono terminati prima del 1 settembre del nuovo anno
                        $anno_scolastico_nuovo = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
                        $primo_settembre = strtotime(explode('/',$anno_scolastico_nuovo)[0] . '-09-01 00:00');

                        $query = "DELETE FROM marketplace_studenti_acquisti
                            WHERE id_marketplace IN (
                                SELECT id_marketplace
                                FROM marketplace
                                WHERE validita_fine < {$primo_settembre}
                            )
                        ";
                        pgsql_query($query);

                        $query = "DELETE
                            FROM marketplace
                            WHERE validita_fine < {$primo_settembre}
                        ";
                        pgsql_query($query);

                        aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'NO', $current_user);

                        $db_key = $vecchio_db;

                        if ($finalizza_cambio_anno == 'NO') {
                            $sync_years_users = nextapi_call('admin/sync_years_users', 'POST', [], $current_key);

                            aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'NO', $current_user);
                            aggiorna_parametri_singoli('END_CAMBIO_ANNO', time(), $current_user);
                        }
                    }

                    $db_key = $vecchio_db;
                    file_put_contents('/tmp/cambio_anno_finalizza', print_r($finalizza_cambio_anno, true));
                    if ($finalizza_cambio_anno == 'SI'){
                        aggiorna_parametri_singoli('FINALIZZA_CAMBIO_ANNO', 'NO', $current_user);
                        //siamo nel db vecchio
                        // tabelle da copiare nel nuovo anno con considerazioni particolari per esempio su chiave esterna,
                        // da gestire in modo specifico singolarmente
                        $da_riportare_con_considerazioni = [
                            'abbinamenti_funzioni_professori',
                            'abbinamenti_funzioni_utenti'
                        ];

                        // tabelle da copiare nel nuovo anno senza considerazioni particolari
                        $da_riportare = [
                            'optionals',
                            'valutazioni_competenze',
                            'adozioni_aie',
                            'marketplace',
                            'marketplace_studenti_acquisti',
                            'abbinamenti_indirizzi_utenti',
                            'abbinamenti_tag',
                            'abbinamenti_template_word_pagelle_classi',
                            'allarmi_controlli',
                            'allarmi_controlli_esclusioni',
                            'attributi_competenze',
                            'badge',
                            'campi_liberi',
                            'campi_liberi_classe_materia',
                            'cartelle',
                            'categorie_risorse',
                            'categorie_tag',
                            'competenze_valori',
                            'comuni',
                            'consiglio_orientativo_template',
                            'consiglio_orientativo_trentino',
                            'corrispondenze_campi_word',
                            'debiti',
                            'elenco_scuole',
                            'elenco_stampe_personalizzate',
                            'fonti_finanziamento',
                            'funzioni_periodiche',
                            'funzioni_periodiche_esecuzioni',
                            'gruppi_attributi_competenze',
                            'importazioni_moduli',
                            'intervalli_predefiniti_orario',
                            'ip_abilitati',
                            'materie_aree_disciplinari',
                            'mensa_servizi',
                            'mensa_calendario',
                            'mensa_pasti_studenti_eccezioni',
                            'messaggi_destinatari_permessi',
                            'messaggi_gruppi',
                            'messaggi_gruppi_utenti',
                            'modelli_badge',
                            'modelli_documenti',
                            'motivazioni_assenze',
                            'messaggi',
                            'motivazioni_crediti',
                            'nomine_docenti',
                            'note_disciplinari_predefinite',
                            'parametri',
                            'parametri_stampe',
                            'permessi_accesso_risorse',
                            'pesi',
                            'risorse',
                            'ruoli_docenti',
                            'satispay',
                            'schema_colori_competenze',
                            'scuole',
                            'sedi',
                            'servizi_giornalieri_consolidamento',
                            'sessioni',
                            'significati_voti',
                            'significati_voti_religione',
                            'simboli',
                            'simboli_materia_classe',
                            'stati_studente_personalizzati',
                            'tag',
                            'template_b1',
                            'template_word',
                            'tipi_corso',
                            'tipi_dati_medici',
                            'tipi_dati_personalizzati',
                            'tipi_recupero',
                            'tipi_scuole',
                            'tipi_voto',
                            'valori_precomp',
                            'valutazioni_competenze',
                            'ai_prompts'
                        ];

                        foreach ($da_riportare as $tabella) {
                            pgsql_query("COPY {$tabella} TO '/var/lib/postgresql/{$tabella}_{$anno_scolastico_db_origine}.copy'");
                        }
                        foreach ($da_riportare_con_considerazioni as $tabella) {
                            pgsql_query("COPY {$tabella} TO '/var/lib/postgresql/{$tabella}_{$anno_scolastico_db_origine}.copy'");
                        }

                        // salvo i crediti degli studenti
                        $query = "SELECT id_studente,
                            crediti_terza,
                            crediti_reintegrati_terza,
                            crediti_sospesi_terza,
                            crediti_quarta,
                            crediti_sospesi_quarta,
                            crediti_reintegrati_quarta,
                            crediti_quinta,
                            crediti_finali_agg,
                            motivi_crediti_terza,
                            motivi_crediti_quarta,
                            motivi_crediti_quinta,
                            motivi_crediti_agg
                        FROM studenti
                        WHERE flag_canc = 0 ";
                        $result = pgsql_query($query);
                        $elenco_studenti_crediti = pg_fetch_all($result);

//                        pgsql_query("COPY optionals TO '/var/lib/postgresql/optionals_{$anno_scolastico_db_origine}.copy'");
//                        pgsql_query("COPY valutazioni_competenze TO '/var/lib/postgresql/valutazioni_competenze_{$anno_scolastico_db_origine}.copy'");
//                        pgsql_query("COPY adozioni_aie TO '/var/lib/postgresql/adozioni_aie_{$anno_scolastico_db_origine}.copy'");
//                        pgsql_query("COPY marketplace TO '/var/lib/postgresql/marketplace_{$anno_scolastico_db_origine}.copy'");
//                        pgsql_query("COPY marketplace_studenti_acquisti TO '/var/lib/postgresql/marketplace_studenti_acquisti_{$anno_scolastico_db_origine}.copy'");

                        $db_key = $nuovo_db;

                        //siamo nel nuovo db, svuotiamo le tabelle e le popoliamo con i copy salvati
                        foreach ($da_riportare as $tabella) {
                            $query = "TRUNCATE TABLE {$tabella} CASCADE";
                            pgsql_query($query);
                            pgsql_query("COPY {$tabella} FROM '/var/lib/postgresql/{$tabella}_{$anno_scolastico_db_origine}.copy'");
                        }

                        foreach ($da_riportare_con_considerazioni as $tabella) {
                            $query = "TRUNCATE TABLE {$tabella} CASCADE";
                            pgsql_query($query);

                            // creo una tabella temporanea
                            $query = "CREATE TEMPORARY TABLE {$tabella}_temp AS TABLE {$tabella} WITH NO DATA";
                            pgsql_query($query);

                            // inserisco i dati del copy
                            pgsql_query("COPY {$tabella}_temp FROM '/var/lib/postgresql/{$tabella}_{$anno_scolastico_db_origine}.copy'");

                            switch ($tabella){
                                case 'abbinamenti_funzioni_professori':
                                    // inserisco i dati nella tabella originale verificando le condizioni necessarie
                                    $query = "INSERT INTO {$tabella} (SELECT * FROM {$tabella}_temp WHERE id_professore IN (SELECT id_utente FROM utenti))";
                                    pgsql_query($query);
                                    break;
                                case 'abbinamenti_funzioni_utenti':
                                    // inserisco i dati nella tabella originale verificando le condizioni necessarie
                                    $query = "INSERT INTO {$tabella} (SELECT * FROM {$tabella}_temp WHERE id_utente IN (SELECT id_utente FROM utenti))";
                                    pgsql_query($query);
                                    break;
                                default:
                                    // inserisco i dati nella tabella originale verificando le condizioni necessarie
                                    $query = "INSERT INTO {$tabella} (SELECT * FROM {$tabella}_temp)";
                                    pgsql_query($query);
                                    break;
                            }
                        }
                        // allineo tutte le sequence
                        $query_seq = "SELECT sequence_schema, sequence_name FROM information_schema.sequences";
                        $res = pgsql_query($query_seq);
                        $seq_da_aggiornare = pg_fetch_all($res);
                        foreach ($seq_da_aggiornare as $sequence) {
                            if ($sequence['sequence_schema'] == 'public')
                            {
                                allinea_sequence($sequence['sequence_name'], true);
                            }
                        }
                        // settaggio parametri finali nel nuovo anno dopo averli copiati dal vecchio db
                        setta_parametri_ca($current_user, $anno);
                        aggiorna_parametri_singoli('ANNO_SCOLASTICO_ATTUALE', $anno . "/" . ($anno + 1), $current_user);
                        //allineo il parametro tread e vuoto dopo i messaggi nel nuovo anno
                        $query = "update parametri SET valore = (select (max(tread) + 1) from messaggi) where nome = 'TREAD_ANNO_MINIMO'";
                        pgsql_query($query);
                        $query = "DELETE FROM messaggi";
                        pgsql_query($query);

//                        // Aggiorno gli optionals
//                        $query = "TRUNCATE TABLE optionals CASCADE";
//                        pgsql_query($query);
//                        pgsql_query("COPY optionals FROM '/var/lib/postgresql/optionals_{$anno_scolastico_db_origine}.copy'");
//
//                        // Aggiorno le valutazioni competenze
//                        $query = "TRUNCATE TABLE valutazioni_competenze CASCADE";
//                        pgsql_query($query);
//                        pgsql_query("COPY valutazioni_competenze FROM '/var/lib/postgresql/valutazioni_competenze_{$anno_scolastico_db_origine}.copy'");
//
//                        // Porto le adozioni aie perche' magari quando e' stato fatto il cambio anno non erano ancora state portate
//                        $query = "TRUNCATE TABLE adozioni_aie CASCADE";
//                        pgsql_query($query);
//                        pgsql_query("COPY adozioni_aie FROM '/var/lib/postgresql/adozioni_aie_{$anno_scolastico_db_origine}.copy'");

                        $query = "UPDATE adozioni_aie
                            SET approvazione_segreteria = 'NO',
                                approvazione_responsabile_didattica = 'NO',
                                stato_adozione = 'PROPOSTA'";
                        pgsql_query($query);

                        //cancelliamo le esecuzioni piu vecchie di 45 giorni per evitare il reinvio di una cosa gia inviata
                        $query = "DELETE FROM funzioni_periodiche_esecuzioni where data_esecuzione < " . strtotime("-45 days");
                        pgsql_query($query);

                        //Ripulisco gli studenti non abbinati e flag-cancellati
                        // Mantengo gli studenti con i seguenti esiti:
                        // - Giudizio sospeso
                        // - Frequenza anno estero
                        $query = "DELETE FROM studenti WHERE
                            (
                                id_studente IN (
                                    SELECT DISTINCT id_studente
                                    FROM studenti
                                    WHERE esito_corrente_calcolato NOT IN ('Giudizio sospeso', 'Frequenza anno estero')
                                )
                                AND id_studente NOT IN (
                                    SELECT DISTINCT id_studente FROM studenti_completi
                                )
                            )";
                        pgsql_query($query);

                        pgsql_query("DELETE FROM studenti WHERE flag_canc > 0");

                        //cancelliamo le sessioni di consultazione storica per i docenti
                        $query = "DELETE FROM sessioni where parametro = 'ANNO_CONSULTAZIONE' and id_utente in (select id_utente from utenti where tipo_utente = 'P')";
                        pgsql_query($query);

                        //cancelliamo le sessioni di consultazione storica per gli studenti
                        $query = "DELETE FROM sessioni where parametro = 'ANNO_CONSULTAZIONE' and id_utente in (select id_studente from studenti)";
                        pgsql_query($query);

                        //togliamo la visione storica a chi ce l'ha impostata
                        $query = "update utenti SET stato_funzionamento = 'NORMALE' where stato_funzionamento != 'NORMALE';";
                        pgsql_query($query);

                        // Svuoto la tabella degli abbinamenti di sostegno
                        $query = "DELETE FROM abbinamenti_studente_docente_sostegno WHERE
                            (
                                id_studente IN (
                                    SELECT DISTINCT id_studente
                                    FROM studenti
                                    WHERE esito_corrente_calcolato NOT IN ('Giudizio sospeso', 'Frequenza anno estero')
                                )
                                AND id_studente NOT IN (
                                    SELECT DISTINCT id_studente FROM studenti_completi
                                )
                            ) OR id_utente NOT IN (
                            SELECT id_utente FROM utenti WHERE flag_canc = 0)";
                        pgsql_query($query);

                        // se c'è almeno un sospeso creo la struttura per i sospesi
                        $query_sosp = "SELECT DISTINCT id_studente FROM studenti_completi where esito_corrente_calcolato ilike '%sospeso%'";
                        $result_sosp = pgsql_query($query_sosp);
                        $numero_sosp = pg_num_rows($result_sosp);

                        if ($numero_sosp > 0)
                        {
                            abbina_sospesi_per_quaderno();
                        }

                        // Svuoto la tabella dei curriculae
                        $query = "DELETE FROM storia_studenti WHERE
                            (
                                id_studente IN (
                                    SELECT DISTINCT id_studente
                                    FROM studenti
                                    WHERE esito_corrente_calcolato NOT IN ('Giudizio sospeso', 'Frequenza anno estero')
                                )
                                AND id_studente NOT IN (
                                    SELECT DISTINCT id_studente FROM studenti_completi
                                )
                            )";
                        pgsql_query($query);

                        $query = "DELETE FROM storia_studenti WHERE id_studente NOT IN (SELECT DISTINCT id_studente FROM studenti)";
                        pgsql_query($query);

                        $query = "DELETE FROM classi_studenti WHERE id_studente NOT IN (SELECT DISTINCT id_studente FROM studenti_completi)";
                        pgsql_query($query);

                        $query = "DELETE FROM debiti WHERE
                                    (
                                        id_studente IN (
                                            SELECT DISTINCT id_studente
                                            FROM studenti
                                            WHERE esito_corrente_calcolato NOT IN ('Giudizio sospeso', 'Frequenza anno estero')
                                        )
                                        AND id_studente NOT IN (
                                            SELECT DISTINCT id_studente FROM studenti_completi
                                        )
                                    )";
                        pgsql_query($query);

                        // pulisco parenti e parenti_studenti di studenti non abbinati
                        $query = "UPDATE parenti_studenti
                                    SET flag_canc = " . $time . "
                                    WHERE id_studente NOT IN (
                                        SELECT DISTINCT id_studente FROM studenti WHERE flag_canc = 0
                                    )";
                        pgsql_query($query);

                        $query = "UPDATE parenti
                                    SET flag_canc = " . $time . "
                                    WHERE id_parente NOT IN (SELECT DISTINCT id_parente FROM parenti_studenti WHERE flag_canc = 0)";
                        pgsql_query($query);

                        // riparto con le delete
                        $query = "DELETE FROM parenti_studenti WHERE id_studente NOT IN (SELECT DISTINCT id_studente FROM studenti)";
                        pgsql_query($query);

                        $query = "DELETE FROM dati_personalizzati WHERE id_studente NOT IN (SELECT DISTINCT id_studente FROM studenti)";
                        pgsql_query($query);

                        pgsql_query("DELETE FROM parenti WHERE id_parente NOT IN (SELECT DISTINCT id_parente FROM parenti_studenti)");

                        // pulisco le tabelle dai dati flagcancellati
                        pgsql_query("DELETE FROM indirizzi WHERE flag_canc > 0");
                        pgsql_query("DELETE FROM classi WHERE flag_canc > 0");
                        pgsql_query("DELETE FROM classi_studenti WHERE flag_canc > 0");
                        pgsql_query("DELETE FROM storia_studenti WHERE flag_canc > 0");
                        pgsql_query("DELETE FROM parenti_studenti WHERE flag_canc > 0");
                        pgsql_query("DELETE FROM parenti WHERE flag_canc > 0");

                        foreach ($mat_tabelle_da_copiare_no_succ as $tabella) {
                            pgsql_query("DELETE FROM {$tabella} WHERE flag_canc > 0");
                        }

                        $query = "DELETE FROM abbinamenti_tag where tipo_abbinato not in ('studente','parente')
                            OR (tipo_abbinato = 'studente' and id_abbinato not in (select id_studente from studenti)
                            OR (tipo_abbinato = 'parente' and id_abbinato not in (select id_parente from parenti)
                                    ";
                        pgsql_query($query);

                        $query = "DELETE FROM messaggi_gruppi_utenti where
                             (tipo_utente = 'studente' and id_utente not in (select id_studente from studenti)
                            OR (tipo_utente = 'parente' and id_utente not in (select id_parente from parenti)
                                    ";
                        pgsql_query($query);

                        // Pulizia tabella Hotspot
                        // Mantengo gli hotspot anche degli studenti non abbinati per includere Giudizi Sospesi e frequentanti all'estero
                        $query = "DELETE FROM client_hot_spot
                                    WHERE (id_utente NOT IN (SELECT id_studente FROM studenti WHERE flag_canc = 0) AND tipo_utente = 'S')
                                        OR (id_utente NOT IN (SELECT id_utente FROM utenti WHERE flag_canc = 0 AND tipo_utente = 'A') AND tipo_utente = 'A')
                                        OR (id_utente NOT IN (SELECT id_utente FROM utenti WHERE flag_canc = 0 AND tipo_utente = 'P') AND tipo_utente = 'P')
                                        OR (id_utente NOT IN (SELECT id_utente FROM utenti WHERE flag_canc = 0 AND tipo_utente = 'E') AND tipo_utente = 'E')";
                        pgsql_query($query);

                        // Aggiorno le date di scadenza degli hotspost per gli utenti ancora presenti
                        $query = "UPDATE client_hot_spot
                        SET data_scadenza = extract(epoch from now())::int + 31536000
                        WHERE (id_utente IN (SELECT id_utente FROM utenti WHERE flag_canc = 0 AND tipo_utente = 'A') AND tipo_utente = 'A')
                            OR (id_utente IN (SELECT id_utente FROM utenti WHERE flag_canc = 0 AND tipo_utente = 'P') AND tipo_utente = 'P')
                            OR (id_utente IN (SELECT id_utente FROM utenti WHERE flag_canc = 0 AND tipo_utente = 'E') AND tipo_utente = 'E')";
                        pgsql_query($query);

                        //Svuoto i campi dei rappresentanti, verificare quelli di istituto
                        $query = "UPDATE studenti SET rappresentante = DEFAULT ";
                        pgsql_query($query);
                        $query = "UPDATE parenti SET rappresentante = DEFAULT ";
                        pgsql_query($query);
                        $query = "UPDATE parenti_studenti SET rappresentante = DEFAULT ";
                        pgsql_query($query);
                        $query = "UPDATE utenti SET rappresentante = DEFAULT ";
                        pgsql_query($query);

                        //Svuoto i dati degli esami
                        $query = "UPDATE studenti
                                    SET voto_primo_scritto = DEFAULT,
                                    voto_secondo_scritto = DEFAULT,
                                    voto_terzo_scritto = DEFAULT,
                                    voto_orale = DEFAULT,
                                    voto_bonus = DEFAULT,
                                    materia_secondo_scr = DEFAULT,
                                    ulteriori_specif_diploma = DEFAULT,
                                    numero_diploma = DEFAULT,
                                    voto_ammissione = DEFAULT,
                                    differenza_punteggio = DEFAULT,
                                    voto_qualifica = DEFAULT,
                                    voto_esame_sc1_qual = DEFAULT,
                                    voto_esame_sc2_qual = DEFAULT,
                                    voto_esame_or_qual = DEFAULT,
                                    stato_privatista = DEFAULT,
                                    ammesso_esame_qualifica = DEFAULT,
                                    ammesso_esame_quinta = DEFAULT,
                                    giudizio_ammissione_quinta = DEFAULT,
                                    stato_licenza_maestro = DEFAULT,
                                    giudizio_esame_sc1_qual = DEFAULT,
                                    giudizio_esame_sc2_qual = DEFAULT,
                                    giudizio_esame_or_qual = DEFAULT,
                                    giudizio_complessivo_esame_qual = DEFAULT,
                                    data_orale = DEFAULT,
                                    tipo_primo_scritto = DEFAULT,
                                    tipo_secondo_scritto = DEFAULT,
                                    tipo_terzo_scritto = DEFAULT,
                                    unanimita_primo_scritto = DEFAULT,
                                    unanimita_secondo_scritto = DEFAULT,
                                    unanimita_terzo_scritto = DEFAULT,
                                    argomento_scelto_orale = DEFAULT,
                                    area_disc_1_orale = DEFAULT,
                                    area_disc_2_orale = DEFAULT,
                                    disc_elaborati_orale = DEFAULT,
                                    unanimita_voto_finale = DEFAULT,
                                    ordine_esame_orale = DEFAULT,
                                    esito_terza_media = DEFAULT,
                                    presente_esame_quinta = DEFAULT,
                                    num_volte_iscritto_classe_attuale = DEFAULT,
                                    voto_esame_medie_italiano = DEFAULT,
                                    voto_esame_medie_inglese = DEFAULT,
                                    voto_esame_medie_matematica = DEFAULT,
                                    voto_esame_medie_seconda_lingua = DEFAULT,
                                    voto_esame_medie_invalsi_ita = DEFAULT,
                                    voto_esame_medie_invalsi_mat = DEFAULT,
                                    voto_esame_medie_orale = DEFAULT,
                                    voto_ammissione_medie = DEFAULT,
                                    esito_prima_elementare = DEFAULT,
                                    esito_seconda_elementare = DEFAULT,
                                    esito_terza_elementare = DEFAULT,
                                    esito_quarta_elementare = DEFAULT,
                                    esito_quinta_elementare = DEFAULT,
                                    tipo_voto_esame_medie_italiano = DEFAULT,
                                    tipo_voto_esame_medie_inglese = DEFAULT,
                                    giudizio_1_medie = DEFAULT,
                                    giudizio_2_medie = DEFAULT,
                                    giudizio_3_medie = DEFAULT,
                                    argomenti_orali_medie = DEFAULT,
                                    giudizio_finale_1_medie = DEFAULT,
                                    giudizio_finale_2_medie = DEFAULT,
                                    giudizio_finale_3_medie = DEFAULT,
                                    consiglio_terza_media = DEFAULT,
                                    giudizio_sintetico_esame_terza_media = DEFAULT,
                                    numero_quesiti_esame_medie_matematica = DEFAULT,
                                    tipo_voto_esame_medie_seconda_lingua = DEFAULT,
                                    voto_esame_medie_invalsi_finale = DEFAULT,
                                    tipo_voto_esame_medie_matematica = DEFAULT,
                                    giudizio_prove_scritte_mat_scuole_medie = DEFAULT,
                                    giudizio_prove_scritte_ing_scuole_medie = DEFAULT";
                        pgsql_query($query);

                        // Estrazione dati mensa studente prima della pulizia per avere i conti dei consolidamenti
                        $elenco_studenti_tmp = estrai_studenti_istituto();
                        $elenco_studenti = [];
                        $elenco_studenti_servizi_giornalieri = [];

                        $db_key = $vecchio_db;
                        $opt_servizi_giornalieri = estrai_optionals_singoli('servizi_giornalieri', 'amministratore');
                        foreach ($elenco_studenti_tmp as $studente) {
                            if ($studente['servizio_mensa'] == 1) {
                                $studente['situazione_mensa'] = estrai_situazione_mensa_studente($studente['id_studente'], $current_key);
                                $elenco_studenti[$studente['id_studente']] = $studente;
                            }

                            // valutare se farlo solo quando l'optional e' attivo
                            if ($opt_servizi_giornalieri == 1){
                                $payload = [
                                    "id_studente" => $studente['id_studente'],
                                    "tipo_risultato" => "RIEPILOGO",
                                    "db_richiesto"  =>  $db_key
                                ];

                                $lista_marketplace = nextapi_call('/marketplace/credito_studente_servizi_giornalieri', 'GET', $payload, $current_key);
                                if (!empty($lista_marketplace)){
                                    $elenco_studenti_servizi_giornalieri[$studente['id_studente']] = $lista_marketplace[$studente['id_studente']];
                                }
                            }
                        }
                        $db_key = $nuovo_db;

                        // Inserimento consolidamenti mense
                        if (!empty($elenco_studenti)) {
                            $elenco_nuovi_studenti = estrai_studenti_istituto();
                            foreach ($elenco_nuovi_studenti as $studente) {
                                $elenco_id_nuovi_studenti[$studente['id_studente']] = $studente['id_studente'];
                            }

                            foreach ($elenco_studenti as $studente) {
                                if (in_array($studente['id_studente'], $elenco_id_nuovi_studenti)) {
                                    $query = "  INSERT INTO mensa_consolidamento (
                                                    id_studente,
                                                    tipo,
                                                    descrizione,
                                                    credito,
                                                    data_inizio,
                                                    data_inserimento,
                                                    tipo_inserimento,
                                                    data_modifica,
                                                    tipo_modifica
                                                    )
                                                VALUES (
                                                    {$studente['id_studente']},
                                                    'CAMBIO_ANNO',
                                                    'CREDITO RIMANENTE " . ($anno - 1) . "/" . $anno . "',
                                                    {$studente['situazione_mensa']['credito_rimasto']},
                                                    " . time() . ",
                                                    " . time() . ",
                                                    'CAMBIO_ANNO',
                                                    " . time() . ",
                                                    'CAMBIO_ANNO'
                                                    )
                                                ";
                                    $result = pgsql_query($query);
                                }
                            }
                        }

                        // Consolidamento servizi giornalieri
                        foreach ($elenco_studenti_servizi_giornalieri as $id_studente_marketplace => $lista_marketplace){
                            foreach ($lista_marketplace as $id_tipo_movimento => $singolo_tipo_movimento){
                                $nuovo_consolidamento['data_consolidamento'] = strtotime($nuovo_consolidamento['data']);
                                $payload = [
                                    "data_consolidamento"   =>  time(),
                                    "db_richiesto"          =>  $db_key,
                                    "id_studente"           =>  $id_studente_marketplace,
                                    "id_tipo_movimento"     =>  $id_tipo_movimento,
                                    "credito_iniziale"      =>  $singolo_tipo_movimento['credito_rimanente'],
                                    "descrizione"           =>  "CREDITO RIMANENTE " . ($anno - 1) . "/" . $anno,
                                    "note"                  =>  "Consolidamento derivato dal cambio anno"
                                ];
                                nextapi_call('marketplace/consolidamenti/inserisci', 'POST', $payload, $current_key);
                            }
                        }

                        $query = "SELECT DISTINCT id_studente FROM studenti_completi";
                        $result = pgsql_query($query);
                        $numero = pg_num_rows($result);

                        if ($numero > 0) {
                            for ($cont = 0; $cont < $numero; $cont++) {
                                $id_studente = pg_fetch_result($result, $cont, "id_studente");
                                aggiorna_curriculum_studente($id_studente, 'iscrizione', $current_user);
                            }

                            aggiorna_esito_corrente_calcolato();
                        }

                        // Reset per Wizard di inizio anno
                        pgsql_query("UPDATE parametri SET valore = '' WHERE nome = 'COMPLETAMENTO_WIZARD_INIZIO_ANNO'");

                        // Svuoto i campi di studenti per il nuovo anno (DA AGGIORNARE CON GLI ALTRI CAMPI!)
                        // - consiglio_orientativo_trentino (tanto nel nuovo anno non serve)
                        // - id_quadro_orario, id_piano_studio, id_flusso (per evitare che gli studenti siano già abbinati a quadri orario, piani studio o flussi vecchi)
                        // - forza_competenze
                        pgsql_query("UPDATE studenti
                                        SET consiglio_orientativo_trentino = '0',
                                            id_quadro_orario_sidi = null,
                                            id_piano_studio = null,
                                            id_flusso = DEFAULT,
                                            forza_competenze = 0
                                        ");

                        // Aggiorno il periodo di valididtà del servizio mensa per gli studenti
                        // che si avvalgono di tale servizio
                        pgsql_query("INSERT INTO mensa_periodi_studenti (id_studente, inizio, fine)
                                    SELECT id_studente,
                                    (SELECT to_timestamp(valore::int)::date FROM parametri WHERE nome = 'DATA_INIZIO_LEZIONI'),
                                    (SELECT to_timestamp(valore::int)::date FROM parametri WHERE nome = 'DATA_FINE_LEZIONI')
                                    FROM studenti
                                    WHERE servizio_mensa = 1
                                        AND flag_canc = 0
                                        AND (SELECT valore FROM parametri WHERE nome = 'ABILITA_SERVIZIO_MENSA') = 'SI'");

                        // riporto i crediti dell'anno precedente
                        foreach ($elenco_studenti_crediti as $studente_crediti) {
                            $query = "UPDATE studenti SET
                                crediti_terza = {$studente_crediti['crediti_terza']},
                                crediti_reintegrati_terza = {$studente_crediti['crediti_reintegrati_terza']},
                                crediti_sospesi_terza = {$studente_crediti['crediti_sospesi_terza']},
                                crediti_quarta = {$studente_crediti['crediti_quarta']},
                                crediti_sospesi_quarta = {$studente_crediti['crediti_sospesi_quarta']},
                                crediti_reintegrati_quarta = {$studente_crediti['crediti_reintegrati_quarta']},
                                crediti_quinta = {$studente_crediti['crediti_quinta']},
                                crediti_finali_agg = {$studente_crediti['crediti_finali_agg']},
                                motivi_crediti_terza = '{$studente_crediti['motivi_crediti_terza']}',
                                motivi_crediti_quarta = '{$studente_crediti['motivi_crediti_quarta']}',
                                motivi_crediti_quinta = '{$studente_crediti['motivi_crediti_quinta']}',
                                motivi_crediti_agg = '{$studente_crediti['motivi_crediti_agg']}'
                            WHERE id_studente = {$studente_crediti['id_studente']}";
                            pgsql_query($query);
                        }

                        //pgsql_query("UPDATE parametri set valore = 'VERSIONE_2' where nome = 'VERSIONE_MESSENGER'");

                        // Riabilitazione sincronizzazione Messenger per nuovo db
                        pgsql_query("SELECT * FROM messenger_set_sync('ENABLE')");
                        pgsql_query("DROP RULE moodle_state_immutable ON parametri;");
                        aggiorna_parametri_singoli('ATTIVAZIONE_MOODLE', 'NO', $current_user);
//                        pgsql_query("CREATE OR REPLACE RULE moodle_state_immutable AS
//                                    ON UPDATE TO parametri WHERE new.nome::text = 'ATTIVAZIONE_MOODLE'::text AND old.valore = 'SI'::text
//                                    DO INSTEAD SELECT 'ERROR: Non è possibile disattivare la sincronizzazione di Moodle una volta attivato!'");

                        aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'NO', $current_user);
                        aggiorna_parametri_singoli('END_CAMBIO_ANNO', '0', $current_user);
                        aggiorna_parametri_singoli('START_CAMBIO_ANNO', '0', $current_user);
                        genera_numeri_registro($current_user);
                        aggiorna_parametri_singoli('RIGENERAZIONE_NUMERI_REGISTRO_WIZARD', 0, $current_user);

                        // TODO: sistemare e rimuovere non appena sanata la sincronizzazione con messenger_sync_classe_abbinamenti_studente
                        pgsql_query("SELECT (
                                        SELECT * FROM messenger_sync_classe_abbinamenti_studente(classi.id_classe, classi.id_classe) AS (id uuid)
                                    ) AS studente,
                                    (
                                        SELECT * FROM messenger_sync_classe_abbinamenti_professore(classi.id_classe, classi.id_classe) AS (id uuid)
                                    ) AS professore,
                                    (
                                        SELECT * FROM messenger_sync_classe_abbinamenti_parente(classi.id_classe, classi.id_classe) AS (id uuid)
                                    ) AS parente
                                    FROM classi
                                    WHERE flag_canc = 0
                                        AND 1 = (
                                            SELECT count(*) FROM parametri
                                            WHERE nome = 'ANNO_SCOLASTICO_ATTUALE'
                                                AND valore = '{$anno}/".($anno + 1)."'
                                        )");

                        // Torno nel database vecchio per togliere i blocchi
                        $db_key = $vecchio_db;


                        //pgsql_query("UPDATE parametri set valore = 'VERSIONE_2' where nome = 'VERSIONE_MESSENGER'");
                        pgsql_query("DROP RULE moodle_state_immutable ON parametri;");
                        aggiorna_parametri_singoli('ATTIVAZIONE_MOODLE', 'NO', $current_user);
                        pgsql_query("CREATE OR REPLACE RULE moodle_state_immutable AS
                                     ON UPDATE TO parametri WHERE new.nome::text = 'ATTIVAZIONE_MOODLE'::text AND old.valore = 'SI'::text
                                     DO INSTEAD  SELECT 'ERROR: Non è possibile disattivare la sincronizzazione di Moodle una volta attivato!'");

                        $vecchio_anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');

                        // Ritorno nel database nuovo
                        $db_key = $nuovo_db;
                        file_put_contents('/tmp/cambio_anno_nuovo_db', print_r($nuovo_db, true));
                        file_put_contents('/tmp/cambio_anno_db_official', print_r($db_official, true));
                        // Allineo gli abbinamenti dei docenti di sostegno nel nuovo anno
                        allinea_sostegno_classi_prof_materie();

                        if ($nuovo_db != $db_official) {
                            file_put_contents('/tmp/cambio_anno_messaggio_db_main', print_r("ENTRO E STO PER CAMBIARE DB", true));
                            file_put_contents(MC_DBMAIN_CONF, $nuovo_db);
//                            $exec_parametri = 'php /var/www-source/mastercom/scripts/database_parametri.php';
//                            shell_exec($exec_parametri);
                            file_put_contents('/tmp/cambio_anno_messaggio_db_parametri', print_r("STO GENERANDO DB PARAMETRI", true));

                            $command = "php /var/www-source/mastercom/scripts/database_parametri.php";
                            $output = array();
                            $returnValue = 0;

                            // Esegue il comando e cattura l'output e il valore di ritorno
                            exec($command . " 2>&1", $output, $returnValue);

                            // Verifica se ci sono errori
                            if ($returnValue !== 0) {
                                $error = implode("\n", $output);
                                // Gestisci l'errore come desideri
                                file_put_contents('/tmp/cambio_anno_log_db_parametri', print_r($error,true));
                                //exit();
                                //valutare se qui cancellare il db appena creato se ha dato errore
                            }
                            file_put_contents('/tmp/cambio_anno_messaggio_festivita', print_r("STO GENERANDO FESTIVITA", true));

                            //genero il calendario delle festivita
                            crea_calendario_festivita($current_user);
                            file_put_contents('/tmp/cambio_anno_messaggio_anni_stud', print_r("STO GENERANDO ANNI STUDENTE", true));
                            $exec_anni = 'php /var/www-source/mastercom/scripts/calcola_anni_studente.php';
                            shell_exec($exec_anni);
                        }

//                        shell_exec("php /var/www-source/mastercom/scripts/curriculum/recupero_righe_da_storico.php {$vecchio_anno_scolastico} S");
                        file_put_contents('/tmp/cambio_anno_messaggio_recupero_curr', print_r("STO RECUPERANDO RIGHE STORICHE CURR", true));
                        $command = "php /var/www-source/mastercom/scripts/curriculum/recupero_righe_da_storico.php . " . $vecchio_anno_scolastico;
                        $output = array();
                        $returnValue = 0;

                        // Esegue il comando e cattura l'output e il valore di ritorno
                        exec($command . " 2>&1", $output, $returnValue);

                        // Verifica se ci sono errori
                        if ($returnValue !== 0) {
                            $error = implode("\n", $output);
                            // Gestisci l'errore come desideri
                            file_put_contents('/tmp/cambio_anno_recupero_righe_da_storico', print_r($error,true));
                            //exit();
                        }

                        // Flussi
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/*.zip");
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale/*.zip");
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/anagrafe_nazionale_infanzia/*.zip");
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/assenze/*.zip");
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/crediti_scolastici/*.zip");
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/esiti_esami_stato/*.zip");
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/esiti_finali/*.zip");
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/esiti_primaria/*.zip");
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/piani_studio/*.zip");
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/scrutini_finali_analitici/*.zip");

                        // INVALSI
                        shell_exec("rm /var/www-source/mastercom/tmp_siis/invalsi/*");

                        //sincronizzazione conj utenti couch
                        $parametri_sync_user = [];
                        $result_sync_user = chiamata_api('localhost/next-api/v1/admin/sync_users', $parametri_sync_user, 'POST', '', $current_key);
                        file_put_contents('/tmp/result_sync_user_fine_cambio_anno.txt', print_r($result_sync_user,true));

                        $sync_years_users = nextapi_call('admin/sync_years_users', 'POST', [], $current_key);

                        // Svuoto le cartelle temporanee per:
                        // - Flussi SIDI
                        // - INVALSI
                        $db_key = $vecchio_db;
                        file_put_contents('/tmp/cambio_anno_messaggio_sblocco', print_r("SBLOCCO VECCHIO ANNO SCOLASTICO", true));
                        aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'NO', $current_user);
                        aggiorna_parametri_singoli('END_CAMBIO_ANNO', time(), $current_user);

                        $db_key = $nuovo_db;
                    }
                }
            }
        }
    }
}

echo $db_key;

exit;
