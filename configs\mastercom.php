<?php
/**
 * Mastercom
 *
 * PHP version 7
 *
 * @category   Impostazioni
 * @package    Mastercom
 * @subpackage Config
 * <AUTHOR> MasterCom <<EMAIL>>
 * @copyright  2024 MasterTraining s.r.l.
 * @license    http://www.mastertraining.it license
 * @link       http://www.mastertraining.it
 * @filesource
 */

if (!defined('MC_PATH')) {
    define('MC_PATH', realpath(__DIR__ . "/../"));
}
if (!defined('MC_CONF')) {
    define('MC_CONF', '/etc/mastercom/mastercom.conf');
}
if (!defined('MC_DBMAIN_CONF')) {
    define('MC_DBMAIN_CONF', '/etc/mastercom/database_main.conf');
}
if (!defined('MC_FONTS')) {
    define('MC_FONTS', MC_PATH . '/fonts');
}

// Impostazioni Logger
if (!defined('LOGGER_APP_NAME')) {
    define('LOGGER_APP_NAME', 'mastercom');
}
// TODO: recuperare versione dinamicamente, cambiarla ogni volta per adesso
if (!defined('LOGGER_APP_VERSION')) {
    define('LOGGER_APP_VERSION', '25.3.08');
}
if (!defined('LOGGER_ERROR_HANDLER')) {
    define('LOGGER_ERROR_HANDLER', true);
}
// This directive controls whether or not PHP will output errors
if (!defined('LOGGER_DISPLAY_ERRORS')) {
    define('LOGGER_DISPLAY_ERRORS', false);
}
if (!defined('LOGGER_PHP_ERROR_HANDLER')) {
    define('LOGGER_PHP_ERROR_HANDLER', true);
}

// Current User
if (PHP_SAPI !== 'cli') {
    if (!defined('LOGGER_USER_ID')) {
        define('LOGGER_USER_ID', $_SESSION['current_user']);
    }
    if (!defined('LOGGER_USER_NAME')) {
        define('LOGGER_USER_NAME', $_SESSION['current_user']);
    }
} else {
    if (!defined('LOGGER_USER_ID')) {
        define('LOGGER_USER_ID', '1');
    }
    if (!defined('LOGGER_USER_NAME')) {
        define('LOGGER_USER_NAME', getenv('USER'));
    }
}

// PROVIDER DATA
if (!defined('PROVIDER_ID')) {
    define('PROVIDER_ID', 'MAST');
}
if (!defined('PROVIDER_ID_SHORT')) {
    define('PROVIDER_ID_SHORT', 'MT');
}
if (!defined('PROVIDER_PI')) {
    define('PROVIDER_PI', '01932770355');
}
if (!defined('SW_VER_TFR')) {
    define('SW_VER_TFR', '20191003');
}
