<?php


if (empty($desc_dirigente)) {
    $desc_dirigente = "Il Dirigente Scolastico";
}
//stampa del curriculum di uno studente
//unico parametro da passare necessario: $id_stud contentente l'ID dello studente
if ($id_stud != "") {
    $ripetente = '';
    $dimensione_font = 8;

    $dati_classe = estrai_classi_studente((int) $id_stud);

    $dati_curriculum = estrai_curriculum_studente((int) $id_stud, $no_iscritto, $no_giudizio_sospeso, $no_ammissione_esame, $no_preiscrizione, $no_licenziato, false, '', $no_trasferito);

    $id_classe = $dati_classe[0]["id_classe"];

    for ($cont_classi = 0; $cont_classi < count($dati_classe); $cont_classi++) {
        $sezioni_stud .= $dati_classe[$cont_classi]["sezione"];
        $indirizzi_stud .= $dati_classe[$cont_classi]["descrizione"] . " ";
    }

    //estraggo i dati dello studente
    $studente = estrai_dati_studente((int) $id_stud);
    $classe_attuale = $studente['classe'];
    $sezione_attuale = $studente['sezione'];
    $indirizzo_attuale = $studente['codice_indirizzi'];
    $indirizzi_stud = $studente['descrizione_indirizzi'];
    $sezioni_stud = $studente['sezione'];
    $comune_domicilio = $studente['descrizione_domicilio'];
    $indirizzo_domicilio = $studente['indirizzo_domicilio'];
    $comune_residenza = $studente['descrizione_residenza'];
    $indirizzo_residenza = $studente['indirizzo'];
    $telefono = $studente['telefono'];
    $sesso = $studente['sesso'];
    $cittadinanza = $studente['descrizione_cittadinanza'];
    $padre = $studente['cognome_padre'] . ' ' . $studente['nome_padre'];
    $madre = $studente['cognome_madre'] . ' ' . $studente['nome_madre'];
    $tutore = $studente['cognome_tutore'] . ' ' . $studente['nome_tutore'];
    $motivazioni_crediti = "3° -  " . $studente["motivi_crediti_terza"] . "\n" .
            "4° -  " . $studente["motivi_crediti_quarta"] . "\n" .
            "5° -  " . $studente["motivi_crediti_quinta"] . " " . $studente["motivi_crediti_agg"];
    $comune_nascita = estrai_provincia_comune($studente["codice_comune_nascita"]);
    $data_nascita = date("d/m/Y", $studente["data_nascita"]);
    $nazione_nascita = estrai_nazione($studente["stato_nascita"]);
    if ($comune_nascita[0] == "ERROR") {
        $luogo_nascita = $studente["citta_nascita_straniera"] . " (" . $nazione_nascita["descrizione"] . ")";
    } else {
        $luogo_nascita = $comune_nascita["descrizione"] . " (" . $comune_nascita["codice"] . ")";
    }

    $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');

    $crediti_terza = traduci_crediti_totali($studente["crediti_terza"]);
    $crediti_quarta = traduci_crediti_totali($studente["crediti_quarta"]);
    $crediti_quinta = traduci_crediti_totali($studente["crediti_quinta"]);
    $int_crediti_terza = traduci_crediti_totali($studente["crediti_reintegrati_terza"]);
    $int_crediti_quarta = traduci_crediti_totali($studente["crediti_reintegrati_quarta"]);
    $crediti_agg = traduci_crediti_totali($studente["crediti_finali_agg"]);

    $totale_crediti_triennio = intval($studente["crediti_terza"]) +
            intval($studente["crediti_quarta"]) +
            intval($studente["crediti_quinta"]) +
            intval($studente["crediti_reintegrati_terza"]) +
            intval($studente["crediti_reintegrati_quarta"]);

    $totale_crediti_finali = $totale_crediti_triennio +
            intval($studente["crediti_finali_agg"]);

    $totale_crediti_triennio_lettere = traduci_crediti_totali($totale_crediti_triennio);
    $totale_crediti_finali_lettere = traduci_crediti_totali($totale_crediti_finali);

    $altezza_celle = 6;
    $pdf->SetFillColor(220);

    if ($stampa_prima_pagina == "SI") {
        //{{{ <editor-fold defaultstate="collapsed" desc="PAGINA 1">
        $pdf->AddPage('P');

        //{{{ <editor-fold defaultstate="collapsed" desc="intestazione">
        $pdf->SetFillColor(220);
        $pdf->SetXY(10, 10);

        inserisci_intestazione_pdf($pdf, (int) $studente["id_classe"]);

        $altezza_base = $pdf->GetY();
        $pdf->SetXY(0, $altezza_base + 5);
        $pdf->SetFont('helvetica', 'B', $dimensione_font + 8);
        $pdf->CellFitScale(0, $altezza_celle, "FOGLIO NOTIZIE DELLO STUDENTE", 0, 1, "C");

        if ($intestazione_ridotta == 'NO') {
            $pdf->SetXY(0, $altezza_base + 18);
            $pdf->SetFont('helvetica', '', $dimensione_font);
            $pdf->CellFitScale(0, $altezza_celle, "C.M. n. 400 del 31-12-91     -    C.M. n. 289 del 12-10-92", 0, 1, "C");
            $pdf->SetXY(0, $altezza_base + 24);
            $pdf->SetFont('helvetica', '', $dimensione_font + 4);
            $pdf->CellFitScale(0, $altezza_celle, "Anno Scolastico: " . $anno_scolastico . "  -  Classe: " . $classe_attuale . $sezione_attuale, 0, 1, "C");
            $pdf->SetXY(0, $altezza_base + 29);
            $pdf->SetFont('helvetica', '', $dimensione_font + 4);
            $pdf->CellFitScale(0, $altezza_celle, "Specializzazione: " . $indirizzi_stud, 0, 1, "C");
        }
        else
        {
            $pdf->SetXY(0, $altezza_base + 18);
            $pdf->SetFont('helvetica', '', $dimensione_font);
            $pdf->CellFitScale(0, $altezza_celle, "", 0, 1, "C");
            $pdf->SetXY(0, $altezza_base + 24);
            $pdf->SetFont('helvetica', '', $dimensione_font + 4);
            $pdf->CellFitScale(0, $altezza_celle, "", 0, 1, "C");
            $pdf->SetXY(0, $altezza_base + 29);
            $pdf->SetFont('helvetica', '', $dimensione_font + 4);
            $pdf->CellFitScale(0, $altezza_celle, "", 0, 1, "C");
        }
        //}}} </editor-fold>
        //prima riga dopo l'intestazione
        $h_ridotto = ( $intestazione_ridotta=='NO'? 0 : -20 );
        $pdf->SetXY(10, $altezza_base + 50 + $h_ridotto);
        $pdf->CellFitScale(0, $altezza_celle, "", 1, 1, "C", 1);

        //{{{ <editor-fold defaultstate="collapsed" desc="dati anagrafici studente">
        $pdf->SetFont('helvetica', '', $dimensione_font + 4);
        $pdf->SetXY(10, $altezza_base + 40 + $h_ridotto);
        $pdf->CellFitScale(0, $altezza_celle, "DATI DELLO STUDENTE", 0, 1, "C");

        if ($stampa_info_anagrafiche == 'NO') {
            //nome del candidato
            $pdf->SetXY(10, $altezza_base + 56 + $h_ridotto);
            $pdf->CellFitScale(0, $altezza_celle, "", 1, 1, "C");

            $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
            $pdf->SetXY(10, $altezza_base + 56 + $h_ridotto);
            $pdf->CellFitScale(90, $altezza_celle, $studente["cognome"] . " " . $studente["nome"], 0, 1, "L");
            $pdf->SetXY(100, $altezza_base + 56 + $h_ridotto);

            if ($matricola_studente == 'SI') {
                $pdf->CellFitScale(0, $altezza_celle, "MATRICOLA: " . $studente["matricola"], 0, 1, "L");
            }

            //data di nascita e luogo
            $pdf->SetXY(10, $altezza_base + 62 + $h_ridotto);
            $pdf->CellFitScale(90, $altezza_celle, "", 1, 1, "C");
            $pdf->SetXY(100, $altezza_base + 62 + $h_ridotto);
            $pdf->CellFitScale(0, $altezza_celle, "", 1, 1, "C");

            $pdf->SetFont('helvetica', '', $dimensione_font + 3);
            $pdf->SetXY(10, $altezza_base + 62 + $h_ridotto);
            $pdf->CellFitScale(90, $altezza_celle, " nato a " . $luogo_nascita, 0, 1, "L");
            $pdf->SetXY(100, $altezza_base + 62 + $h_ridotto);
            $pdf->CellFitScale(0, $altezza_celle, " il " . $data_nascita, 0, 1, "L");
        } else {
            //nome del candidato
            $pdf->SetXY(10, $altezza_base + 56 + $h_ridotto);
            $pdf->CellFitScale(0, $altezza_celle, "", 1, 1, "C");

            $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
            $pdf->SetXY(10, $altezza_base + 56 + $h_ridotto);
            $pdf->CellFitScale(90, $altezza_celle, $studente["cognome"] . " " . $studente["nome"], 0, 1, "L");
            $pdf->SetXY(100, $altezza_base + 56 + $h_ridotto);

            if ($matricola_studente == 'SI') {
                $pdf->CellFitScale(0, $altezza_celle, "MATRICOLA: " . $studente["matricola"], 0, 1, "L");
            }

            //data di nascita e luogo
            $pdf->SetXY(10, $altezza_base + 62 + $h_ridotto);
            $pdf->CellFitScale(90, $altezza_celle, "", 1, 1, "C");
            $pdf->SetXY(100, $altezza_base + 62 + $h_ridotto);
            $pdf->CellFitScale(0, $altezza_celle, "", 1, 1, "C");

            $pdf->SetFont('helvetica', '', $dimensione_font + 3);
            $pdf->SetXY(10, $altezza_base + 62 + $h_ridotto);
            $pdf->CellFitScale(90, $altezza_celle, " nato a " . $luogo_nascita, 0, 1, "L");
            $pdf->SetXY(100, $altezza_base + 62 + $h_ridotto);
            $pdf->CellFitScale(0, $altezza_celle, " il " . $data_nascita, 0, 1, "L");
            //info aggiuntive
            if ($comune_domicilio == '' && $comune_residenza != ''){
                $pdf->CellFitScale(90, $altezza_celle, "Residenza:  " . $indirizzo_residenza, 1, 0, "L");
                $pdf->CellFitScale(100, $altezza_celle, "Comune:  " . $comune_residenza . " (" . $studente['provincia_residenza_da_comune'] . ")", 1, 1, "L");
            } else {
                $pdf->CellFitScale(90, $altezza_celle, "Domicilio:  " . $indirizzo_domicilio, 1, 0, "L");
                $pdf->CellFitScale(100, $altezza_celle, "Comune:  " . $comune_domicilio . " (" . $studente['provincia_domicilio_da_comune'] . ")", 1, 1, "L");
            }

            $pdf->CellFitScale(90, $altezza_celle, "Telefono:  " . $telefono, 1, 0, "L");
            $pdf->CellFitScale(100, $altezza_celle, "Distretto scol. del domicilio:  ", 1, 1, "L");

            $pdf->CellFitScale(50, $altezza_celle, "Sesso:  " . $sesso, 1, 0, "L");
            $pdf->CellFitScale(70, $altezza_celle, "Materna anni:  ", 1, 0, "L");
            $pdf->CellFitScale(70, $altezza_celle, "Cittadinanza: " . $cittadinanza, 1, 1, "L");

            $pdf->CellFitScale(190, $altezza_celle, "Cognome e nome del padre: " . $padre, 1, 1, "L");
            $pdf->CellFitScale(190, $altezza_celle, "Cognome e nome della madre: " . $madre, 1, 1, "L");
            $pdf->CellFitScale(190, $altezza_celle, "Cognome e nome di chi ne fa le veci: " . $tutore, 1, 1, "L");
            $altezza_base = $altezza_base + 30;
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="curriculum">
        //intestazione carriera scolastica
        $pdf->SetXY(10, $altezza_base + 80 + $h_ridotto);
        $pdf->CellFitScale(0, $altezza_celle + 5, "", 1, 1, "C", 1);
        $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
        $pdf->SetXY(10, $altezza_base + 80 + $h_ridotto);
        $pdf->CellFitScale(0, $altezza_celle + 5, "CARRIERA SCOLASTICA", 0, 1, "C");
        //testo carriera scolastica

        if ($stampa_curriculum_compatto == 'SI') {
            $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
            $pdf->SetXY(10, $altezza_base + 90 + $h_ridotto);
            $pdf->CellFitScale(20, $altezza_celle, "A.S.", 1, 1, "C", 1);
            $pdf->SetXY(30, $altezza_base + 90 + $h_ridotto);
            $pdf->CellFitScale(80, $altezza_celle, "CLASSE FREQUENTATA", 1, 1, "C", 1);
            $pdf->SetXY(110, $altezza_base + 90 + $h_ridotto);
            $pdf->CellFitScale(40, $altezza_celle, "ESITO", 1, 1, "C", 1);
            $pdf->SetXY(150, $altezza_base + 90 + $h_ridotto);
            $pdf->CellFitScale(0, $altezza_celle, "NOTE", 1, 1, "C", 1);

            $numero_righe_curr = count($dati_curriculum);
            for ($cont_righe = 0; $cont_righe < $numero_righe_curr; $cont_righe++) {
                $anno_scolastico = $dati_curriculum[$cont_righe]['anno_scolastico'];
                $esito = decode($dati_curriculum[$cont_righe]['esito']);
                //{{{ <editor-fold defaultstate="collapsed" desc="curriculum">
                switch ($dati_curriculum[$cont_righe]['tipo_scuola']) {
                    case "scuola_media":
                        $classe_frequentata = "Scuola media: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                        break;
                    case "scuola_superiore":
                        if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                            $classe_frequentata = "Istituto: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                        } else {
                            $classe_frequentata = "Classe: " . $dati_curriculum[$cont_righe]['classe'] .
                                    " " .
                                    "Istituto: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                        }
                        break;
                    case "classe_interna":
                        if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                            $classe_frequentata = $dati_curriculum[$cont_righe]['sezione'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['indirizzo'];
                        } else {
                            $classe_frequentata = $dati_curriculum[$cont_righe]['classe'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['sezione'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['indirizzo'];
                        }
                        break;
                    case "altro":
                        $classe_frequentata = $dati_curriculum[$cont_righe]['altro'];
                        break;
                    default:
                        if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                            $classe_frequentata = $dati_curriculum[$cont_righe]['altro'];
                        } else {
                            if (strlen($dati_curriculum[$cont_righe]['altro']) > 0) {
                                $classe_frequentata = $dati_curriculum[$cont_righe]['altro'] . " ";
                            } else {
                                $classe_frequentata = "";
                            }

                            $classe_frequentata .= $dati_curriculum[$cont_righe]['classe'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['sezione'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['indirizzo'];
                        }
                        break;
                }
                //}}} </editor-fold>
                if (($studente['codice_meccanografico'] != $dati_curriculum[$cont_righe]['id_scuola']) && ($studente['codice_meccanografico_secondario'] != $dati_curriculum[$cont_righe]['id_scuola'])) {
                    $classe_frequentata = $dati_curriculum[$cont_righe]['classe_desc'] . ' ' . $dati_curriculum[$cont_righe]['nome_scuola'];
                    $classe_frequentata = $dati_curriculum[$cont_righe]['classe_tradotta'] . ' ' . $dati_curriculum[$cont_righe]['descrizione'];
                } else {
                    $classe_frequentata = $dati_curriculum[$cont_righe]['classe_tradotta'] . ' ' . $dati_curriculum[$cont_righe]['descrizione'];
                }
                $pdf->SetFont('helvetica', '', $dimensione_font + 2);
//                $pdf->SetXY(10, $altezza_base + 96 + ($altezza_celle * $cont_righe));
                $pdf->CellFitScale(20, $altezza_celle, $anno_scolastico, 1, 0, "C", 0);
//                $pdf->SetXY(30, $altezza_base + 96 + ($altezza_celle * $cont_righe));
                $pdf->CellFitScale(80, $altezza_celle, $classe_frequentata, 1, 0, "L", 0);
//                $pdf->SetXY(110, $altezza_base + 96 + ($altezza_celle * $cont_righe));
                $pdf->CellFitScale(40, $altezza_celle, $esito, 1, 0, "C", 0);
//                $pdf->SetXY(150, $altezza_base + 96 + ($altezza_celle * $cont_righe));
                $pdf->CellFitScale(0, $altezza_celle, "", 1, 1, "C", 0);
            }

            $y_corrente = $pdf->GetY() + 5;
        } else {
            $pdf->SetFont('helvetica', 'B', $dimensione_font);
            $pdf->CellFitScale(10, $altezza_celle, "A.S.", 1, 0, "C", 1);
            $pdf->CellFitScale(30, $altezza_celle, "CLASSE FREQUENTATA", 1, 0, "C", 1);
            $pdf->CellFitScale(40, $altezza_celle, "REGOLARITA' RISPETTO ALL'ETA' SCOLARE", 1, 0, "C", 1);
            $pdf->CellFitScale(20, $altezza_celle, "RIPETENTE", 1, 0, "C", 1);
            $pdf->CellFitScale(10, $altezza_celle, "ISC PART.", 1, 0, "C", 1);
            $pdf->CellFitScale(40, $altezza_celle, "INTERRUZIONE DI FREQUENZA", 1, 0, "C", 1);
            $pdf->CellFitScale(40, $altezza_celle, "ESITO", 1, 1, "C", 1);

            $numero_righe_curr = count($dati_curriculum);
            for ($cont_righe = 0; $cont_righe < $numero_righe_curr; $cont_righe++) {
                $anno_scolastico = $dati_curriculum[$cont_righe]['anno_scolastico'];
                $esito = decode($dati_curriculum[$cont_righe]['esito']);
                //{{{ <editor-fold defaultstate="collapsed" desc="curriculum">
                switch ($dati_curriculum[$cont_righe]['tipo_scuola']) {
                    case "scuola_media":
                        $classe_frequentata = "Scuola media: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                        break;
                    case "scuola_superiore":
                        if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                            $classe_frequentata = "Istituto: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                        } else {
                            $classe_frequentata = "Classe: " . $dati_curriculum[$cont_righe]['classe'] .
                                    " " .
                                    "Istituto: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                        }
                        break;
                    case "classe_interna":
                        if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                            $classe_frequentata = $dati_curriculum[$cont_righe]['sezione'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['indirizzo'];
                        } else {
                            $classe_frequentata = $dati_curriculum[$cont_righe]['classe'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['sezione'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['indirizzo'];
                        }
                        break;
                    case "altro":
                        $classe_frequentata = $dati_curriculum[$cont_righe]['altro'];
                        break;
                    default:
                        if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                            $classe_frequentata = $dati_curriculum[$cont_righe]['altro'];
                        } else {
                            if (strlen($dati_curriculum[$cont_righe]['altro']) > 0) {
                                $classe_frequentata = $dati_curriculum[$cont_righe]['altro'] . " ";
                            } else {
                                $classe_frequentata = "";
                            }

                            $classe_frequentata .= $dati_curriculum[$cont_righe]['classe'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['sezione'] .
                                    " " .
                                    $dati_curriculum[$cont_righe]['indirizzo'];
                        }
                        break;
                }
                //}}} </editor-fold>
                $stampa_ripetente = '';
                $esito_compatto = '';
                switch ($esito) {
                    case 'Ammesso alla classe successiva':
                    case 'Ammesso al successivo grado dell\'istruzione obbligatoria':
                    case 'Qualificato':
                    case 'Licenziato':
                    case 'Diplomato':
                        $esito_compatto = 'am';
                        break;
                    case 'Non ammesso alla classe successiva':
                    case 'Non ammesso al successivo grado dell\'istruzione obbligatoria':
                    case 'Non ammesso esame di stato':
                    case 'Non qualificato':
                    case 'Non diplomato':
                        $esito_compatto = 'n/a';
                        $ripetente = explode('/',$anno_scolastico)[1] . "/" . (explode('/',$anno_scolastico)[1]  +1);
                        break;
                    case 'Iscritto':
                        $esito_compatto = 'isc';
                        break;
                    case 'Ammesso esame di stato':
                        $esito_compatto = 'am_e';
                        break;
                    case 'Trasferito':
                        $esito_compatto = 'tra';
                        break;
                    case 'Ritirato':
                        $esito_compatto = 'rit';
                        break;
                    default:
                        $esito_compatto = '';
                        break;
                }
                if ($studente['codice_meccanografico'] != $dati_curriculum[$cont_righe]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $dati_curriculum[$cont_righe]['id_scuola']) {
                    $classe_frequentata = $dati_curriculum[$cont_righe]['classe_desc'] . ' ' . $dati_curriculum[$cont_righe]['nome_scuola'];
                    $classe_frequentata = $dati_curriculum[$cont_righe]['classe_tradotta'] . $dati_curriculum[$cont_righe]['descrizione'] . ' ' . $dati_curriculum[$cont_righe]['nome_scuola'];
                } else {
                    $classe_frequentata = $dati_curriculum[$cont_righe]['classe_tradotta'] . ' ' . $dati_curriculum[$cont_righe]['descrizione'];
                    $classe_frequentata = $dati_curriculum[$cont_righe]['classe_desc'] . ' ' . $dati_curriculum[$cont_righe]['nome_scuola'];
                    $classe_frequentata = $dati_curriculum[$cont_righe]['classe_tradotta'] . $dati_curriculum[$cont_righe]['descrizione'] . ' ' . $dati_curriculum[$cont_righe]['nome_scuola'];
                }
                $pdf->SetFont('helvetica', '', $dimensione_font);
                $pdf->CellFitScale(10, $altezza_celle, $anno_scolastico, 1, 0, "C");
                $pdf->CellFitScale(30, $altezza_celle, $classe_frequentata, 1, 0, "C");
                $pdf->CellFitScale(40, $altezza_celle, "[ ] ANT   [ ] REG   [ ] RIT", 1, 0, "C");
                if ($ripetente == $anno_scolastico) {
                    $stampa_ripetente = 'SI';
                }
                $pdf->CellFitScale(20, $altezza_celle, $stampa_ripetente, 1, 0, "C");
                $pdf->CellFitScale(10, $altezza_celle, "", 1, 0, "C");
                $pdf->CellFitScale(40, $altezza_celle, "", 1, 0, "C");
                $pdf->CellFitScale(40, $altezza_celle, $esito, 1, 1, "C");
            }

            $pdf->SetXY(10, $pdf->GetY() + 5);

            $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
            $pdf->CellFitScale(0, $altezza_celle, "DA COMPILARE ALL'ATTO DEI TRASFERIMENTI E DEI PASSAGGI DA UN ORDINE E GRADO DI SCUOLA ALL'ALTRO", 1, 1, "C", 1);
            $pdf->SetFont('helvetica', '', $dimensione_font + 2);
            $pdf->CellFitScale(0, $altezza_celle, "Dall'a.s._____/_____ all'a.s._____/_____ Denom. della scuola ___________________________________________________________", 'LR', 1, "L");
            $pdf->SetFont('helvetica', 'I', $dimensione_font + 2);
            $pdf->CellFitScale(0, $altezza_celle, "__________________________________________________________ Firma del Dirigente Scolastico ______________________________", 'LRB', 1, "L");
            $pdf->SetFont('helvetica', '', $dimensione_font + 2);
            $pdf->CellFitScale(0, $altezza_celle, "Dall'a.s._____/_____ all'a.s._____/_____ Denom. della scuola ___________________________________________________________", 'LR', 1, "L");
            $pdf->SetFont('helvetica', 'I', $dimensione_font + 2);
            $pdf->CellFitScale(0, $altezza_celle, "__________________________________________________________ Firma del Dirigente Scolastico ______________________________", 'LRB', 1, "L");
            $pdf->SetFont('helvetica', '', $dimensione_font + 2);
            $pdf->CellFitScale(0, $altezza_celle, "Dall'a.s._____/_____ all'a.s._____/_____ Denom. della scuola ___________________________________________________________", 'LR', 1, "L");
            $pdf->SetFont('helvetica', 'I', $dimensione_font + 2);
            $pdf->CellFitScale(0, $altezza_celle, "__________________________________________________________ Firma del Dirigente Scolastico ______________________________", 'LRB', 1, "L");

            $y_corrente = $pdf->GetY() + 5;
        }
        //}}} </editor-fold>

        if ($note_credito == 'SI')
        {
            if ($dati_classe[0]['tipo_indirizzo'] != 4 && $dati_classe[0]['tipo_indirizzo'] != 6 && $dati_classe[0]['tipo_indirizzo'] != 7 && $dati_classe[0]['tipo_indirizzo'] != 8) {
                if ($pdf->GetY() + 48 > 250) {
                    $pdf->AddPage('P');
                    //{{{ <editor-fold defaultstate="collapsed" desc="tabella riepilogo triennale e totale crediti">
                    $y_corrente = $pdf->GetY() + 5;

                    $pdf->SetXY(10, $y_corrente);
                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
                    $pdf->CellFitScale(0, $altezza_celle + 5, "RIEPILOGO TRIENNALE E TOTALE CREDITO SCOLASTICO ATTRIBUITO", 1, 1, "C", 1);
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);

                    $y_corrente += $altezza_celle + 5;

                    //3° anno
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->SetX(10);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->Cell(22, $altezza_celle + 8, 'III ANNO', 0, 1, "C");
                    //int 3° anno
                    $pdf->SetXY(32, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 2);
                    $pdf->SetXY(32, $y_corrente);
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "Integrazione", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 2);
                    $pdf->SetXY(32, ($y_corrente + (($altezza_celle + 8) / 2)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "III ANNO", 0, 1, "C");
                    //4° anno
                    $pdf->SetXY(54, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(54, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "IV ANNO", 0, 1, "C");
                    //int 4° anno
                    $pdf->SetXY(76, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 2);
                    $pdf->SetXY(76, $y_corrente);
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "Integrazione", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 2);
                    $pdf->SetXY(76, ($y_corrente + (($altezza_celle + 8) / 2)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "IV ANNO", 0, 1, "C");
                    //5° anno
                    $pdf->SetXY(98, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(98, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "V ANNO", 0, 1, "C");
                    //totale triennio
                    $pdf->SetXY(120, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(120, $y_corrente);
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "TOTALE", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(120, ($y_corrente + (($altezza_celle + 8) / 2)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "TRIENNIO", 0, 1, "C");
                    //ulteriore integraz per triennio
                    $pdf->SetXY(142, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                    $pdf->SetXY(142, $y_corrente);
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 4, "ULTERIORE", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                    $pdf->SetXY(142, ($y_corrente + (($altezza_celle + 8) / 4)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 4, "INTEGRAZIONE", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                    $pdf->SetXY(142, ($y_corrente + ((($altezza_celle + 8) / 4) * 2)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 4, "MOTIVATA PER", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                    $pdf->SetXY(142, ($y_corrente + ((($altezza_celle + 8) / 4) * 3)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 4, "IL TRIENNIO", 0, 1, "C");
                    //5° anno
                    $pdf->SetXY(164, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(164, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle + 8, "TOTALE", 0, 1, "C");

                    $y_corrente += $altezza_celle + 8;
                    //riga "IN LETTERE"
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(36, $altezza_celle - 2, "IN LETTERE", 1, 1, 'C');

                    $y_corrente += ($altezza_celle - 2);
                    //valori crediti IN LETTERE
                    $pdf->CellFitScale(22, $altezza_celle, $crediti_terza, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $int_crediti_terza, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $crediti_quarta, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $int_crediti_quarta, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $crediti_quinta, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $totale_crediti_triennio_lettere, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $crediti_agg, 1, 0, 'C');
                    $pdf->CellFitScale(36, $altezza_celle, $totale_crediti_finali_lettere, 1, 1, 'C');
                    //}}} </editor-fold>

                    $y_corrente += $altezza_celle + 2;

                    //{{{ <editor-fold defaultstate="collapsed" desc="motivazione crediti">
                    //intestazione Motivazione crediti
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle + 5, "", 1, 1, "C", 1);
                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle + 5, "CREDITI FORMATIVI DOCUMENTATI", 0, 1, "C");
                    $y_corrente += $altezza_celle + 7;

                    $tot_righe = 14;
                    $righe_motivazioni = $tot_righe - $numero_righe_curr;

                    if ($righe_motivazioni > 9) {
                        $righe_motivazioni = 9;
                    }

                    //testo motivazione crediti
                    if (strlen(trim($motivazioni_crediti)) > 0) {
                        $pdf->SetXY(10, $y_corrente);
                        $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                        $pdf->MultiCell(0, $altezza_celle, $motivazioni_crediti, 1, 1, "L");
                        $pdf->CellFitScale(0, 2, "", 0, 1, "L");
                    } else {
                        $pdf->SetXY(10, $y_corrente);
                        //$pdf->CellFitScale(0,$altezza_celle + 6 * $righe_motivazioni, "",1,1,"L");

                        for ($num_righe = 1; $num_righe < 5; $num_righe++) {
                            $pdf->SetFont('helvetica', '', $dimensione_font);
                            $pdf->SetXY(10, ($y_corrente - 6) + (($altezza_celle + 35) / 5) * $num_righe);
                            $pdf->CellFitScale(0, ($altezza_celle + 30) / 5, "____________________________________________________________________________________________________________________________________________________________", 0, 1, "L");
                        }
                    }
                    //}}} </editor-fold>
                } else {
                    //{{{ <editor-fold defaultstate="collapsed" desc="tabella riepilogo triennale e totale crediti">
                    //intestazione crediti
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
                    $pdf->CellFitScale(0, $altezza_celle + 5, "RIEPILOGO TRIENNALE E TOTALE CREDITO SCOLASTICO ATTRIBUITO", 1, 1, "C", 1);
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);

                    $y_corrente += $altezza_celle + 5;

                    //3° anno
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->SetX(10);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->Cell(22, $altezza_celle + 8, 'III ANNO', 0, 1, "C");
                    //int 3° anno
                    $pdf->SetXY(32, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 2);
                    $pdf->SetXY(32, $y_corrente);
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "Integrazione", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 2);
                    $pdf->SetXY(32, ($y_corrente + (($altezza_celle + 8) / 2)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "III ANNO", 0, 1, "C");
                    //4° anno
                    $pdf->SetXY(54, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(54, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "IV ANNO", 0, 1, "C");
                    //int 4° anno
                    $pdf->SetXY(76, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 2);
                    $pdf->SetXY(76, $y_corrente);
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "Integrazione", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 2);
                    $pdf->SetXY(76, ($y_corrente + (($altezza_celle + 8) / 2)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "IV ANNO", 0, 1, "C");
                    //5° anno
                    $pdf->SetXY(98, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(98, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "V ANNO", 0, 1, "C");
                    //totale triennio
                    $pdf->SetXY(120, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(120, $y_corrente);
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "TOTALE", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(120, ($y_corrente + (($altezza_celle + 8) / 2)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 2, "TRIENNIO", 0, 1, "C");
                    //ulteriore integraz per triennio
                    $pdf->SetXY(142, $y_corrente);
                    $pdf->CellFitScale(22, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                    $pdf->SetXY(142, $y_corrente);
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 4, "ULTERIORE", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                    $pdf->SetXY(142, ($y_corrente + (($altezza_celle + 8) / 4)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 4, "INTEGRAZIONE", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                    $pdf->SetXY(142, ($y_corrente + ((($altezza_celle + 8) / 4) * 2)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 4, "MOTIVATA PER", 0, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                    $pdf->SetXY(142, ($y_corrente + ((($altezza_celle + 8) / 4) * 3)));
                    $pdf->CellFitScale(22, ($altezza_celle + 8) / 4, "IL TRIENNIO", 0, 1, "C");
                    //5° anno
                    $pdf->SetXY(164, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle + 8, "", 1, 1, "C");
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(164, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle + 8, "TOTALE", 0, 1, "C");

                    $y_corrente += $altezza_celle + 8;
                    //riga "IN LETTERE"
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle - 2, "IN LETTERE", 1, 0, 'C');
                    $pdf->CellFitScale(36, $altezza_celle - 2, "IN LETTERE", 1, 1, 'C');

                    $y_corrente += ($altezza_celle - 2);
                    //valori crediti IN LETTERE
                    $pdf->CellFitScale(22, $altezza_celle, $crediti_terza, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $int_crediti_terza, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $crediti_quarta, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $int_crediti_quarta, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $crediti_quinta, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $totale_crediti_triennio_lettere, 1, 0, 'C');
                    $pdf->CellFitScale(22, $altezza_celle, $crediti_agg, 1, 0, 'C');
                    $pdf->CellFitScale(36, $altezza_celle, $totale_crediti_finali_lettere, 1, 1, 'C');
                    //}}} </editor-fold>

                    $y_corrente += $altezza_celle + 2;

                    //{{{ <editor-fold defaultstate="collapsed" desc="motivazione crediti">
                    //intestazione Motivazione crediti
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle + 5, "", 1, 1, "C", 1);
                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle + 5, "CREDITI FORMATIVI DOCUMENTATI", 0, 1, "C");
                    $y_corrente += $altezza_celle + 7;

                    $tot_righe = 14;
                    $righe_motivazioni = $tot_righe - $numero_righe_curr;

                    if ($righe_motivazioni > 9) {
                        $righe_motivazioni = 9;
                    }

                    //testo motivazione crediti
                    if (strlen(trim($motivazioni_crediti)) > 0) {
                        $pdf->SetXY(10, $y_corrente);
                        $pdf->SetFont('helvetica', '', $dimensione_font + 1);
                        $pdf->MultiCell(0, $altezza_celle, $motivazioni_crediti, 1, 1, "L");
                        $pdf->CellFitScale(0, 2, "", 0, 1, "L");
                    } else {
                        $pdf->SetXY(10, $y_corrente);
                        //$pdf->CellFitScale(0,$altezza_celle + 6 * $righe_motivazioni, "",1,1,"L");

                        for ($num_righe = 1; $num_righe < 5; $num_righe++) {
                            $pdf->SetFont('helvetica', '', $dimensione_font);
                            $pdf->SetXY(10, ($y_corrente - 6) + (($altezza_celle + 35) / 5) * $num_righe);
                            $pdf->CellFitScale(0, ($altezza_celle + 30) / 5, "____________________________________________________________________________________________________________________________________________________________", 0, 1, "L");
                        }
                    }
                    //}}} </editor-fold>
                }
            }
        }
        //}}} </editor-fold>
    }

    //{{{ <editor-fold defaultstate="collapsed" desc="PAGIN 2 - Riepilogo Voti e Giudizio Ammissione">
    if($stampa_giudizio_ammissione == 'SI' || $stampa_riepilogo_pagelle != 'NO')
    {
        $pdf->AddPage('P');
        $y_corrente = 10;
        $x_corrente = 10;
        $pdf->SetFont('helvetica', '', $dimensione_font + 4);
        $pdf->SetXY(10, $y_corrente);
        $pdf->CellFitScale(0, $altezza_celle, "DATI DELLO STUDENTE", 1, 0, "C", 1);

        $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
        $y_corrente += $altezza_celle;
        $pdf->SetXY(10, $y_corrente);
        $pdf->CellFitScale(0, $altezza_celle, '', 1, 0, "L");
        $pdf->SetXY(10, $y_corrente);
        $pdf->CellFitScale(90, $altezza_celle, $studente["cognome"] . " " . $studente["nome"], 0, 0, "L");
        $pdf->SetXY(100, $y_corrente);

        if ($matricola_studente == 'SI') {
            $pdf->CellFitScale(0, $altezza_celle, "MATRICOLA: " . $studente["matricola"], 0, 0, "L");
        }

        $y_corrente += $altezza_celle + 2;
        $pdf->SetXY(10, $y_corrente);


        if($stampa_giudizio_ammissione == 'SI')
        {
            $pdf->SetXY(10, $y_corrente);
            $pdf->CellFitScale(0, $altezza_celle + 5, '', 1, 1, 'C', 1);
            $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
            $pdf->SetXY(10, $y_corrente);
            $pdf->CellFitScale(0, $altezza_celle + 5, "GIUDIZIO DI AMMISSIONE ALL'ESAME", 0, 1, 'C');
            $y_corrente += $altezza_celle + 15;
            $pdf->SetFont('helvetica', '', $dimensione_font);
            if($studente['ammesso_esame_quinta'] == 'SI')
            {
                $testo_ammissione = ' ammettere ';
            }
            else
            {
                $testo_ammissione = ' non ammettere ';
            }

            $y_corrente = $pdf->GetY() + 2;
            $pdf->SetXY(10, $y_corrente);
            $pdf->SetFont('helvetica', 'B', $dimensione_font - 2);
            $pdf->MultiCell(0, $altezza_celle - 2, "VALUTAZIONE COMPLESSIVA DELLO STUDENTE PER L'AMMISSIONE ALL'ESAME DI STATO: " . chr(13) . chr(10) . $studente['giudizio_ammissione_quinta'], 0, 'L');

            $y_corrente += $altezza_celle + 5;
            $pdf->SetXY(10, $y_corrente);

        }

        if($stampa_riepilogo_pagelle == 'SI')
        {
            //{{{ <editor-fold defaultstate="collapsed" desc="STAMPA VOTI NEGLI ANNI">
            //intestazione carriera scolastica
            $y_corrente = $pdf->GetY() + 5;

            $pdf->SetXY(10, $y_corrente);
            $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
            $pdf->CellFitScale(0, $altezza_celle + 5, "RIEPILOGO DEI VOTI PER ANNI DI CORSO", 1, 1, "C", 1);
            $pdf->SetFont('helvetica', '', $dimensione_font + 4);

            $y_corrente += $altezza_celle + 5;
            $pdf->SetFont('helvetica', 'B', $dimensione_font);
            $pdf->CellFitScale(10, $altezza_celle*2, 'N.', 1, 0, 'C', 1);
            $pdf->CellFitScale(80, $altezza_celle*2, 'DISCIPLINA', 1, 0, 'C', 1);
            $pdf->CellFitScale(20, $altezza_celle*2, 'I', 1, 0, 'C', 1);
            $pdf->CellFitScale(20, $altezza_celle*2, 'II', 1, 0, 'C', 1);
            $pdf->CellFitScale(20, $altezza_celle*2, 'III', 1, 0, 'C', 1);
            $pdf->CellFitScale(20, $altezza_celle*2, 'IV', 1, 0, 'C', 1);
            $pdf->CellFitScale(20, $altezza_celle*2, 'V', 1, 1, 'C', 1);

            $dati_curriculum_per_voti = estrai_curriculum_studente((int) $id_stud);

            $array_db_anno = array();
            $cont_array_db_anno = 0;
            for($cont_righe=0;$cont_righe<count($dati_curriculum_per_voti);$cont_righe++)
            {
                $anno_scolastico = $dati_curriculum_per_voti[$cont_righe]['anno_scolastico'];
                $esito = $dati_curriculum_per_voti[$cont_righe]['esito_tradotto'];
                if((stripos($esito, 'ammesso') !== false) and (stripos($esito, 'non') === false))
                {
                    $anno_scolastico_tmp = explode('/', $anno_scolastico);
                    $db_anno = 'mastercom_'.$anno_scolastico_tmp[0].'_'.$anno_scolastico_tmp[1];
                    $array_db_anno[$cont_array_db_anno]['anno'] = $db_anno;
                    if($dati_curriculum_per_voti[$cont_righe]['tipo_scuola'] == 'scuola_media')
                    {
                        $array_db_anno[$cont_array_db_anno]['periodo'] = '29';
                    }
                    else
                    {
                        $array_db_anno[$cont_array_db_anno]['periodo'] = '9';
                    }
                    $cont_array_db_anno++;
                }
            }

            $elenco_pagelle = estrai_pagelline_studente_periodo_anno((int) $studente['id_studente'], $studente['codice_fiscale'], $array_db_anno, (int) $current_user);

            $pdf->SetFont('helvetica', '', $dimensione_font);
            $cont_materia = 0;
            foreach($elenco_pagelle as $id_materia => $dati_materia)
            {
                if(is_numeric($id_materia))
                {
                    $cont_materia++;
                    $pdf->CellFitScale(10, $altezza_celle, $cont_materia, 1, 0, 'C');
                    $pdf->CellFitScale(80, $altezza_celle, $dati_materia['descrizione'], 1, 0, 'L');
                    $pdf->CellFitScale(20, $altezza_celle, $dati_materia['1'], 1, 0, 'C');
                    $pdf->CellFitScale(20, $altezza_celle, $dati_materia['2'], 1, 0, 'C');
                    $pdf->CellFitScale(20, $altezza_celle, $dati_materia['3'], 1, 0, 'C');
                    $pdf->CellFitScale(20, $altezza_celle, $dati_materia['4'], 1, 0, 'C');
                    $pdf->CellFitScale(20, $altezza_celle, $dati_materia['5'], 1, 1, 'C');
                }
                else
                {
                    $media['descrizione'] = $dati_materia['descrizione'];
                    $media['1'] = $dati_materia['1'];
                    $media['2'] = $dati_materia['2'];
                    $media['3'] = $dati_materia['3'];
                    $media['4'] = $dati_materia['4'];
                    $media['5'] = $dati_materia['5'];
                }
            }
            $pdf->SetFont('helvetica', 'B', $dimensione_font);
            $pdf->CellFitScale(90, $altezza_celle, $media['descrizione'], 1, 0, 'L');
            $pdf->CellFitScale(20, $altezza_celle, $media['1'], 1, 0, 'C');
            $pdf->CellFitScale(20, $altezza_celle, $media['2'], 1, 0, 'C');
            $pdf->CellFitScale(20, $altezza_celle, $media['3'], 1, 0, 'C');
            $pdf->CellFitScale(20, $altezza_celle, $media['4'], 1, 0, 'C');
            $pdf->CellFitScale(20, $altezza_celle, $media['5'], 1, 1, 'C');
            $pdf->SetFont('helvetica', '', $dimensione_font);
            //}}} </editor-fold>
        } else if ($stampa_riepilogo_pagelle == 'MEDIE') {

            //{{{ <editor-fold defaultstate="collapsed" desc="STAMPA MEDIE VOTO NEGLI ANNI">
            //intestazione carriera scolastica
            $y_corrente = $pdf->GetY() + 5;

            $pdf->SetXY(10, $y_corrente);
            $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
            $pdf->CellFitScale(0, $altezza_celle + 5, "RIEPILOGO DELLA MEDIA FINALE PER ANNI DI CORSO", 1, 1, "C", 1);
            $pdf->SetFont('helvetica', '', $dimensione_font + 4);

            $y_corrente += $altezza_celle + 5;
            $pdf->SetFont('helvetica', 'B', $dimensione_font);
            $pdf->CellFitScale(38, $altezza_celle*2, 'I', 1, 0, 'C', 1);
            $pdf->CellFitScale(38, $altezza_celle*2, 'II', 1, 0, 'C', 1);
            $pdf->CellFitScale(38, $altezza_celle*2, 'III', 1, 0, 'C', 1);
            $pdf->CellFitScale(38, $altezza_celle*2, 'IV', 1, 0, 'C', 1);
            $pdf->CellFitScale(38, $altezza_celle*2, 'V', 1, 1, 'C', 1);

            $dati_curriculum_per_voti = estrai_curriculum_studente((int) $id_stud, true, true, true, true);

            $riepilogo_medie = [];
            foreach ($dati_curriculum_per_voti as $riga_curriculum)
            {
                if ($riga_curriculum['esito'] == 'Ammesso alla classe successiva' || $riga_curriculum['esito'] == 'Ammesso alla classe successiva con debito formativo' || $riga_curriculum['esito'] == 'Ammesso alla classe successiva con carenze')
                {
                    $riepilogo_medie[$riga_curriculum['classe_tradotta']] = $riga_curriculum['media'];
                }
            }
            $pdf->SetFont('helvetica', 'B', $dimensione_font);
            $pdf->CellFitScale(38, $altezza_celle, $riepilogo_medie['1'], 1, 0, 'C');
            $pdf->CellFitScale(38, $altezza_celle, $riepilogo_medie['2'], 1, 0, 'C');
            $pdf->CellFitScale(38, $altezza_celle, $riepilogo_medie['3'], 1, 0, 'C');
            $pdf->CellFitScale(38, $altezza_celle, $riepilogo_medie['4'], 1, 0, 'C');
            $pdf->CellFitScale(38, $altezza_celle, $riepilogo_medie['5'], 1, 1, 'C');
        }

    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="PAGINA 3 e OLTRE">
    $periodi_abilitati = [];

    if ($stampa_primo_quadr == 'SI') {
        if ($dati_classe[0]["tipo_indirizzo"] == 4) {
            $periodi_abilitati[] = '27';
        } else {
            $periodi_abilitati[] = '7';
        }
    }

    if ($stampa_secondo_tri == 'SI') {
        if ($dati_classe[0]["tipo_indirizzo"] == 4) {
            $periodi_abilitati[] = '28';
        } else {
            $periodi_abilitati[] = '8';
        }
    }

    if ($stampa_fine_anno == 'SI') {
        if ($dati_classe[0]["tipo_indirizzo"] == 4) {
            $periodi_abilitati[] = '29';
        } else {
            $periodi_abilitati[] = '9';
        }
    }

    $anni_abilitati = [$anno_selezionato];
    $x_corrente = 10;
    $db_key_orig = $db_key;

    foreach ($anni_abilitati as $anno_inizio_corrente) {
        //{{{ <editor-fold defaultstate="collapsed" desc="ciclo per anni">
        $anno_base = $anno_inizio_corrente;
        $anno_base_fine = intval($anno_inizio_corrente) + 1;

        $db_attuale = 'mastercom_' . $anno_base . '_' . $anno_base_fine;
        $as_underscore = $anno_base . '_' . $anno_base_fine;

        if ($db_attuale != $db_key_orig) {
            //{{{ <editor-fold defaultstate="collapsed" desc="definisco $mat_ricerca">
            $mat_ricerca['cognome_nome_data_nascita']['valore'] = 'SI';  // coincidenza su cognome, nome e data nascita
            $mat_ricerca['codice_fiscale']['valore'] = 'SI';    // coincidenza su codice fiscale
            $mat_ricerca['cognome_nome']['valore'] = 'SI';     // coincidenza su cognome e nome
            $mat_ricerca['id_studente_cognome_nome']['valore'] = 'SI';  // coincidenza su id e cognome e nome
            $mat_ricerca['id_studente']['valore'] = 'NO';     // coincidenza su id
            //}}} </editor-fold>
            $ris_ricerca = ricerca_studenti_tra_database($studente, $mat_ricerca, $db_attuale);

            if ($ris_ricerca[$as_underscore]['risultato'] == 'trovato') {
                $dati_stud_storico = $ris_ricerca[$as_underscore]['dati_studente'];
                $id_stud = $dati_stud_storico['id_studente'];
                $db_key = 'mastercom_' . $anno_base . "_" . $anno_base_fine;
            }
        }

        if (${"idcon" . $db_key} > 0) {
            $anno_scolastico_corrente = $anno_base . "/" . $anno_base_fine;

            $dati_classe_corrente = estrai_classi_studente((int) $id_stud);

            $classe = "";
            $sezione = "";

            foreach ($dati_classe_corrente as $classe_corrente) {
                if (!$dirigente || $classe_corrente['ordinamento'] == '0'){
                    $classe = $classe_corrente['classe'];
                    $sezione .= $classe_corrente['sezione'] . "-" . $classe_corrente['codice'] . " ";
                    $dirigente = $classe_corrente['nome_dirigente'];
                }
            }

            $voti_totali = [];
            $media = [];

            //{{{ <editor-fold defaultstate="collapsed" desc="estrazione medie e voti in base a periodi abilitati">
            foreach ($periodi_abilitati as $periodo_corrente) {
                $tipo_visualizzazione_voti_periodo = identifica_periodo_tipo_voto($periodo_corrente, (int) $id_classe);
                $elenco_voti_pagelline = estrai_voti_pagellina_studente_multi_classe((int) $id_classe, $periodo_corrente, (int) $id_stud, $tipo_visualizzazione_voti_periodo);
                $voti_totali[$periodo_corrente] = $elenco_voti_pagelline;

                $cont_voti = 0;
                $totale_voti = 0;

                foreach ($elenco_voti_pagelline as $voto_pagellina) {
                    if ($voto_pagellina["tipo_materia"] != "RELIGIONE") {
                        if ($periodo_corrente != 9) {
                            if ($voto_pagellina['voto_scritto_pagella'] != "" && intval($voto_pagellina['voto_scritto_pagella']) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina['voto_scritto_pagella'];
                                $cont_voti++;
                            }

                            if ($voto_pagellina['voto_orale_pagella'] != "" && intval($voto_pagellina['voto_orale_pagella']) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina['voto_orale_pagella'];
                                $cont_voti++;
                            }

                            if ($voto_pagellina['voto_pratico_pagella'] != "" && intval($voto_pagellina['voto_pratico_pagella']) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina['voto_pratico_pagella'];
                                $cont_voti++;
                            }

                            if ($voto_pagellina['voto_pagellina'] != "" && intval($voto_pagellina['voto_pagellina']) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina['voto_pagellina'];
                                $cont_voti++;
                            }
                        } else {
                            if ($voto_pagellina['voto_pagellina'] != "" && intval($voto_pagellina['voto_pagellina']) != 0) {
                                $totale_voti = $totale_voti + $voto_pagellina['voto_pagellina'];
                                $cont_voti++;
                            }
                        }
                    }
                }

                if ($cont_voti > 0) {
                    $media[$periodo_corrente] = round(($totale_voti / $cont_voti), 2);
                } else {
                    $media[$periodo_corrente] = 0;
                }
            }
            //}}} </editor-fold>

            // se ha senso stampare la terza pagina e le successive
            if ($note_aggiuntive == "SI"
                ||
                $profilo_generale == "SI"
                ||
                $esperienze == "SI"
                ||
                $note_credito == "SI"
                ||
                !empty($voti_totali)
            )
            {
                $pdf->AddPage('P');
                $y_corrente = 10;
                $x_corrente = 10;
                $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                $pdf->SetXY(10, $y_corrente);
                $pdf->CellFitScale(0, $altezza_celle, "DATI DELLO STUDENTE", 1, 0, "C", 1);

                $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
                $y_corrente += $altezza_celle;
                $pdf->SetXY(10, $y_corrente);
                $pdf->CellFitScale(0, $altezza_celle, '', 1, 0, "L");
                $pdf->SetXY(10, $y_corrente);
                $pdf->CellFitScale(90, $altezza_celle, $studente["cognome"] . " " . $studente["nome"], 0, 0, "L");
                $pdf->SetXY(100, $y_corrente);

                if ($matricola_studente == 'SI') {
                    $pdf->CellFitScale(0, $altezza_celle, "MATRICOLA: " . $studente["matricola"], 0, 0, "L");
                }

                $y_corrente += $altezza_celle + 2;

                //intestazione tabella voti
                $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
                $pdf->SetXY(10, $y_corrente);
                $pdf->CellFitScale(0, $altezza_celle + 5, 'ANNO SCOLASTICO ' . $anno_scolastico_corrente, 0, 1, "C");

                $y_corrente += $altezza_celle + 5;

                foreach ($voti_totali as $periodo_corrente => $elenco_voti_pagelline) {
                    $tipo_visualizzazione_voti_periodo = identifica_periodo_tipo_voto($periodo_corrente, (int) $id_classe);

                    switch ($periodo_corrente) {
                        case '7':
                            $intestazione_voti = 'Profitto scolastico I quadrimestre/trimestre';
                            break;
                        case '8':
                            $intestazione_voti = 'Profitto scolastico II trimestre';
                            break;
                        case '9':
                            $intestazione_voti = 'Profitto scolastico finale';
                            break;
                    }

                    if ((((count($elenco_voti_pagelline) + 3) * $altezza_celle) + 7 + $y_corrente) > ($altezza_foglio - 20)) {
                        $pdf->AddPage('P');
                        $y_corrente = 10;
                        $x_corrente = 10;
                        $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                        $pdf->SetXY(10, $y_corrente);
                        $pdf->CellFitScale(0, $altezza_celle, "DATI DELLO STUDENTE", 1, 0, "C", 1);

                        $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
                        $y_corrente += $altezza_celle;
                        $pdf->SetXY(10, $y_corrente);
                        $pdf->CellFitScale(0, $altezza_celle, '', 1, 0, "L");
                        $pdf->SetXY(10, $y_corrente);
                        $pdf->CellFitScale(90, $altezza_celle, $studente["cognome"] . " " . $studente["nome"], 0, 0, "L");
                        $pdf->SetXY(100, $y_corrente);

                        if ($matricola_studente == 'SI') {
                            $pdf->CellFitScale(0, $altezza_celle, "MATRICOLA: " . $studente["matricola"], 0, 0, "L");
                        }

                        $y_corrente += $altezza_celle + 2;
                    }

                    if (count($elenco_voti_pagelline) > 0) {
                        //intestazione tabella voti
                        $pdf->SetXY(10, $y_corrente);
                        $pdf->CellFitScale(0, $altezza_celle + 5, "", 1, 1, "C", 1);
                        $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
                        $pdf->SetXY(10, $y_corrente);
                        $pdf->CellFitScale(0, $altezza_celle + 5, $intestazione_voti, 0, 1, "C");

                        $y_corrente += $altezza_celle + 5 + 2;

                        //testo tabella voti
                        $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
                        $pdf->SetXY(10, $y_corrente);
                        $pdf->CellFitScale(70, $altezza_celle, "Materie di insegnamento", 1, 1, "C", 1);

                        if ($tipo_visualizzazione_voti_periodo == "voto_singolo" || $periodo_corrente == 9) {
                            $pdf->SetXY(80, $y_corrente);
                            $pdf->CellFitScale(30, $altezza_celle, "Voti", 1, 1, "C", 1);
                            $x_corrente = 130;
                        } else {
                            $pdf->SetXY(80, $y_corrente);
                            $pdf->CellFitScale(20, $altezza_celle, "Scritto", 1, 1, "C", 1);
                            $pdf->SetXY(100, $y_corrente);
                            $pdf->CellFitScale(20, $altezza_celle, "Orale", 1, 1, "C", 1);
                            $pdf->SetXY(120, $y_corrente);
                            $pdf->CellFitScale(20, $altezza_celle, "Pratico", 1, 1, "C", 1);
                            $pdf->SetXY(140, $y_corrente);
                            $pdf->CellFitScale(20, $altezza_celle, "Unico", 1, 1, "C", 1);
                            $x_corrente = 160;
                        }

                        if ($tipo_visualizzazione_voti_periodo == "voto_singolo" || $periodo_corrente == 9) {
                            $pdf->SetXY(110, $y_corrente);
                            $pdf->CellFitScale(40, $altezza_celle, "Ore Ass.", 1, 1, "C", 1);
                        } else {
                            $pdf->SetXY($x_corrente, $y_corrente);
                            $pdf->CellFitScale(40, $altezza_celle, "Ore Ass.", 1, 1, "C", 1);
                        }

                        foreach ($elenco_voti_pagelline as $voto_pagellina) {
                            $y_corrente += $altezza_celle;

                            $pdf->SetFont('helvetica', '', $dimensione_font);
                            $pdf->SetXY(10, $y_corrente);
                            $pdf->CellFitScale(70, $altezza_celle, $voto_pagellina['descrizione_materia'], 1, 1, "L", 0);

                            if ($tipo_visualizzazione_voti_periodo == "voto_singolo" || $periodo_corrente == 9) {
                                if ($voti_numerici == 'SI') {
                                    $pdf->SetXY(80, $y_corrente);
                                    $pdf->CellFitScale(30, $altezza_celle, $voto_pagellina['voto_pagellina'], 1, 1, "C", 0);
                                } else {
                                    foreach ($voto_pagellina['significati_voto'] as $significato) {
                                        if ($voto_pagellina['voto_pagellina'] == $significato['voto']) {
                                            $pdf->SetXY(80, $y_corrente);
                                            $pdf->CellFitScale(30, $altezza_celle, $significato['valore'], 1, 1, "C", 0);
                                        }
                                    }
                                }

                                $x_corrente = 130;
                            } else {
                                if ($voti_numerici == 'SI') {
                                    $pdf->SetXY(80, $y_corrente);
                                    $pdf->CellFitScale(20, $altezza_celle, $voto_pagellina['voto_scritto_pagella'], 1, 1, "C", 0);
                                    $pdf->SetXY(100, $y_corrente);
                                    $pdf->CellFitScale(20, $altezza_celle, $voto_pagellina['voto_orale_pagella'], 1, 1, "C", 0);
                                    $pdf->SetXY(120, $y_corrente);
                                    $pdf->CellFitScale(20, $altezza_celle, $voto_pagellina['voto_pratico_pagella'], 1, 1, "C", 0);
                                    $pdf->SetXY(140, $y_corrente);
                                    $pdf->CellFitScale(20, $altezza_celle, $voto_pagellina['voto_pagellina'], 1, 1, "C", 0);
                                } else {
                                    $trovato_scritto = 'NO';

                                    foreach ($voto_pagellina['significati_voto'] as $significato) {
                                        if ($voto_pagellina['voto_scritto_pagella'] == $significato['voto']) {
                                            $pdf->SetXY(80, $y_corrente);
                                            $pdf->CellFitScale(20, $altezza_celle, $significato['valore'], 1, 1, "C", 0);
                                            $trovato_scritto = 'SI';
                                        }
                                    }
                                    if ($trovato_scritto == 'NO') {
                                        $pdf->SetXY(100, $y_corrente);
                                        $pdf->CellFitScale(20, $altezza_celle, ' ', 1, 1, "C", 0);
                                    }

                                    $trovato_orale = 'NO';

                                    foreach ($voto_pagellina['significati_voto'] as $significato) {
                                        if ($voto_pagellina['voto_orale_pagella'] == $significato['voto']) {
                                            $pdf->SetXY(120, $y_corrente);
                                            $pdf->CellFitScale(20, $altezza_celle, $significato['valore'], 1, 1, "C", 0);
                                            $trovato_orale = 'SI';
                                        }
                                    }
                                    if ($trovato_orale == 'NO') {
                                        $pdf->SetXY(120, $y_corrente);
                                        $pdf->CellFitScale(20, $altezza_celle, ' ', 1, 1, "C", 0);
                                    }

                                    $trovato_pratico = 'NO';

                                    foreach ($voto_pagellina['significati_voto'] as $significato) {
                                        if ($voto_pagellina['voto_pratico_pagella'] == $significato['voto']) {
                                            $pdf->SetXY(140, $y_corrente);
                                            $pdf->CellFitScale(20, $altezza_celle, $significato['valore'], 1, 1, "C", 0);
                                            $trovato_pratico = 'SI';
                                        }
                                    }

                                    if ($trovato_pratico == 'NO') {
                                        $pdf->SetXY(140, $y_corrente);
                                        $pdf->CellFitScale(20, $altezza_celle, ' ', 1, 1, "C", 0);
                                    }
                                }

                                $x_corrente = 160;
                            }

                            if ($tipo_visualizzazione_voti_periodo == "voto_singolo" || $periodo_corrente == 9) {
                                $pdf->SetXY(110, $y_corrente);
                                $pdf->CellFitScale(40, $altezza_celle, stampa_ore_o_minuti($voto_pagellina['ore_assenza']), 1, 1, "C", 0);
                            } else {
                                $pdf->SetXY($x_corrente, $y_corrente);
                                $pdf->CellFitScale(40, $altezza_celle, stampa_ore_o_minuti($voto_pagellina['ore_assenza']), 1, 1, "C", 0);
                            }
                        }

                        $y_corrente += $altezza_celle;
                        $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
                        $pdf->SetXY(10, $y_corrente);
                        $pdf->CellFitScale(70, $altezza_celle + 3, 'Media dei Voti', 1, 1, 'C', 1);
                        $pdf->SetXY(80, $y_corrente);

                        if ($tipo_visualizzazione_voti_periodo == "voto_singolo" || $periodo_corrente == 9) {
                            $pdf->CellFitScale(30, $altezza_celle + 3, $media[$periodo_corrente], 1, 1, "C", 1);
                        } else {
                            $pdf->CellFitScale(80, $altezza_celle + 3, $media[$periodo_corrente], 1, 1, "C", 1);
                        }

                        $y_corrente += $altezza_celle + 6;
                    }
                }

                $fill = true;
                $tot_righe = 0;

                if ($profilo_generale == "SI") {
                    $tot_righe += 9;
                }

                if ($esperienze == "SI") {
                    $tot_righe += 8;
                }

                if ($note_credito == "SI") {
                    $tot_righe += 5;
                }

                if ($note_aggiuntive == "SI") {
                    $tot_righe += 5;
                }

                if (($y_corrente + ($tot_righe * $altezza_celle)) > ($altezza_foglio - 20)) {
                    $pdf->AddPage('P');
                    $y_corrente = 10;
                    $x_corrente = 10;
                    $pdf->SetFont('helvetica', '', $dimensione_font + 4);
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "DATI DELLO STUDENTE", 1, 0, "C", 1);

                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 4);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, '', 1, 0, "L");
                    $pdf->SetXY(10, $y_corrente);
                    $pdf->CellFitScale(90, $altezza_celle, $studente["cognome"] . " " . $studente["nome"], 0, 0, "L");
                    $pdf->SetXY(100, $y_corrente);

                    if ($matricola_studente == 'SI') {
                        $pdf->CellFitScale(0, $altezza_celle, "MATRICOLA: " . $studente["matricola"], 0, 0, "L");
                    }

                    $y_corrente += $altezza_celle + 2;
                }

                if ($profilo_generale == "SI") {
                    //{{{ <editor-fold defaultstate="collapsed">
                    $x_corrente = 10;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "PROFILO GENERALE", 1, 1, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $fill = !$fill;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(50, $altezza_celle, "FREQUENZA", 1, 0, "L", $fill);
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] saltuaria", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] non costante", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] regolare", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] assidua", 1, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(50, $altezza_celle, "IMPEGNO", 1, 0, "L", $fill);
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] insufficiente", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] sufficiente", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] buono", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] notevole", 1, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(50, $altezza_celle, "INTERESSE", 1, 0, "L", $fill);
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] insufficiente", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] sufficiente", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] adeguato", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] elevato", 1, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(50, $altezza_celle, "PARTECIPAZIONE", 1, 0, "L", $fill);
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] passiva", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] sufficiente", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] buona", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] ottima", 1, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(50, $altezza_celle, "COMPORTAMENTO", 1, 0, "L", $fill);
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] scorretto", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] accettabile", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] adeguato", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] responsabile", 1, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(50, $altezza_celle, "CRESCITA CULTURALE", 1, 0, "L", $fill);
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] insufficiente", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] sufficiente", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] buona", 1, 0, "L", $fill);
                    $pdf->CellFitScale(35, $altezza_celle, "[  ] ottima", 1, 0, "L", $fill);
                    //}}} </editor-fold>
                }

                if ($esperienze == "SI") {
                    //{{{ <editor-fold defaultstate="collapsed">
                    $x_corrente = 10;
                    $y_corrente += $altezza_celle + $altezza_celle;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "ESPERIENZE MATURATE ALL'INTERNO DELLA SCUOLA", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $pdf->CellFitScale(0, $altezza_celle, "[  ] Partecipazione a stages, concorsi, scambi culturali, commissioni ____________________________________________________________________", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "[  ] Partecipazione ad attivita' culturali ____________________________________________________________________________________________", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "[  ] Partecipazione ad attivita' sportive (campionati studenteschi: partecipazione a livelli superiori a quello d'istituto) _______________________________", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "[  ] Partecipazione ad organi collegiali _________________________________________________________________________________________________", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "[  ] Altro ________________________________________________________________________________________________________________________", 0, 0, "L", $fill);
                    //}}} </editor-fold>
                }

                if ($note_credito == "SI") {
                    //{{{ <editor-fold defaultstate="collapsed">
                    $x_corrente = 10;
                    $y_corrente += $altezza_celle + $altezza_celle;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "NOTE DI CREDITO FORMATIVO (esperienze maturate all'esterno della scuola)", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $pdf->CellFitScale(0, $altezza_celle, "_________________________________________________________________________________________________________________________________________________________", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "_________________________________________________________________________________________________________________________________________________________", 0, 0, "L", $fill);
                    //}}} </editor-fold>
                }

                if ($note_aggiuntive == "SI") {
                    //{{{ <editor-fold defaultstate="collapsed">
                    $x_corrente = 10;
                    $y_corrente += $altezza_celle + $altezza_celle;
                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 2);
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "NOTE AGGIUNTIVE", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->SetFont('helvetica', '', $dimensione_font);
                    $pdf->CellFitScale(0, $altezza_celle, "_________________________________________________________________________________________________________________________________________________________", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    $pdf->SetXY($x_corrente, $y_corrente);
                    $pdf->CellFitScale(0, $altezza_celle, "_________________________________________________________________________________________________________________________________________________________", 0, 0, "L", $fill);
                    $y_corrente += $altezza_celle;
                    //}}} </editor-fold>
                }
            }

            if ($firma_dirigente != "NO")
            {
                $pdf->ln(10);
                $pdf->SetFont('helvetica', '', $dimensione_font);

                $pdf->CellFitScale(90, 0, "", 0, 0, "C", false);
                $pdf->CellFitScale(90, 0, $desc_dirigente, 0, 1, "C", false);

                $pdf->SetFont('helvetica', 'B', $dimensione_font);
                $pdf->CellFitScale(90, 0, "", 0, 0, "C", false);
                $pdf->CellFitScale(90, 0, $dirigente, 0, 1, "C", false);

                if ($firma_dirigente == "AUTOGRAFA")
                {
                    $pdf->SetFont('helvetica', '', $dimensione_font - 1);
                    $pdf->CellFitScale(90, 0 , "", 0, 0, "C", false);
                    $pdf->MultiCell(90, 0, "(Firma autografa sostituita dall’indicazione a stampa del nominativo,\ncome previsto dall'art. 3, c. 2, D. Lgs. n. 39/1993)", 0, "C", 0);
                }
            }
        }
        //}}} </editor-fold>
    }

    $db_key = $db_key_orig;
    //}}} </editor-fold>
}