#!/usr/bin/php
<?php
/**
 * Script per analizzare le dipendenze e le tabelle che potrebbero essere coinvolte
 * nell'importazione di studenti, parenti e relazioni
 * 
 * Questo script analizza il database per identificare:
 * - <PERSON>belle che contengono riferimenti a id_studente
 * - Tabelle che contengono riferimenti a id_parente  
 * - Tabelle che contengono riferimenti a id_classe
 * - Vincoli di chiave esterna
 * - Possibili dipendenze non evidenti
 */

require __DIR__ . '/../configs/mastercom.php';
require MC_PATH . '/functions/common.php';
require MC_PATH . '/common/dbconnect.php';

// Configurazione database (modifica secondo le tue impostazioni)
$config = [
    'db_a' => [
        'name' => 'mastercom_2025_2026',
        'host' => 'localhost',
        'port' => 5432,
        'user' => 'postgres',
        'password' => 'postgres'
    ]
];

// Connessione database
$conn = pg_connect("host={$config['db_a']['host']} port={$config['db_a']['port']} dbname={$config['db_a']['name']} user={$config['db_a']['user']} password={$config['db_a']['password']}");

if (!$conn) {
    die("Errore di connessione al database\n");
}

echo "=== ANALISI DIPENDENZE PER IMPORTAZIONE ===\n\n";

// 1. ANALISI TABELLE CON id_studente
echo "1. TABELLE CHE CONTENGONO id_studente:\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_id_studente = "
    SELECT 
        t.table_name,
        c.column_name,
        c.data_type,
        c.is_nullable
    FROM 
        information_schema.tables t
    JOIN 
        information_schema.columns c ON t.table_name = c.table_name
    WHERE 
        t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
        AND c.column_name = 'id_studente'
    ORDER BY t.table_name
";

$result = pg_query($conn, $query_id_studente);
$tabelle_id_studente = [];

while ($row = pg_fetch_assoc($result)) {
    $tabelle_id_studente[] = $row['table_name'];
    echo "- {$row['table_name']} ({$row['column_name']} {$row['data_type']}, nullable: {$row['is_nullable']})\n";
}

echo "\nTotale tabelle con id_studente: " . count($tabelle_id_studente) . "\n\n";

// 2. ANALISI TABELLE CON id_parente
echo "2. TABELLE CHE CONTENGONO id_parente:\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_id_parente = "
    SELECT 
        t.table_name,
        c.column_name,
        c.data_type,
        c.is_nullable
    FROM 
        information_schema.tables t
    JOIN 
        information_schema.columns c ON t.table_name = c.table_name
    WHERE 
        t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
        AND c.column_name = 'id_parente'
    ORDER BY t.table_name
";

$result = pg_query($conn, $query_id_parente);
$tabelle_id_parente = [];

while ($row = pg_fetch_assoc($result)) {
    $tabelle_id_parente[] = $row['table_name'];
    echo "- {$row['table_name']} ({$row['column_name']} {$row['data_type']}, nullable: {$row['is_nullable']})\n";
}

echo "\nTotale tabelle con id_parente: " . count($tabelle_id_parente) . "\n\n";

// 3. ANALISI TABELLE CON id_classe
echo "3. TABELLE CHE CONTENGONO id_classe:\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_id_classe = "
    SELECT 
        t.table_name,
        c.column_name,
        c.data_type,
        c.is_nullable
    FROM 
        information_schema.tables t
    JOIN 
        information_schema.columns c ON t.table_name = c.table_name
    WHERE 
        t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
        AND c.column_name = 'id_classe'
    ORDER BY t.table_name
";

$result = pg_query($conn, $query_id_classe);
$tabelle_id_classe = [];

while ($row = pg_fetch_assoc($result)) {
    $tabelle_id_classe[] = $row['table_name'];
    echo "- {$row['table_name']} ({$row['column_name']} {$row['data_type']}, nullable: {$row['is_nullable']})\n";
}

echo "\nTotale tabelle con id_classe: " . count($tabelle_id_classe) . "\n\n";

// 4. ANALISI VINCOLI DI CHIAVE ESTERNA
echo "4. VINCOLI DI CHIAVE ESTERNA RILEVANTI:\n";
echo "=" . str_repeat("=", 50) . "\n";

$query_fk = "
    SELECT
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name,
        tc.constraint_name
    FROM
        information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
    WHERE 
        tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_schema = 'public'
        AND (
            kcu.column_name IN ('id_studente', 'id_parente', 'id_classe')
            OR ccu.column_name IN ('id_studente', 'id_parente', 'id_classe')
        )
    ORDER BY tc.table_name, kcu.column_name
";

$result = pg_query($conn, $query_fk);
echo "Vincoli di chiave esterna trovati:\n";

while ($row = pg_fetch_assoc($result)) {
    echo "- {$row['table_name']}.{$row['column_name']} -> {$row['foreign_table_name']}.{$row['foreign_column_name']} ({$row['constraint_name']})\n";
}

// 5. CONTEGGIO RECORD PER TABELLA
echo "\n5. CONTEGGIO RECORD NELLE TABELLE PRINCIPALI:\n";
echo "=" . str_repeat("=", 50) . "\n";

$tabelle_principali = array_unique(array_merge($tabelle_id_studente, $tabelle_id_parente, $tabelle_id_classe));

foreach ($tabelle_principali as $tabella) {
    $query_count = "SELECT COUNT(*) as count FROM $tabella WHERE flag_canc = 0";
    $result_count = pg_query($conn, $query_count);
    
    if ($result_count) {
        $count = pg_fetch_result($result_count, 0, 0);
        echo "- $tabella: $count record attivi\n";
    } else {
        // Prova senza flag_canc se la colonna non esiste
        $query_count = "SELECT COUNT(*) as count FROM $tabella";
        $result_count = pg_query($conn, $query_count);
        if ($result_count) {
            $count = pg_fetch_result($result_count, 0, 0);
            echo "- $tabella: $count record totali\n";
        } else {
            echo "- $tabella: Errore nel conteggio\n";
        }
    }
}

// 6. RACCOMANDAZIONI
echo "\n6. RACCOMANDAZIONI PER L'IMPORTAZIONE:\n";
echo "=" . str_repeat("=", 50) . "\n";

echo "Tabelle che DEVONO essere considerate nell'importazione:\n";
$tabelle_critiche = ['studenti', 'parenti', 'parenti_studenti', 'storia_studenti', 'classi_studenti'];

foreach ($tabelle_critiche as $tabella) {
    if (in_array($tabella, $tabelle_principali)) {
        echo "✓ $tabella - GIÀ INCLUSA nello script\n";
    } else {
        echo "✗ $tabella - NON TROVATA nel database\n";
    }
}

echo "\nTabelle che POTREBBERO richiedere importazione aggiuntiva:\n";
$tabelle_aggiuntive = array_diff($tabelle_principali, $tabelle_critiche);

foreach ($tabelle_aggiuntive as $tabella) {
    echo "? $tabella - Da valutare se necessaria\n";
}

echo "\n7. SCRIPT SQL PER VERIFICA MANUALE:\n";
echo "=" . str_repeat("=", 50) . "\n";
echo "-- Verifica studenti con stesso codice fiscale\n";
echo "SELECT codice_fiscale, COUNT(*) FROM studenti WHERE flag_canc = 0 AND codice_fiscale != '' GROUP BY codice_fiscale HAVING COUNT(*) > 1;\n\n";

echo "-- Verifica parenti con stesso codice fiscale\n";
echo "SELECT codice_fiscale, COUNT(*) FROM parenti WHERE flag_canc = 0 AND codice_fiscale != '' GROUP BY codice_fiscale HAVING COUNT(*) > 1;\n\n";

echo "-- Verifica classi per mapping\n";
echo "SELECT classe, sezione, COUNT(*) FROM classi WHERE flag_canc = 0 GROUP BY classe, sezione HAVING COUNT(*) > 1;\n\n";

pg_close($conn);

echo "\n=== ANALISI COMPLETATA ===\n";
echo "Rivedi l'output per identificare eventuali tabelle aggiuntive da includere nell'importazione.\n";

?>
