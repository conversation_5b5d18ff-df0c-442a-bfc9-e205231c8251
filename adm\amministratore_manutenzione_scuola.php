<?php

// Funzione per generare HTML per LOG PARENTI
function genera_html_log_parenti($dato, $campo_cercato) {
    $html = '<h2 style="text-align: center; margin-bottom: 20px;">LOG PARENTI - CAMPO: ' . htmlspecialchars($campo_cercato) . '</h2>';

    if(empty($dato)){
        $html .= '<p style="text-align: center; font-weight: bold;">NESSUN RISULTATO TROVATO</p>';
        return $html;
    }

    foreach($dato as $nome_db => $logs){
        $html .= '<h3 style="color: #333; border-bottom: 2px solid #ccc; padding-bottom: 8px; margin: 20px 0 15px 0;">DATABASE: ' . htmlspecialchars($nome_db) . '</h3>';

        foreach($logs as $id_log => $log_data){
            $html .= '<div style="margin: 15px 0; padding: 15px; border: 2px solid #ddd; background-color: #f9f9f9; page-break-inside: avoid;">';
            $html .= '<h4 style="margin: 0 0 15px 0; color: #444; font-weight: bold; border-bottom: 1px solid #bbb; padding-bottom: 5px;">MODIFICHE EFFETTUATE IL: ' . htmlspecialchars($log_data['quando']) . ' da ' . htmlspecialchars($log_data['quale_utente']) . '</h4>';

            if(isset($log_data['modifiche']) && is_array($log_data['modifiche'])){
                $current_parent = '';
                $current_child = '';
                $in_child_section = false;

                foreach($log_data['modifiche'] as $modifica){
                    // Identifica se è un nome di parente
                    if(strpos($modifica, 'Parente: ') === 0){
                        if($current_parent != ''){
                            $html .= '</div>'; // Chiude il div del parente precedente
                        }
                        $current_parent = $modifica;
                        $in_child_section = false;
                        $html .= '<div style="margin: 10px 0; padding: 10px; background-color: #fff; border-left: 4px solid #007cba;">';
                        $html .= '<div style="font-weight: bold; color: #007cba; margin-bottom: 8px;">' . htmlspecialchars($modifica) . '</div>';
                    }
                    // Identifica se è un nome di figlio
                    elseif(strpos($modifica, 'Figlio: ') === 0){
                        if($in_child_section && $current_child != ''){
                            $html .= '</div>'; // Chiude il div del figlio precedente
                        }
                        $current_child = $modifica;
                        $in_child_section = true;
                        $html .= '<div style="margin: 8px 0 8px 20px; padding: 8px; background-color: #f0f8ff; border-left: 3px solid #4CAF50;">';
                        $html .= '<div style="font-weight: bold; color: #4CAF50; margin-bottom: 5px;">' . htmlspecialchars($modifica) . '</div>';
                    }
                    // È una modifica
                    else {
                        if($in_child_section){
                            // Modifica di un figlio - indentata di più
                            $html .= '<div style="margin: 3px 0 3px 15px; padding: 3px 0; color: #555; font-size: 90%;">• ' . htmlspecialchars($modifica) . '</div>';
                        } else {
                            // Modifica del parente - indentata normalmente
                            $html .= '<div style="margin: 3px 0 3px 10px; padding: 3px 0; color: #555;">• ' . htmlspecialchars($modifica) . '</div>';
                        }
                    }
                }

                // Chiude gli eventuali div aperti
                if($in_child_section && $current_child != ''){
                    $html .= '</div>'; // Chiude il div del figlio
                }
                if($current_parent != ''){
                    $html .= '</div>'; // Chiude il div del parente
                }
            }
            $html .= '</div>'; // Chiude il div principale della modifica
            $html .= '<div style="margin: 10px 0; height: 1px; background-color: #ddd;"></div>'; // Separatore tra blocchi
        }
    }

    return $html;
}

$scuola_bloccata = estrai_parametri_singoli('BLOCCO_OPERAZIONI');
switch ($stato_secondario) {
    case "blocca":
        if ($scuola_bloccata == 'NO') {
            aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'SI', $current_user);
            $scuola_bloccata = 'SI';
        } else {
            aggiorna_parametri_singoli('BLOCCO_OPERAZIONI', 'NO', $current_user);
            $scuola_bloccata = 'NO';
        }
        break;
    case "tipo_manutenzione":
        switch ($tipo_manutenzione) {
            case "log_docenti_display":
                break;
            case "log_docenti_update":
                $data_tradotta = mktime(0, 0, 0, $mm, $gg, $aaaa);
                $log_docenti = estrai_log_docenti((int) $id_professore, $errori, $data_tradotta, $accessi_falliti);
                $template->assign("log_docenti", $log_docenti);

                if ($accessi_falliti) {
                    $stato_log = 'visualizza_accessi_falliti';
                } elseif (is_numeric($id_professore)) {
                    $stato_log = 'visualizza_singolo_docente';
                } else {
                    $stato_log = 'visualizza_tutto';
                }

                $template->assign("stato_log", $stato_log);
            case "log_mensa_update":
                $data = date('Y-m-d', mktime(0, 0, 0, $mm, $gg, $aaaa));

                $payload = [];
                $payload['id_studente'] = $id_studente;
                $payload['data'] = $data;
                $payload['db_richiesto'] = $db_key;
                $path = "mensa/estrai_log_mensa";
                $log_mensa_dettaglio = nextapi_call($path, 'GET', $payload, $current_key);
                $template->assign("log_mensa_dettaglio", $log_mensa_dettaglio);
                $dati_studente = estrai_dati_studente((int) $id_studente);
                $template->assign("dati_studente", $dati_studente);
                //ATTENZIONE break omesso volontariamente
            case "log_mensa_display":
                $elenco_classi = estrai_classi('base', 'no_preiscrizioni_solo_classi_principali');
                $mat_classi = [];
                foreach ($elenco_classi as $classe) {
                    $mat_classi[] = $classe['id_classe'];
                }
                $limita_elenco_studenti_mensa = [];
                $elenco_studenti_mensa = estrai_studenti_istituto('dati_base_con_tutte_le_classi_e_mensa_attiva', $mat_classi, '', $limita_elenco_studenti_mensa, 'cognome_nome');
                $template->assign("elenco_studenti_mensa", $elenco_studenti_mensa);
                break;
            case "log_parenti_update":
                // Elaborazione della ricerca nel log parenti
                if (strlen(trim($campo_cercato)) > 0) {
                    $dato = [];
                    $db_attuale = $db_key;

                    foreach ($dbname as $key => $db_in_esame) {
                        if (strpos($db_in_esame["nome"], 'mastercom') !== false) {
                            $db_key = $db_in_esame["nome"];

                            // Estraggo l'elenco dei log da verificare
                            $sql_elenco_log = "SELECT * FROM log_storico
                                              WHERE dato ilike '%" . pg_escape_string($campo_cercato) . "%'
                                              ORDER BY data";
                            $result = pgsql_query($sql_elenco_log);
                            $elenco_log = pg_fetch_all($result);

                            if ($elenco_log) {
                                foreach ($elenco_log as $log) {
                                    $dato_log = unserialize($log['dato']);
                                    if ($dato_log && isset($dato_log['parente_originale'])) {
                                        $dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato'] = $dato_log;
                                        $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quando'] = date('d-m-Y H:i:s', $log['data']);
                                        $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'] = explode('::', $log['chi'])[2];

                                        // Determina il tipo di utente per nexus in base ai campi modificati
                                        if(stripos($dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'], "nexus") !== false){
                                            $modifiche_pagante_iban = false;

                                            // Controlla modifiche sui campi del parente
                                            foreach($dato_log['parente_originale'] as $chiave => $campo){
                                                if(($chiave == 'pagante' || $chiave == 'iban') &&
                                                   isset($dato_log['parente_modificato'][$chiave]) &&
                                                   $dato_log['parente_modificato'][$chiave] != $campo){
                                                    $modifiche_pagante_iban = true;
                                                    break;
                                                }
                                            }

                                            // Controlla modifiche sui campi dei figli (parenti_studenti)
                                            if(!$modifiche_pagante_iban && isset($dato_log['parente_originale']['parenti_studenti'])){
                                                foreach($dato_log['parente_originale']['parenti_studenti'] as $idx => $parentela_orig){
                                                    if(isset($dato_log['parente_modificato']['parenti_studenti'][$idx])){
                                                        $parentela_mod = $dato_log['parente_modificato']['parenti_studenti'][$idx];
                                                        if(($parentela_orig['pagante'] != $parentela_mod['pagante']) ||
                                                           (isset($parentela_orig['iban']) && isset($parentela_mod['iban']) && $parentela_orig['iban'] != $parentela_mod['iban'])){
                                                            $modifiche_pagante_iban = true;
                                                            break;
                                                        }
                                                    }
                                                }
                                            }

                                            if($modifiche_pagante_iban){
                                                $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'] = "Utente MasterCom2";
                                            } else {
                                                $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'] = "Assistenza-importazione";
                                            }
                                        }

                                        // Analisi delle modifiche
                                        $array_figli_originale = [];
                                        $array_figli_nuovo = [];
                                        $modifiche = [];
                                        $modifiche[] = "Parente: " . $dato_log['parente_originale']['cognome'] . " " . $dato_log['parente_originale']['nome'];

                                        foreach($dato_log['parente_originale'] as $chiave => $campo){
                                            if(($chiave != 'parenti_studenti') && ($chiave != 'chi_modifica') && ($chiave != 'data_modifica')){
                                                if($dato_log['parente_modificato'][$chiave] != $campo){
                                                    if($chiave == 'data_nascita'){
                                                        $valore_originale = date('d-m-Y H:i', $campo);
                                                        $valore_nuovo = date('d-m-Y H:i', $dato_log['parente_modificato'][$chiave]);
                                                    }else{
                                                        $valore_originale = $campo;
                                                        $valore_nuovo = $dato_log['parente_modificato'][$chiave];
                                                    }
                                                    if(strlen($valore_originale) == 0) $valore_originale = "VUOTO";
                                                    if(strlen($valore_nuovo) == 0) $valore_nuovo = "VUOTO";

                                                    $modifiche[] = "Modifica rilevata in: " . $chiave . " da " . $valore_originale . " a " . $valore_nuovo;
                                                }
                                            }elseif($chiave == 'parenti_studenti'){
                                                // Gestione dei figli - array originale
                                                foreach($campo as $parentela){
                                                    $sql_nome_stud = "SELECT cognome, nome FROM studenti WHERE id_studente = " . (int)$parentela['id_studente'];
                                                    $result_stud = pgsql_query($sql_nome_stud);
                                                    $dati_stud = pg_fetch_all($result_stud);
                                                    if($dati_stud && count($dati_stud) > 0){
                                                        $array_figli_originale[$parentela['id_studente']]['dati'] = $parentela;
                                                        $array_figli_originale[$parentela['id_studente']]['nome'] = "Figlio: " . $dati_stud[0]['cognome'] . " " . $dati_stud[0]['nome'];
                                                    }
                                                }

                                                // Gestione dei figli - array modificato
                                                if(isset($dato_log['parente_modificato']['parenti_studenti'])){
                                                    foreach($dato_log['parente_modificato']['parenti_studenti'] as $parentela){
                                                        $sql_nome_stud = "SELECT cognome, nome FROM studenti WHERE id_studente = " . (int)$parentela['id_studente'];
                                                        $result_stud = pgsql_query($sql_nome_stud);
                                                        $dati_stud = pg_fetch_all($result_stud);
                                                        if($dati_stud && count($dati_stud) > 0){
                                                            $array_figli_nuovo[$parentela['id_studente']]['dati'] = $parentela;
                                                            $array_figli_nuovo[$parentela['id_studente']]['nome'] = "Figlio: " . $dati_stud[0]['cognome'] . " " . $dati_stud[0]['nome'];
                                                        }
                                                    }
                                                }

                                                // Confronto delle modifiche sui figli
                                                foreach($array_figli_originale as $id_figlio => $figlio){
                                                    $modifiche[] = $figlio['nome'];
                                                    if(isset($array_figli_nuovo[$id_figlio])){
                                                        foreach($figlio['dati'] as $chiave_figlio => $campo_figlio){
                                                            if(($chiave_figlio != 'chi_modifica') && ($chiave_figlio != 'data_modifica')){
                                                                if($campo_figlio != $array_figli_nuovo[$id_figlio]['dati'][$chiave_figlio]){
                                                                    if($chiave_figlio == 'data_mandato_rid'){
                                                                        $valore_originale = date('d-m-Y', $campo_figlio);
                                                                        $valore_nuovo = date('d-m-Y', $array_figli_nuovo[$id_figlio]['dati'][$chiave_figlio]);
                                                                    }elseif($chiave_figlio == 'pagante'){
                                                                        $valore_originale = ($campo_figlio == '1') ? 'SI' : 'NO';
                                                                        $valore_nuovo = ($array_figli_nuovo[$id_figlio]['dati'][$chiave_figlio] == '1') ? 'SI' : 'NO';
                                                                    }else{
                                                                        $valore_originale = $campo_figlio;
                                                                        $valore_nuovo = $array_figli_nuovo[$id_figlio]['dati'][$chiave_figlio];
                                                                    }

                                                                    if(strlen($valore_originale) == 0) $valore_originale = "VUOTO";
                                                                    if(strlen($valore_nuovo) == 0) $valore_nuovo = "VUOTO";

                                                                    $modifiche[] = "Modifica rilevata in: " . $chiave_figlio . " da " . $valore_originale . " a " . $valore_nuovo;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        $dato[$db_in_esame["nome"]][$log['id_log_storico']]['modifiche'] = $modifiche;
                                    }
                                }
                            }
                        }
                    }

                    // Salva i dati in sessione per il PDF
                    $_SESSION['log_parenti_dati'] = $dato;
                    $_SESSION['log_parenti_campo'] = $campo_cercato;

                    // Genera HTML per la stampa
                    $html_stampa = genera_html_log_parenti($dato, $campo_cercato);

                    $db_key = $db_attuale;
                    $template->assign("log_parenti_risultati", $dato);
                    $template->assign("campo_cercato_parenti", $campo_cercato);
                    $template->assign("html_stampa_parenti", $html_stampa);
                }
                //ATTENZIONE break omesso volontariamente
            case "log_parenti_display":
                break;

            case "log_parenti_pdf":
                // Generazione PDF per LOG PARENTI usando i dati dalla sessione
                $campo_cercato = $_POST['campo_cercato'];

                // Recupera i dati dalla sessione se disponibili
                if(isset($_SESSION['log_parenti_dati']) && isset($_SESSION['log_parenti_campo']) &&
                   $_SESSION['log_parenti_campo'] == $campo_cercato){
                    $dato = $_SESSION['log_parenti_dati'];
                } else {
                    // Se non ci sono dati in sessione, esegui la ricerca
                    $dato = [];
                    $sql_db = "SELECT nome FROM database_anni ORDER BY nome DESC";
                    $result_db = pgsql_query($sql_db);
                    $database_anni = pg_fetch_all($result_db);

                    foreach($database_anni as $db_in_esame){
                        $sql_log = "SELECT * FROM " . $db_in_esame["nome"] . ".log_storico
                                   WHERE dato LIKE '%$campo_cercato%'
                                   AND tabella = 'parenti'
                                   ORDER BY data DESC";
                        $result_log = pgsql_query($sql_log);
                        $log_entries = pg_fetch_all($result_log);

                        if($log_entries){
                            foreach($log_entries as $log){
                                $dato_log = unserialize($log['dato']);
                                if($dato_log && isset($dato_log['parente_originale']) && isset($dato_log['parente_modificato'])){
                                    $dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato'] = $dato_log;
                                    $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quando'] = date('d-m-Y H:i:s', $log['data']);
                                    $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'] = explode('::', $log['chi'])[2];

                                    // Determina il tipo di utente per nexus in base ai campi modificati
                                    if(stripos($dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'], "nexus") !== false){
                                        $modifiche_pagante_iban = false;

                                        // Controlla modifiche sui campi del parente
                                        foreach($dato_log['parente_originale'] as $chiave => $campo){
                                            if(($chiave == 'pagante' || $chiave == 'iban') &&
                                               isset($dato_log['parente_modificato'][$chiave]) &&
                                               $dato_log['parente_modificato'][$chiave] != $campo){
                                                $modifiche_pagante_iban = true;
                                                break;
                                            }
                                        }

                                        // Controlla modifiche sui campi dei figli (parenti_studenti)
                                        if(!$modifiche_pagante_iban && isset($dato_log['parente_originale']['parenti_studenti'])){
                                            foreach($dato_log['parente_originale']['parenti_studenti'] as $idx => $parentela_orig){
                                                if(isset($dato_log['parente_modificato']['parenti_studenti'][$idx])){
                                                    $parentela_mod = $dato_log['parente_modificato']['parenti_studenti'][$idx];
                                                    if(($parentela_orig['pagante'] != $parentela_mod['pagante']) ||
                                                       (isset($parentela_orig['iban']) && isset($parentela_mod['iban']) && $parentela_orig['iban'] != $parentela_mod['iban'])){
                                                        $modifiche_pagante_iban = true;
                                                        break;
                                                    }
                                                }
                                            }
                                        }

                                        if($modifiche_pagante_iban){
                                            $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'] = "Utente MasterCom2";
                                        } else {
                                            $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'] = "Assistenza-importazione";
                                        }
                                    }

                                    // Analisi delle modifiche (stessa logica del case precedente)
                                    $array_figli_originale = [];
                                    $array_figli_nuovo = [];
                                    $modifiche = [];
                                    $modifiche[] = "Parente: " . $dato_log['parente_originale']['cognome'] . " " . $dato_log['parente_originale']['nome'];

                                    foreach($dato_log['parente_originale'] as $chiave => $campo){
                                        if(($chiave != 'parenti_studenti') && ($chiave != 'chi_modifica') && ($chiave != 'data_modifica')){
                                            if($dato_log['parente_modificato'][$chiave] != $campo){
                                                if($chiave == 'data_nascita'){
                                                    $valore_originale = date('d-m-Y H:i', $campo);
                                                    $valore_nuovo = date('d-m-Y H:i', $dato_log['parente_modificato'][$chiave]);
                                                }else{
                                                    $valore_originale = $campo;
                                                    $valore_nuovo = $dato_log['parente_modificato'][$chiave];
                                                }
                                                if(strlen($valore_originale) == 0) $valore_originale = "VUOTO";
                                                if(strlen($valore_nuovo) == 0) $valore_nuovo = "VUOTO";

                                                $modifiche[] = "Modifica rilevata in: " . $chiave . " da " . $valore_originale . " a " . $valore_nuovo;
                                            }
                                        }elseif($chiave == 'parenti_studenti'){
                                            // Gestione dei figli - array originale
                                            foreach($campo as $parentela){
                                                $sql_nome_stud = "SELECT cognome, nome FROM studenti WHERE id_studente = " . (int)$parentela['id_studente'];
                                                $result_stud = pgsql_query($sql_nome_stud);
                                                $dati_stud = pg_fetch_all($result_stud);
                                                if($dati_stud && count($dati_stud) > 0){
                                                    $array_figli_originale[$parentela['id_studente']]['dati'] = $parentela;
                                                    $array_figli_originale[$parentela['id_studente']]['nome'] = "Figlio: " . $dati_stud[0]['cognome'] . " " . $dati_stud[0]['nome'];
                                                }
                                            }

                                            // Gestione dei figli - array modificato
                                            if(isset($dato_log['parente_modificato']['parenti_studenti'])){
                                                foreach($dato_log['parente_modificato']['parenti_studenti'] as $parentela){
                                                    $sql_nome_stud = "SELECT cognome, nome FROM studenti WHERE id_studente = " . (int)$parentela['id_studente'];
                                                    $result_stud = pgsql_query($sql_nome_stud);
                                                    $dati_stud = pg_fetch_all($result_stud);
                                                    if($dati_stud && count($dati_stud) > 0){
                                                        $array_figli_nuovo[$parentela['id_studente']]['dati'] = $parentela;
                                                        $array_figli_nuovo[$parentela['id_studente']]['nome'] = "Figlio: " . $dati_stud[0]['cognome'] . " " . $dati_stud[0]['nome'];
                                                    }
                                                }
                                            }

                                            // Confronto delle modifiche sui figli
                                            foreach($array_figli_originale as $id_figlio => $figlio){
                                                $modifiche[] = $figlio['nome'];
                                                if(isset($array_figli_nuovo[$id_figlio])){
                                                    foreach($figlio['dati'] as $chiave_figlio => $campo_figlio){
                                                        if(($chiave_figlio != 'chi_modifica') && ($chiave_figlio != 'data_modifica')){
                                                            if($campo_figlio != $array_figli_nuovo[$id_figlio]['dati'][$chiave_figlio]){
                                                                if($chiave_figlio == 'data_mandato_rid'){
                                                                    $valore_originale = date('d-m-Y', $campo_figlio);
                                                                    $valore_nuovo = date('d-m-Y', $array_figli_nuovo[$id_figlio]['dati'][$chiave_figlio]);
                                                                }elseif($chiave_figlio == 'pagante'){
                                                                    $valore_originale = ($campo_figlio == '1') ? 'SI' : 'NO';
                                                                    $valore_nuovo = ($array_figli_nuovo[$id_figlio]['dati'][$chiave_figlio] == '1') ? 'SI' : 'NO';
                                                                }else{
                                                                    $valore_originale = $campo_figlio;
                                                                    $valore_nuovo = $array_figli_nuovo[$id_figlio]['dati'][$chiave_figlio];
                                                                }

                                                                if(strlen($valore_originale) == 0) $valore_originale = "VUOTO";
                                                                if(strlen($valore_nuovo) == 0) $valore_nuovo = "VUOTO";

                                                                $modifiche[] = "Modifica rilevata in: " . $chiave_figlio . " da " . $valore_originale . " a " . $valore_nuovo;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    $dato[$db_in_esame["nome"]][$log['id_log_storico']]['modifiche'] = $modifiche;
                                }
                            }
                        }
                    }
                }

                // Genera HTML per il PDF
                $html_content = genera_html_log_parenti($dato, $campo_cercato);

                // Generazione del PDF
                include_once("class/NEXUS_PDF.php");
                $pdf = new MASTERCOM_PDF('P', 'mm', 'A4');

                // Imposta header con logo se esiste
                $logo_path = '';
                if(file_exists('/var/www-source/mastercom/immagini_scuola/logo.jpg')){
                    $logo_path = '/var/www-source/mastercom/immagini_scuola/logo.jpg';
                } elseif(file_exists('/var/www-source/mastercom/immagini_scuola/logo.png')){
                    $logo_path = '/var/www-source/mastercom/immagini_scuola/logo.png';
                }

                if($logo_path != ''){
                    $pdf->setHeaderData($logo_path, 0, 20, 'L', 'LOG PARENTI', 'helvetica', 14, 'Campo: ' . $campo_cercato, 'helvetica', 10);
                    $pdf->SetMargins(10, 35, 10);
                } else {
                    $pdf->SetMargins(10, 10, 10);
                }

                $pdf->AddPage();
                $pdf->SetFont('helvetica', '', 10);

                // Usa writeHTML per generare il PDF dall'HTML
                $pdf->writeHTML($html_content, true, false, false, false, '');

                // Output del PDF
                $pdf->Output('log_parenti_' . date('Y-m-d_H-i') . '.pdf', 'D');
                exit;
                break;
            case "compiti_scuola":
                $elenco_compiti = estrai_notizie_portale((int) $current_user);
                $template->assign("stato_compiti", $stato_compiti);

                foreach ($elenco_compiti as $key => $singolo_compito) {
                    $singolo_compito['descrizione'] = str_replace('@#nl#@', '<br>', $singolo_compito['descrizione']);
                    $temp = explode("@@@", $singolo_compito['descrizione']);
                    $elenco_compiti[$key]['titolo'] = $temp[0];
                    $elenco_compiti[$key]['descrizione'] = $temp[1];
                    $elenco_compiti[$key]['titolo_risposta'] = $temp[2];
                    $elenco_compiti[$key]['risposta'] = $temp[3];
                    $elenco_compiti[$key]['data_apertura_tradotta'] = date('d/m/Y H:i', $elenco_compiti[$key]['data_apertura']);
                    $elenco_compiti[$key]['data_aggiornamento_tradotta'] = date('d/m/Y H:i', $elenco_compiti[$key]['data_aggiornamento']);
                    $elenco_compiti[$key]['data_chiusura_tradotta'] = date('d/m/Y H:i', $elenco_compiti[$key]['data_chiusura']);

                    if ($elenco_compiti[$key]['tipo'] != 'error') {
                        $elenco_compiti[$key]['emergenza'] = $elenco_compiti[$key]['emergenza'] * 2;
                    }

                    if ($elenco_compiti[$key]['emergenza'] < 4) {
                        $elenco_compiti[$key]['emergenza_tradotta'] = "ALTO";
                        $elenco_compiti[$key]['emergenza_colore'] = "classetd_rossa";
                    } elseif ($elenco_compiti[$key]['emergenza'] < 8) {
                        $elenco_compiti[$key]['emergenza_tradotta'] = "MEDIO";
                        $elenco_compiti[$key]['emergenza_colore'] = "classetd_gialla";
                    } else {
                        $elenco_compiti[$key]['emergenza_tradotta'] = "BASSO";
                        $elenco_compiti[$key]['emergenza_colore'] = "classetd_verde";
                    }

                    if ($singolo_compito['data_chiusura'] > 0) {
                        $elenco_compiti[$key]['data_chiusura_tradotta'] = date('d/m/Y H:i', $elenco_compiti[$key]['data_chiusura']);
                    }
                }

                $template->assign("elenco_compiti", $elenco_compiti);
                break;
            case "log_ins_mod_del":
                $elenco_studenti = estrai_studenti_istituto('rapida');
                $template->assign("elenco_studenti", $elenco_studenti);

                if ($stato_log_ins_mod_del == 'verifica') {
                    $data_inizio = mktime(0, 0, 0, $inizio_Month, $inizio_Day, $inizio_Year);
                    $data_fine = mktime(0, 0, 0, $fine_Month, $fine_Day + 1, $fine_Year);
                    $elenco_dati_verificati = estrai_dati_per_verifica_ins_mod_del((int) $id_studente, $tipo_dato, $data_inizio, $data_fine);
                    $template->assign("elenco_dati_verificati", $elenco_dati_verificati);
                    $template->assign("stato_log_ins_mod_del", $stato_log_ins_mod_del);
                    $template->assign("tipo_dato", $tipo_dato);
                    $dati_studente = estrai_dati_studente((int) $id_studente);
                    $template->assign("dati_studente", $dati_studente);
                    $template->assign("data_inizio_sel", $inizio_Day . '/' . $inizio_Month . '/' . $inizio_Year);
                    $template->assign("data_fine_sel", $fine_Day . '/' . $fine_Month . '/' . $fine_Year);
                }
                break;
            case "ips_ids":
                $template->assign("data_partenza", mktime(0, 0, 0, date('m') - 1, date('d'), date('Y')));
                $elenco_studenti = estrai_studenti_istituto('rapida');
                $template->assign("elenco_studenti", $elenco_studenti);
                $elenco_classi = estrai_classi('', 'no_preiscrizioni');
                $template->assign("elenco_classi", $elenco_classi);
                $elenco_utenti = estrai_utenti("TUTTI");
                $template->assign("elenco_utenti", $elenco_utenti);

                if ($stato_ips_ids == 'verifica') {
                    if ($data_inizio == "") {
                        $data_inizio = mktime(0, 0, 0, $inizio_Month, $inizio_Day, $inizio_Year);
                    }
                    if ($data_fine == "") {
                        $data_fine = mktime(0, 0, 0, $fine_Month, $fine_Day + 1, $fine_Year);
                    }
                    $filtro = array();
                    if ($id_studente > 0) {
                        $filtro['id_studente'] = $id_studente;
                        $template->assign("dati_studente", estrai_dati_studente($id_studente));
                    } else {
                        $filtro['id_studente'] = 'non_attivato';
                    }
                    if ($id_classe > 0) {
                        $filtro['id_classe'] = $id_classe;
                        $template->assign("dati_classe", estrai_classe($id_classe));
                    } else {
                        $filtro['id_classe'] = 'non_attivato';
                    }
                    if ($id_utente > 0) {
                        $filtro['id_utente'] = $id_utente;
                        $template->assign("dati_utente", estrai_utente($id_utente));
                    } else {
                        $filtro['id_utente'] = 'non_attivato';
                    }

                    if ($orario_sospetto == 'attivato') {
                        $filtro['ora_inizio'] = intval($ora_inizio_Hour);
                        $filtro['ora_fine'] = intval($ora_fine_Hour);
                    } else {
                        $filtro['ora_inizio'] = 0;
                        $filtro['ora_fine'] = 23;
                        $orario_sospetto = "non_attivato";
                    }

                    $filtro['dati_visualizzati'] = $dati_visualizzati;


                    if ($voti_sospetti == 'attivato') {
                        $filtro['voti']['sospetti'] = $voti_sospetti;
                    } else {
                        $filtro['voti']['sospetti'] = 'non_attivato';
                    }

                    $elenco_dati = estrai_dati_ips_ids($data_inizio, $data_fine, $filtro);
                    $elenco_dati_filtrato = array();
                    if ($voti_sospetti == 'attivato') {
                        foreach ($elenco_dati['voti'] as $key => $value) {
                            $ricerca = "voti.id_voto=" . $value['id_voto'];
                            if ($value['id_voto'] > 0) {
                                $dettaglio_voto_attuale = estrai_voto($value['id_voto']);
                            }
                            $dettaglio_voto = estrai_log_storico($data_inizio, $data_fine, '', $ricerca);
                            $trovato = false;
                            foreach ($dettaglio_voto as $chiave => $valore) {
                                if (is_array($dettaglio_voto_attuale)) {
                                    if (($valore['dato']['voto'] < $dettaglio_voto_attuale['voto'] or!is_numeric($valore['dato']['voto'])) and!$trovato) {
                                        $trovato = true;
                                        $elenco_dati_filtrato[] = $elenco_dati['voti'][$key];
                                    }
                                } else {
                                    if (($valore['dato']['voto'] < 6 or!is_numeric($valore['dato']['voto'])) and!$trovato) {
                                        $trovato = true;
                                        $elenco_dati_filtrato[] = $elenco_dati['voti'][$key];
                                    }
                                }
                            }
                        }
                        $elenco_dati['voti'] = $elenco_dati_filtrato;
                    }

                    if ($stato_ips_ids_dettaglio == 'info') {
                        $ricerca = "voti.id_voto=$id_voto";
                        $dettaglio_voto_attuale = estrai_voto($id_voto);
                        $dettaglio_voto = estrai_log_storico($data_inizio, $data_fine, '', $ricerca);
                        $temp['dato'] = $dettaglio_voto_attuale;
                        $temp['dato']['data_modifica_tradotta'] = date('d-m-Y H:i:s', $temp['dato']['data_modifica']);
                        $temp['dato']['dati_utente'] = estrai_utente($temp['dato']['chi_modifica']);
                        $temp['dato']['dati_materia'] = estrai_dati_materia($temp['dato']['id_materia']);
                        $temp['dettaglio_utente'] = estrai_login_utente_log_storico($dettaglio_voto_attuale['data_modifica'], $dettaglio_voto_attuale['chi_modifica']);
                        $dettaglio_voto[] = $temp;
                        $cont_colore = 1;
                        foreach ($dettaglio_voto as $key => $value) {
                            if (($cont_colore % 2) > 0) {
                                $dettaglio_voto[$key]['colore_colonna'] = 1;
                            } else {
                                $dettaglio_voto[$key]['colore_colonna'] = 0;
                            }
                            $cont_colore++;
                        }
                        foreach ($elenco_dati['voti'] as $key => $value) {
                            if ($value['id_voto'] == $id_voto) {
                                $elenco_dati['voti'][$key]['info'] = $dettaglio_voto;
                            } else {
                                $elenco_dati['voti'][$key]['info'] = '';
                            }
                        }
                    }

                    $template->assign("elenco_dati_voti", $elenco_dati['voti']);
                    $template->assign("stato_dati", $stato_dati);
                    $template->assign("dati_visualizzati", $dati_visualizzati);
                    $template->assign("voti_sospetti", $voti_sospetti);
                    $template->assign("orario_sospetto", $orario_sospetto);
                    $template->assign("id_studente", $id_studente);
                    $template->assign("id_classe", $id_classe);
                    $template->assign("id_utente", $id_utente);
                    $template->assign("ora_inizio_Hour", $ora_inizio_Hour);
                    $template->assign("ora_fine_Hour", $ora_fine_Hour);
                    $template->assign("stato_ips_ids", $stato_ips_ids);
                    $template->assign("data_inizio", $data_inizio);
                    $template->assign("data_fine", $data_fine);
                    $template->assign("data_inizio_sel", date('d/m/Y', $data_inizio));
                    $template->assign("data_fine_sel", date('d/m/Y', $data_fine));
                }
                break;
        }
        break;
}

$d = date("j", time());
$m = date("n", time());
$y = date("Y", time());
$template->assign("d", $d);
$template->assign("m", $m);
$template->assign("y", $y);
$professori = estrai_professori();
$cont = 0;

foreach ($professori as $singolo_prof) {
    $array_prof[$cont]['nome'] = $singolo_prof['cognome'] . ' ' . $singolo_prof['nome'];
    $array_prof[$cont]['valore'] = $singolo_prof['id_utente'];
    $cont++;
}

$template->assign("array_prof", $array_prof);
$elenco_parametri = estrai_elenco_parametri();
$template->assign("elenco_parametri", $elenco_parametri);
$template->assign("scuola_bloccata", $scuola_bloccata);

$template->assign("stato_secondario", $stato_secondario);
$template->assign("tipo_manutenzione", $tipo_manutenzione);
