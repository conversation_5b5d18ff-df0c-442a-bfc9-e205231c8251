<?php

/*
 * Tipo: Pagella superiori
 * Nome: ss_12345_finale_A3_madonnadn_bs_01
 */
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G', 'ss_12345_finale_A3_madonnadn_bs_01', 'Pagella Scuola Secondaria II Grado (personalizzata)', 1, 'madonna-bs', 1);
 */

$orientamento = 'L';
$formato = 'A3';
$periodo_pagella = 'finale';
$periodo = 9;

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $id_classe = $parametri_stampa['id_classe'];
    $periodo = $parametri_stampa['periodo'];
    $forza_provenienza = $parametri_stampa['forza_provenienza'];
    $id_studente = $studente['id_studente'];
    $data_p1 = "{$parametri_stampa['data_p1_day']}/{$parametri_stampa['data_p1_month']}/{$parametri_stampa['data_p1_year']}";
    $data_p2 = "{$parametri_stampa['data_p2_day']}/{$parametri_stampa['data_p2_month']}/{$parametri_stampa['data_p2_year']}";
    $data_p3 = "{$parametri_stampa['data_p3_day']}/{$parametri_stampa['data_p3_month']}/{$parametri_stampa['data_p3_year']}";
    $data_fin = "{$parametri_stampa['data_day']}/{$parametri_stampa['data_month']}/{$parametri_stampa['data_year']}";
    $parametro_stampa_solo_giudizi_sospesi = $parametri_stampa['parametro_stampa_solo_giudizi_sospesi'];
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $comune_nascita = $studente['citta_nascita_straniera'];
        $provincia_nascita = '';
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $provincia_nascita = $stato['descrizione'];
        }
    }
    else
    {
        $comune_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_p1 = estrai_voti_pagellina_studente_multi_classe($id_classe, 7, $id_studente);
    $voti_p2 = estrai_voti_pagellina_studente_multi_classe($id_classe, 8, $id_studente);
    $voti_p3 = estrai_voti_pagellina_studente_multi_classe($id_classe, 1, $id_studente);
    $voti_p4 = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $id_studente);

    $arr_voti = $condotta = $religione = $materie_sospese = [];
    $monteore_finale = $assenze_finale = $somma_voti = $cont_voti = 0;
    $txt_ampliamento = '';
    $da_giudizio_sospeso = 'NO';
    $scrutinato = $recuperi_inseriti = $bocciato = $esistenza_recuperi_con_sufficienze = false;
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $monteore_finale += $voti_p4[$id_materia]['monteore_totale'];
        $assenze_finale += $voti_p4[$id_materia]['ore_assenza'];

        if ($materia['in_media_pagelle'] == 'SI' && $voti_p4[$id_materia]['voto_pagellina'] != '') {
            $somma_voti += $voti_p4[$id_materia]['voto_pagellina'];
            $cont_voti++;
        }

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            && (
                    !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'RELIGIONE', 'SOSTEGNO', 'OPZIONALE', 'CONDOTTA'])
                )
            )
        {
            $arr_voti[$id_materia]['p1'] = $voti_p1[$id_materia];
            $arr_voti[$id_materia]['p2'] = $voti_p2[$id_materia];
            $arr_voti[$id_materia]['p3'] = $voti_p3[$id_materia];
            $arr_voti[$id_materia]['p4'] = $voti_p4[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV'
            &&
            $materia['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 0
            )
        {
            $religione[$id_materia]['p1'] = $voti_p1[$id_materia];
            $religione[$id_materia]['p2'] = $voti_p2[$id_materia];
            $religione[$id_materia]['p3'] = $voti_p3[$id_materia];
            $religione[$id_materia]['p4'] = $voti_p4[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {
            $condotta[$id_materia]['p1'] = $voti_p1[$id_materia];
            $condotta[$id_materia]['p2'] = $voti_p2[$id_materia];
            $condotta[$id_materia]['p3'] = $voti_p3[$id_materia];
            $condotta[$id_materia]['p4'] = $voti_p4[$id_materia];

//            foreach ($voti_p4[$id_materia]['campi_liberi'] as $campo_libero) {
//                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
//                    if (stripos($campo_libero['nome'], 'Ampliamento offerta formativa') !== false)
//                    {
//                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
//                        if ($value !== '') {
//                            $txt_ampliamento .= $value;
//                        }
//                    }
//                }
//            }
        }
        // scrutinio
        if ($voti_p4[$id_materia]['voto_pagellina'] > 0 && $voti_p4[$id_materia]['voto_pagellina'] != '')
        {
            $scrutinato = true;
        }
        // recuperi inseriti
        if ($materia['in_media_pagelle'] != 'NV')
        {
            if ($voti_p4[$id_materia]['tipo_recupero']!=''
                    ||
                $voti_p4[$id_materia]['esito_recupero']!='')
            {
                $recuperi_inseriti = true;
                $materie_sospese[] = $materia['descrizione'];

                if ($voti_p4[$id_materia]['voto_pagellina'] > 6) {
                    $esistenza_recuperi_con_sufficienze = true;
                }
            }
        }
    }
    $arr_voti = $religione + $arr_voti + $condotta;

    if($recuperi_inseriti)
    {
        $da_giudizio_sospeso = 'SI';
    }
    if (
           ($parametro_stampa_solo_giudizi_sospesi == 'SI' && $da_giudizio_sospeso != 'SI')
                ||
           ($parametro_stampa_solo_giudizi_sospesi == 'NI' && $da_giudizio_sospeso == 'SI')

        )
    {
        return 'NO';
    }

    if ($studente['codice_indirizzi']=='LIN')
    {
        switch ($studente['classe']) {
            case 1:
                $txt_ampliamento = "Ampliamento offerta formativa: Italiano, Inglese, Spagnolo, Tedesco";
                break;
            case 2:
                $txt_ampliamento = "Ampliamento offerta formativa: Italiano, Inglese, Spagnolo, Tedesco";
                break;
            case 3:
                $txt_ampliamento = "Ampliamento offerta formativa: Inglese";
                break;
            case 4:
                $txt_ampliamento = "Ampliamento offerta formativa: Inglese";
                break;
            case 5:
                $txt_ampliamento = "Ampliamento offerta formativa: Inglese";
                break;
        }
    }
    elseif ($studente['codice_indirizzi']=='SCI')
    {
        switch ($studente['classe']) {
            case 1:
                $txt_ampliamento = "Ampliamento offerta formativa: Italiano, Inglese, Matematica, Scienze";
                break;
            case 2:
                $txt_ampliamento = "Ampliamento offerta formativa: Italiano, Inglese, Matematica, Scienze";
                break;
            case 3:
                $txt_ampliamento = "Ampliamento offerta formativa: Inglese, Scienze";
                break;
            case 4:
                $txt_ampliamento = "Ampliamento offerta formativa: Inglese, Scienze";
                break;
            case 5:
                $txt_ampliamento = "Ampliamento offerta formativa: Storia, Fisica";
                break;
        }
    }


    if ($monteore_finale > 0)
    {
        $perc_assenza = round($assenze_finale / $monteore_finale, 2) * 100;
        if ($perc_assenza < 25)
        {
            $validazione_anno = "SI";
        }
        else
        {
            $validazione_anno = ($scrutinato) ? "DEROGA" : "NO";
        }
    }
    else
    {
        $validazione_anno = "SI";
    }

    if ( stripos($studente['esito'], 'non ammesso') !== false ) {
        $bocciato = true;
    }

    $a = explode('/', $anno_scolastico_attuale);
    $anno_inizio = (int) $a[0];
    $anno_fine = (int) $a[1];
    $curriculum_studente = estrai_curriculum_studente((int) $id_studente);
    $anno_scolastico_corrente = $anno_inizio . "/" . $anno_fine;
    $anno_scolastico_precedente = ($anno_inizio - 1) . "/" . ($anno_fine - 1);
    $provenienza = '';
    for($cont_curr=0; $cont_curr <count($curriculum_studente); $cont_curr++)
    {
        if($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_precedente)
        {
            $classe_precedente = $curriculum_studente[$cont_curr]['classe'];
            $nome_scuola =  empty(trim($curriculum_studente[$cont_curr]['descrizione_libera_scuola'])) ?$curriculum_studente[$cont_curr]['nome_scuola']:($curriculum_studente[$cont_curr]['descrizione_libera_scuola']);
            if($studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                $provenienza = $curriculum_studente[$cont_curr]['classe_desc'].' '.$nome_scuola;
            }
            else
            {
                $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . 'a ' . $nome_scuola;
            }

            if ($forza_provenienza == 'DA_QUESTO_ISTITUTO') {
                if ($classe_precedente == 8) {
                    $provenienza = 'SCUOLA SECONDARIA I GRADO';
                } else {
                    $provenienza = 'DA QUESTO ISTITUTO';
                }
            }
        }
        else {
            $classe_attuale = $curriculum_studente[$cont_curr]['classe'];

            if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_corrente
                && $classe_attuale == $curriculum_studente[$cont_curr]['classe']
                && $studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola']
                && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                if (($curriculum_studente[$cont_curr]['esito'] == 'Iscritto') || ($cont_curr <count($curriculum_studente) -1)) {

                    // Nel caso nel curriculum non sia stata inserita la scuola di provenienza
                    if($curriculum_studente[$cont_curr]['nome_scuola'] != "") {
                        $provenienza = $curriculum_studente[$cont_curr]['nome_scuola'];
                    } else {
                        $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . 'a ' . $curriculum_studente[$cont_curr]['descrizione'];
                    }
                }


                if ($forza_provenienza == 'DA_QUESTO_ISTITUTO') {
                    if ($classe_precedente == 8) {
                        $provenienza = 'SCUOLA SECONDARIA I GRADO';
                    } else {
                        $provenienza = 'DA QUESTO ISTITUTO';
                    }
                }
            }
        }
    }

    $numero_volte_iscritto_studente_classe = $studente['mat_esito']['numero_volte_iscritto'];
    $numero_volte_iscritto_studente_classe_trad = '';
    switch ($numero_volte_iscritto_studente_classe) {
        case 1: $numero_volte_iscritto_studente_classe_trad = 'PRIMA';break;
        case 2: $numero_volte_iscritto_studente_classe_trad = 'SECONDA';break;
        case 3: $numero_volte_iscritto_studente_classe_trad = 'TERZA';break;
    }

    $media_voti = round(($somma_voti / $cont_voti) ,2);

    $tot_crediti = $studente['crediti_prima'] + $studente['crediti_reintegrati_prima'] +
            $studente['crediti_seconda'] + $studente['crediti_reintegrati_seconda'] +
            $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'] +
            $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'] +
            $studente['crediti_quinta'] + $studente['crediti_reintegrati_quinta'] + $studente['crediti_finali_agg'];
//echo_debug($studente);
    $crediti_stampa = 0;
    if ( $studente['tipo_indirizzo'] == 5 ) { // liceo linguistico quadriennale
        switch ($studente['classe'])
        {
            case 2:
                $crediti_stampa = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'];
                break;
            case 3:
                $crediti_stampa = $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'];
                break;
            case 4:
                $crediti_stampa = $studente['crediti_quinta'] + $studente['crediti_finali_agg'];
                break;
        }
    }
    else
    {   // indirizzi normali
        switch ($studente['classe'])
        {
            case 1:
                $crediti_stampa = $studente['crediti_prima'] + $studente['crediti_reintegrati_prima'];
                break;
            case 2:
                $crediti_stampa = $studente['crediti_seconda'] + $studente['crediti_reintegrati_seconda'];
                break;
            case 3:
                $crediti_stampa = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'];
                break;
            case 4:
                $crediti_stampa = $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'];
                break;
            case 5:
                $crediti_stampa = $studente['crediti_quinta'] + $studente['crediti_finali_agg'];
                break;
        }
    }
    $tot_crediti = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'] + $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'] + $studente['crediti_quinta'] + $studente['crediti_finali_agg'];

    $dati_classe = estrai_classe($id_classe);
    // Dizionario temporaneo
    $labels = [

        "titolo"                    => "Ministero dell'Istruzione e del Merito",

        "alunno"                    => "Alunn||min_oa||",
        "classe"                    => "Classe ",
        "sezione"                   => "Sez. ",
        "anno_scolastico"           => "Anno Scolastico $anno_scolastico_attuale",
        "codice_fiscale"            => "Codice Fiscale ",

        "desc_scuola"               => "{$studente['descrizione_indirizzi']}
Paritario \"Madonna della Neve\"
{$studente['codice_meccanografico']}
{$studente['indirizzo_sedi']}
25030 {$studente['descrizione_comuni']} - {$studente['provincia_comuni']}",

        "attestato_titolo"          => "ATTESTATO",
        "attestato_valutazione"     => "Visti i risultati conseguiti si dichiara che<br>l'alunn||min_oa|| ",
        "attestato_frequenta"       => "L'alunn||min_oa|| ha frequentato la classe {$studente['classe']}{$studente['sezione']} Primaria e (1) ",

        "iscritto_classe"           => "Iscritt||min_oa|| alla classe {$studente['classe']} {$studente['sezione']} ".$studente['descrizione_indirizzi'],
        "attestato_ammissione"    => " è stat||min_oa|| ammess||min_oa|| alla classe successiva",

        "luogo_data"                => "(luogo e data dello scrutinio)",

        "direttore"                 => "La Direttrice",

        "note"                      => "(1) Scrivere per esteso la dizione valida:"
                . "<ul>"
                . '<li>"è stat||min_oa|| ammess||min_oa|| alla classe successiva"; ovvero "è stat||min_oa|| ammess||min_oa|| al successivo grado dell\'istruzione obbligatoria".</li>'
                . '<li>"non è stat||min_oa|| ammess||min_oa|| alla classe successiva"; ovvero "non è stat||min_oa|| ammess||min_oa|| al successivo grado dell\'istruzione obbligatoria".</li>'
                . "</ul>"
                . "<br>"
                . 'Legge 169/08 "Nella scuola primaria, i docenti, con decisione assunta all\'unanimità, possono non ammettere l\'alunn||min_oa|| alla classe successiva solo in casi eccezionali e comprovati da specifica motivazione".',


        "disciplina"                => "DISCIPLINA",
        "voto_IQ"                   => "I° Quadrimestre <b>(1)</b>",
        "voto_finale"               => "FINALE <b>(1)</b>",
        "nota_voti"                 => "2) PER LA VALUTAZIONE DEGLI APPRENDIMENTI E DEL COMPORTAMENTO SI FA RIFERIMENTO AI DESCRITTORI RIPORTATI SUL SITO (primaria)",

        "assenze"                   => "Totale assenze annue ",

        "firma_docenti"             => "Per i Docenti della classe",
        "firma_genitore"          => "Il (i) genitori (i) o chi ne fa le veci",

        "data_stampa"               => "Data ",
        "firma_dirigente"           => "La Direttrice",

        "legenda"                   =>
            '<table cellpadding="3" border="0.1px" align="center">
                <tr align="center" colspan="2">
                    <td><b>LEGENDA</b></td>
                </tr>
                <tr>
                    <td width="23%"><b>AVANZATO</b></td>
                    <td width="77%">L\'alunno porta a termine i compiti in situazioni note e non note, mobilitando una varietà di risorse
sia fornite dal docente sia reperite altrove, in modo autonomo e con continuità.</td>
                </tr>
                <tr>
                    <td><b>INTERMEDIO</b></td>
                    <td>L\'alunno porta a termine i compiti in situazioni note in modo autonomo e continuo; risolve compiti
in situazioni non note utilizzando le risorse fornite dal docente o reperite altrove, anche se in modo
discontinuo e non del tutto autonomo.</td>
                </tr>
                <tr>
                    <td><b>BASE</b></td>
                    <td>L\'alunno porta a termine i compiti solo in situazioni note e utilizzando le risorse fornite dal docente,
sia in modo autonomo ma discontinuo, sia in modo non autonomo, ma con continuità.</td>
                </tr>
                <tr>
                    <td><b>IN VIA DI PRIMA ACQUISIZIONE</b></td>
                    <td>L\'alunno porta a termine compiti solo in situazioni note e unicamente con il supporto del docente e
di risorse fornite appositamente.</td>
                </tr>
            </table>',

        "scrutinio_tit_1"           => "VALIDITÀ DELL’ANNO SCOLASTICO",
        "scrutinio_tit_2"           => "(Art. 14, comma 7 del D.P.R. n. 122/2009)",
        "scrutinio_desc"            => "Ai fini della validità dell’anno e dell’ammissione allo scrutinio finale, l’alunn||min_oa||:",
        "scrutinio_desc_val_1"      => "ha frequentato per almeno tre quarti dell’orario annuale;",
        "scrutinio_desc_val_2"      => "non ha frequentato per almeno tre quarti dell’orario annuale, ma ha usufruito della deroga;",
        "scrutinio_desc_val_3"      => "non ha frequentato per almeno tre quarti dell’orario annuale.",

        "dati_titolo"               => "Posizione scolastica dello studente",
        "dati_anno"                 => "Anno Scolastico ",
        "dati_registro"             => "N. Registro Generale",
        "dati_classe"               => "Classe",
        "dati_sezione"              => "Sezione",
        "dati_provenienza"          => "Provenienza",
        "dati_ammissione"           => "Titolo di Ammissione <sup>(1)</sup>",
        "dati_indirizzo"            => "Indirizzo",
        "iscrizione_n_volte"        => "Iscrizione per la " . $numero_volte_iscritto_studente_classe_trad .  " volta <sup>(2)</sup>",
        "dati_firma_dirigente"      => "Il Dir. Serv. Gen. E Amm <sup>(3)</sup><br>{$dati_classe['nome_dsga']}",


        "note_titolo"               => "NOTE",
        "note_tot"                  =>
            "<table>
                <tr>
                  <td width=\"4%\"><i>(1)</i></td>
                  <td width=\"96%\">PROMOZIONE; IDONEITA’; QUALIFICA; Idoneità all’ultima classe a seguito di esito positivo dell’esame preliminare e mancato superamento esami di Stato.</td>
                </tr>
                <tr>
                  <td><i>(2)</i></td>
                  <td>PRIMA; SECONDA; TERZA.</td>
                </tr>
                <tr>
                  <td><i>(3)</i></td>
                  <td>La firma può essere omessa ai sensi dell’Art. 3, D.to Lgs. 12/02/1993, n. 39.</td>
                </tr>
                <tr>
                  <td><i>(4)</i></td>
                  <td>“Il riquadro può essere utilizzato anche:<br>
              - per l’annotazione delle materie Art. 4, comma 6 del D.P.R. 122/2009;<br>
              - per l’annotazione prevista dall’Art. 9, comma 1 del D.P.R. 122/2009;<br>
              - per eventuali altre annotazioni o indicazione di rilascio di certificazione.</td>
                </tr>
                <tr>
                  <td><i>(5)</i></td>
                  <td>Per le classi terminali indicare: ammess||min_oa|| agli esami – non ammess||min_oa|| agli esami.</td>
                </tr>
                <tr>
                  <td><i>(6)</i></td>
                  <td>Solo per esami di qualifica professionale.</td>
                </tr>
                <tr>
                  <td><i>(7)</i></td>
                  <td>promoss||min_oa|| – non promoss||min_oa||.<br>Per le classi terminali indicare: ammess||min_oa|| – non ammess||min_oa||.</td>
                </tr>
          </table>",
//        "note_finale"               => "<i>\"Il presente documento non può essere prodotto agli organi della pubblica amministrazione o ai privati gestori di pubblici servizi\"</i>",

    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }

    // Attestato
    $esito = '';
    switch($studente['esito'])
    {
        case "Ammesso alla classe successiva":
        case "Ammessa alla classe successiva":
            $esito = "è stat{$min_oa} Ammess{$min_oa} alla classe successiva";
            break;
        case "Non ammesso alla classe successiva":
        case "Non ammessa alla classe successiva":
            $esito = "non è stat{$min_oa} ammess{$min_oa} alla classe successiva";
            break;
        case "In corso":
            $esito = "è In corso";
            break;
        case "Iscritto":
            $esito = "è Iscritt{$min_oa}";
            break;
        case "Ammesso al successivo grado dell'istruzione obbligatoria":
        case "Ammessa al successivo grado dell'istruzione obbligatoria":
            $esito = "è stat{$min_oa} Ammess{$min_oa} al successivo grado dell'istruzione obbligatoria";
            break;
        case "Non ammesso al successivo grado dell'istruzione obbligatoria":
        case "Non ammessa al successivo grado dell'istruzione obbligatoria":
            $esito = "Non è stat{$min_oa} ammess{$min_oa} al successivo grado dell'istruzione obbligatoria";
            break;
        case "Ammessa esame di stato":
        case "Ammesso esame di stato":
            $esito = "è stat{$min_oa} Ammess{$min_oa} all'Esame di Stato";
            break;
        case "Non ammessa esame di stato":
        case "Non ammesso esame di stato":
            $esito = "non è stat{$min_oa} Ammess{$min_oa} all'Esame di Stato";
            break;
        default:
            $esito = $studente['esito'];
            break;
    }

    $mat_risultato = calcola_esito_finale_studente((int) $id_studente, (int) $current_user, true);
    $stato_promozione = safe_decode($mat_risultato['esito']);
    switch($stato_promozione)
    {
        case "Ammesso alla classe successiva":
        case "Ammessa alla classe successiva":
            $stato_promozione = "è stat{$min_oa} Ammess{$min_oa} alla classe successiva";
            break;
        case "Non ammesso alla classe successiva":
        case "Non ammessa alla classe successiva":
            $stato_promozione = "non è stat{$min_oa} ammess{$min_oa} alla classe successiva";
            break;
        case "In corso":
            $stato_promozione = "è In corso";
            break;
        case "Iscritto":
            $stato_promozione = "è Iscritt{$min_oa}";
            break;
        case "Ammesso al successivo grado dell'istruzione obbligatoria":
        case "Ammessa al successivo grado dell'istruzione obbligatoria":
            $stato_promozione = "è stat{$min_oa} Ammess{$min_oa} al successivo grado dell'istruzione obbligatoria";
            break;
        case "Non ammesso al successivo grado dell'istruzione obbligatoria":
        case "Non ammessa al successivo grado dell'istruzione obbligatoria":
            $stato_promozione = "Non è stat{$min_oa} ammess{$min_oa} al successivo grado dell'istruzione obbligatoria";
            break;
        case "Ammessa esame di stato":
        case "Ammesso esame di stato":
            $stato_promozione = "è stat{$min_oa} Ammess{$min_oa} all'Esame di Stato";
            break;
        default:
            $stato_promozione = $stato_promozione;
            break;
    }
    $h_header = 10;
    $tbl_header = '<table align="center">
            <tr>
                <td width="20%"><b>'.$studente['cognome'].'</b></td>
                <td width="20%"><b>'.$studente['nome'].'</b></td>
                <td width="25%"><b>'.$studente['codice_fiscale'].'</b></td>
                <td width="20%"><b>'.$studente['codice_meccanografico'].'</b></td>
                <td width="15%"><b>'.$anno_scolastico_attuale.'</b></td>
            </tr>
            <tr>
                <td><font size="-3">COGNOME</font></td>
                <td><font size="-3">NOME</font></td>
                <td><font size="-3">CODICE FISCALE</font></td>
                <td><font size="-3">CODICE ISTITUTO</font></td>
                <td><font size="-3">ANNO SCOLASTICO</font></td>
            </tr>
        </table>';

    $firme = '<table>
            <tr>
                <td width="60%">'.$studente['descrizione_comuni'].", $data_fin".'</td>
                <td width="40%"  align="center">'.'</td>
            </tr>
            <tr>
                <td width="60%"><br><br><br>_________________________________<br>'.$labels['firma_genitore'].'</td>
                <td width="40%"  align="center"><br><br><br>_________________________________<br>Il Dirigente Scolastico <sup>(3)</sup><br>'.$studente['nome_dirigente'].'</td>
            </tr>
        </table>';
    $firme_fine = '<table>
            <tr>
                <td width="60%"><br><br><br>_________________________________<br>Il (i) genitore (i) o chi ne fa le veci</td>
                <td width="40%" align="center" align="center"><br><br><br>_________________________________<br>Il Dirigente Scolastico <sup>(3)</sup><br>'.$studente['nome_dirigente'].'</td>
            </tr>
        </table>';
    if ($studente['codice_indirizzi'] == 'CLA4') {
        $firme_1 = '<table>
                <tr>
                    <td width="60%"></td>
                    <td width="40%" align="center">_______________________<br>Il Dirigente Scolastico <sup>(3)</sup><br>'.$studente['nome_dirigente'].'</td>
                </tr>
            </table>';
    } else {
        $firme_1 = '<table>
                <tr>
                    <td width="60%">'.$studente['descrizione_comuni'].", $data_p1".'</td>
                    <td width="40%" align="center">_______________________<br>Il Dirigente Scolastico <sup>(3)</sup><br>'.$studente['nome_dirigente'].'</td>
                </tr>
            </table>';
    }
    $firme_2 = '<table>
            <tr>
                <td width="60%">'.$studente['descrizione_comuni'].", $data_fin".'</td>
                <td width="40%" align="center">_______________________<br>Il Dirigente Scolastico <sup>(3)</sup><br>'.$studente['nome_dirigente'].'</td>
            </tr>
        </table>';

    switch ($validazione_anno) {
        case 'SI':
            $scrut_label_1 = '[X]&nbsp;&nbsp;' . $labels['scrutinio_desc_val_1'];
            $scrut_label_2 = '[&nbsp;&nbsp;&nbsp;]&nbsp;&nbsp;' . $labels['scrutinio_desc_val_2'];
            $scrut_label_3 = '[&nbsp;&nbsp;&nbsp;]&nbsp;&nbsp;' . $labels['scrutinio_desc_val_3'];
            break;
        case 'DEROGA':
            $scrut_label_1 = '[&nbsp;&nbsp;&nbsp;]&nbsp;&nbsp;' . $labels['scrutinio_desc_val_1'];
            $scrut_label_2 = '[X]&nbsp;&nbsp;' . $labels['scrutinio_desc_val_2'];
            $scrut_label_3 = '[&nbsp;&nbsp;&nbsp;]&nbsp;&nbsp;' . $labels['scrutinio_desc_val_3'];
            break;
        case 'NO':
            $scrut_label_1 = '[&nbsp;&nbsp;&nbsp;]&nbsp;&nbsp;' . $labels['scrutinio_desc_val_1'];
            $scrut_label_2 = '[&nbsp;&nbsp;&nbsp;]&nbsp;&nbsp;' . $labels['scrutinio_desc_val_2'];
            $scrut_label_3 = '[X]&nbsp;&nbsp;' . $labels['scrutinio_desc_val_3'];
            break;
    }
    $scrutinio_tot = $labels['scrutinio_desc'] . '<br>' . $scrut_label_1 . '<br>' . $scrut_label_2 . '<br>' . $scrut_label_3 . '<br>';

    $esito_precedente = estrai_esito_e_volte_iscritto_da_curriculum_studente((int) $studente['id_studente'], $anno_scolastico_precedente);
    $titolo_ammissione = $esito_precedente['esito'];
    $tbl_dati = # tablre border to style="border:1px solid"
        '<table  style="border:0.1px solid black" cellpadding="2">
            <tr>
                <td width="50%"><b>' . $labels['dati_titolo'] . '</b></td>
                <td width="50%" align="center"><b>' . $labels['dati_anno'] .'</b>' . $anno_scolastico_attuale . '</td>
            </tr>

            <tr>
                <td width="17%">&nbsp;' . $studente['registro'] . '</td>
                <td width="12%">' . $studente['classe']. 'ª</td>
                <td width="12%">' . $studente['sezione']. '</td>
                <td width="59%">' . $provenienza . '</td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['dati_registro'] . '</font></td>
                <td><font size="-2">' . $labels['dati_classe'] . '</font></td>
                <td><font size="-2">' . $labels['dati_sezione'] . '</font></td>
                <td><font size="-2">' . $labels['dati_provenienza'] . '</font></td>
            </tr>

            <tr>
                <td>&nbsp;</td>
            </tr>

            <tr>
                <td width="100%">&nbsp;' . $titolo_ammissione . '</td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['dati_ammissione'] . '</font></td>
            </tr>

            <tr>
                <td>&nbsp;</td>
            </tr>

            <tr>
                <td width="40%">&nbsp;' . ucwords(strtolower($studente['descrizione_indirizzi'])) . '</td>
                <td width="60%" align="center">' . $labels['iscrizione_n_volte'] . '</td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['dati_indirizzo'] . '</font></td>
                <td></td>
            </tr>

            <tr>
                <td width="100%" align="right"><font size="-2">' . $labels['dati_firma_dirigente'] . '</font></td>
            </tr>
        </table>';

    //{{{ <editor-fold defaultstate="collapsed" desc="Carenze: anno corrente e precedente">
    // anno corrente
    $arr_debiti_anno_corrente_non_recuperati = [];
    $arr_id_materie_debiti_non_recuperati = [];
    $debiti_anno_corrente = estrai_debiti_studente((int) $id_studente, $anno_inizio . "/" . $anno_fine);
    foreach ($debiti_anno_corrente as $debito) {
        if ($debito['debito_recuperato'] == 'NO') {
            $arr_debiti_anno_corrente_non_recuperati[] = $debito['descrizione_materia'];
            $arr_id_materie_debiti_non_recuperati[$debito['id_materia']] = $debito['id_materia'];
        }
    }

    // anno precedente
    $arr_debiti_anno_prec_recuperati = [];
    $debiti_anno_prec = estrai_debiti_studente( (int) $id_studente, (((int) $anno_inizio)-1) . "/" . (((int) $anno_fine)-1) );
    foreach ($debiti_anno_prec as $debito) {
        if ($debito['debito_recuperato'] == 'SI') {
            $arr_debiti_anno_prec_recuperati[] = $debito['descrizione_materia'];
        }
    }
    //}}} </editor-fold>

    $annotazioni_txt1 = '';
    $annotazioni_txt2 = '';
    if($studente['pei'] == 'SI') {
        $annotazioni_txt1 .= "La votazione è riferita al P.E.I. (Piano Educativo Individualizzato) conforme all'O.M. n°90 del 2001 art. " . $studente['articolo_pei'] . "<br>";
    }
//    if ($studente["tipo_indirizzo"] != '4' && $studente["tipo_indirizzo"] != '6' && $studente["tipo_indirizzo"] != '7')
//    {
//        if($bocciato)
//        {
//            if($recuperi_inseriti)
//            {
//                $annotazioni_txt2 .= 'Situazione scrutinio di giugno: Giudizio sospeso<br>';
//            }
//        }
//        else
//        {
//            if($esistenza_recuperi_con_sufficienze)
//            {
//                $data_inizio_calcolo_sospesi = estrai_parametri_singoli("DATA_INIZIO_CALCOLO_GIUDIZI_SOSPESI", $id_classe, 'classe');
//                if(time() >= $data_inizio_calcolo_sospesi)
//                {
//                    $annotazioni_txt2 .= 'Situazione scrutinio di giugno: Giudizio Sospeso<br>';
//                }
//                else
//                {
//                    $annotazioni_txt2 .= 'Situazione scrutinio di giugno: Consolidamento<br>';
//                }
//            }
//        }
//    }

    if(strlen($materie_sospese) > 0) {
        $annotazioni_txt2 .= implode(', ', $materie_sospese) . "<br>";
    }

    if ( count($arr_debiti_anno_corrente_non_recuperati) != 0 ) {
        $annotazioni_txt1 .= 'Debiti: '. $labels['discipline_carenze_corr'] . strtoupper( implode( ', ', $arr_debiti_anno_corrente_non_recuperati) ) . '.<br>';
    }
    if ( count($arr_debiti_anno_prec_recuperati) != 0 ) {
        $annotazioni_txt1 .= 'Debiti recuperati dell\'anno precedente: '. $labels['discipline_carenze_prec'] . strtoupper( implode( ', ', $arr_debiti_anno_prec_recuperati) ) . '.<br>';
    }

    if($studente['pei'] == 'SI') {
        $annotazioni_txt2 .= "La votazione è riferita al P.E.I. (Piano Educativo Individualizzato) conforme all'O.M. n°90 del 2001 art. " . $studente['articolo_pei'] . "<br>";
    }


//			if($stampa_debiti_fine_anno != 'NO' && strlen($elenco_materie_sospese) > 0)
//			{
//				if(strlen($commento_esito) > 0)
//				{
//					$annotazioni_txt2 .= ' in:';
//				}
//
//				$annotazioni_txt2 .= $elenco_materie_sospese . "<br>";
//			}
//
////			if($stampa_carenze_fine_anno != 'NO' && strlen($elenco_materie_carenze) > 0)
//            if($stampa_carenze_fine_anno != 'NO' && !empty($materie_carenze))
//            {
//                $elenco_carenze = '';
//                foreach ($materie_carenze as $value) {
//                    $elenco_carenze .= "<br>" . $value . " ";
//                }
//
//				$annotazioni_txt2 .= $testo_finale['consolidamento'] . $elenco_carenze . "<br>";
//			}

	if ($txt_ampliamento != '') {
        $annotazioni_txt2 = "$txt_ampliamento<br>$annotazioni_txt2";
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="pdf">
    //{{{ <editor-fold defaultstate="collapsed" desc="######   PAGINA 4   ######">
    $f = 'Times';
    $fd = 10;

    $pdf->AddPage('L', 'A3');
    $pdf->SetAutoPageBreak("off", 1);
    $page_width = $pdf->getPageWidth();
    $page_dims = $pdf->getPageDimensions();
    $m_sinistro = $page_dims['lm'];
    $m_top = $page_dims['tm'];
    $m_destro = $page_dims['rm'];
    $half_page = $page_width / 2;
    $wp = $half_page - $m_destro - $m_sinistro;
    $x2 = $half_page + $m_sinistro;

    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, $h_header, $m_sinistro, $m_top, $tbl_header, 1, 1);
    $pdf->ln(7);
    $y_attestato_box = $pdf->GetY();
    $pdf->ln(3);
    $pdf->SetFont($f, 'B', $fd+3);
    $pdf->CellFitScale($wp, 0, $labels['scrutinio_tit_1'], 0, 1, 'C');
    $pdf->SetFont($f, 'B', $fd+1);
    $pdf->CellFitScale($wp, 0, $labels['scrutinio_tit_2'], 0, 1, 'C');
    $pdf->Ln(4);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', $scrutinio_tot, 0, 1);
    $pdf->Ln(3);
    $pdf->writeHTMLCell($wp, $pdf->GetY()-$y_attestato_box, '', $y_attestato_box, '', 1, 1);
    $pdf->Ln(10);

//    $pdf->SetFont($f, 'B', $fd+1);
//    $pdf->writeHTMLCell($wp, 0, '', '', "VALUTAZIONE $tipo_periodo_stampa", 1, 1, false, true, 'C');
//    $pdf->SetFont($f, '', $fd);
//    $y_st = $pdf->GetY();
//    $pdf->ln(1);
//    $pdf->writeHTMLCell($wp, 0, '', '', $giudizio, 0, 1, false, true, 'L');
//    $pdf->writeHTMLCell($wp, $pdf->GetY()+5-$y_st, '', $y_st, '', 1, 1);

    $pdf->ln(10);
    $pdf->SetFont($f, '', $fd);

//    $pdf->writeHTMLCell($wp, 0, '', '',$firme, 0, 1);

    $pdf->ln(10);
    $pdf->SetFont($f, 'B', $fd+1);
    $pdf->CellFitScale($wp, 10, "RISULTATO FINALE", 0, 1, 'C');

    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', $labels['attestato_valutazione'].$esito.'<sup>(7)</sup>', 0, 1, false, true, 'C');
    $pdf->ln(7);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '',$firme);
    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell($wp, 0, '', 210, $labels['note_titolo'], 0, 1, false, true, 'C');
    $pdf->ln(2);
    $pdf->SetFont($f, '', $fd-2);
    $pdf->writeHTMLCell($wp-20, 0, $m_sinistro+15, '', $labels['note_tot'], 0, 1, false, true, 'L');
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="######   PAGINA 1   ######">
    $pdf->Image('immagini_scuola/logo_repubblica.jpg', $x2 + ($wp/2) - 12, $m_top+12, 25, 25, 'JPG', false);
    $pdf->setY($m_top+31);
    $pdf->Ln(8);
    $pdf->SetFont($f, 'B', 17);
    $pdf->SetX($x2);
    $pdf->MultiCell($wp, 0, $labels['titolo'], 0, 'C');
    $pdf->Ln(10);
    $pdf->SetX($x2);
    $pdf->SetFont($f, 'B', $fd+2);
    $pdf->MultiCell($wp*0.35, 16, "Istituzione\nscolastica", 1, 'C', false, 0, '', '', true, 0, false, true, 16, 'M');
    $pdf->SetFont($f, '', $fd+2);
    $pdf->MultiCell($wp*0.65, 16, "{$studente['descrizione_indirizzi']}\n{$studente['descrizione_comuni']}, {$studente['provincia_comuni']}", 1, 'C', false, 1, '', '', true, 0, false, true, 16, 'M');
    $pdf->ln(6);

    $pdf->SetX($x2);
    $pdf->SetFont($f, 'B', $fd+2);
    $pdf->MultiCell($wp*0.35, 30, "Scuola Secondaria
di Secondo Grado
Paritaria", 1, 'C', false, 0, '', '', true, 0, false, true, 30, 'M');
    $pdf->SetFont($f, '', $fd+2);
    $pdf->MultiCell($wp*0.65, 30, $labels['desc_scuola'], 1, 'C', false, 1, '', '', true, 0, false, true, 30, 'M');
    $pdf->SetFont($f, '', $fd+4);

    $pdf->ln(7);
    $pdf->SetFont($f, 'B', $fd+6);
    $pdf->SetX($x2);
    $pdf->CellFitScale(0, 0, "Pagella scolastica", 0, 1, 'C');
    $pdf->ln(2);
    $pdf->SetX($x2);
    $pdf->SetFont($f, 'B', $fd+4);
    $pdf->CellFitScale(0, 0, $labels['anno_scolastico'], 0, 1, 'C');
    $pdf->ln(7);

	$altezza_cella = 4;
    $pdf->ln(1);
	$pdf->SetX($x2);
    $pdf->SetFont($f, 'B', 14);
	$pdf->CellFitScale($wp, $altezza_cella, 'Dati anagrafici dello studente', 'TRL', 1, 'C');
	$pdf->SetX($x2);
    $pdf->CellFitScale($wp, 0, '', 'RL', 1, 'C');
    $pdf->SetFont($f, '', $fd);

	$pdf->SetX($x2);
    $pdf->SetFont($f, 'B', $fd+1);
	$pdf->CellFitScale($wp/3, $altezza_cella, $studente['cognome'], 'L', 0, 'C');
	$pdf->CellFitScale($wp/3, $altezza_cella, $studente['nome'], 0, 0, 'C',FALSE,'',0, FALSE,'T', 'B');
	$pdf->CellFitScale($wp/3, $altezza_cella, $studente['codice_fiscale'], 'R', 1, 'C');

	$pdf->SetX($x2);
    $pdf->SetFont($f, '', $fd);
	$pdf->CellFitScale($wp/3, $altezza_cella+3, 'COGNOME', 'L', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->CellFitScale($wp/3, $altezza_cella+3, 'NOME', 0, 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->CellFitScale($wp/3, $altezza_cella+3, 'CODICE FISCALE', 'R', 1, 'C',FALSE,'',0, FALSE,'T', 'T');

    $pdf->SetFont($f, 'B', $fd+1);
	$pdf->SetX($x2);
    $pdf->CellFitScale($wp, 2, '', 'RL', 1, 'C');
	$pdf->SetX($x2);
	$pdf->CellFitScale($wp/3, $altezza_cella, $studente['data_nascita_ext'], 'L', 0, 'C');
	$pdf->CellFitScale($wp/3, $altezza_cella, $comune_nascita, 0, 0, 'C');
	$pdf->CellFitScale($wp/3, $altezza_cella, $provincia_nascita, 'R', 1, 'C');

	$pdf->SetX($x2);
    $pdf->SetFont($f, '', $fd);
	$pdf->CellFitScale($wp/3, $altezza_cella+3, 'DATA DI NASCITA', 'LB', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->CellFitScale($wp/3, $altezza_cella+3, 'COMUNE DI NASCITA', 'B', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->CellFitScale($wp/3, $altezza_cella+3, 'PROV .O STATO ESTERO', 'RB', 1, 'C',FALSE,'',0, FALSE,'T', 'T');

    $pdf->ln(10);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, $x2, '', $tbl_dati, 0, 1);

//    $pdf->ln(10);
//    $pdf->SetX($x2);
//    $pdf->SetFont($f, 'B', 10);
//    $pdf->CellFitScale(0, 8, $labels['iscritto_classe'], 1, 1, 'L');

    $pdf->ln(9);
	$pdf->SetX($x2);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, $x2, '',$firme_2);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="######   PAGINA 2 - 3  ######">
    $pdf->AddPage('L', 'A3');
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, $h_header, $m_sinistro, $m_top, $tbl_header, 1, 0);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, $h_header, $x2, $m_top, $tbl_header, 1, 1);
    $pdf->ln(3);
    $yst = $pdf->GetY();

    $pdf->SetFont($f, 'B', $fd);
    if ($studente['codice_indirizzi'] != 'CLA4') {
        $pdf->CellFitScale( $wp*0.40, 20, "DISCIPLINE", 1, 0, 'C');
        $xad = $pdf->GetX();
        $pdf->CellFitScale( $wp*0.60, 10, "VALUTAZIONE PERIODICA PRIMA FRAZIONE TEMPORALE", 1, 1, 'C', false, '', 1, false, 'T', 'M' );
        $pdf->SetX($xad);
        $pdf->CellFitScale( $wp*0.45, 10, "UNICO", 1, 0, 'C', false, '', 1);
        $pdf->CellFitScale( $wp*0.15, 10, "ORE ASSENZA", 1, 1, 'C', false, '', 1);
    } else {
        $pdf->CellFitScale( $wp*0.34, 20, "DISCIPLINE", 1, 0, 'C');
        $xad = $pdf->GetX();
        $pdf->CellFitScale( $wp*0.66, 10, "VALUTAZIONE PERIODICA", 1, 1, 'C', false, '', 1, false, 'T', 'M' );
        $pdf->SetX($xad);
        $pdf->CellFitScale( $wp*0.22, 10, "1°TRIMESTRE", 1, 0, 'C', false, '', 1);
        $pdf->CellFitScale( $wp*0.22, 10, "2°TRIMESTRE ", 1, 0, 'C', false, '', 1);
        $pdf->CellFitScale( $wp*0.22, 10, "3°TRIMESTRE ", 1, 1, 'C', false, '', 1);
    }
    $pdf->SetXY($x2, $yst);
    $pdf->SetFont($f, 'B', 8);
    $pdf->CellFitScale( $wp*0.28, 20, "DISCIPLINE", 1, 0, 'C');
    $xad = $pdf->GetX();
    $pdf->MultiCell( $wp*0.35, 11, "SCRUTINIO FINALE", 1, 'C', $fill = false, $ln = 0, $x = '', $y = '', $reseth = true, $stretch = 0, $ishtml = false, $autopadding = true, 11, $valign = 'M', $fitcell = false );
    $pdf->MultiCell( $wp*0.0, 11, "", 'TLR', 'C', $fill = false, $ln = 1, $x = '', $y = '', $reseth = true, $stretch = 0, $ishtml = false, $autopadding = true, 11, $valign = 'M', $fitcell = false );
    $pdf->CellFitScale( $wp*0.01, 10, "", 0, 0, 'C');
    $pdf->SetX($xad);
    $pdf->MultiCell( $wp*0.20, 9, "VOTO UNICO\n(in lettere)", 1, 'C', $fill = false, $ln = 0, $x = '', $y = '', $reseth = true, $stretch = 0, $ishtml = false, $autopadding = true, 9, $valign = 'M', $fitcell = false );
    $pdf->MultiCell( $wp*0.15, 9, "ORE assenza", 1, 'C', $fill = false, $ln = 0, $x = '', $y = '', $reseth = true, $stretch = 0, $ishtml = false, $autopadding = true, 9, $valign = 'M', $fitcell = false );
    $xann = $pdf->GetX();
    $pdf->CellFitScale(0, 9, "", 0, 1, 'C');

    $pdf->SetFont($f, '', 8);
//    echo_debug($arr_voti);
    foreach($arr_voti as $voto)
    {
        $vp1 = $vp2 = $vp3 = $vp4 = $ass1 = $ass2 = $ass3 = $ass4 =
            $vpl1 = $vpl2 = $vpl3 = $vpl4 = '';

        $mat_p1 = $voto['p1'] ;
        $mat_p2 = $voto['p2'] ;
        $mat_p3 = $voto['p3'] ;
        $mat_p4 = $voto['p4'] ;
        $vp1 = $mat_p1['voto_pagellina'];
        $vp2 = $mat_p2['voto_pagellina'];
        $vp3 = $mat_p3['voto_pagellina'];
        $vp4 = $mat_p4['voto_pagellina'];

        foreach ($mat_p1['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $mat_p1['voto_pagellina']) {
                $vpl1 = $significato['valore_pagella'];
            }
        }
        foreach ($mat_p2['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $mat_p2['voto_pagellina']) {
                $vpl2 = $significato['valore_pagella'];
            }
            if ($significato['voto'] == $mat_p3['voto_pagellina']) {
                $vpl3 = $significato['valore_pagella'];
            }
        }
        foreach ($mat_p4['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $mat_p4['voto_pagellina']) {
                $vpl4 = $significato['valore_pagella'];
            }
        }

        $ass1 = traduci_minuti_in_ore_minuti($mat_p1['ore_assenza'], 'ORE_ARROTONDATE');
        $ass2 = traduci_minuti_in_ore_minuti($mat_p2['ore_assenza'], 'ORE_ARROTONDATE');
        $ass3 = traduci_minuti_in_ore_minuti($mat_p3['ore_assenza'], 'ORE_ARROTONDATE');
        $ass4 = traduci_minuti_in_ore_minuti($mat_p4['ore_assenza'], 'ORE_ARROTONDATE');
        if ($voto['p1']['tipo_materia'] == 'CONDOTTA') {
            $ass1 = $ass2 = $ass3 = $ass4 = '';
        }

        $desc = $mat_p1['descrizione'];

        if ($studente['codice_indirizzi'] != 'CLA4') {
            $pdf->MultiCell($wp*.40, 11, $desc, 1, 'L', false, 0, '', '', true, 0, false, true, 11, 'M');
            $pdf->MultiCell($wp*.15, 11, $vp1, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
            $pdf->MultiCell($wp*.30, 11, $vpl1, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
            $pdf->MultiCell($wp*.15, 11, $ass1, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
        } else {
            $pdf->MultiCell($wp*.34, 11, $desc, 1, 'L', false, 0, '', '', true, 0, false, true, 11, 'M');
            $pdf->MultiCell($wp*.05, 11, $vp1, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
            $pdf->MultiCell($wp*.17, 11, $vpl1, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
            $pdf->MultiCell($wp*.05, 11, $vp2, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
            $pdf->MultiCell($wp*.17, 11, $vpl2, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
            $pdf->MultiCell($wp*.05, 11, $vp3, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
            $pdf->MultiCell($wp*.17, 11, $vpl3, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
        }
        $pdf->SetX($x2);
        $pdf->MultiCell($wp*.28, 11, $desc, 1, 'L', false, 0, '', '', true, 0, false, true, 11, 'M');
        $pdf->MultiCell($wp*.20, 11, $vpl4, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
        $pdf->MultiCell($wp*.15, 11, $ass4, 1, 'C', false, 0, '', '', true, 0, false, true, 11, 'M');
        $pdf->CellFitScale( $wp*0.01, 11, "", 0, 1, 'C');
    }

    $yfntab = $pdf->GetY();
    $pdf->writeHTMLCell(0, $pdf->GetY()-$yst, $xann, $yst, "", 1, 1);
    $pdf->SetXY($xann, $yst);
    $pdf->SetFont($f, 'B', $fd);
    $pdf->CellFitScale( 0, 11, "ESAMI", 1, 1, 'C');
    $pdf->SetX($xann);
    $pdf->MultiCell(0, 9, "VOTO UNICO (in lettere)", 1, 'C', false, 1, '', '', true, 0, false, true, 10, 'M');
    $pdf->SetXY($xann, $yst+42);
    $pdf->SetFont($f, 'B', $fd+5);
    $pdf->MultiCell(0, 14, "CREDITO\nSCOLASTICO", 1, 'C', 0, 1);
    $pdf->SetX($xann);
    $pdf->SetFont($f, '', $fd);
    $pdf->MultiCell(0, 18, "Media dei voti conseguiti nello scrutinio finale\n$media_voti", 0, 'C', 0, 1);
    $pdf->SetX($xann);
    $pdf->MultiCell(0, 18, "Credito scolastico attribuito nell'anno scolastico in corso\n$crediti_stampa", 0, 'C', 0, 1, '', '', true, 0, false, true, 20, 'T');
    $pdf->SetX($xann);
    $pdf->MultiCell(0, 18, "Totale dei crediti\n$tot_crediti"/*.($tot_crediti>0?$tot_crediti:'')*/, 'B', 'C', 0, 1, '', '', true, 0, false, true, 20, 'T');

	if(
		(
			intval($studente['classe']) == 5
			&& $studente["tipo_indirizzo"] != '1'
			&& $studente["tipo_indirizzo"] != '6'
		)
		||
		(
			intval($studente['classe']) == 3
			&& $studente["tipo_indirizzo"] == '1'
		)
		||
		(
			intval($studente['classe']) == 4
			&& $studente["tipo_indirizzo"] == '5'
		)
		||
		(
			intval($studente['classe']) == 3
			&& ($studente["tipo_indirizzo"] == '2' || $studente["tipo_indirizzo"] == '3')
		)
	) {
//    if ($studente['tipo_indirizzo']==5 && $studente['classe']==4) {
        $pdf->SetXY($xann, $yst+135);
        $pdf->MultiCell(0, 0, $stato_promozione . ' (5)', 0, 'C', 0, 1);
    }
    $pdf->SetY($yfntab);

    if ($studente['codice_indirizzi'] != 'CLA4') {
    } else {
        $pdf->CellFitScale( $wp*.34, 0, '', 0, 0);
        $pdf->CellFitScale( $wp*0.22, 5, $data_p1, 1, 0, 'C', false, '', 1);
        $pdf->CellFitScale( $wp*0.22, 5, $data_p2, 1, 0, 'C', false, '', 1);
        $pdf->CellFitScale( $wp*0.22, 5, $data_p3, 1, 1, 'C', false, '', 1);
    }

    $pdf->ln(5);
    $pdf->SetFont($f, 'B', $fd+5);
    $pdf->CellFitScale($wp, 7,  "ANNOTAZIONI (4)", 1, 0, 'C');
    $pdf->SetX($x2);
    $pdf->CellFitScale($wp, 7,  "ANNOTAZIONI (4)", 1, 1, 'C');
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 20, '', '', $annotazioni_txt1, 1, 0, false, true, 'L');
    $pdf->SetX($x2);
    $pdf->writeHTMLCell($wp, 20, '', '', "$annotazioni_txt2", 1, 1, false, true, 'L');
    $pdf->ln(9);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', $firme_1);
    $pdf->writeHTMLCell($wp, 0, $x2, '', $firme_2);
    //}}} </editor-fold>
    //}}} </editor-fold>
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'         => $id_classe,
    'data_day'          => $data_Day,
    'data_month'        => $data_Month,
    'data_year'         => $data_Year,
    'periodo'           => $periodo,
    'orientamento'      => $orientamento,
    'forza_provenienza' => $forza_provenienza,
    'data_p1_day'          => $data_p1_Day,
    'data_p1_month'        => $data_p1_Month,
    'data_p1_year'         => $data_p1_Year,
    'data_p2_day'          => $data_p2_Day,
    'data_p2_month'        => $data_p2_Month,
    'data_p2_year'         => $data_p2_Year,
    'data_p3_day'          => $data_p3_Day,
    'data_p3_month'        => $data_p3_Month,
    'data_p3_year'         => $data_p3_Year,
    'parametro_stampa_solo_giudizi_sospesi'     => $parametro_stampa_solo_giudizi_sospesi
];

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);

            if ($da_includere != "NO")
            {
                $file_name = $nome_pagella_per_file . '.pdf';
                $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

                if (file_exists($pagella)) {
                    unlink($pagella);
                }
                $pdf->Output($pagella, "F");

                $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
                $content = file_get_contents($pagella);

                // NOTA: presume vi sia al max un solo file per i criteri di ricerca
                if ($file[0]['id']) {
                    messengerUpdateFile($file[0]['id'], $content);
                } else {
                    // Destinatari: Studente + genitori
                    $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                    messengerSaveFile([
                        'content'    => $content,
                        'hidden'     => false,
                        'mime'       => 'application/pdf',
                        'name'       => $file_name,
                        'owner'      => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_stud_per_stampa_sito,
                            'year'   => "{$anno_inizio}/{$anno_fine}",
                            'period' => $periodo_pagella
                        ],
                        'recipients' => $recipients,
                        'tags'       => ['PAGELLE']
                    ]);
                }

                if (file_exists($pagella)) {
                    $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                    unlink($pagella);
                }
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);
            if ($da_includere != "NO") {
                $pdf->Output($file, "F");
            }
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);

            if ($da_includere != "NO") {
                $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
                $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

                if (file_exists($pagella)) {
                    unlink($pagella);
                }
                $pdf->Output($pagella, "F");

                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
                $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

                foreach ($pagelle as $pagella) {
                    $external_data[basename($pagella)] = [
                        'hidden'     => false,
                        'mime'       => 'application/pdf',
                        'name'       => basename($pagella),
                        'class'      => $classe_studente,
                        'owner'      => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_stud_per_stampa_sito,
                            'year'   => "{$anno_inizio}/{$anno_fine}",
                            'period' => $descrizione_periodo
                        ],
                        'recipients' => $recipients,
                        'tags'       => ['PAGELLE']
                    ];
                }
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
