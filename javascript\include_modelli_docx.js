var url_api = '../next-api/v1/';
var current_user = document.getElementsByName('current_user')[0].value;
var current_key = document.getElementsByName('current_key')[0].value;
var db_key_element = document.getElementsByName('db_key')[0];
var db_key = db_key_element ? db_key_element.value : null;

function messageDisplay(messaggio, tipo=null, durata=4000) {
    let mess = $('#message');

    mess.html(messaggio);

    if (tipo === true) {
        mess.addClass("message-success").removeClass("message-failure");
    } else if (tipo === false) {
        mess.addClass("message-failure").removeClass("message-success");
    } else {
        mess.removeClass("message-success message-failure");
    }

    mess.show().delay(durata).fadeOut().queue(function() {
        $(this).removeClass("message-success message-failure").dequeue();
    });
}

function toggleCodaFunzioni(id_coda, attiva_funzioni) {
    var row = $(`#coda_pagella_word_${id_coda}`);
    if (row.length > 0) {
        if (attiva_funzioni) {
            // Add transition for background-color before removing the class
            row.css('background-color', '');
            // row.css('pointer-events', 'auto'); // Re-enable pointer events
            // Re-enable buttons
            row.find(`#btn_pubblica_${id_coda}`).prop('disabled', false);
            row.find(`#btn_spubblica_${id_coda}`).prop('disabled', false);
            row.find(`#btn_elimina_${id_coda}`).prop('disabled', false);
            row.find(`#btn_modifica_${id_coda}`).prop('disabled', false);
        } else {
            row.css('background-color', '#f9f59fc2');
            // row.css('pointer-events', 'none'); // Disable pointer events to block interactions
            // Disable buttons related to the row
            row.find(`#btn_pubblica_${id_coda}`).prop('disabled', true);
            row.find(`#btn_spubblica_${id_coda}`).prop('disabled', true);
            row.find(`#btn_elimina_${id_coda}`).prop('disabled', true);
            row.find(`#btn_modifica_${id_coda}`).prop('disabled', true);
        }
    }
}

function validaFormStampa(elem) {
    var classi_checked = $(elem).closest('form').find("input[name='mat_classi[]']:checked").map(function() {
        return this.value;
    }).get();

    if (typeof classi_checked === 'undefined' || classi_checked.length == 0) {
        alert('Seleziona almeno una classe');
        return false;
    }
    if (classi_checked.length > 1) {
        alert('Seleziona solo una classe per questa opzione');
        return false;
    }

    return true;
}

function downloadFile(fileId) {
    if (fileId > 0) {
        return new Promise((resolve, reject) => {
            $.ajax({
                type: "POST",
                data: {
                    "authorization": current_key,
                    "direct": true,
                    "only_link": true,
                    "applicazione": "mastercom",
                },
                url: url_api + "download/" + fileId,
                headers: { 'Authorization': current_key },
                success: function (data) {
                    const link = document.createElement('a');
                    link.href = data;
                    link.target = '_blank';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    resolve(data);
                },
                error: function (data) {
                    alert('Errore: ' + (data.responseJSON?.message || data.statusText));
                    reject(data);
                },
                complete: function (data) {
                    resolve(data);
                }
            });
        });
    } else {
        alert('File non valido.');
        return;
    }
}

function validaEliminaCoda(id_coda) {
    // check if stato non pubblicato
    var data_pubblicazione_coda = $(`#data_pubblicazione_${id_coda}`).val();
    if (data_pubblicazione_coda > 0) {
        alert('Non puoi eliminare una coda già pubblicata');
        return false;
    }

    if (!confirm('Sei sicuro di voler eliminare questa coda?')) {
        return false;
    }
    
    toggleCodaFunzioni(id_coda, false);
    return new Promise((resolve, reject) => {
        $.ajax({ type: "POST",
            url: "ajat_registro_light.php",
            data: {
                "form_azione": "elimina_coda",
                "current_user": current_user,
                "current_key": current_key,
                "id_coda": id_coda
            },
            cache: false,
            success: function (data) {
                console.log('Coda eliminata con successo:', data);
                // Remove the row dynamically
                var row = $(`#coda_pagella_word_${id_coda}`);
                if (row.length > 0) {
                    row.fadeOut(300, function() {
                        $(this).remove();
                    });
                }

                messageDisplay('Coda eliminata con successo', true);
                resolve(data);
            },
            error: function (data) {
                messageDisplay('Errore nell\'eliminazione della coda: ' + (data.responseJSON?.message || data.statusText), false);
                reject(data);
            },
            complete: function (data) {
                toggleCodaFunzioni(id_coda, true);
                resolve(data);
            }
        });
    });
}

function disabilitaBtnDefault(id_coda, disabilita) {
    if (disabilita) {
        $('#btn_pubblica_' + id_coda).attr('disabled', true).addClass('btn_disabled');
        $('#btn_spubblica_' + id_coda).attr('disabled', false).removeClass('btn_disabled');
        $('#btn_elimina_' + id_coda).attr('disabled', true).addClass('btn_disabled');
        $('#btn_modifica_' + id_coda).attr('disabled', true).addClass('btn_disabled');
    } else {
        $('#btn_pubblica_' + id_coda).attr('disabled', false).removeClass('btn_disabled');
        $('#btn_spubblica_' + id_coda).attr('disabled', true).addClass('btn_disabled');
        $('#btn_elimina_' + id_coda).attr('disabled', false).removeClass('btn_disabled');
        $('#btn_modifica_' + id_coda).attr('disabled', false).removeClass('btn_disabled');
    }
}
 
function validaPubblicaDocumentiCoda(id_coda, tag_destinazione = 'PAGELLE', showMessage = true) {
    toggleCodaFunzioni(id_coda, false);
    return new Promise((resolve, reject) => {
        $.ajax({ type: "POST",
            url: "ajat_registro_light.php",
            data: {
                "form_azione": "pubblica_coda",
                "current_user": current_user,
                "current_key": current_key,
                "id_coda": id_coda,
                "tag_destinazione": [tag_destinazione]
            },
            cache: false,
            success: function (data) {
                console.log('Coda pubblicata con successo:', data);
                $(`#data_pubblicazione_${id_coda}`).val(Math.floor(Date.now() / 1000)).trigger('onchange');
                if (showMessage) {messageDisplay((data.responseJSON?.message || data.statusText || data.message), true); }
                resolve(data);
            },
            error: function (data) {
                if (showMessage) {messageDisplay('Errore nella pubblicazione: ' + (data.responseJSON?.message || data.statusText), false);}
                reject(data);
            },
            complete: function (data) {
                toggleCodaFunzioni(id_coda, true);
                resolve(data);
            }
        });
    });
}

function validaSpubblicaDocumentiCoda(id_coda, tag_destinazione = 'PAGELLE') {
    toggleCodaFunzioni(id_coda, false);
    return new Promise((resolve, reject) => {
        $.ajax({ type: "POST",
            url: "ajat_registro_light.php",
            data: {
                "form_azione": "spubblica_coda",
                "current_user": current_user,
                "current_key": current_key,
                "id_coda": id_coda,
                "tag_destinazione": [tag_destinazione]
            },
            cache: false,
            success: function (data) {
                console.log('Coda spubblicata con successo:', data);
                $(`#data_pubblicazione_${id_coda}`).val(0).trigger('onchange');
                
                messageDisplay((data.responseJSON?.message || data.statusText || data.message), true);
                resolve(data);
            },
            error: function (data) {
                messageDisplay('Errore nella spubblicazione: ' + (data.responseJSON?.message || data.statusText), false);
                reject(data);
            },
            complete: function (data) {
                toggleCodaFunzioni(id_coda, true);
                resolve(data);
            }
        });
    });
}

function getCoda(id_coda, esteso = false) {
    console.log('Coda da recuperare:', id_coda);
    return new Promise((resolve, reject) => {
        $.ajax({
            type: "GET",
            url: "ajat_registro_light.php",
            data: {
                "form_azione": "get_coda",
                "current_user": current_user,
                "current_key": current_key,
                "id_coda": id_coda,
                "esteso": esteso
            },
            cache: false,
            success: function (data) {
                const coda = data.coda;
                resolve(coda);
            },
            error: function (data) {
                console.error('Errore nel recupero della coda:', data);
                alert('Errore: ' + (data.responseJSON?.message || data.statusText));
            },
            complete: function (data) {
                console.log('Richiesta completata call coda:', data);
                // Esegui operazioni al termine della richiesta
                resolve(data);
            }
        });
    });
}

function validaFormCode(elem) {
    let form = $(elem).closest('form');
    var classi_checked = form.find("input[name='mat_classi[]']:checked").map(function() {
        return this.value;
    }).get();

    if (typeof classi_checked === 'undefined' || classi_checked.length == 0) {
        alert('seleziona almeno una classe');
        return false;
    }

    return new Promise((resolve, reject) => {
        var list_input_parametri_pers = form.find("[name^='parametri_personalizzati[']");
        var parametri_pers = [];
        list_input_parametri_pers.each(function() {
            var input = $(this);
            // Extract the key name from the input's name attribute
            var key = input.attr('name').match(/\[([^\]]+)\]/)[1];
            var value;
            if (input.is('select')) {
                value = input.find('option:selected').val();
            } else if (input.is(':checkbox')) {
                value = input.is(':checked') ? input.val() : null;
            } else {
                value = input.val();
            }
            parametri_pers.push({ key: key, value: value });
        });

        $.ajax({ type: "POST",
            url: "ajat_registro_light.php",
            data: {
                "form_azione": "inserisci_code",
                "current_user": current_user,
                "current_key": current_key,
                "mat_classi": classi_checked,
                "periodo": form.find("select[name='periodo']").val(),
                "parametri_personalizzati": parametri_pers,
                "tipo_abbinamento": form.find("input[name='tipo_abbinamento']").val(),
                "tipo_pagella": form.find("input[name='tipo_pagella']").val(),
                "tipo_stampa": form.find("input[name='tipo_stampa']").val()
                },
            cache: false,
            complete: function(response) {
                resp = response.responseJSON;
                new_ids = resp.ids_inserite;
                mod_ids = resp.ids_aggiornate;

                console.log('mod_ids', mod_ids);
                for (let i = 0; i < mod_ids.length; i++) {
                    getCoda(mod_ids[i], true).then(codaData => {
                        // Aggiorna i valori degli input all'interno del contenitore con id="parametri_stampa_{codaData.id_coda}"
                        const parametriContainer = $(`#parametri_stampa_${codaData.id_coda}`);
                        const parametriContainer_convertito = $(`#parametri_stampa_${codaData.id_coda}_convertito`);
                        if (parametriContainer.length > 0) {
                            if (codaData.parametri_stampa && typeof codaData.parametri_stampa === 'object') {
                                parametriContainer.val('');
                                parametriContainer_convertito.val('');
                                const lines = [];
                                Object.entries(codaData.parametri_stampa).forEach(([key, value]) => {
                                    lines.push(`${key}: ${value}`);
                                });
                                parametriContainer.val(lines.join('<br>')).trigger('onchange');
                                parametriContainer_convertito.val(codaData.parametri_personalizzati_txt).trigger('onchange');
                            }
                        }
                    }).catch(err => {
                        console.error('Errore durante l\'aggiornamento dei parametri:', err);
                    });
                }

                console.log('new_ids', new_ids);
                if (Array.isArray(new_ids) && new_ids.length > 0) {
                    new_ids.forEach(id_coda => {
                        getCoda(id_coda, true).then(coda => {
                            if (coda) {
                                console.log('Coda recuperata:', coda);
                                aggiungiRigheCode(coda);
                            }
                        }).catch(err => {
                            console.error('Errore durante il recupero della coda:', err);
                        });
                    });
                }

                messageDisplay(resp.message, true);
                resolve(response.responseJSON);
            }
       });
    });
}

function aggiornaCoda(id_coda) {
    getCoda(id_coda, true).then(codaData => {
        // Aggiorna i valori degli input all'interno del contenitore con id="parametri_stampa_{id_coda}"
        const parametriContainer = $(`#parametri_stampa_${id_coda}`);
        if (parametriContainer.length > 0) {
            parametriContainer.val('');
            if (codaData.parametri_stampa && typeof codaData.parametri_stampa === 'object') {
                const lines = [];
                Object.entries(codaData.parametri_stampa).forEach(([key, value]) => {
                    lines.push(`${key}: ${value}`);
                });
                parametriContainer.val(lines.join('<br>'));
            }
            parametriContainer.trigger('onchange');
        }
        const parametriContainer_convertito = $(`#parametri_stampa_${codaData.id_coda}_convertito`);
        parametriContainer_convertito.val('');
        parametriContainer_convertito.val(codaData.parametri_personalizzati_txt).trigger('onchange');

        // Aggiorna lo stato della coda nell'interfaccia, se presente
        const statoContainer = $(`#stato_${id_coda}`);
        statoContainer.val(codaData.stato).text(codaData.stato).trigger('onchange');
        
        // Aggiorna i valori degli input hidden relativi all'elaborazione e pubblicazione, se presenti
        $(`#inizio_elaborazione_${id_coda}`).val(codaData.inizio_elaborazione).trigger('onchange');
        $(`#fine_elaborazione_${id_coda}`).val(codaData.fine_elaborazione).trigger('onchange');
        $(`#data_pubblicazione_${id_coda}`).val(codaData.data_pubblicazione).trigger('onchange');
        
        // Aggiorna il pulsante di download
        const btnDownload = $(`#btn_download_${id_coda}`);
        btnDownload.off('click').attr('onclick', `downloadFile(${codaData.id_file})`);
        if (codaData.id_file) {
            btnDownload.prop('disabled', false).removeClass('btn_disabled');
        } else {
            btnDownload.prop('disabled', true).addClass('btn_disabled');
            $(`#btn_pubblica_${id_coda}`).prop('disabled', true).addClass('btn_disabled');
        }

        // Puoi aggiornare altri valori dell'interfaccia qui se necessario


    }).catch(err => {
        console.error('Errore durante il recupero della coda:', err);
    });
}


function estraiCodeModello() {
    var tipo_pagella = $("input[name='tipo_pagella']").val();
    if (!tipo_pagella) {
        reject('tipo_pagella non trovato');
        return;
    }
    return new Promise((resolve, reject) => {
        $.ajax({
            type: "GET",
            url: "ajat_registro_light.php",
            data: {
                "form_azione": "estrai_code_modello",
                "current_user": current_user,
                "current_key": current_key,
                "tipo_stampa": tipo_pagella,
            },
            cache: false,
            success: function (data) {
                resolve(data);
            },
            error: function (data) {
                console.error('Errore nel recupero delle code del modello '+id_modello+':', data);
            },
            complete: function (data) {
                console.log('Richiesta completata call estraiCodeModello:', data);
                resolve(data);
            }
        });
    });
}


function aggiungiRigheCode(coda) {
    // Costruisci la riga HTML come da template Smarty
    let statoText = '-';
    let statoClass = 'span_status';
    if (coda.data_pubblicazione_ext && coda.data_pubblicazione_ext !== "") {
        statoText = 'Pubblicato';
        statoClass += ' sfondo_verde_light';
    } else if (!coda.stato || coda.stato === "") {
        statoText = 'In Attesa';
        statoClass += ' btn-waiting';
    } else if (coda.stato === "IN CORSO") {
        statoText = 'In Corso';
        statoClass += ' btn-in-progress';
    } else if (coda.stato === "COMPLETATO") {
        statoText = 'Completato';
        statoClass += ' btn-completed';
    } else if (coda.stato === "ERRORE") {
        statoText = 'Errore';
        statoClass += ' btn-error';
    } else {
        statoText = coda.stato || '-';
    }

    // Tooltip text
    let tooltipText = '';
    if (coda.inizio_elaborazione_ext) tooltipText += `Inizio: ${coda.inizio_elaborazione_ext}<br>`;
    if (coda.fine_elaborazione_ext) tooltipText += `Fine: ${coda.fine_elaborazione_ext}<br>`;
    if (coda.data_pubblicazione_ext) tooltipText += `Pubblicato: ${coda.data_pubblicazione_ext}<br>`;

    // Parametri stampa
    let parametriStampaHtml = '';
    let parametriStampaValue = '';
    if (coda.parametri_stampa && typeof coda.parametri_stampa === 'object') {
        Object.entries(coda.parametri_stampa).forEach(([key, value]) => {
            parametriStampaValue += `${key}: ${value}; `;
        });
    }
    parametriStampaHtml = coda.parametri_personalizzati_txt && coda.parametri_personalizzati_txt.trim() !== '' 
        ? coda.parametri_personalizzati_txt 
        : 'Nessun parametro personalizzato';

    // Costruisci la riga
    const newRow = `
    <tr class="grid-row" id="coda_pagella_word_${coda.id_coda}">
        <td class="grid-cell"><input type="checkbox" name="sel_code[${coda.id_coda}]"></td>
        <td class="grid-cell">${coda.classe || ''}${coda.sezione || ''} ${coda.descrizione_indirizzi || ''}</td>
        <td class="grid-cell">${coda.periodo_desc || ''}</td>
        <td class="grid-cell">
            <span id="btn_stato_${coda.id_coda}" class="${statoClass}">${statoText}</span>
            <span class="tooltip">&#9432;
                <span id="coda_pagella_word_${coda.id_coda}_stato_tooltiptext" class="tooltiptext" style="padding: 1px 2px;">
                    ${tooltipText}
                </span>
            </span>
            <input type="hidden"
                id="stato_${coda.id_coda}" 
                value="${coda.stato || ''}" 
                onchange="
                    var btn = document.getElementById('btn_stato_${coda.id_coda}');
                    var stato = this.value;
                    var dataPub = document.getElementById('data_pubblicazione_${coda.id_coda}').value;
                    if (dataPub > 0) {
                    } else {
                        btn.classList.remove('btn-waiting', 'btn-in-progress', 'btn-completed', 'btn-error');
                        if (stato === '') {
                            btn.textContent = 'In Attesa';
                            btn.classList.add('btn-waiting');
                        } else if (stato === 'IN CORSO') {
                            btn.textContent = 'In Corso';
                            btn.classList.add('btn-in-progress');
                        } else if (stato === 'COMPLETATO') {
                            btn.textContent = 'Completato';
                            btn.classList.add('btn-completed');
                        } else if (stato === 'ERRORE') {
                            btn.textContent = 'Errore';
                            btn.classList.add('btn-error');
                        } else {
                            btn.textContent = stato || '-';
                        }
                    }
                "
            />
            <input type="hidden" id="inizio_elaborazione_${coda.id_coda}" value="${coda.inizio_elaborazione || ''}" 
                onchange="
                    document.getElementById('coda_pagella_word_${coda.id_coda}_stato_tooltiptext').innerHTML = 
                        (this.value > 0  ? 'Inizio: ' + new Date(this.value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '') + 
                        (document.getElementById('data_pubblicazione_${coda.id_coda}').value > 0 ? 'Pubblicato: ' + new Date(document.getElementById('data_pubblicazione_${coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '');
                "
            />
            <input type="hidden" id="fine_elaborazione_${coda.id_coda}" value="${coda.fine_elaborazione || ''}" 
                onchange="
                    document.getElementById('coda_pagella_word_${coda.id_coda}_stato_tooltiptext').innerHTML = 
                        (document.getElementById('inizio_elaborazione_${coda.id_coda}').value > 0  ? 'Inizio: ' + new Date(document.getElementById('inizio_elaborazione_${coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '') +
                        (this.value > 0  ? 'Fine: ' + new Date(this.value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>'  : '') +
                        (document.getElementById('data_pubblicazione_${coda.id_coda}').value > 0 ? 'Pubblicato: ' + new Date(document.getElementById('data_pubblicazione_${coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '');
                "
            />
            <input type="hidden" id="data_pubblicazione_${coda.id_coda}" value="${coda.data_pubblicazione || ''}" 
                onchange="
                    if (this.value > 0) {
                        var btn = document.getElementById('btn_stato_${coda.id_coda}');
                        btn.classList.add('sfondo_verde_light');
                        btn.textContent = 'Pubblicato';
                        disabilitaBtnDefault('${coda.id_coda}', true);
                    } else {
                        var btn = document.getElementById('btn_stato_${coda.id_coda}');
                        btn.classList.remove('sfondo_verde_light');
                        var stato = document.getElementById('stato_${coda.id_coda}').value;
                        btn.classList.remove('btn-waiting', 'btn-in-progress', 'btn-completed', 'btn-error');
                        if (stato === '') {
                            btn.textContent = 'In Attesa';
                            btn.classList.add('btn-waiting');
                        } else if (stato === 'IN CORSO') {
                            btn.textContent = 'In Corso';
                            btn.classList.add('btn-in-progress');
                        } else if (stato === 'COMPLETATO') {
                            btn.textContent = 'Completato';
                            btn.classList.add('btn-completed');
                        } else if (stato === 'ERRORE') {
                            btn.textContent = 'Errore';
                            btn.classList.add('btn-error');
                        } else {
                            btn.textContent = stato || '-';
                        }
                        disabilitaBtnDefault('${coda.id_coda}', false);
                    }
                    document.getElementById('coda_pagella_word_${coda.id_coda}_stato_tooltiptext').innerHTML = 
                        (document.getElementById('inizio_elaborazione_${coda.id_coda}').value > 0 ? 'Inizio: ' + new Date(document.getElementById('inizio_elaborazione_${coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '') +
                        (document.getElementById('fine_elaborazione_${coda.id_coda}').value > 0? 'Fine: ' + new Date(document.getElementById('fine_elaborazione_${coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>'  : '') +
                        (this.value > 0 ? 'Pubblicato: ' + new Date(this.value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '');"
            />
        </td>
        <td class="grid-cell">
            <span>
                <a href="javascript:void(0);" 
                    onclick="var el = document.getElementById('coda_pagella_word_${coda.id_coda}_parametri_stampa_text'); el.style.display = (el.style.display === 'none' ? 'block' : 'none'); this.textContent = (el.style.display === 'none' ? '[+]' : '[-]');"
                    style="text-decoration: underline; cursor: pointer;">[+]</a>
                <span id="coda_pagella_word_${coda.id_coda}_parametri_stampa_text" style="display: none; margin-left: 5px;">
                    ${parametriStampaHtml}
                </span>
            </span>
            <input type="hidden" 
                id="parametri_stampa_${coda.id_coda}" 
                value="${parametriStampaValue}"/>
            <input type="hidden" 
                id="parametri_stampa_${coda.id_coda}_convertito" 
                value="${coda.parametri_personalizzati_txt}"
                onchange="document.getElementById('coda_pagella_word_${coda.id_coda}_parametri_stampa_text').innerHTML = this.value"/>
        </td>
        <td class="grid-cell">
            <span style="display: flex; gap: 6px; flex-wrap: wrap;">
            <button 
                id="btn_download_${coda.id_coda}"
                type="button" 
                disabled
                class="btn_flat_new sfondo_azzurro testo_bianco ripples hover_opacity btn_responsive_padding" 
                title="Download"
                onclick="downloadFile(${coda.id_file})">
                &nbsp;<i class="fa fa-download"></i>&nbsp;
            </button>  
            <button 
                id="btn_pubblica_${coda.id_coda}"
                disabled
                type="button" 
                class="btn_flat_new sfondo_verde_light testo_bianco ripples hover_opacity btn_responsive_padding" 
                title="Pubblica"
                onclick="validaPubblicaDocumentiCoda('${coda.id_coda}', '${coda.destinazione_pubblicazione}')">
                &nbsp;<i class="fas fa-cloud-upload-alt"></i>&nbsp;
            </button>
            <button 
                id="btn_spubblica_${coda.id_coda}"
                disabled
                type="button" 
                class="btn_flat_new sfondo_azzurro testo_bianco ripples hover_opacity btn_responsive_padding" 
                title="Revoca pubblicazione" 
                style="background-color: #ffc107c7; color: #000000cc;"
                onclick="validaSpubblicaDocumentiCoda('${coda.id_coda}', '${coda.destinazione_pubblicazione}')">
                &nbsp;<i class="far fa-eye-slash"></i>&nbsp;
            </button>
            <button 
                id="btn_modifica_${coda.id_coda}"
                type="button" 
                class="btn_flat_new sfondo_arancio_new testo_bianco ripples hover_opacity btn_responsive_padding" 
                title="Modifica parametri coda"
                onclick="validaModificaCoda('${coda.id_coda}', 
                    '${(coda.classe || '').replace(/'/g, "\\'")}${(coda.sezione || '').replace(/'/g, "\\'")} ${(coda.descrizione_indirizzi || '').replace(/'/g, "\\'")}', 
                    '${(coda.periodo_desc || '').replace(/'/g, "\\'")}',
                    '${(coda.tipo_abbinamento || '').replace(/'/g, "\\'")}')">
                &nbsp;<i class="fas fa-edit"></i>&nbsp;
            </button> 
            <button 
                id="btn_elimina_${coda.id_coda}"
                type="button" 
                class="btn_flat_new sfondo_rosso testo_bianco ripples hover_opacity btn_responsive_padding"
                title="Elimina coda"
                onclick="validaEliminaCoda('${coda.id_coda}')">
                &nbsp;<i class="fa fa-trash"></i>&nbsp;
            </button>
            </span>
        </td>
    </tr>
    `;

    // Se la riga "nessuna coda" esiste, eliminala
    if ($('#riga_nessuna_coda').length > 0) {
        $('#riga_nessuna_coda').remove();
    }
    // Aggiungi la riga in fondo al tbody
    $('#tbody_coda_pagella_word').append(newRow);

}


function aggiornaCodeDaTimer(message = false) {
    const startTime = Date.now();
    let id_code = [];
    $('#tbody_coda_pagella_word tr[id^="coda_pagella_word_"]').each(function() {
        let rowId = $(this).attr('id');
        let id_coda = rowId.replace('coda_pagella_word_', '');
        id_code.push(id_coda);
    });

    // estrai code del modello
    estraiCodeModello().then(data => {
        // data is an array of coda objects, each with key id_coda and other fields

        // Get all id_code already present in the table
        const existingIds = new Set(id_code.map(Number));
        // Get all ids from the new data
        const gotIds = new Set(
            Object.values(data).map(coda => Number(coda.id_coda))
        );
        console.log('existingIds:', existingIds);
        console.log('gotIds:', gotIds);

        // Code to add: present in gotIds but not in existingIds
        const codeToAdd = Array.from(gotIds).filter(id => !existingIds.has(id));
        // Code to update: present in both
        const codeToUpdate = Array.from(gotIds).filter(id => existingIds.has(id));
        // Code to delete: present in existingIds but not in gotIds
        const codeToDelete = Array.from(existingIds).filter(id => !gotIds.has(id));


        console.log('Code da aggiungere:', codeToAdd);
        console.log('Code da aggiornare:', codeToUpdate);   
        console.log('Code da eliminare:', codeToDelete);

        if (codeToUpdate.length > 0) {
            console.log('Code da aggiornare:', codeToUpdate);
            // Qui puoi aggiornare le code esistenti
            codeToUpdate.forEach(id_coda => {
                aggiornaCoda(id_coda);
            });
        }
        if (codeToDelete.length > 0) {
            console.log('Code da eliminare:', codeToDelete);
            // Qui puoi rimuovere le code dalla tabella o notificare l'utente
            codeToDelete.forEach(id_coda => {
                // Remove the row dynamically
                var row = $(`#coda_pagella_word_${id_coda}`);
                if (row.length > 0) {
                    row.fadeOut(300, function() {
                        $(this).remove();
                    });
                }
            });
        }
        
        // Optionally, do something with these arrays
        if (codeToAdd.length > 0) {
            // Aggiungi nuove code alla tabella
            codeToAdd.forEach(id_coda => {
                const coda = data[id_coda] || (Array.isArray(data) ? data.find(c => Number(c.id_coda) === Number(id_coda)) : null);
                console.log('data json to take fields: ',coda)
    
                // Salta se la coda non è valida
                if (!coda) return;

                aggiungiRigheCode(coda);
            });
        }
    });

    const endTime = Date.now();
    console.log('Aggiornamento code iniziato alle:', new Date(startTime).toLocaleString());
    console.log('Tempo impiegato per aggiornare tutte le code:', (endTime - startTime), 'ms');

    // Update the input with id 'last_update_code' with endTime in local timezone
    const lastUpdateInput = $('#last_update_code');
    const localTime = new Date(endTime).toLocaleString();
    lastUpdateInput.val(localTime).trigger('onchange');
    
    if (message) {
        messageDisplay(message);
    }
}


// Simple table sort for columns 0,1,2
function sortTable(tableId, col) {
    var table = document.getElementById(tableId);
    var tbody = table.tBodies[0];
    var rows = Array.prototype.slice.call(tbody.rows, 0);
    var asc = table.getAttribute('data-sort-col') != col || table.getAttribute('data-sort-dir') !== 'asc';
    rows.sort(function(a, b) {
        var A = a.cells[col].innerText.trim().toUpperCase();
        var B = b.cells[col].innerText.trim().toUpperCase();
        if (A < B) return asc ? -1 : 1;
        if (A > B) return asc ? 1 : -1;
        return 0;
    });
    // Remove all rows
    while (tbody.firstChild) tbody.removeChild(tbody.firstChild);
    // Add sorted rows
    for (var i = 0; i < rows.length; i++) tbody.appendChild(rows[i]);
    table.setAttribute('data-sort-col', col);
    table.setAttribute('data-sort-dir', asc ? 'asc' : 'desc');
}


function apriChiudiPopup(id_oggetto, sfondo = '', modale = false, array_ids = []) {
    if (sfondo !== '') {
        var sfondo_oscurato = document.getElementById(sfondo);
    }
    var popup = document.getElementById(id_oggetto);
    var body = $('body')[0];
    var div_tabella_code = document.getElementById('div_tabella_code');

    if (popup.style.display === 'none') {
        if (modale === true) {
            $('#modal-open').val('SI').trigger('change');
        }
        if (sfondo !== '') {
            //sfondo_oscurato.style.display = '';
            $(sfondo_oscurato).fadeIn(200);
        }
        popup.style.display = 'block';
        body.style.overflow='hidden';
        div_tabella_code.style.overflow='hidden';
    }
    else {
        //popup.style.display = 'none';
        body.style.overflow='visible';
        div_tabella_code.style.overflow='auto';
        $(popup).fadeOut(200);
        if (modale === true) {
            $('#modal-open').val('NO').trigger('change');
        }
        if (sfondo !== '') {
            $(sfondo_oscurato).fadeOut(200);
            //sfondo_oscurato.style.display = 'none';
        }
    }

    if (array_ids.length > 0) {
        $.each(array_ids, function (i, el) {
            $('#' + el).attr("disabled", false);
        });
    }
}

function salvaModificaCode() {
    console.log('salvaModificaCode');
    // Get all input values for modifica_coda_parametri_personalizzati[]
    var parametri_pers = {};
    $('[name^="modifica_coda_parametri_personalizzati["]').each(function() {
        var input = $(this);
        var key = input.attr('name').match(/\[([^\]]+)\]/)[1];
        var value;
        if (input.is('select')) {
            value = input.find('option:selected').val();
        } else if (input.is(':checkbox')) {
            value = input.is(':checked') ? input.val() : null;
        } else {
            value = input.val();
        }
        parametri_pers[key] = value;
    });

    var id_coda = $('#modifica_coda_id_coda').val();
    var tipo_abbinamento = $('#modifica_tipo_abbinamento').val();

    console.log('parametri_pers', parametri_pers);
    return new Promise((resolve, reject) => {
        $.ajax({
            type: "POST",
            url: "ajat_registro_light.php",
            data: {
                "form_azione": "modifica_coda",
                "current_user": current_user,
                "current_key": current_key,
                "id_coda": id_coda,
                "tipo_abbinamento": tipo_abbinamento,
                "parametri_personalizzati": JSON.stringify(parametri_pers)
            },
            cache: false,
            success: function (data) {
                console.log('Coda aggiornata con successo:', data);
                aggiornaCoda(id_coda);
                messageDisplay('Aggiornati i parametri della coda', true);
                resolve(data);
            },
            error: function (data) {
                messageDisplay('Errore nell\'aggiornamento della coda: ' + (data.responseJSON?.message || data.statusText || data.message), false);
                reject(data);
            },
            complete: function (data) {
                apriChiudiPopup('popupGestioneCode', 'sfondo_oscurato', true);
                resolve(data);
            }
        });
    });
}

function validaModificaCoda(id_coda, desc_classe, desc_periodo, tipo_abbinamento) {
    var data_pubblicazione_coda = $(`#data_pubblicazione_${id_coda}`).val();
    if (data_pubblicazione_coda > 0) {
        alert('Non puoi modificare una coda già pubblicata');
        return false;
    }

    $('#modifica_coda_id_coda').val(id_coda);
    $('#modifica_tipo_abbinamento').val(tipo_abbinamento);
    $('#modifica_coda_classe').val(desc_classe).trigger('onchange');
    $('#modifica_coda_periodo').val(desc_periodo).trigger('onchange');

    // Popola i parametri personalizzati nel form di modifica
    getCoda(id_coda, true).then(function(coda) {
        if (coda && coda.parametri_stampa && typeof coda.parametri_stampa === 'object') {
            Object.entries(coda.parametri_stampa).forEach(function([key, value]) {
                // Cerca l'input corrispondente nel form di modifica
                console.log('Popolamento input parametro:', key, value);
                var input = $(`[name="modifica_coda_parametri_personalizzati[${key}]"]`);
                if (input.length > 0) {
                    if (input.is(':checkbox')) {
                        input.prop('checked', !!value);
                    } else if (input.is('select')) {
                        input.val(value).trigger('change');
                    } else {
                        input.val(value);
                    }
                }
            });
        }
    }).finally(function() {
        apriChiudiPopup('popupGestioneCode', 'sfondo_oscurato', false);
    });
}

setInterval(function() {
    aggiornaCodeDaTimer();
}, 5 * 60 * 1000);

function validaPubblicaMassivaDocumentiCoda() {
    console.log('validaPubblicaMassivaDocumentiCoda');

    // Controlla se ci sono code selezionate
    var id_code_checked = [];
    $('#tbody_coda_pagella_word input[name^="sel_code"]:checked').each(function() {
        id_code_checked.push($(this).closest('tr').attr('id').replace('coda_pagella_word_', ''));
    });

    if (id_code_checked.length === 0) {
        messageDisplay('Nessuna coda selezionata', null);
        return false;
    }

    $('#btn_pubblica_massiva_code').prop('disabled', true).addClass('btn_disabled');
    messageAdd = messageAdd2 = '';
    count_pubblicate = 0; count_non_pubblicate = 0;
    let promises_pubblica = [];
    for (let i = 0; i < id_code_checked.length; i++) {
        let id_coda = id_code_checked[i];
        let data_pubblicazione_coda = $(`#data_pubblicazione_${id_coda}`).val();
        if (data_pubblicazione_coda > 0) {
            count_non_pubblicate++; // già pubblicata
        } else {
            let promise = getCoda(id_coda, true).then(function(coda) {
                if (coda) {
                    console.log('Coda da pubblicare:', coda);
                    if (coda.stato === '' || coda.stato === 'IN CORSO') {
                        count_non_pubblicate++;
                        messageAdd2 = '<br>Alcune code non sono completate. Si prega di attendere il completamento dell\'elaborazione.';
                    }  else if (coda.id_file == null || coda.data_pubblicazione > 0) {
                        count_non_pubblicate++; // già pubblicata o non presente file
                    } else {
                        return validaPubblicaDocumentiCoda(id_coda, coda.destinazione_pubblicazione, false)
                            .then(() => {
                                count_pubblicate++;
                            })
                            .catch((data) => {
                                count_non_pubblicate++;
                                // Check if the error message contains the specific text about open scrutini
                                if (
                                    data &&
                                    data.responseJSON &&
                                    typeof data.responseJSON.message === 'string' &&
                                    data.responseJSON.message.includes('Si prega di chiudere lo scrutinio')
                                ) {
                                    messageAdd = '<br>Alcuni scrutini sono ancora aperti. Si prega di chiudere lo scrutinio dalla sezione "Pagelle" (icona mappamondo rosso)';
                                }
                            });
                    }
                }
            }).catch(err => {
                count_non_pubblicate++;
                console.error('Errore durante estrazione coda per la pubblicazione massiva: ', id_coda);
            });
            promises_pubblica.push(promise);
        }
    }

    // quando sono state completate tutte le promesse, mostra il messaggio finale
    Promise.all(promises_pubblica).then(() => {
        $('#btn_pubblica_massiva_code').prop('disabled', false).removeClass('btn_disabled');
        messageDisplay('Pubblicazione massiva completata: '+count_pubblicate+' pubblicate, '+count_non_pubblicate+' non pubblicabili'+messageAdd+messageAdd2, null, 9000);
    });
}
