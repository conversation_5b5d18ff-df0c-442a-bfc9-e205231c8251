<?php
/*
 * Tipo: Pagella Fine Anno Scuola Secondaria di I Grado - istituto margherita ba
 * Nome: mm_123_finale_A3_istitutomargherita_ba_01
 * Richiesta da: istituto margherita ba
 * Data: 14/06/21
 *
 */

/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'mm_123_finale_A3_istitutomargherita_ba_01', 'Pagella Fine Anno Scuola Secondaria di I Grado', 1, 'istitutomargherita-ba', 6);
*/

$orientamento = 'L';
$formato = 'A3';
$periodo_pagella = 'finale';

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'                     => $id_classe,
    'periodo_pagella'               => $periodo_pagella,
    'orientamento'                  => $orientamento,

    'data_attestato_day'            => $data_attestato_Day,
    'data_attestato_month'          => $data_attestato_Month,
    'data_attestato_year'           => $data_attestato_Year,

    'data_attestato_IQ_day'            => $data_attestato_IQ_Day,
    'data_attestato_IQ_month'          => $data_attestato_IQ_Month,
    'data_attestato_IQ_year'           => $data_attestato_IQ_Year,
    'firma_digitale'                => $firma_digitale
];


function genera_stampa(&$pdf, $studente, $parametri_stampa) {

    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $id_classe = $parametri_stampa['id_classe'];
    $orientamento = $parametri_stampa['orientamento'];
    $data_attestato_IQ = $parametri_stampa['data_attestato_IQ_day'] . '/' . $parametri_stampa['data_attestato_IQ_month'] . '/' . $parametri_stampa['data_attestato_IQ_year'];
    $data_attestato = $parametri_stampa['data_attestato_day'] . '/' . $parametri_stampa['data_attestato_month'] . '/' . $parametri_stampa['data_attestato_year'];
    $id_studente = $studente['id_studente'];
    $firma_digitale = $parametri_stampa['firma_digitale'];

    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $provincia_nascita = $stato['descrizione'];
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_pagellina_primo_trimestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 27, $studente['id_studente']);
    $voti_pagella_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, 29, $studente['id_studente']);

    $arr_voti = $comportamento = $giudizi = [];

    $scrutinato = false;
    $monteore_finale = 0;
    $assenze_finale = 0;
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $monteore_finale += $voti_pagella_finale[$id_materia]['monteore_totale'];
        $assenze_finale += $voti_pagella_finale[$id_materia]['ore_assenza'];

        if ($materia['in_media_pagelle'] != 'NV' &&
           !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA', 'RELIGIONE', 'SOSTEGNO', 'OPZIONALE'])
            )
        {
            $arr_voti[$id_materia]['IQ'] = $voti_pagellina_primo_trimestre[$id_materia];
            $arr_voti[$id_materia]['finale'] = $voti_pagella_finale[$id_materia];
        }


        if ( strtoupper($materia['descrizione']) == 'COMPORTAMENTO' )
        {
            $comportamento[$id_materia]['IQ'] = $voti_pagellina_primo_trimestre[$id_materia];
            $comportamento[$id_materia]['finale'] = $voti_pagella_finale[$id_materia];
        }


        foreach ($voti_pagellina_primo_trimestre[$id_materia]['campi_liberi'] as $campo_libero) {
            if (strpos(strtoupper($materia['descrizione']), 'GIUDIZIO GLOBALE') === false) {
                $value =estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value != '') {
                    $giudizi['IQ'] .= $value;
                }
            }
        }

        foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero) {
            if (strpos(strtoupper($materia['descrizione']), 'GIUDIZIO GLOBALE') === false) {
                $value =estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value != '') {
                    $giudizi['finale'] .= $value;
                }
            }
        }


        if ($voti_pagella_finale[$id_materia]['voto_pagellina'] > 0 && $voti_pagella_finale[$id_materia]['voto_pagellina'] != '')
        {
            $scrutinato = true;
        }
    }
    $arr_voti += $comportamento;

    if ($monteore_finale > 0)
    {
        $perc_assenza = round($assenze_finale / $monteore_finale, 2) * 100;
        if ($perc_assenza < 25)
        {
            $validazione_anno = "SI";
        }
        else
        {
            $validazione_anno = ($scrutinato) ? "DEROGA" : "NO";
        }
    }
    else
    {
        $validazione_anno = "SI";
    }

    // Dizionario temporaneo
    $labels = [
        "ministero"                 => "Ministero dell’Istruzione",

        "collegio_barbarigo"        => "COLLEGIO VESCOVILE BARBARIGO",
        "scuola_via"                => "Via Rogati, 17",
        "scuola_comune"             => "35122 PADOVA (PD)",

        "documento_ogg"             => "DOCUMENTO DI VALUTAZIONE",
        "anno_scolastico"           => "Anno <i>Scolastico </i>",

        "alunno"                    => "dell'alunn||min_oa|| ",
        "nascita_luogo"             => "nat||min_oa|| a ",
        "nascita_data"              => "il ",
        "iscritto"                  => "iscritt||min_oa|| presso questo Istituto nella classe ",

        "attestato_titolo"          => "ATTESTATO",
        "attestato_desc1"           => "Visti gli atti d’ufficio e accertato che l’alunn||min_oa||, ai fini della validità dell’anno scolastico (comma 1, art. 11, DL 19 febbraio 2004, n. 59), ",
        "attestato_desc2"           => "ha frequentato le lezioni e le attività didattiche per almeno i ¾ dell’orario personale previsto; si attesta che l'alunn||min_oa||",
        "dirigente"                 => "Il Coordinatore Educativo e Didattico",

        "titolo_pagella"            => "VALUTAZIONI PERIODICHE DEGLI APPRENDIMENTI NELLE DISCIPLINE<br>E DEL COMPORTAMENTO",
        "periodo_primo"             => "Primo Quadrimestre",
        "periodo_finale"            => "Finale",
        "voto"                      => "Voto",
        "ass"                       => "Ass",

        "giudizio"                  => "GIUDIZIO FINALE",
        "attestato_risultato"       => "è stat||min_oa|| ammess||min_oa|| agli esami conclusivi del primo ciclo di<br>istruzione",
        "voto_idoneita"             => " con il Voto di Idoneità: ",

        "monteore"                  => "Monte ore annuo previsto dalla normativa ministeriale: ",
        "monteore_desc"             => "Ai fini della validità dell'anno, per la valutazione è richiesta la frequenza di almeno tre quarti dell'orario annuale personalizzato di ciascun alunno.",
        "monteore_ass"              => "Totale ore di assenza: ",

        "attivita_opzionali"        => "Attività o laboratori opzionali:",



        "scrutinio_desc"            => "Ai fini della validità dell’anno e dell’ammissione allo scrutinio finale, l’alunn||min_oa||:",
        "scrutinio_desc_val_1"      => "ha frequentato per almeno tre quarti dell’orario annuale;",
        "scrutinio_desc_val_2"      => "non ha frequentato per almeno tre quarti dell’orario annuale, ma ha usufruito della deroga;",
        "scrutinio_desc_val_3"      => "non ha frequentato per almeno tre quarti dell’orario annuale.",


        "p1_alunno"                 => "dell’alunn||min_oa|| ",
        "p1_nato"                   => "nat||min_oa|| ",

        "attestato_titolo"          => "ATTESTATO",
        "attestato_valutazione"     => "Vista la valutazione del Consiglio di classe si attesta che l'alunn||min_oa||<br> è stat||min_oa|| ",


        "iscritto_classe"           => "Iscritt||min_oa|| alla classe: ",
        "note_pag4"                 => "(1) La firma può essere omessa ai sensi dell’art. 3, D.to lgs. 12.02.1993, n.39.<br>"
        . "(2) Specifica nota illustrativa di cui all’art. 2, comma 8, del D.P.R. n. 122/2009.<br>"
        . "(3) “ammesso/a (ovvero non ammesso/a) alla classe successiva” oppure “ammesso/a (ovvero non ammesso/a) al successivo grado dell’istruzione obbligatoria”",

        "p4_titolo"                 => "RILEVAZIONE DEI PROGRESSI NELL'APPRENDIMENTO E NELLO SVILUPPO PERSONALE E SOCIALE DELL'ALUNN||max_oa||",


        "p4_testo_firma_genitori"           => "Firma di uno dei genitori o di chi ne fa le veci",
        "attestato_alunno"          => "L'alunn||min_oa|| "

    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
        $studente['esito'] = str_replace('Licenziato', 'Licenziata', $studente['esito']);
        $studente['esito'] = str_replace('licenziato', 'licenziata', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k=>$label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }
    //}}} </editor-fold>


    //{{{ <editor-fold defaultstate="collapsed" desc="pagina 4">
    $font = 'helvetica';
    $font_dim = 9;

    $tbl_header = '<table>
          <tr>
              <td width="20%"><b>'.$studente['cognome'].'</b></td>
              <td width="20%"><b>'.$studente['nome'].'</b></td>
              <td width="25%"><b>'.$studente['codice_fiscale'].'</b></td>
              <td width="20%"><b>'.$studente['codice_meccanografico_secondario'].'</b></td>
              <td width="15%"><b>'.$anno_scolastico_attuale.'</b></td>
          </tr>
          <tr>
              <td><font size="-3">COGNOME</font></td>
              <td><font size="-3">NOME</font></td>
              <td><font size="-3">CODICE FISCALE</font></td>
              <td><font size="-3">CODICE ISTITUTO</font></td>
              <td><font size="-3">ANNO SCOLASTICO</font></td>
          </tr>
      </table>';


    $pdf->AddPage($orientamento);
    $pdf->SetAutoPageBreak("off", 0);
    $y_start = $pdf->getY();
    $page_width = $pdf->getPageWidth();
    $page_dims = $pdf->getPageDimensions();
    $m_sinistro = $page_dims['lm'];
    $m_top = $page_dims['tm'];
    $m_destro = $page_dims['rm'];
    $half_page = $page_width / 2;
    $w_page_parts = $half_page - $m_destro - $m_sinistro;
    $x_start2 = $half_page + $m_sinistro;


    $pdf->SetFont($font, '', $font_dim);
    $pdf->writeHTMLCell($w_page_parts, 7, $m_sinistro, $m_top, $tbl_header, 1);
    $pdf->SetFont($font, '', $font_dim);

    $pdf->SetY($m_top+18);
    $pdf->setCellHeightRatio( 1.25 );
    $pdf->SetFont($font, 'B', $font_dim + 3);
    $pdf->MultiCell( $w_page_parts, 0, $labels['p4_titolo'], 0, $align = 'C', false, 1);
    $pdf->SetFont($font, '', $font_dim);
    $pdf->Ln(4);

    $pdf->SetFont($font, 'B', $font_dim + 2);
    $pdf->Cell($w_page_parts, $font_dim+1, "VALUTAZIONE INTERMEDIA", 1, 1, 'C');
    $pdf->SetFont($font, '', $font_dim);
//    $pdf->setCellPaddings(2);
    $pdf->MultiCell($w_page_parts, 37, $giudizi['IQ'], 1, 'L', false, 1);
//    $pdf->setCellPaddings(1);
    $pdf->Ln(2);

    $pdf->CellFitScale(90, 0, $studente['descrizione_comuni'] . ', lì ' . $data_attestato_IQ, 0, 0);
    $pdf->MultiCell(90, 15, "Il Coordinatore Educativo e Didattico (1)\n".$studente['nome_dirigente'].($firma_digitale == 'SI'? "\n(Documento firmato digitalmente)" : ''), 0, 'C', false, 0, '', '', true, 0, false, true, 15, 'M');
    $pdf->ln(4);
    $pdf->MultiCell( $w_page_parts, 0, "____________________________\n".$labels['p4_testo_firma_genitori'], 0, $align = 'L', false, 1);
    $pdf->ln(5);

    $pdf->SetFont($font, 'B', $font_dim + 2);
    $pdf->Cell($w_page_parts, $font_dim+1, "VALUTAZIONE FINALE", 1, 1, 'C');
    $pdf->SetFont($font, '', $font_dim);
//    $pdf->setCellPaddings(2);
    $pdf->MultiCell($w_page_parts, 37, $giudizi['finale'], 1, 'L', false, 1);
//    $pdf->setCellPaddings(1);
    $pdf->Ln(4);
    $pdf->SetFont($font, 'B', $font_dim);
    $pdf->CellFitScale($w_page_parts, 0, "VALIDITA' DELL'ANNO SCOLASTICO (art. 5 del decreto legislativo 62 del 13 aprile 2017)", 'RLT', 1, 'C');
    $pdf->SetFont($font, '', $font_dim);

    $testo_attestato = "Ai fini della validità dell'anno e dell'ammissione allo scrutinio finale, l'alunn$min_oa:\n";

    switch ($validazione_anno) {
        case 'SI':
            $testo_attestato .= "[X] ha frequentato per almeno tre quarti dell'orario annuale; \n";
            $testo_attestato .= "[   ] non ha frequentato per almeno tre quarti dell'orario annuale, ma ha usufruito della deroga; \n";
            $testo_attestato .= "[   ] non ha frequentato per almeno tre quarti dell'orario annuale. \n";
            break;
        case 'DEROGA':
            $testo_attestato .= "[   ] ha frequentato per almeno tre quarti dell'orario annuale; \n";
            $testo_attestato .= "[X] non ha frequentato per almeno tre quarti dell'orario annuale, ma ha usufruito della deroga; \n";
            $testo_attestato .= "[   ] non ha frequentato per almeno tre quarti dell'orario annuale. \n";
            break;
        case 'NO':
            $testo_attestato .= "[   ] ha frequentato per almeno tre quarti dell'orario annuale; \n";
            $testo_attestato .= "[   ] non ha frequentato per almeno tre quarti dell'orario annuale, ma ha usufruito della deroga; \n";
            $testo_attestato .= "[X] non ha frequentato per almeno tre quarti dell'orario annuale. \n";
            break;
    }
    $pdf->SetFont($font, '', $font_dim);
    $pdf->MultiCell($w_page_parts, 23, $testo_attestato, 1, 'L');


//    if ((int)$studente['classe'] == 3) {
//        $tbl_amm = '<table align="center">
//                <tr>
//                    <td><b>VOTO DI AMMISSIONE</b><br>Voto (in cifre e in lettere)</td>
//                    <td>'.$studente['voto_ammissione_medie'].'/10</td>
//                    <td>'.strtoupper(traduci_numero_in_lettere($studente['voto_ammissione_medie'])) .'/decimi</td>
//                </tr>
//            </table>
//            ';
//        $pdf->Ln(3);
//        $pdf->SetFont($font, '', 8);
//        $pdf->writeHTMLCell($w_page_parts, 10, '', '', $tbl_amm, 1, 1);
//        $pdf->Ln(2);
//    }

    $pdf->Ln(8);
    $pdf->CellFitScale(90, 0, $studente['descrizione_comuni'] . ', lì ' . $data_attestato, 0, 1);
    $pdf->CellFitScale(90, 0, "LUOGO E DATA", 0, 1);
    $pdf->Ln(2);
    $pdf->MultiCell( $w_page_parts, 0, "____________________________\n".$labels['p4_testo_firma_genitori'], 0, $align = 'L', false, 1);
    $pdf->ln(4);

    $pdf->SetFont($font, 'B', $font_dim);
    $pdf->Cell($w_page_parts, 5, "ATTESTAZIONE", 0, 1, 'C');
    $pdf->SetFont($font, '', $font_dim);
    $pdf->CellFitScale($w_page_parts, 0, "Visti gli atti d'ufficio e la valutazione dei docenti della classe, si attesta che", 0, 1, 'C');
    $pdf->ln(1);
    $pdf->SetFont($font, 'B', $font_dim);
    $pdf->CellFitScale($w_page_parts, 0, $labels['attestato_alunno'].$studente['esito'], 0, 1, 'C');
    $pdf->SetFont($font, '', $font_dim);
    $pdf->ln(2);
    $pdf->CellFitScale(90, 0, $studente['descrizione_comuni'] . ', ' . $data_attestato, 0, 1);
    $pdf->ln(4);
    $pdf->MultiCell( 100, 0, $labels['p4_testo_firma_genitori'], 0, $align = 'L', false, 0);
    $pdf->MultiCell( 80, 0, "Il Coordinatore Educativo e Didattico (1)\n".$studente['nome_dirigente'].($firma_digitale == 'SI'? "\n(Documento firmato digitalmente)" : ''),  0, $align = 'C', false, 1);
    $pdf->SetFont($font, '', $font_dim-2);
    $pdf->writeHTMLCell($w_page_parts, 0, '', 275, $labels['note_pag4'], 0, 1, false, true, 'L');
    $pdf->SetFont($font, '', $font_dim);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="pagina 1">
    $pdf->Image('immagini_scuola/logo_repubblica.jpg', $x_start2 + ($w_page_parts/2) - 10, $m_top+15, 25, 25, 'JPG', false);
    $pdf->SetFont($font, 'B', $font_dim + 9);
    $pdf->setXY($x_start2, 54);
    $pdf->MultiCell($w_page_parts, 0, "Ministero dell'Istruzione", 0, 'C');
    $pdf->Ln(9);

    $pdf->SetFont($font, 'B', $font_dim + 3);
    $pdf->SetX($x_start2);
    $pdf->MultiCell($w_page_parts*0.35, 20, "Istituzione\nscolastica", 1, 'C', false, 0, '', '', true, 0, false, true, 20, 'M');
    $pdf->SetFont($font, '', $font_dim + 2);
    $pdf->MultiCell($w_page_parts*0.65, 20, "SCUOLA SECONDARIA DI I° GRADO PARITARIA\nBARI, BA", 1, 'C', false, 1, '', '', true, 0, false, true, 20, 'M');;
    $pdf->Ln(6);
    $pdf->SetFont($font, 'B', $font_dim + 3);
    $pdf->SetX($x_start2);
    $pdf->MultiCell($w_page_parts*0.35, 26, "Scuola Secondaria\ndi Primo Grado\nStatale", 1, 'C', false, 0, '', '', true, 0, false, true, 26, 'M');
    $pdf->SetFont($font, '', $font_dim + 2);
    $pdf->MultiCell($w_page_parts*0.65, 26, "Istituto Margherita Scuola Secondaria di I grado\n{$studente['codice_meccanografico_secondario']}\n{$studente['indirizzo_sedi']}\n{$studente['cap_comuni']} {$studente['descrizione_comuni']}, {$studente['provincia_comuni']}", 1, 'C', false, 1, '', '', true, 0, false, true, 26, 'M');

    $pdf->SetCellHeightRatio(1.25);
    $pdf->Ln(14);
    $pdf->SetFont($font, 'B', $font_dim + 10);
    $pdf->SetX($x_start2);
    $pdf->CellFitScale(0, 0, "Documento di valutazione", 0, 1, 'C');
    $pdf->SetX($x_start2);
    $pdf->CellFitScale(0, 0, "Anno scolastico $anno_scolastico_attuale", 0, 1, 'C');
    $pdf->Ln(14);

    $larghezza_max_cella = $w_page_parts;
	$altezza_cella = 5;

	$pdf->SetX($x_start2);
    $pdf->SetCellPadding( 1 );
    $pdf->SetFont($font, 'B', $font_dim + 4);
	$pdf->CellFitScale($larghezza_max_cella, $altezza_cella+4, 'Dati anagrafici dello studente', 'TRL', 1, 'C');
    $pdf->SetFont($font, '', $font_dim);

	$pdf->SetX($x_start2);
    $pdf->SetFont($font, 'B', $font_dim + 2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['cognome'], 'L', 0, 'C');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['nome'], 0, 0, 'C',FALSE,'',0, FALSE,'T', 'B');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['codice_fiscale'], 'R', 1, 'C');

	$pdf->SetX($x_start2);
    $pdf->SetFont($font, '', $font_dim-2);
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'COGNOME', 'L', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'NOME', 0, 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'CODICE FISCALE', 'R', 1, 'C',FALSE,'',0, FALSE,'T', 'T');

	$pdf->SetX($x_start2);
    $pdf->SetFont($font, 'B', $font_dim + 2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['data_nascita_ext'], 'L', 0, 'C');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $luogo_nascita, 0, 0, 'C');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $provincia_nascita, 'R', 1, 'C');

	$pdf->SetX($x_start2);
    $pdf->SetFont($font, '', $font_dim-2);
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'DATA DI NASCITA', 'LB', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'COMUNE DI NASCITA', 'B', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'PROV. O STATO ESTERO', 'RB', 1, 'C',FALSE,'',0, FALSE,'T', 'T');

    $pdf->SetFont($font, '', $font_dim);
    $pdf->setCellHeightRatio( 1.25 );
    $pdf->ln(9);

    $pdf->SetX($x_start2);
    $pdf->SetFont($font, 'B', $font_dim);
    $pdf->CellFitScale(110, 0, $labels['iscritto_classe'].$studente['classe'], 'TLB', 0);
    $pdf->CellFitScale(0, 0, 'Sezione '.$studente['sezione'], 'TRB', 1);
    $pdf->SetFont($font, '', $font_dim);
    $pdf->ln(12);

    $pdf->SetX($x_start2);
    $pdf->SetFont($font, '', $font_dim);
    $pdf->CellFitScale(90, 0, $studente['descrizione_comuni'] . ', lì ' . $data_attestato, 0, 0);
    $pdf->MultiCell(0, 0, "__________________________\nIl Coordinatore Educativo e Didattico (1)\n".$studente['nome_dirigente'].($firma_digitale == 'SI'? "\n(Documento firmato digitalmente)" : ''),  0, 'C', false, 0, '', '', true, 0, false, true, 0, 'M');
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2-3">
    $pdf->AddPage($orientamento);

    $pdf->SetFont($font, '', $font_dim);
    $pdf->writeHTMLCell($w_page_parts, 7, $m_sinistro, $m_top, $tbl_header, 1);
    $pdf->writeHTMLCell($w_page_parts, 7, $x_start2, $m_top, $tbl_header, 1, 1);

    $pdf->ln(4);
    $pdf->SetFont($font, 'B', $font_dim);
    $pdf->writeHTMLCell($w_page_parts, 7, '', '', "VALUTAZIONE PERIODICHE", 1, 0, false, true, 'C');
    $pdf->writeHTMLCell($w_page_parts, 7, $x_start2, '', "VALUTAZIONE PERIODICHE", 1, 1, false, true, 'C');
    $pdf->SetFont($font, '', $font_dim);

    $y_st_voti = $pdf->getY();

    $p3 = false;
    foreach ($arr_voti as $id_materia => $voto)
    {

        $voto_IQ = $voto_IQ_trad = $voto_finale = $voto_finale_trad = '';

        if ($pdf->GetY() + 11
                >
            240) {
            $p3 = true;
            $pdf->SetY($y_st_voti);
        }

        if ($p3) {
            $pdf->SetX($x_start2);
        }

        foreach ($voto['IQ']['significati_voto'] as $significato)
        {
            if ( $significato['voto'] == $voto['IQ']['voto_pagellina'] ) {
                $voto_IQ = $voto['IQ']['voto_pagellina'].'/10';
                $voto_IQ_trad = strtoupper($significato['valore_pagella']).'/decimi';
            }
        }

        foreach ($voto['finale']['significati_voto'] as $significato)
        {
            if ( $significato['voto'] == $voto['finale']['voto_pagellina'] ) {
                $voto_finale = $voto['finale']['voto_pagellina'].'/10';
                $voto_finale_trad = strtoupper($significato['valore_pagella']).'/decimi';
            }
        }


        $pdf->SetFont($font, 'B', $font_dim);
        $pdf->CellFitScale($w_page_parts*0.34, 11, $voto['finale']['descrizione'], 1, 0, 'L');
        $pdf->SetFont($font, '', $font_dim);
        $pdf->CellFitScale($w_page_parts*0.33, 11, "I° Quadrimestre", 1, 0, 'C');
        $pdf->CellFitScale($w_page_parts*0.33, 11, "Fine Anno", 1, 1, 'C');
        if ($p3) {
            $pdf->SetX($x_start2);
        }
        $pdf->CellFitScale($w_page_parts*0.34, 11, "Voto (in cifre e in lettere)", 1, 0, 'L');
        $pdf->CellFitScale($w_page_parts*0.16, 11, $voto_IQ, 1, 0, 'C');
        $pdf->CellFitScale($w_page_parts*0.17, 11, $voto_IQ_trad, 1, 0, 'C');
        $pdf->CellFitScale($w_page_parts*0.16, 11, $voto_finale, 1, 0, 'C');
        $pdf->CellFitScale($w_page_parts*0.17, 11, $voto_finale_trad, 1, 1, 'C');

    }

    $pdf->SetX($x_start2);
    $pdf->CellFitScale(90, 10, $studente['descrizione_comuni'] . ', lì ' . $data_attestato, 0, 1);
    $pdf->ln(4);
    $pdf->SetX($x_start2);
    $pdf->MultiCell(90, 15, "__________________________\n".$labels['p4_testo_firma_genitori'], 0, 'L', false, 0, '', '', true, 0, false, true, 15, 'M');
    $pdf->MultiCell(90, 0, "__________________________\nIl Coordinatore Educativo e Didattico (1)\n".$studente['nome_dirigente'].($firma_digitale == 'SI'? "\n(Documento firmato digitalmente)" : ''), 0, 'C', false, 0, '', '', true, 0, false, true, 0, 'M');


    $pdf->SetXY(10,255);
    $pdf->CellFitScale(90, 10, $studente['descrizione_comuni'] . ', lì ' . $data_attestato, 0, 1);
    $pdf->ln(4);
    $pdf->MultiCell(90, 15, "__________________________\n".$labels['p4_testo_firma_genitori'], 0, 'L', false, 0, '', '', true, 0, false, true, 15, 'M');
    $pdf->MultiCell(90, 0, "__________________________\nIl Coordinatore Educativo e Didattico (1)\n".$studente['nome_dirigente'].($firma_digitale == 'SI'? "\n(Documento firmato digitalmente)" : ''), 0, 'C', false, 0, '', '', true, 0, false, true, 0, 'M');
    //}}} </editor-fold>

    //}}} </editor-fold>
    //}}} </editor-fold>
}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
