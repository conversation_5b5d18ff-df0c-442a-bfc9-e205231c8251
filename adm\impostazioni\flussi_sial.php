<?php

namespace MT\Mastercom;
ini_set('memory_limit', '8196M');
ini_set('max_execution_time', 88800);

// TODO: da sistemare
function callSialService($param)
{
	ini_set("soap.wsdl_cache_enabled", 0);

	$options = [
		'trace'		 => 1,
		'exceptions' => true,
		'cache_wsdl' => WSDL_CACHE_NONE
	];

	$client = new \SoapClient('/var/www-source/webservices/1.0/module/SialService/config/produzione/SialService.wsdl', $options);
	$codiceFiscale = '';

	try
    {
		$return = $client->inserisciGiudizi($param);
		file_put_contents('/tmp/xml_finale.xml', print_r($client->__getLastRequest(), true), FILE_APPEND);
	}
    catch (\SoapFault $e)
    {
		file_put_contents('/tmp/eccezione', print_r($e, true), FILE_APPEND);
		$exception = $e->getMessage();
		$codiceFiscale = str_replace('inserisciGiudizi-codiceFiscale:', '', $e->detail->ServiceException->descrizione);
	}

	if ($return->return->controllo == 1)
    {
		$success = true;
		$message = 'Esiti trasmessi correttamente';
	}
    else
    {
		$success = false;
		$message = "ERRORE SISTEMA REGIONALE: {$exception}\n"
				. "    Codice Fiscale alunno: {$codiceFiscale}";
	}

	return [
		'success' => $success,
		'message' => $message
	];
}

function tipoVotoPeriodo($tipo)
{
    switch (intval($tipo))
    {
        case 1:
            $tipoValutazione = 'voto_singolo';
            break;
        case 2:
            $tipoValutazione = 'scritto_orale';
            break;
        case 3:
            $tipoValutazione = 'scritto_orale_pratico';
            break;
        case 4:
            $tipoValutazione = 'personalizzato';
            break;

        default:
            $tipoValutazione = 'voto_singolo';
    }

    return $tipoValutazione;
}

function traduci_voto_in_giudizio($voto, $schema_voti)
{
    // Salvo il voto in caso non vada tradotto
    $giudizio = $voto;

    // Se è NC lo forzo a Non classificato
    $voto = in_array($voto, ['NC', 'A']) ? "Non classificato" : $voto;

    $array_giudizi = [
        11 => "Non sufficiente",
        12 => "Sufficiente",
        13 => "Buono",
        14 => "Distinto",
        15 => "Ottimo",
        16 => "Non classificato",
        17 => "Molto",
        18 => "Moltissimo",
        19 => "Discreto",
    ];

    $voto_tradotto = estrai_significati_voti_specifici($voto, $schema_voti);
    foreach ($array_giudizi as $valore => $voto_giudizio)
    {
        if (strtolower($voto_giudizio) == strtolower($voto_tradotto['valore_pagella']))
        {
            $giudizio = $valore;
            break;
        }
    }

    return $giudizio;
}

switch($servizio)
{
	case 'scrutini':
	case 'scrutini_sospesi':
		$anno_scolastico_attuale = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
		$arr_as = explode('/', $anno_scolastico_attuale);

		$sql = "SELECT DISTINCT "
				. ClasseTable::FIELD_CLASS . ", "
				. IndirizzoTable::FIELD_TYPE . ", "
				. StudenteTable::FIELD_ID . ", "
				. StudenteTable::FIELD_SURNAME . ", "
				. StudenteTable::FIELD_NAME . ", "
				. StudenteTable::FIELD_CODICE_FISCALE . ", "
				. StudenteTable::FIELD_AMMESSO_ESAME_QUINTA . ", "
				. StudenteTable::FIELD_ESITO_PRIMA_ELEMENTARE . ", "
				. StudenteTable::FIELD_ESITO_SECONDA_ELEMENTARE . ", "
				. StudenteTable::FIELD_ESITO_TERZA_ELEMENTARE . ", "
				. StudenteTable::FIELD_ESITO_QUARTA_ELEMENTARE . ", "
				. StudenteTable::FIELD_ESITO_QUINTA_ELEMENTARE . ", "
				. StudenteTable::FIELD_ESITO_PRIMA_MEDIA . ", "
				. StudenteTable::FIELD_ESITO_SECONDA_MEDIA . ", "
				. StudenteTable::FIELD_ESITO_TERZA_MEDIA . ", "
				. "votazione_pagella_fine_quadrimestre AS tipo_voto_periodo, "
                . "pei "
				. "FROM " . StudenteTable::VIEW . " "
				. "WHERE codice_indirizzi NOT LIKE '%ZZZ%' "
				. "AND descrizione_indirizzi NOT ILIKE '%iscr%' "
				. "AND descrizione_indirizzi NOT ILIKE '%trasf%' "
				. "AND descrizione_indirizzi NOT ILIKE '%ritir%' "
				. "AND " . StudenteTable::FIELD_CODICE_FISCALE . " != '' "
				. "AND " . IndirizzoTable::FIELD_TYPE . " != '7' "
				. "AND " . StudenteTable::FIELD_ESITO_CORRENTE . " != 'Trasferito prima del 15/3' "
				. "AND " . StudenteTable::FIELD_ESITO_CORRENTE . " != 'Ritirato prima del 15/3' "
				. "AND " . StudenteTable::FIELD_ESITO_CORRENTE . " != 'Trasferito' "
				. "ORDER BY "
				. IndirizzoTable::FIELD_TYPE . ", "
				. ClasseTable::FIELD_CLASS . ", "
				. StudenteTable::FIELD_CODICE_FISCALE;

		$result = pgsql_query($sql);
        $studenti = pg_fetch_all($result);
        file_put_contents('/tmp/studentiSial', print_r($sql, true));
		$gruppiEsiti = [];
		$i = 0;

        $giudiziComplessivi = [];

		foreach($studenti as $studente)
        {
            $giudizioPerMateria = [];
            $giudizio ='';

            if ($servizio == 'scrutini_sospesi')
            {
                $periodo = $studente[IndirizzoTable::FIELD_TYPE] == 4 ? 29 : 9;
            }
            else
            {
                if ($studente[IndirizzoTable::FIELD_TYPE] == 4)
                {
                    $periodo = $periodo_va == 7 ? 27 : 29;
                }
                else
                {
                    $periodo = $periodo_va;
                }
            }

            $sql = "SELECT "
                . MateriaTable::FIELD_TYPE .", "
                ."ltrim(". ltrim(MateriaTable::FIELD_COD_MINISTERIALE) . ", '0') as codice_ministeriale, "
                . VotoPagellinaTable::FIELD_ID . ", "
                . VotoPagellinaTable::FIELD_VOTE . ", "
                . VotoPagellinaTable::FIELD_ORAL_VOTE . ", "
                . VotoPagellinaTable::FIELD_PRACT_VOTE . ", "
                . VotoPagellinaTable::FIELD_WRITE_VOTE . ", "
                . VotoPagellinaTable::FIELD_RECOVERY_TYPE . ", "
                . VotoPagellinaTable::FIELD_RECOVERY_RESULTS . ", "
                . VotoPagellinaTable::FIELD_ABSENCE_HOUR . ", "
                . VotoPagellinaTable::FIELD_TOTAL . ", "
                . "materie.id_materia AS id_materia, "
                . "materie.tipo_voto_personalizzato AS tipo_voto_materia, "
                . "materie.tipo_valutazione AS tipo_valutazione_materia, "
                . "voti_pagelline.voto_giudizio_sospeso AS voto_giudizio_sospeso, "
                . "voti_pagelline.tipo_recupero_giudizio_sospeso AS tipo_recupero_giudizio_sospeso "
                . " FROM " . VotoPagellinaTable::TABLE . ", "
                . PagellinaTable::TABLE . ", "
                . MateriaTable::TABLE . " "
                . "WHERE " . VotoPagellinaTable::TABLE . "." . VotoPagellinaTable::FIELD_ID_REPORT . " = " . PagellinaTable::TABLE . "." . PagellinaTable::FIELD_ID . " "
                . "AND " . MateriaTable::TABLE . "." . MateriaTable::FIELD_ID . " = " . VotoPagellinaTable::TABLE . "." . VotoPagellinaTable::FIELD_ID_SUBJECT . " "
                . "AND " . PagellinaTable::TABLE . "." . PagellinaTable::FIELD_ID_STUDENT . " = {$studente['id_studente']} "
                . "AND " . PagellinaTable::TABLE . "." . PagellinaTable::FIELD_PERIOD . " = '{$periodo}' "
                . "AND " . PagellinaTable::TABLE . "." . PagellinaTable::FIELD_DELETED . " = 0 "
                . "AND " . VotoPagellinaTable::TABLE . "." . VotoPagellinaTable::FIELD_DELETED . " = 0 "
                . "AND " . MateriaTable::TABLE . "." . MateriaTable::FIELD_DELETED . " = 0 "
                // . "AND " . MateriaTable::TABLE . "." . MateriaTable::FIELD_COD_MINISTERIALE . " <> '' "
                . "AND (" . MateriaTable::TABLE . ".in_media_pagelle = 'SI' OR " . MateriaTable::TABLE . ".tipo_materia = 'RELIGIONE'  OR " . MateriaTable::TABLE . ".tipo_materia = 'CONDOTTA') ";

            $result = pgsql_query($sql);
            $voti = pg_fetch_all($result);
			file_put_contents('/tmp/votiSial', print_r("<br>$sql<br>", true), FILE_APPEND);
            file_put_contents('/tmp/votiSial', print_r($voti, true), FILE_APPEND);

			$flgGiudizioSospeso = null;
			$assenze_da_pagella = 0;
			$monteore_da_pagella = 0;
			$almeno_un_voto = false;
			$tipi_recupero = true;
			$voti_insuff = false;
			$recupero = false;
			$esito_recupero = 'SI';
            $tipoVoto = tipoVotoPeriodo($studente['tipo_voto_periodo']);

			foreach($voti as $voto)
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="controllo voti">
				if($voto['monteore_totale'] > 0 && $voto['tipo_materia'] != 'CONDOTTA')
				{
					$assenze_da_pagella += $voto['ore_assenza'];
					$monteore_da_pagella += $voto['monteore_totale'];
				}

                // C'è almeno un voto
				if($voto['voto_pagellina'] != ''
                    || $voto['voto_scritto_pagella']  != ''
                    || $voto['voto_orale_pagella']  != ''
                    || $voto['voto_pratico_pagella'] != ''
                )
				{
					$almeno_un_voto = true;

					if (empty($voto['codice_ministeriale'])) {
						$cod_mat = $voto['id_materia'];
						$giudizioPerMateria[$cod_mat] = [
							'codiceMinisteriale' => null,
							'idMateria' => $voto['id_materia']
						];
					} else {
						$cod_mat = $voto['codice_ministeriale'];
						$giudizioPerMateria[$cod_mat] = [
							'codiceMinisteriale' => $voto['codice_ministeriale'],
							'idMateria' => null
						];
					}

                    // Voto Insufficiente
                    if(
						!empty($voto['voto_pagellina'])
						&&
                        (floatval($voto['voto_pagellina']) < 6 || !is_numeric($voto['voto_pagellina']))
					)
					{
						if($voto['tipo_recupero'] == '' || $voto['esito_recupero'] != 'SI')
						{
							if($voto['tipo_recupero'] == '')
							{
								$tipi_recupero = false;
							}
							else
							{
								$recupero = true;
							}

							if($voto['esito_recupero'] == '')
							{
								$esito_recupero = '';
							}
							elseif($voto['esito_recupero'] != 'SI' && $esito_recupero == 'SI')
							{
								$esito_recupero = 'NO';
							}

							$voti_insuff = true;
						}
					}
					else
					{
						if($voto['esito_recupero'] != '')
						{
							$recupero = true;
						}
					}

                    $tipoVotoMateria = $voto['tipo_voto_materia'];

                    if ($tipoVoto == 'personalizzato')
                    {
                        if ($tipoVotoMateria == '1')
                        {
                            $checkTipo = "voto_singolo";
                        }

                        if ($tipoVotoMateria == '2')
                        {
                            $checkTipo = "scritto_orale";
                        }

                        if ($tipoVotoMateria == '3')
                        {
                            $checkTipo = "scritto_orale_pratico";
                        }
                    }
                    else
                    {
                        $checkTipo = $tipoVoto;
                    }

                    // Pagella intermedia e il voto non è singolo
                    if ($checkTipo != 'voto_singolo' && !in_array($periodo, [9, 29]))
                    {

                        // Pagella intermedia
                        if (intval($voto['voto_orale_pagella']) > 0)
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoOrale'] = $voto['voto_orale_pagella'];
                        }
                        elseif ($voto['voto_orale_pagella'] == 'NC' || $voto['voto_orale_pagella'] == 'A')
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoOrale'] = 16;
                        } else {
                            $giudizioPerMateria[$cod_mat]['idVotoOrale'] = null;
						}

                        if (intval($voto['voto_pratico_pagella']) > 0)
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoPratica'] = $voto['voto_pratico_pagella'];
                        }
                        elseif ($voto['voto_pratico_pagella'] == 'NC' || $voto['voto_pratico_pagella'] == 'A')
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoPratica'] = 16;
                        } else {
                            $giudizioPerMateria[$cod_mat]['idVotoPratica'] = null;
						}

                        if (intval($voto['voto_scritto_pagella']) > 0)
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoScritto'] = $voto['voto_scritto_pagella'];
                        }
                        elseif ($voto['voto_scritto_pagella'] == 'NC' || $voto['voto_scritto_pagella'] == 'A')
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoScritto'] = 16;
                        } else {
                            $giudizioPerMateria[$cod_mat]['idVotoScritto'] = null;
						}

                        // se Religione o Condotta
                        if (in_array($voto['tipo_materia'], ['CONDOTTA', 'RELIGIONE']))
                        {
                            if (intval($voto['voto_orale_pagella']) > 0 || in_array($voto['voto_orale_pagella'], ['NC', 'A']))
                            {
                                $giudizioPerMateria[$cod_mat]['idVotoOrale'] = traduci_voto_in_giudizio($voto['voto_orale_pagella'], $voto['tipo_valutazione_materia']);
                            }
                            if (intval($voto['voto_pratico_pagella']) > 0 || in_array($voto['voto_pratico_pagella'], ['NC', 'A']))
                            {
                                $giudizioPerMateria[$cod_mat]['idVotoPratica'] = traduci_voto_in_giudizio($voto['voto_pratico_pagella'], $voto['tipo_valutazione_materia']);
                            }
                            if (intval($voto['voto_scritto_pagella']) > 0 || in_array($voto['voto_scritto_pagella'], ['NC', 'A']))
                            {
                                $giudizioPerMateria[$cod_mat]['idVotoScritto'] = traduci_voto_in_giudizio($voto['voto_scritto_pagella'], $voto['tipo_valutazione_materia']);
                            }
                        }

                    }
                    else
                    {

                        // Pagella finale
                        if (intval($voto['voto_pagellina']) > 0)
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoScritto'] = $voto['voto_pagellina'];
                        }
                        elseif ($voto['voto_pagellina'] == 'NC' || $voto['voto_pagellina'] == 'A')
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoScritto'] = 16;
                        } else {
                            $giudizioPerMateria[$cod_mat]['idVotoScritto'] = null;
						}

                        // se Religione o Condotta
                        if (in_array($voto['tipo_materia'], ['CONDOTTA', 'RELIGIONE'])
                           &&
                           (intval($voto['voto_pagellina']) > 0 || in_array($voto['voto_pagellina'], ['NC', 'A']))
                        )
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoScritto'] = traduci_voto_in_giudizio($voto['voto_pagellina'], $voto['tipo_valutazione_materia']);
                        }

                    }

                    if ($periodo == '9')
                    {
                        if ($voto['tipo_recupero'] !== '')
                        {
                            $giudizioPerMateria[$cod_mat]['flgGiudizioSospeso'] = 1;
                        }

                        if (intval($voto['voto_giudizio_sospeso']) > 0)
                        {
                            $giudizioPerMateria[$cod_mat]['idVotoSospeso'] = $voto['voto_giudizio_sospeso'];
                        }
                    }

					if (!isset($giudizioPerMateria[$cod_mat]['idVotoScritto'])) {
						$giudizioPerMateria[$cod_mat]['idVotoScritto'] = null;
					}
					if (!isset($giudizioPerMateria[$cod_mat]['idVotoOrale'])) {
						$giudizioPerMateria[$cod_mat]['idVotoOrale'] = null;
					}
					if (!isset($giudizioPerMateria[$cod_mat]['idVotoPratica'])) {
						$giudizioPerMateria[$cod_mat]['idVotoPratica'] = null;
					}

                    //giudizioSintetico
                    $sqlGiudizio = "SELECT valore_testuale
                                    FROM valori_campi_liberi vcl
                                    WHERE id_oggetto = {$voto['id_voto_pagellina']}";
                    $resultGiudizio = pgsql_query($sqlGiudizio);
                    $elencoGiudizi = pg_fetch_all($resultGiudizio);

                    foreach ($elencoGiudizi as $giudizioSingolo)
                    {
                        if ($giudizioSingolo['valore_testuale'] !== '')
                        {
                            $giudizio = $giudizioSingolo;
                        }
                    }
                }
                //}}} </editor-fold>
            }

            /**
			 * Esiti possibili
			 *
			 * 1 = Ammesso anno successivo
			 * 2 = Non ammesso anno successivo
			 * 3 = Ammesso esame
			 * 4 = Non ammesso esame
			 * 5 = Giudizio sospeso
			 */
            /**
			 * Tipi di attestato
			 *
			 * 1 = Licenziato
			 * 2 = Non licenziato
			 * 3 = Qualificato
			 * 4 = Non qualificato
			 * 5 = Diplomato
			 * 6 = Non diplomato
			 */
			$idTipoAttestato = '';
			if($studente['tipo_indirizzo'] == 4)
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="scuola media">
				switch((int)$studente['classe'])
				{
					case 1:
						//{{{ <editor-fold defaultstate="collapsed" desc="esito prima media">
						if($studente['esito_prima_media'] == 'SI')
						{
							//AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 1;
						}
						else
						{
							//NON AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 2;
						}
						//}}} </editor-fold>
						break;
					case 2:
						//{{{ <editor-fold defaultstate="collapsed" desc="esito seconda media">
						if($studente['esito_seconda_media'] == 'SI')
						{
							//AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 1;
						}
						else
						{
							//NON AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 2;
						}
						//}}} </editor-fold>
						break;
					case 3:
						//{{{ <editor-fold defaultstate="collapsed" desc="esito terza media">
						if($studente['esito_terza_media'] == 'SI')
						{
							//AMMESSO/A ALL'ESAME DI STATO
							$idEsitoFineAnno = 3;
						}
						else
						{
							//NON AMMESSO/A ALL'ESAME DI STATO
							$idEsitoFineAnno = 4;
						}
						//}}} </editor-fold>
						break;
				}
				//}}} </editor-fold>
			}
			elseif($studente['tipo_indirizzo'] == 6)
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="scuola elementare">
				switch((int)$studente['classe'])
				{
					case 1:
						//{{{ <editor-fold defaultstate="collapsed" desc="esito prima elementare">
						if($studente['esito_prima_elementare'] == 'SI')
						{
							//AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 1;
						}
						else
						{
							//NON AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 2;
						}
						//}}} </editor-fold>
						break;
					case 2:
						//{{{ <editor-fold defaultstate="collapsed" desc="esito seconda elementare">
						if($studente['esito_seconda_elementare'] == 'SI')
						{
							//AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 1;
						}
						else
						{
							//NON AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 2;
						}
						//}}} </editor-fold>
						break;
					case 3:
						//{{{ <editor-fold defaultstate="collapsed" desc="esito terza elementare">
						if($studente['esito_terza_elementare'] == 'SI')
						{
							//AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 1;
						}
						else
						{
							//NON AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 2;
						}
						//}}} </editor-fold>
						break;
					case 4:
						//{{{ <editor-fold defaultstate="collapsed" desc="esito quarta elementare">
						if($studente['esito_quarta_elementare'] == 'SI')
						{
							//AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 1;
						}
						else
						{
							//NON AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 2;
						}
						//}}} </editor-fold>
						break;
					case 5:
						//{{{ <editor-fold defaultstate="collapsed" desc="esito quinta elementare">
						if($studente['esito_quinta_elementare'] == 'SI')
						{
							//AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 1;
						}
						else
						{
							//NON AMMESSO/A ALLA CLASSE SUCCESSIVA
							$idEsitoFineAnno = 2;
						}
						//}}} </editor-fold>
						break;
				}
				//}}} </editor-fold>
			}
			else
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="scuola superiore">
				if(
					$studente['classe'] == 5
					or ($studente['classe'] == 3 and $studente['tipo_indirizzo'] == 1)
				)
				{
					//{{{ <editor-fold defaultstate="collapsed" desc="ammissione maturità">
					if($studente['ammesso_esame_quinta'] == 'SI')
					{
						$idEsitoFineAnno = 3;
					}
					else
					{
						$idEsitoFineAnno = 4;
					}
					//}}} </editor-fold>
				}
				else
				{
					if($almeno_un_voto)
					{
						//{{{ <editor-fold defaultstate="collapsed" desc="controllo voti">
						if(!($voti_insuff))
						{
							//Non ho nessun voto insufficiente
							if($recupero and $esito_recupero == 'SI')
							{
								//Ho dei recuperi con esito positivo
								$idEsitoFineAnno = 1;
								$flgGiudizioSospeso = 1;
							}
							else
							{
								//Lo studente non ha fatto recuperi
								$idEsitoFineAnno = 1;
							}
						}
						elseif(!($tipi_recupero))
						{
							//Ho delle insufficienze ma almeno una di esse non ha un tipo di recupero
							$idEsitoFineAnno = 2;
						}
						elseif($esito_recupero == 'NO' or $esito_recupero == 'ASSENTE' or $esito_recupero == 'NI')
						{
							//Ho delle insufficenze, tutte con recupero compilato, e almeno un recupero negativo
							$idEsitoFineAnno = 2;
							$flgGiudizioSospeso = 1;
						}
						elseif($esito_recupero == '')
						{
							//Ho delle insufficenze, tutte con recupero, ma di cui almeno uno non compilato
							$idEsitoFineAnno = 5;
							$flgGiudizioSospeso = 1;
						}
						//}}} </editor-fold>
					}
					else
					{
						//NON AMMESSO/A
						$idEsitoFineAnno = 2;
					}
				}
				//}}} </editor-fold>
			}

            if ($studente['pei']== 'SI') {
                $idEsitoFineAnno = 1;
            }

            /**
			 * Creazione giudizioComplessivo
			 */
            if(
                ($servizio == 'scrutini_sospesi' && $flgGiudizioSospeso == 1)
                ||
                $servizio == 'scrutini'
            )
            {
                if ($periodo == '7' || $periodo == '27')
                {
                    $giudiziComplessivi[] = [
                        'cfStudente'         => $studente['codice_fiscale'],
                        'annoCronologico'    => $arr_as[0],
                        'idTipoPeriodo'      => 1,
                        'giudizioSintetico'  => $giudizio['valore_testuale'],
                        'totMinAssenza'      => $assenze_da_pagella,
                        'giudizioPerMateria' => array_values($giudizioPerMateria)
                    ];
                }

                if ($periodo == '9' || $periodo == '29')
                {
                    $giudiziComplessivi[] = [
                        'cfStudente'         => $studente['codice_fiscale'],
                        'annoCronologico'    => $arr_as[0],
                        'idTipoPeriodo'      => 2,
                        'giudizioSintetico'  => $giudizio['valore_testuale'],
                        'totMinAssenza'      => $assenze_da_pagella,
                        'idEsitoFineAnno'    => $idEsitoFineAnno,
						'idTipoAttestato'	 => $idTipoAttestato,
                        'giudizioPerMateria' => array_values($giudizioPerMateria)
                    ];
                }
            }
		}

        /**
		 * Inizio composizione chiamate per gruppi di studenti
		 */
		set_time_limit(0);

		$autenticazione = new \ArrayObject;
		$autenticazione->login = 'sial';
        $autenticazione->password = 'x24EXTa@2';
        // Pwd ambiente di test
//        $autenticazione->password = 'sial';

		$errori = [];

		$log = "Inzio elaborazione: " . date('j/m/Y G:i:s') . "\n";
        file_put_contents('/tmp/giudiziComplessivi', print_r($giudiziComplessivi, true));
        $debug = array();
        foreach ($giudiziComplessivi as $giudizioComplessivo)
        {
            $data = new \ArrayObject;
            $data->autenticazione = $autenticazione;
            $data->giudizioComplessivo = $giudizioComplessivo;

            $esito = callSialService($data);
            $debug[0] = $giudizioComplessivo['cfStudente'];
            $debug[1] = $esito;
			file_put_contents("/tmp/esito_sial", print_r($debug, true), FILE_APPEND);
            if (!$esito['success'])
            {
                $errori[] = $esito['message'];
            }
        }

		$log .= empty($errori) ? $esito['message'] : implode("\n", $errori)
				. "\nFine elaborazione: " . date('j/m/Y G:i:s') . "\n";

		$sialPath = '/var/www-source/mastercom/tmp_sial';
		$sialLog = $sialPath . '/inserisciGiudizi.txt';

		file_put_contents($sialLog, $log);

		// Viene prodotto il file ... quindi non occorre output, la chiamata viene effettuata via Ajax
		exit(str_replace('/var/www-source', '', $sialLog));

		break;
	case 'esami_terza_media':
		$anno_scolastico_attuale = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
		$arr_as = explode('/', $anno_scolastico_attuale);

		$sql = "SELECT DISTINCT "
				. StudenteTable::FIELD_ID . ", "
				. StudenteTable::FIELD_SURNAME . ", "
				. StudenteTable::FIELD_NAME . ", "
				. StudenteTable::FIELD_CODICE_FISCALE . ", "
				. StudenteTable::FIELD_VOTO_TERZA_MEDIA . ", "
                . "voto_esame_medie_invalsi_finale "
				. "FROM " . StudenteTable::VIEW . " "
				. "WHERE codice_indirizzi NOT LIKE '%ZZZ%' "
				. "AND descrizione_indirizzi NOT ILIKE '%iscr%' "
				. "AND descrizione_indirizzi NOT ILIKE '%trasf%' "
				. "AND descrizione_indirizzi NOT ILIKE '%ritir%' "
				. "AND " . StudenteTable::FIELD_CODICE_FISCALE . " != '' "
				. "AND " . ClasseTable::FIELD_CLASS . " = '3' "
				. "AND " . IndirizzoTable::FIELD_TYPE . " = '4' "
				. "AND " . StudenteTable::FIELD_ESITO_TERZA_MEDIA . " = 'SI' "
				. "ORDER BY "
				. StudenteTable::FIELD_CODICE_FISCALE;

		$db = new Db;
		$studenti = $db->query($sql, true);

		$gruppiEsiti = [];

		foreach($studenti as $studente)
        {

			/**
			 * Esiti possibili
			 *
			 * 1 = Ammesso anno successivo
			 * 2 = Non ammesso anno successivo
			 */
			$idVotoEsame = intval($studente[StudenteTable::FIELD_VOTO_TERZA_MEDIA]);
			$idVotoInvalsi = intval($studente['voto_esame_medie_invalsi_finale']);
            $idEsitoFineAnno = 2;
            $idTipoAttestato = 2;
            $idEsitoEsameFinale = 2;
            $lode = null;

            if($idVotoEsame >= 6)
			{
				$idEsitoFineAnno = 1;
				$idTipoAttestato = 1;
				$idEsitoEsameFinale = 1;
                $lode = null;

				if($idVotoEsame == 10)
				{
					$lode = stripos($studente[StudenteTable::FIELD_VOTO_TERZA_MEDIA], 'lode') !== false ? true : false;
				}
			}

			$gruppiEsiti[] = [
				'cfStudente'		 => $studente['codice_fiscale'],
				'annoCronologico'	 => $arr_as[0],
				'idTipoPeriodo'		 => 3,
				'idEsitoFineAnno'	 => $idEsitoFineAnno,
				'idTipoAttestato'	 => $idTipoAttestato,
				'idEsitoEsameFinale' => $idEsitoEsameFinale,
				'idVotoEsame'		 => $idVotoEsame,
				'idVotoInvalsi'		 => $idVotoInvalsi,
				'lode'				 => $lode,
			];
		}

		/**
		 * Inizio composizione chiamate per gruppi di studenti
		 */
		$autenticazione = new \ArrayObject;
		$autenticazione->login = 'sial';
		$autenticazione->password = 'x24EXTa@2';
//		// Pwd ambiente di test
        //$autenticazione->password = 'sial';

		$chiamate = count($gruppiEsiti);
		$errori = [];

		set_time_limit(0);

		$log = "Inzio elaborazione: " . date('j/m/Y G:i:s') . "\n";
        $log .= "Studenti estratti: " . $chiamate . "\n";

        for($g=0; $g < $chiamate; $g++)
        {
			$data = new \ArrayObject;
			$data->autenticazione = $autenticazione;
			$data->giudizioComplessivo = $gruppiEsiti[$g];

			$esito = callSialService($data);

			if (!$esito['success'])
            {
				$errori[] = $esito['message'];
			}
		}

		$log .= empty($errori) ? $esito['message'] : implode("\n", $errori);
        $log .= "\nFine elaborazione: " . date('j/m/Y G:i:s') . "\n";

        $sialPath = '/var/www-source/mastercom/tmp_sial';
		$sialLog = $sialPath . '/inserisciGiudizi.txt';

		file_put_contents($sialLog, $log);

		// Viene prodotto il file ... quindi non occorre output, la chiamata viene effettuata via Ajax
		exit(str_replace('/var/www-source', '', $sialLog));

		break;
}
