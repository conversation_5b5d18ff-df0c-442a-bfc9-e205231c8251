<?php

/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G', 'certificato_competenze_donbosco_bs_01', 'Certificato delle competenze di base', 1, 'certifficato competenze donbosco-bs', 9);
 */


$orientamento = 'P';
$formato = 'A4';

if ($periodo ==7) {
    $periodo_pagella = "intermedia" ;
}
elseif ($periodo ==8) {
    $periodo_pagella = "pagellinast" ;
}
elseif ($periodo ==9) {
    $periodo_pagella = "finale" ;
}
else {
    $periodo_pagella = "pagellina" . $periodo;
}


function genera_stampa(&$pdf, $studente, $parametri_stampa)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Sezione dedicata ai parametri, al dizionario e alle impostazioni specifiche dei campi liberi">
    $id_classe = $parametri_stampa['id_classe'];
    $data_Day = $parametri_stampa['data_day'];
    $data_Month = $parametri_stampa['data_month'];
    $data_Year = $parametri_stampa['data_year'];
    $periodo_st = $parametri_stampa['periodo'];
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $anno = explode("/", $anno_scolastico_attuale);
    $anno_inizio = $anno[0];

    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $stato_nascita .=   $stato['descrizione'] ;
        }
    }
    else
    {
        $stato_nascita = 'ITALIA';
        $luogo_nascita = $studente['descrizione_nascita'] . ' (' .$studente['provincia_nascita_da_comune']. ')';
    }

    $id_studente = $studente['id_studente'];
    // $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    // $voti_pagella = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo_st, $id_studente);
    // $arr_comp = [];
    // foreach ($materie_studente as $materia)
    // {
    //     $id_materia = $materia['id_materia'];

    //     if ($materia['in_media_pagelle'] != 'NV') {
    //         foreach ($voti_pagella[$id_materia]['campi_liberi'] as $campo_libero) {
    //             if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {

    //                 $value = estrai_valore_campo_libero_selezionato($campo_libero);

    //                 $cmpname = $campo_libero['nome'];

    //                 if (stripos($cmpname, 'Soft Skill:') !== false) {

    //                     $arr_comp['SOFT SKILL'][$cmpname];

    //                     if ($value !== '') {
    //                         $arr_comp['SOFT SKILL'][$cmpname] = $value;
    //                     }
    //                 }
    //                 elseif (stripos($cmpname, 'Ed. civica:') !== false) {

    //                     $arr_comp['EDUCAZIONE CIVICA'][$cmpname];

    //                     if ($value !== '') {
    //                         $arr_comp['EDUCAZIONE CIVICA'][$cmpname] = $value;
    //                     }
    //                 }
    //                 elseif (stripos($cmpname, 'PCTO:') !== false) {

    //                     $arr_comp['PCTO'][$cmpname];

    //                     if ($value !== '') {
    //                         $arr_comp['PCTO'][$cmpname] = $value;
    //                     }
    //                 }
    //                 elseif (stripos($cmpname, 'Sales:') !== false) {

    //                     $arr_comp['SALES'][$cmpname];

    //                     if ($value !== '') {
    //                         $arr_comp['SALES'][$cmpname] = $value;
    //                     }
    //                 }
    //                 elseif (stripos($cmpname, 'Competenza ') !== false) {

    //                     $arr_comp['COMPETENZE CHIAVE EUROPEE'][$cmpname];

    //                     if ($value !== '') {
    //                         $arr_comp['COMPETENZE CHIAVE EUROPEE'][$cmpname] = $value;
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    // $arr_def_desc = [
    //     "Soft Skill: Personali" => "Personali:
    // • È consapevole di sé, sa gestire le emozioni ed è capace di autodisciplinarsi
    // • È in grado di mettere a frutto i propri talenti
    // • È dedito ai compiti e impegni assegnati e li affronta con serietà",
    //     "Soft Skill: Sociali" => "Sociali:
    // • Collabora con gli altri membri del gruppo al conseguimento dell'obiettivo dato
    // • Lavora in gruppo esprimendo il proprio contributo e rispettando le idee degli altri
    // • Sa confrontarsi con i pari e con gli adulti
    // • Ha un atteggiamento inclusivo e condivide positivamente le dinamiche educative dalla scuola",
    //     "Soft Skill: Metodologiche" => "Metodologiche:
    // • Sa organizzare il lavoro e lo studio personale in diversi ambiti
    // • Sa attuare strategie appropriate per affrontare e superare problemi di varia natura
    // • Sa compiere le necessarie interconnessioni tra i metodi e contenuti delle singole discipline",

    //     "Ed. civica: Costituzione" => "Costituzione:
    // • Conosce e sa riconoscere i principi fondamentali della costituzione italiana, e sa applicarli nel contesto scolastico e sociale.
    // • Conosce e sa riconoscere con senso critico i principi di legalitá e agisce in maniera ad essi conforme nel contesto scolastico e sociale.
    // • Conosce l'organizzazione e il funzionamento delle principali istituzioni nazionali, europee e internazionali. Si pone verso queste istituzioni in modo rispettoso e consapevole durante dibattiti, confronti e relazioni interpersonali.
    // • Conosce e sa utilizzare concetti e strumenti geopolitici per la lettura dei processi storici e per l'analisi della società contemporanea.
    // • Conosce e rispetta diritti e doveri legati al mondo lavorativo e professionale.",
    //     "Ed. Civica: Digitale" => "Digitale:
    // • Analizza, confronta e valuta criticamente la credibilità e l'affidabilità delle fonti di dati, informazioni e contenuti digitali.
    // • Sa creare e gestire l’identità digitale, è in grado di proteggere la propria reputazione, gestire e tutelare i dati che si producono attraverso diversi strumenti digitali, ambienti e servizi, rispettare i dati e le identità altrui;
    // • Conosce le norme comportamentali da osservare nell’ambito dell’utilizzo delle tecnologie digitali e dell’interazione in ambienti digitali. Adatta le strategie di comunicazione al pubblico specifico ed è consapevole della diversità culturale e generazionale negli ambienti digitali;
    // • È in grado di evitare, usando tecnologie digitali, rischi per la salute e minacce al proprio benessere fisico e psicologico; è in grado di proteggere se stessi e gli altri da eventuali pericoli in ambienti digitali; è consapevole delle tecnologie digitali per il benessere psicofisico e l’inclusione sociale.",
    //     "Ed. Civica: Salute & Ambiente" => "Ambiente & Salute:
    // • Adotta comportamenti di tutela e prevenzione per la salute propria, altrui e dell’ambiente
    // • Cura l'ambiente e sviluppare una coscienza ecologica. Comprende ed avverte la necessità di uno sviluppo equo e sostenibile, rispettoso dell'ecosistema e di un utilizzo consapevole delle risorse ambientali.
    // • Comprende e riconosce l'importanza e la ricaduta degli aspetti psicologici e relazionali concernenti il mondo sanitario e biomedicale, è disponibile ad un ascolto attento e ad un confronto critico sulle tematiche affrontate.
    // • Comprende e riconosce l'importanza e la ricaduta degli aspetti psicologici e relazionali concernenti il mondo affettivo - sessuale, è disponibile ad un ascolto attento e ad un confronto critico sulle tematiche affrontate.",

    //     "Sales: Formazione" => "Formazione:
    // • Cresce consapevolmente nella preparazione personale
    // • Applica conoscenze e competenze alla vita quotidiana, dimostrando coerenza e assertività",
    //     "Sales: Gruppo" => "Gruppo:
    // • Collabora e si confronta con gli altri, rispettando lo stile della comunità
    // • Accoglie e include nel gruppo i nuovi membri, custodisce buone relazioni anche al di fuori dei momenti organizzati",
    //     "Sales: Servizio" => "Servizio:
    // • È disponibile a svolgere i servizi richiesti e si fa carico dei bisogni ordinari
    // • Si preoccupa attivamente dei bisogni e si propone per servizi e disponibilità ulteriori",

    //     "PCTO: Hard Skill" => "Hard Skill:
    // • Applica conoscenze, competenze e abilità (linguistica, di scrittura, digitale, analitica, etc.) nel contesto operativo specific
    // • Interagisce costruttivamente e attivamente con le figure e le strutture, le istituzioni e gli enti, legati al progetto operativo specific
    // • Gestisce consapevolmente e compiutamente la realizzazione del progetto proposto, dalla programmazione al suo compimento",
    //     "PCTO: Orientamento" => "Orientamento:
    // • Valorizza e approfondisce le vocazioni personali, gli interessi e gli stili di apprendimento
    // • Integra la formazione scolastica, valorizzando le risorse acquisite, con elementi atti a favorire un corretto orientamento in uscita, la loro trasferibilità nel mondo del lavoro e le scelte future della persona",


    //     "Competenza alfabetica funzionale" => "Competenza  alfabetica funzionale:
    // • Sa individuare, comprendere, esprimere, creare e interpretare concetti, sentimenti, fatti e opinioni, in forma sia orale sia scritta, utilizzando materiali visivi, sonori e digitali attingendo a varie discipline e contesti.",
    //     "Competenza multilinguistica" => "Competenza  multilinguistica:
    // • Tale competenza definisce la capacità di utilizzare diverse lingue in modo appropriato ed efficace allo scopo di comunicare.",
    //     "Competenza matematica e c. di base in scienze e tecnologie" => "Competenza  in matematica, scienze e tecnologia:
    // • Sa sviluppare e applicare il pensiero e la comprensione matematici per risolvere una serie di problemi in situazioni quotidiane.
    // • Sa spiegare il mondo che ci circonda usando l’insieme delle conoscenze e delle metodologie, comprese l’osservazione e la sperimentazione, per identificare le problematiche e trarre conclusioni che siano basate su fatti empirici, e alla disponibilità a farlo.",
    //     "Competenza digitale" => "Competenza  digitale:
    // • Mostra interesse per le tecnologie digitali e il loro utilizzo; si muove tra di esse con dimestichezza e spirito critico e responsabile per apprendere, lavorare e partecipare alla società.",
    //     "Competenza personale, sociale e capacità di imparare ad imparare" => "Competenza personale, sociale e capacità di imparare a imparare:
    // • Sa riflettere su sé stesso, gestisce efficacemente il tempo e le informazioni, lavora con gli altri in maniera costruttiva, è resiliente, gestisce il proprio apprendimento e la propria carriera.",
    //     "Competenza sociale e civica in materia di cittadinanza" => "Competenza in materia di cittadinanza:
    // • Sa agire da cittadino responsabile e partecipa pienamente alla vita civica e sociale, in base alla comprensione delle strutture e dei concetti sociali, economici, giuridici e politici oltre che dell’evoluzione a livello globale e della sostenibilità.",
    //     "Competenza imprenditoriale" => "Competenza  imprenditoriale:
    // • Sa agire sulla base di idee e opportunità e di trasformarle in valori per gli altri.",
    //     "Competenza in materia di consapevolezza ed espressione culturali" => "Competenza in materia di consapevolezza ed espressione culturale:
    // • Comprende e rispetta come le idee e i significati vengono espressi creativamente e comunicati in diverse culture e tramite tutta una serie di arti e altre forme culturali.",
    // ];


    $competenze = estrai_competenze_scolastiche_studente($studente['id_studente'], 'SS', $anno_inizio);

    // Dizionario
    $labels = [
        "p1_titolo"           => "CERTIFICAZIONE delle COMPETENZE DI BASE<br>acquisite nel percorso formativo offerto dal nostro Istituto",

        "p1_studente"         => "che lo student||min_eessa|| {$studente['cognome']} {$studente['nome']}",
        "p1_nascita"          => "nat||min_oa|| il ".date('d/m/Y', $studente['data_nascita'])." a $luogo_nascita Stato $stato_nascita",
        "p1_iscritto"         => "iscritt||min_oa|| presso questo Istituto nella classe {$studente['classe']} sez. {$studente['sezione']}",
        "p1_indirizzo"        => "indirizzo di studio: {$studente['descrizione_indirizzi']}",
        "p1_periodo"          => "nell'anno scolastico $anno_scolastico_attuale",

        "legenda"             =>
            "<b>(1)</b> livelli relativi all'acquisizione delle competenze di ciascun asse:<br><br>
<b>A – Avanzato</b><br>
L’alunno/a svolge compiti e risolve problemi complessi, mostrando padronanza nell’uso delle conoscenze e delle abilità; propone e sostiene le proprie opinioni e assume in modo responsabile decisioni consapevoli.
<br><br>
<b>B – Intermedio</b><br>
L’alunno/a svolge compiti e risolve problemi in situazioni nuove, compie scelte consapevoli, mostrando di saper utilizzare le conoscenze e le abilità acquisite.
<br><br>
<b>C – Base</b><br>
L’alunno/a svolge compiti semplici anche in situazioni nuove, mostrando di possedere conoscenze e abilità fondamentali e di saper applicare basilari regole e procedure apprese.
<br><br>
<b>D – Iniziale</b><br>
L’alunno/a, se opportunamente guidato/a, svolge compiti semplici in situazioni note.",

    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
    }
    //}}} </editor-fold>


    $f = 'helvetica';
    $fd = 10;

    $pdf->AddPage('P');
    $pdf->SetAutoPageBreak("off", 1);
    inserisci_intestazione_pdf($pdf, $id_classe);
    $pdf->ln(10);
    $pdf->SetFont($f, 'B', 14);
    $pdf->writeHTMLCell(0, 0, '', '', $labels['p1_titolo'], 0, 1, false, true, 'C');
    $pdf->ln(7);
    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', 'Il Coordinatore Didattico', 0, 1, false, true, 'C');
    $pdf->ln(3);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', 'Visti gli atti di ufficio;', 0, 1);
    $pdf->ln(3);
    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', 'CERTIFICA', 0, 1, false, true, 'C');
    $pdf->ln(5);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 7, '', '', $labels['p1_studente'], 0, 1);
    $pdf->writeHTMLCell(0, 7, '', '', $labels['p1_nascita'], 0, 1);
    $pdf->writeHTMLCell(0, 7, '', '', $labels['p1_iscritto'], 0, 1);
    $pdf->writeHTMLCell(0, 7, '', '', $labels['p1_indirizzo'], 0, 1);
    $pdf->writeHTMLCell(0, 7, '', '', $labels['p1_periodo'], 0, 1);
    $pdf->writeHTMLCell(0, 0, '', '', 'nell’assolvimento del percorso formativo dell’anno scolastico concluso', 0, 1);

    $pdf->ln(5);
    $pdf->SetFont($f, 'BI', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', 'ha acquisito', 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', 'le competenze di base di seguito indicate.', 0, 1);
//    $pdf->AddPage('P');
    $pdf->ln(4);

    $pdf->SetFont($f, '', 8);
    // $tbl_comp =
    //     '<table border="0.1px" cellpadding="2">'
    //         . '<tr>'
    //             . '<td colspan="2" align="center"><b>COMPETENZE DI BASE E RELATIVI LIVELLI RAGGIUNTI (1)</b></td>'
    //         . '</tr>';
    // foreach ($arr_comp as $tit_area => $comps_area) {
    //     $tbl_comp .=
    //           '<tr align="center">'
    //                 . '<td width="80%"><b>'.$tit_area.'</b></td>'
    //                 . '<td width="20%"><b>LIVELLI</b></td>'
    //         . '</tr>';
    //     foreach ($comps_area as $cmp_name => $cmp_val)
    //     {
    //         $nome_st = $arr_def_desc[$cmp_name];
    //         $tbl_comp .=
    //               '<tr>'
    //                     . '<td>'.$nome_st.'</td>'
    //                     . '<td>'.$cmp_val.'</td>'
    //             . '</tr>';
    //     }
    // }
    // $tbl_comp .= '</table>';
    // $pdf->writeHTMLCell(0, 0, '', '', $tbl_comp, 0, 1);

    $pdf->SetFont('helvetica', 'B', 9);
    $pdf->MultiCell(40, 12, "COMPETENZA CHIAVE", "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
    $pdf->MultiCell(125, 12, "COMPETENZE DI BASE E RELATIVI LIVELLI RAGGIUNTI (1)", "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
    $pdf->MultiCell(25, 12, "LIVELLO*", "LTRB", "C", false, 1, "", "", true, 0, false, true, 12, "M");
    $pdf->SetFont('helvetica', '', 9);
    $pdf->SetFont($f, '', 8);
    // Identifico e raggruppo le competenze che hanno la stessa chiave e le divido da quelle dove invece va mostrato solo il testo
    $competenze_livello = [];
    $competenze_testo = [];
    foreach ($competenze as $competenza)
    {
        if ($competenza['competenze_chiave'] == "")
        {
            $competenze_testo[] = $competenza;
        }
        else
        {
            $competenze_livello[$competenza['competenze_chiave']][$competenza['id_competenza_scolastica']] = $competenza;
        }
    }

    $cont = 1;
    // echo_debug($competenze_livello);
    // Scrivo le competenze con i livelli
    foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
    {
        // scansione lingue
        $chiave_lingue_compilata = false;
        foreach ($raggruppamento_chiave as $key => $competenza_singola)
        {
            $desc_tmp = $competenza_singola['descrizione'];
            if (stripos($desc_tmp, '###id_lingua_') !== false ) {
                $lingua_varsrc = explode('###', $desc_tmp)[1];
                
                if (!empty($competenza_singola['testo'])) {
                    $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", $competenza_singola['testo'], $desc_tmp);
                    $raggruppamento_chiave[$key]['lingua_valutazione'] = 'si';
                    $chiave_lingue_compilata = true;
                } 
                // elseif (!$chiave_lingue_compilata) {
                //     $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", '______________________________', $desc_tmp);
                //     $raggruppamento_chiave[$key]['lingua_valutazione'] = 'si';
                // } 
                else {
                    unset($raggruppamento_chiave[$key]);
                }
            }
        }

        $h_competenze = 0;
        foreach ($raggruppamento_chiave as $k => $competenza_singola)
        {
            if (stripos($chiave, 'Competenza multilinguistica') !== false &&
                !isset($competenza_singola['lingua_valutazione']) ) {
                $raggruppamento_chiave[$k]['h_riga'] = $pdf->getStringHeight(150, decode($competenza_singola['descrizione']),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 );
            } else {
                $raggruppamento_chiave[$k]['h_riga'] = max($pdf->getStringHeight(125, decode($competenza_singola['descrizione']),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                    $pdf->getStringHeight(25, $competenza_singola['valore'],  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                    5);
            }
            $h_competenze +=  $raggruppamento_chiave[$k]['h_riga'];
        }
        $h_chiave = max($h_competenze, $pdf->getStringHeight(40, decode($chiave),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1));

        if ($pdf->GetY()+$h_chiave>$pdf->GetPageHeight()) {
            $pdf->AddPage('P');
        }
        $pdf->SetFont('helvetica', 'B', 9);
        $pdf->MultiCell(40, $h_chiave, decode($chiave), 1, "L", false, 0, "", "", true, 1, false, true, $h_chiave, "M");
        $pdf->SetFont('helvetica', '', 9);
        $x_rel = $pdf->getX();
        foreach ($raggruppamento_chiave as $competenza_singola)
        {
            if ($competenza_singola['valore'] == '') { $competenza_singola['valore'] = ' ';}

            if (stripos($chiave, 'Competenza multilinguistica') !== false &&
                !isset($competenza_singola['lingua_valutazione'])) {
                $pdf->MultiCell(150, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 1, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
            } else {
                $pdf->MultiCell(125, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 0, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                $pdf->MultiCell(25, $competenza_singola['h_riga'], $competenza_singola['valore'], 1, "C", false, 1, "", "", true, 0, false, true, $competenza_singola['h_riga'], "M");
            }
            // $pdf->setX($x_rel);
        }

        $cont++;
    }

    // Scrivo le competenze con il testo
    foreach ($competenze_testo as $competenza_singola)
    {
        $riga_controllo_altezza = decode(str_replace("alunno/a", "alunn$min_oa",$competenza_singola['descrizione'])) . "\n " . decode($competenza_singola['testo']);
        $riga = $pdf->MultiCellNbLines(180, $riga_controllo_altezza) * 4.5;
        $pdf->MultiCell(190, $riga, $riga_controllo_altezza, "LBR", "L", false, 1, "", "", true, 0, false, true, $riga, "M");
        $cont++;
    }

    $pdf->ln(3);
    // Calcola l'altezza totale delle prossime celle
    $pdf->SetFont($f, '', $fd);
    if ($pdf->GetY() + 30 > $pdf->GetPageHeight()) {
        $pdf->AddPage('P');
    }

    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(140, 0, '', '', "BRESCIA, {$data_Day}/{$data_Month}/{$data_Year}", 0, 0);
    $pdf->writeHTMLCell(0, 0, '', '', "Il Dirigente Scolastico<br>".$studente['nome_dirigente'], 0, 1, false, true, 'C');
    $pdf->ln(5);
    $pdf->writeHTMLCell(0, 0, '', '', $labels['legenda'], 0, 1);
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'       => $id_classe,
    'data_day'        => $data_Day,
    'data_month'      => $data_Month,
    'data_year'       => $data_Year,
    'periodo'         => $periodo,
    'orientamento'    => $orientamento,
];


switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Competenze ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Competenze ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
