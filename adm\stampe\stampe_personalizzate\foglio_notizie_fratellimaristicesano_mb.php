<?php

$altezza_celle = 6;
$font_dim = 8;
$font = 'helvetica';

if ($id_classe == "TUTTE") {
    $elenco_classi = estrai_classi();
} else {
    $elenco_classi[0]["id_classe"] = $id_classe;
}

foreach ($elenco_classi as $classe_attuale) {
    $id_classe = $classe_attuale["id_classe"];
    $elenco_studenti = estrai_studenti_classe((int) $id_classe);

    foreach ($elenco_studenti as $stud_sel) {
        $id_stud = $stud_sel["id_studente"];
        $dati_classe = estrai_classi_studente((int) $id_stud);
//        $dati_curriculum = estrai_curriculum_studente((int) $id_stud, $no_iscritto=true, $no_giudizio_sospeso, $no_ammissione_esame, $no_preiscrizione, $no_licenziato, false, '', $no_trasferito);
        $dati_curriculum = estrai_curriculum_studente((int) $id_stud, $no_iscritto=true);
        foreach ($dati_curriculum as $c => $riga_curriculum) {
            if ($riga_curriculum['esito'] == 'Ammesso esame di stato') {
                unset($dati_curriculum[$c]);
            }
        }
        $dati_curriculum = array_values($dati_curriculum);
        $id_classe = $dati_classe[0]["id_classe"];

        //estraggo i dati dello studente
        $studente = estrai_dati_studente((int) $id_stud);
        $nome = $studente['cognome'].' '.$studente['nome'];
        $classe_attuale = $studente['classe'];
        $sezione_attuale = $studente['sezione'];
        $indirizzo_attuale = $studente['codice_indirizzi'];
        $indirizzi_stud = $studente['descrizione_indirizzi'];
        $sezioni_stud = $studente['sezione'];
        $comune_domicilio = $studente['descrizione_domicilio'];
        $indirizzo_domicilio = $studente['indirizzo_domicilio'];
        $comune_residenza = $studente['descrizione_residenza'];
        $indirizzo_residenza = $studente['indirizzo'];
        $telefono = $studente['telefono'];
        $sesso = $studente['sesso'];
        $cittadinanza = $studente['descrizione_cittadinanza'];
        $padre = $studente['cognome_padre'] . ' ' . $studente['nome_padre'];
        $madre = $studente['cognome_madre'] . ' ' . $studente['nome_madre'];
        $tutore = $studente['cognome_tutore'] . ' ' . $studente['nome_tutore'];
        $motivazioni_crediti = "3° -  " . $studente["motivi_crediti_terza"] . "\n" .
                "4° -  " . $studente["motivi_crediti_quarta"] . "\n" .
                "5° -  " . $studente["motivi_crediti_quinta"] . " " . $studente["motivi_crediti_agg"];
        $comune_nascita = estrai_provincia_comune($studente["codice_comune_nascita"]);
        $data_nascita = date("d/m/Y", $studente["data_nascita"]);
        $nazione_nascita = estrai_nazione($studente["stato_nascita"]);
        if ($comune_nascita[0] == "ERROR") {
            $luogo_nascita = $studente["citta_nascita_straniera"] . " (" . $nazione_nascita["descrizione"] . ")";
        } else {
            $luogo_nascita = $comune_nascita["descrizione"] . " (" . $comune_nascita["codice"] . ")";
        }

        $anno_scolastico_curr = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');

        $crediti_terza = traduci_crediti_totali($studente["crediti_terza"]);
        $crediti_quarta = traduci_crediti_totali($studente["crediti_quarta"]);
        $crediti_quinta = traduci_crediti_totali($studente["crediti_quinta"]);
        $int_crediti_terza = traduci_crediti_totali($studente["crediti_reintegrati_terza"]);
        $int_crediti_quarta = traduci_crediti_totali($studente["crediti_reintegrati_quarta"]);
        $crediti_agg = traduci_crediti_totali($studente["crediti_finali_agg"]);

        $totale_crediti_triennio = intval($studente["crediti_terza"]) +
                intval($studente["crediti_quarta"]) +
                intval($studente["crediti_quinta"]) +
                intval($studente["crediti_reintegrati_terza"]) +
                intval($studente["crediti_reintegrati_quarta"]);

        $totale_crediti_finali = $totale_crediti_triennio +
                intval($studente["crediti_finali_agg"]);

        $totale_crediti_triennio_lettere = traduci_crediti_totali($totale_crediti_triennio);
        $totale_crediti_finali_lettere = traduci_crediti_totali($totale_crediti_finali);


        // Dizionario temporaneo
        $labels = [
            "nato"               => "Nat||min_oa|| a ",
            'notizie' =>"NOTIZIE SULLE SCUOLE FREQUENTATE E SUI TRASFERIMENTI DELL’ALUNN||max_oa||"
        ];

        // Identificazione genere da dati
        if (strtoupper($studente['sesso']) == 'F') {
            $min_oa = "a";
            $min_eessa = "essa";
            $max_oa = "A";
            $max_eessa = "ESSA";
            $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
            $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
            $studente['esito_corrente_calcolato'] = str_replace('Ammesso', 'Ammessa', $studente['esito_corrente_calcolato']);
            $studente['esito_corrente_calcolato'] = str_replace('ammesso', 'ammessa', $studente['esito_corrente_calcolato']);
        } else {
            $min_oa = "o";
            $min_eessa = "e";
            $max_oa = "O";
            $max_eessa = "E";
        }

        // replace lettere finali per genere
        foreach ($labels as $k => $label) {
            $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
            $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
            $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
            $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
        }


        // pdf studente
        $pdf->SetFillColor(220);
        $pdf->AddPage('L');
        $pdf->SetAutoPageBreak("off", 1);

        //{{{ <editor-fold defaultstate="collapsed" desc="Intestazione">
        $pdf->SetCellHeightRatio(1.40);
        $pdf->Image('immagini_scuola/logo_repubblica_colori.jpg', 10, 8, 20, 20, 'JPG', false);
        $pdf->SetFont($font, 'B', $font_dim + 3);
        $pdf->MultiCell(0, 0, "MINISTERO DELL’ISTRUZIONE, DELL’UNIVERSITA’ E DELLA RICERCA", 0, 'C');
        $pdf->SetFont($font, '', $font_dim + 2);
        $pdf->MultiCell(0, 0, "C. M. n. 400 DEL 31/12/1991 – C.M. n.289 del 12-10-92", 0, 'C');
        $pdf->CellFitScale(0, 0, "_________________________________________________________", 0, 1, "C");
        $pdf->SetFont($font, '', $font_dim);
        $pdf->Ln(2);
        $pdf->MultiCell(0, 0, "Scuola secondaria di primo grado FRATELLI MARISTI\nVia San Carlo, 20 – 20211 Cesano Maderno (MB) – Cod.Mecc. MB1M01000C", 0, 'C', false, 1, '', '', true, 0, false, true, 0, 'M');
        $pdf->CellFitScale(0, 0, "_________________________________________________________", 0, 1, "C");
        $pdf->SetCellHeightRatio(1.25);
//            inserisci_intestazione_pdf($pdf, (int) $studente["id_classe"]);
        //}}} </editor-fold>

        $pdf->Ln(3);

        $pdf->SetFont($font, 'B', $font_dim + 9);
        $pdf->CellFitScale(0, 0, "FOGLIO NOTIZIE ALUNNO: ".$nome, 0, 1, "C");
        $pdf->Ln(8);

        $pdf->SetFont($font, '', $font_dim);
        $pdf->CellFitScale(130, 0, "Residente in: ".$comune_residenza . " " . $studente['provincia_residenza_da_comune'] . ", " . ' '. $indirizzo_residenza, 0, 0, "L");
        $pdf->CellFitScale(0, 0, "{$labels['nato']} $luogo_nascita il $data_nascita", 0, 1, "L");
        $pdf->CellFitScale(150, 0, "Codice Fiscale: {$studente['codice_fiscale']}", 0, 0, "L");
        $pdf->CellFitScale(0, 0, "Cittadinanza: $cittadinanza", 0, 1, "L");
        $pdf->Ln(4);

        $pdf->CellFitScale(0, 1, "", 'T', 1, "C");
        $pdf->writeHTMLCell(0, 0, '', '', '' . '(Padre) <b>' . $padre. '</b>', 0, 1);
        $pdf->writeHTMLCell(0, 0, '', '', '' . '(Madre) <b>' . $madre . '</b>', 0, 1);
        $pdf->CellFitScale(0, 1, "", 'B', 1, "C");
        $pdf->Ln(4);
//    echo_debug($dati_curriculum);
        $pdf->CellFitScale(0, 0, $labels['notizie'], 0, 1, "C");
        $pdf->Ln(1);
        $pdf->CellFitScale(40, $altezza_celle, "Anno scolastico", 1, 0, "C", 1);
        $pdf->CellFitScale(190, $altezza_celle, "Intitolazione Scuola", 1, 0, "C", 1);
        $pdf->CellFitScale(0, $altezza_celle, "Non statale", 1, 1, "C", 1);
        $numero_righe_curr = count($dati_curriculum);
        for ($cont_righe = 0; $cont_righe < $numero_righe_curr; $cont_righe++) {
            $anno_scolastico = $dati_curriculum[$cont_righe]['anno_scolastico'];
            //{{{ <editor-fold defaultstate="collapsed" desc="curriculum">
            switch ($dati_curriculum[$cont_righe]['tipo_scuola']) {
                case "scuola_media":
                    $classe_frequentata = "Scuola media: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                    break;
                case "scuola_superiore":
                    if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                        $classe_frequentata = "Istituto: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                    } else {
                        $classe_frequentata = "Classe: " . $dati_curriculum[$cont_righe]['classe'] .
                                " " .
                                "Istituto: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                    }
                    break;
                case "classe_interna":
                    if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                        $classe_frequentata = $dati_curriculum[$cont_righe]['sezione'] .
                                " " .
                                $dati_curriculum[$cont_righe]['indirizzo'];
                    } else {
                        $classe_frequentata = $dati_curriculum[$cont_righe]['classe'] .
                                " " .
                                $dati_curriculum[$cont_righe]['sezione'] .
                                " " .
                                $dati_curriculum[$cont_righe]['indirizzo'];
                    }
                    break;
                case "altro":
                    $classe_frequentata = $dati_curriculum[$cont_righe]['altro'];
                    break;
                default:
                    if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                        $classe_frequentata = $dati_curriculum[$cont_righe]['altro'];
                    } else {
                        if (strlen($dati_curriculum[$cont_righe]['altro']) > 0) {
                            $classe_frequentata = $dati_curriculum[$cont_righe]['altro'] . " ";
                        } else {
                            $classe_frequentata = "";
                        }

                        $classe_frequentata .= $dati_curriculum[$cont_righe]['classe'] .
                                " " .
                                $dati_curriculum[$cont_righe]['sezione'] .
                                " " .
                                $dati_curriculum[$cont_righe]['indirizzo'];
                    }
                    break;
            }
            //}}} </editor-fold>
            if ($studente['codice_meccanografico'] != $dati_curriculum[$cont_righe]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $dati_curriculum[$cont_righe]['id_scuola']) {
                $classe_frequentata = $dati_curriculum[$cont_righe]['classe_desc'] . ' ' . $dati_curriculum[$cont_righe]['nome_scuola'];
            } else {
                $classe_frequentata = $dati_curriculum[$cont_righe]['classe_tradotta'] . ' ' . $dati_curriculum[$cont_righe]['descrizione'];
            }
            if ($dati_curriculum[$cont_righe]['id_scuola']== 'MB1M01000C') {
                $classe_frequentata .= " - FRATELLI MARISTI";
            }

            $non_statale = $dati_curriculum['stato_privatista']=='SI'?'X':'';
            $non_statale = 'X';
//            echo_debug($dati_curriculum);
            $pdf->SetFont($font, '', $font_dim);
            $pdf->CellFitScale(40, 0, $anno_scolastico, 1, 0, "C");
            $pdf->CellFitScale(190, 0, $classe_frequentata, 1, 0, "L");
            $pdf->CellFitScale(0, 0, $non_statale, 1, 1, "C");
        }

        $pdf->Ln(4);
        $pdf->CellFitScale(0, 0, "CURRICULUM SCOLASTICO", 0, 1, "C");
        $pdf->Ln(1);
        $pdf->SetFont('helvetica', 'B', $font_dim);
        $pdf->CellFitScale(25, $altezza_celle*2, "ANNO SCOLASTICO", 1, 0, "C", 1);
        $pdf->CellFitScale(30, $altezza_celle*2, "CLASSE", 1, 0, "C", 1);
        $x_st = $pdf->GetX();
        $y_st = $pdf->GetY();
        $pdf->CellFitScale(65, $altezza_celle, "RISPETTO ALL'ETA SCOLARE", 1, 0, "C", 1);
        $pdf->SetXY($x_st,$y_st+$altezza_celle);
        $pdf->CellFitScale(15, $altezza_celle, "ANT", 1, 0, "C", 1);
        $pdf->CellFitScale(15, $altezza_celle, "REG", 1, 0, "C", 1);
        $pdf->CellFitScale(15, $altezza_celle, "RIT", 1, 0, "C", 1);
        $pdf->CellFitScale(20, $altezza_celle, "ANNI", 1, 0, "C", 1);
        $pdf->SetXY($x_st+65,$y_st);
        $pdf->CellFitScale(20, $altezza_celle*2, "RIPETENTE", 1, 0, "C", 1);
        $pdf->CellFitScale(20, $altezza_celle*2, "DATA ISCRIZIONE", 1, 0, "C", 1);
        $pdf->CellFitScale(40, $altezza_celle*2, "INTERRUZIONE FREQUENZA IN CORSO D'A.S.", 1, 0, "C", 1);
        $pdf->CellFitScale(0, $altezza_celle*2, "RISULTATO FINALE", 1, 1, "C", 1);
        for ($cont_righe = 0; $cont_righe < $numero_righe_curr; $cont_righe++) {
            $anno_scolastico = $dati_curriculum[$cont_righe]['anno_scolastico'];
            $esito = decode($dati_curriculum[$cont_righe]['esito']);

            $stampa_ripetente = '';
            $esito_compatto = '';
            $val_ant = '';
            $val_reg = '';
            $val_rit = '';
            $val_anni = '';
            $val_iscr_partic = '';
            $data_iscr = $dati_curriculum[$cont_righe]['data_tradotta'];
            $data_iscr = '';
            $val_interruzione = '';
            //{{{ <editor-fold defaultstate="collapsed" desc="curriculum">
            switch ($dati_curriculum[$cont_righe]['tipo_scuola']) {
                case "scuola_media":
                    $classe_frequentata = "Scuola media: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                    break;
                case "scuola_superiore":
                    if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                        $classe_frequentata = "Istituto: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                    } else {
                        $classe_frequentata = "Classe: " . $dati_curriculum[$cont_righe]['classe'] .
                                " " .
                                "Istituto: " . $dati_curriculum[$cont_righe]['nome_scuola'];
                    }
                    break;
                case "classe_interna":
                    if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                        $classe_frequentata = $dati_curriculum[$cont_righe]['sezione'] .
                                " " .
                                $dati_curriculum[$cont_righe]['indirizzo'];
                    } else {
                        $classe_frequentata = $dati_curriculum[$cont_righe]['classe'] .
                                " " .
                                $dati_curriculum[$cont_righe]['sezione'] .
                                " " .
                                $dati_curriculum[$cont_righe]['indirizzo'];
                    }
                    break;
                case "altro":
                    $classe_frequentata = $dati_curriculum[$cont_righe]['altro'];
                    break;
                default:
                    if ($dati_curriculum[$cont_righe]['classe'] == 0) {
                        $classe_frequentata = $dati_curriculum[$cont_righe]['altro'];
                    } else {
                        if (strlen($dati_curriculum[$cont_righe]['altro']) > 0) {
                            $classe_frequentata = $dati_curriculum[$cont_righe]['altro'] . " ";
                        } else {
                            $classe_frequentata = "";
                        }

                        $classe_frequentata .= $dati_curriculum[$cont_righe]['classe'] .
                                " " .
                                $dati_curriculum[$cont_righe]['sezione'] .
                                " " .
                                $dati_curriculum[$cont_righe]['indirizzo'];
                    }
                    break;
            }
            //}}} </editor-fold>
            switch ($esito) {
                case 'Ammesso alla classe successiva':
                case 'Ammesso al successivo grado dell\'istruzione obbligatoria':
                case 'Qualificato':
                case 'Licenziato':
                case 'Diplomato':
                    $esito_compatto = 'am';
                    $val_reg = 'X';
                    break;
                case 'Non ammesso alla classe successiva':
                case 'Non ammesso al successivo grado dell\'istruzione obbligatoria':
                case 'Non ammesso esame di stato':
                case 'Non qualificato':
                case 'Non diplomato':
                    $esito_compatto = 'n/a';
                    $ripetente = explode('/',$anno_scolastico)[1] . "/" . (explode('/',$anno_scolastico)[1]  +1);
                    $val_reg = 'X';
                    break;
                case 'Iscritto':
                    $esito_compatto = 'isc';
                    $val_reg = 'X';
                    break;
                case 'Ammesso esame di stato':
                    $esito_compatto = 'am_e';
                    break;
                case 'Trasferito':
                    $esito_compatto = 'tra';
                    $val_interruzione = 'X' . ($dati_curriculum[$cont_righe]['data_tradotta']!=''?" (il {$dati_curriculum[$cont_righe]['data_tradotta']})":"");
                    break;
                case 'Ritirato':
                    $esito_compatto = 'rit';
                    $val_rit = 'X';
                    $val_interruzione = 'X' . ($dati_curriculum[$cont_righe]['data_tradotta']!=''?" (il {$dati_curriculum[$cont_righe]['data_tradotta']})":"");
                    break;
                default:
                    $esito_compatto = '';
                    break;
            }
            if ($studente['codice_meccanografico'] != $dati_curriculum[$cont_righe]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $dati_curriculum[$cont_righe]['id_scuola']) {
                $classe_frequentata = $dati_curriculum[$cont_righe]['classe_desc'] . ' ' . $dati_curriculum[$cont_righe]['nome_scuola'];
            } else {
                $classe_frequentata = $dati_curriculum[$cont_righe]['classe_tradotta'] . ' ' . $dati_curriculum[$cont_righe]['descrizione'];
            }
            if ($ripetente == $anno_scolastico) {
                $stampa_ripetente = 'X';
                $val_anni++;
            }

            if ($studente['cognome'].' '.$studente['nome'] == 'SAPONARA GIOVANNI PAOLO') {
                $val_ant = 'X';
                $val_reg = '';
                $val_rit = '';
                $val_anni = '';
            }

            $pdf->SetFont('helvetica', '', $font_dim);
            $pdf->CellFitScale(25, $altezza_celle, $anno_scolastico, 1, 0, "C");

            $pdf->CellFitScale(30, $altezza_celle, $classe_frequentata, 1, 0, "C");
            $pdf->CellFitScale(15, $altezza_celle, $val_ant, 1, 0, "C");
            $pdf->CellFitScale(15, $altezza_celle, $val_reg, 1, 0, "C");
            $pdf->CellFitScale(15, $altezza_celle, $val_rit, 1, 0, "C");
            $pdf->CellFitScale(20, $altezza_celle, $val_anni, 1, 0, "C");
            $pdf->CellFitScale(20, $altezza_celle, $stampa_ripetente, 1, 0, "C");
            $pdf->CellFitScale(20, $altezza_celle, $data_iscr, 1, 0, "C");
            $pdf->CellFitScale(40, $altezza_celle, $val_interruzione, 1, 0, "C");
            $pdf->CellFitScale(0, $altezza_celle, $esito, 1, 1, "C");
        }

        $pdf->Ln(4);
        $pdf->SetFont($font, 'B', $font_dim);
        $pdf->CellFitScale(180, 0, "", 0, 0, "C", false);
        $pdf->writeHTMLCell(0, 0, '', '', "Il Direttore Didattico<br>{$studente['nome_dirigente']}<br>"
               .'<img src="immagini_scuola/firma_dirigente.png" height="60" width="100">'
                , 0, 1, false, true, 'C');
        $pdf->SetFont($font, '', $font_dim);

        $pdf->ln(5);
        $pdf->SetFont($font, 'I', $font_dim);
        $pdf->writeHTMLCell(0, 0, '', '', 'Il presente certificato non può essere prodotto agli organi della pubblica amministrazione o ai privati gestori di pubblici servizi (art. 15, comma 1, L. 183/11)', 0, 0, false, true, 'C');
    }
}