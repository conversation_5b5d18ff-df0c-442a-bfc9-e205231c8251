<?php
// setto il tempo di esecuzione massima per non bloccare l'esecuzione se il processo è lungo
ini_set('max_execution_time', '240');
$formatter = new IntlDateFormatter('it_IT', IntlDateFormatter::FULL, IntlDateFormatter::NONE);

// estraggo l'id del template pagella selezionato
if (empty($id_template_pagella_word) || isset($tipo_stampa)) {
    $id_template_pagella_word = str_replace('pagella_word_', '', $tipo_stampa);
}

// dati template word selezionato
$pagella_word = estrai_modelli_word($id_template_pagella_word, null, ">= 0")[0];
$abbinamenti_word = estrai_abbinamenti_modello_word($id_template_pagella_word, $id_classe, $periodo);

// ritraggo i campi usati dal template
$parametri = [];
if ($db_official != $db_key) {
    $parametri['anno_db'] = $db_key;
}
$parametri['flag_canc'] = '>= 0';
$variabili_template = nextapi_call('phpoffice/phpword/getTemplateVariables/' . $id_template_pagella_word, 'GET', $parametri, $current_key);

// dati base
$tipo_abbinamento = $abbinamenti_word[0]['tipo_abbinamento'];
$anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
$anno_inizio = explode('/', $anno_scolastico)[0];
$anno_fine = explode('/', $anno_scolastico)[1];
$dati_classe = estrai_classe($id_classe);
$elenco_studenti = estrai_studenti_classe($id_classe);
$dati_sede = estrai_sede_singola($dati_classe["id_sede"]);
$periodo_tabellone = ($dati_classe['tipo_indirizzo'] == 4) ? $periodo + 20 : $periodo;
$data_estesa_inizio = estrai_parametri_singoli('DATA_INIZIO_ANNO_SCOLASTICO').'/'.explode("/", estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE"))[0];
$data_estesa_inizio_ts = strtotime(str_replace('/', '-', $data_estesa_inizio));
$data_estesa_fine = estrai_parametri_singoli('DATA_FINE_ANNO_SCOLASTICO').'/'.explode("/", estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE"))[1];
$data_estesa_fine_ts = strtotime(str_replace('/', '-', $data_estesa_fine));
$data_estesa_inizio_1qt_ts = estrai_parametri_singoli('DATA_INIZIO_LEZIONI', $id_classe, 'classe');
$data_estesa_fine_1qt_ts = (int) estrai_parametri_singoli('DATA_INIZIO_SECONDO_QUADRIMESTRE', $id_classe, 'classe')-60;
$data_estesa_inizio_finale_ts = estrai_parametri_singoli('DATA_INIZIO_SECONDO_QUADRIMESTRE', $id_classe, 'classe');
$data_estesa_fine_finale_ts = estrai_parametri_singoli('DATA_FINE_LEZIONI', $id_classe, 'classe');
if (isset($parametri_personalizzati['data_calcolo_inizio_Day'])) {
    $data_estesa_inizio_intervallo = $parametri_personalizzati['data_calcolo_inizio_Day'].'/'.$parametri_personalizzati['data_calcolo_inizio_Month'].'/'.$parametri_personalizzati['data_calcolo_inizio_Year'];
    $data_estesa_inizio_intervallo_ts = strtotime(str_replace('/', '-', $data_estesa_inizio_intervallo));
}
if (isset($parametri_personalizzati['data_calcolo_fine_Day'])) {
    $data_estesa_fine_intervallo = $parametri_personalizzati['data_calcolo_fine_Day'].'/'.$parametri_personalizzati['data_calcolo_fine_Month'].'/'.$parametri_personalizzati['data_calcolo_fine_Year'];
    $data_estesa_fine_intervallo_ts = strtotime(str_replace('/', '-', $data_estesa_fine_intervallo));
}
switch ($periodo) {
    case '7':
    case '27':
        $periodo_pagella = 'intermedia';
        break;
    case '8':
    case '28':
        $periodo_pagella = 'pagellinast';
        break;
    case '9':
    case '29':
        $periodo_pagella = 'finale';
        break;
    default:
        $periodo_pagella = 'pagellina' . $periodo;
        break;
}

$containsAttendance = false;
foreach ($variabili_template as $variabile) {
    if (stripos($variabile, 'attendance') !== false) {
        $containsAttendance = true;
        break;
    }
}
$assenze_classe = [];
if ($containsAttendance) {
    $anni = explode('/', $anno_scolastico);
    $dati_assenze = estrai_riepilogo_assenze_periodo_classe((int) $id_classe, intval(07), (int) $anni[0], intval(06), (int) $anni[1]);
    for ($cont = 0; $cont < count($dati_assenze); $cont++) {
        for ($cont_int = 0; $cont_int < count($dati_assenze[$cont]); $cont_int++) {
            for ($cont_ult = 0; $cont_ult < 21; $cont_ult++) {
                if (intval($dati_assenze[$cont][$cont_int][$cont_ult]) > 0) {
                    $assenze_classe[$dati_assenze[$cont][$cont_int][52]][$cont_ult] = $assenze_classe[$dati_assenze[$cont][$cont_int][52]][$cont_ult] + $dati_assenze[$cont][$cont_int][$cont_ult];
                }
            }
        }
    }
}

//{{{ <editor-fold defaultstate="collapsed" desc="template">
// nome del file
$nome = $pagella_word['nome'] . ' ' . $dati_classe['classe'] . $dati_classe['sezione'] . ' ' . $dati_classe['codice_indirizzi'] . ' ' . date('YmdHi');

// percorso directory
$rel_dir = 'tmp_word/';
$base_dir = '/var/www-source/mastercom/' . $rel_dir;
$rand_dir = substr(md5(rand(0, 1000000)), 0, 12);
$temp_dir = $base_dir . $rand_dir;
$dir = $temp_dir . '/';
exec('mkdir ' . $temp_dir);
// modifica owner directory temporanea con il proprietario corrente, necessario per sistemare i permessi scrittura sulla directory anche per l esecuzione da script
chown($temp_dir, 'www-data');

// parametri di default usati nella chiamata next-api (all'interno del ciclo studenti) per traslare i campi in valori
$parametri = [];
$parametri['flag_canc'] = '>= 0';
$parametri['temp_dir'] = $dir;
if ($destinazione_stampa != 'ZIP_WORD'){
    $parametri['formato_output'] = 'pdf';
}
if ($db_official != $db_key) {
    $parametri['anno_db'] = $db_key;
}
// array file usati nel controllo della destinazione
$array_file_stampa = $array_per_sito = [];
// ciclo per studente
foreach ($elenco_studenti as $studente)
{
    $id_studente = $studente['id_studente'];

    // filtro gli studenti da stampare in base al tipo della pagella
    if ($tipo_abbinamento == 'RELIGIONE' && $studente['esonero_religione'] == 1){
        continue;
    }
    if ($tipo_abbinamento == 'ALTERNATIVA' && $studente['esonero_religione'] == 0){
        continue;
    }

    //{{{ <editor-fold defaultstate="collapsed" desc="estraggo i dati delle variabili richieste dal modello per lo studente">
    $array_dati = [];
    // dati passati di default
    $array_dati['tipo_visualizzazione_voti_primo_quad'] = identifica_periodo_tipo_voto(($dati_classe['tipo_indirizzo'] == 4 ? '27' : '7'),(int) $id_classe);

    $dati_studente = estrai_dati_studente($id_studente);
    //    $array_dati['tipo'] = preg_replace('/[^A-Za-z\_]/', '', $dati_studente['cognome'].'_'.$dati_studente['nome']); // momentaneamente per metterlo nel nome file in output e non farlo sovrascrivere
    // recupero da parametri_personalizzati solo i parametri che sono utilizzati per fare calcoli o altre lavorazioni (es. stampare o meno le competenze trasversali)

    // ricerca quali periodi del tabellone estrarre in base alle variabili del template (contengono i codici _1qt - _2t - _finale) @@@ to add pagellinaXX
    $periodo_default = $periodo_tabellone; // periodo default: se lo stesso modello viene usato per piu periodi (es intermedio e finale) e se non è specificato un codice specifico per il periodo, viene usato quello di default
    $periodi_tmp = $periodi_cmplib_tmp = $periodi_competenze_tmp = [];
    //{{{ <editor-fold defaultstate="collapsed" desc="sviluppo">
    // dati su tag da estrarre 
    $include_struttura_campi_liberi = $include_discipline_aggregate = $include_elenco_materie = $include_materie = $include_elenco_materie_normali = $include_elenco_materie_opzionali = $include_elenco_materie_religione = $include_elenco_materie_condotta = $include_elenco_materie_sospese = $include_elenco_materie_totali = false; 
    // periodi specifici se presenti nel modello, i tag non vengono mostrati se si cercano specificando un periodo superiore a quello selezionato nei parametri di stampa (es. non verranno stampati i tag _finale, se il periodo selezionato in fase di stampa è intermedia)
    $trad_periodo_cod = [
        7 => "_1qt",
        8 => "_2t",
        9 => "_finale"
    ];
    foreach ($variabili_template as $variabile)
    {
        if (stripos($variabile,"_1qt") !== false) {
            $periodi_tmp['intermedia'] = 'intermedia';
            if (stripos($variabile,"campi_liberi") !== false || stripos($variabile,"giudizio_finale_") !== false) {
                $periodi_cmplib_tmp['_1qt'] = '_1qt';
            }
        }
        if (stripos($variabile,"_2t") !== false) {
            $periodi_tmp['pagellinast'] = 'pagellinast';
            if (stripos($variabile,"campi_liberi") !== false || stripos($variabile,"giudizio_finale_") !== false) {
                $periodi_cmplib_tmp['_2t'] = '_2t';
            }
        }
        if (stripos($variabile,"_finale") !== false && ($periodo_default==9 || $periodo_default==29)) {
            $periodi_tmp['finale'] = 'finale';
            if (stripos($variabile,"campi_liberi") !== false || stripos($variabile,"giudizio_finale_") !== false) {
                $periodi_cmplib_tmp['_finale'] = '_finale';
            }
        }
        if (preg_match('/_pagellina(\d+)/', $variabile, $matches)) {
            $desc_infraperiodo = "pagellina" . $matches[1];
            $periodi_tmp[$desc_infraperiodo] = $desc_infraperiodo;
            if (stripos($variabile, "campi_liberi") !== false) {
                $periodi_cmplib_tmp["_$desc_infraperiodo"] = "_$desc_infraperiodo";
            }
        }
        // campi liberi/competenze
        if ( stripos($variabile,"campi_liberi") !== false
                && stripos($variabile,"_finale")===false && stripos($variabile,"_2t")===false  && stripos($variabile,"_1qt")===false) {
            $periodi_cmplib_tmp['elenco_campi_liberi'] = '';
        }
        if ( $variabile == "giudizio_finale") {
            $periodi_cmplib_tmp['elenco_campi_liberi'] = '';
        }
        if (stripos($variabile,"elenco_competenze") !== false)
        {
            if (stripos($variabile,"_finale")===false && stripos($variabile,"_2t")===false  && stripos($variabile,"_1qt")===false) {
//                $periodi_competenze_tmp[7] = '_1qt';
//                if ($periodo>=8) {
//                    $periodi_competenze_tmp[8] = '_2t';
//                }
//                if ($periodo>=9) {
//                    $periodi_competenze_tmp[9] = '_finale';
//                }
            } elseif (stripos($variabile,"_finale")!==false) {
                $periodi_competenze_tmp[9] = '_finale';
            } elseif (stripos($variabile,"_2t")!==false) {
                $periodi_competenze_tmp[8] = '_2t';
            } elseif (stripos($variabile,"_1qt")!==false) {
                $periodi_competenze_tmp[7] = '_1qt';
            }
        }
//         @@@ to add pagellinaXX

        // altri tag da caricare
        if (!$include_struttura_campi_liberi && stripos($variabile,"struttura_campi_liberi") !== false) {
            $include_struttura_campi_liberi = true;
        }
        if (!$include_discipline_aggregate && (stripos($variabile, "discipline_aggregate") === 0 || 
                 preg_match('/\d+_discipline_aggregate/', $variabile)) !== false) {
            $include_discipline_aggregate = true;
        }
        if (!$include_materie && 
                (stripos($variabile, "materie") === 0 || 
                 preg_match('/\d+_materie/', $variabile))) {
            $include_materie = true;
        }
         
        if (!$include_elenco_materie && (stripos($variabile, "elenco_materie") === 0 || 
                 preg_match('/\d+_elenco_materie/', $variabile)) !== false) {
            $include_elenco_materie = true;
        }
        if (!$include_elenco_materie_normali && stripos($variabile,"elenco_materie_normali") !== false) {
            $include_elenco_materie_normali = true;
        }
        if (!$include_elenco_materie_condotta && stripos($variabile,"elenco_materie_condotta") !== false) {
            $include_elenco_materie_condotta = true;
        }
        if (!$include_elenco_materie_opzionali && stripos($variabile,"elenco_materie_opzionali") !== false) {
            $include_elenco_materie_opzionali = true;
        }
        if (!$include_elenco_materie_religione && stripos($variabile,"religione") !== false) {
            $include_elenco_materie_religione = true;
        }
        if (!$include_elenco_materie_sospese && stripos($variabile,"elenco_materie_sospese") !== false) {
            $include_elenco_materie_sospese = true;
        }
        if (!$include_elenco_materie_totali && stripos($variabile,"elenco_materie_totali") !== false) {
            $include_elenco_materie_totali = true;
        }
    }
    $periodi_competenze_tmp[$periodo] = $trad_periodo_cod[$periodo];
    $periodi_tmp['default'] = $periodo_default;
    // estrai i tabelloni per i periodi ricavati dello studente // @@@ to add pagellinaXX
    $voti_pagella_1qt = $voti_pagella_2t = $voti_pagella_finale = $voti_pagella = [];
    foreach ($periodi_tmp as $desc_periodo) {
        switch ($desc_periodo) {
            case 'intermedia':
                $periodo_tmp = ($dati_classe['tipo_indirizzo'] == 4) ? 7 + 20 : 7;
                $voti_pagella_1qt = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo_tmp, $id_studente);
                break;
            case 'pagellinast':
                $periodo_tmp = ($dati_classe['tipo_indirizzo'] == 4) ? 8 + 20 : 8;
                $voti_pagella_2t = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo_tmp, $id_studente);
                break;
            case 'finale':
                $periodo_tmp = ($dati_classe['tipo_indirizzo'] == 4) ? 9 + 20 : 9;
                $voti_pagella_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo_tmp, $id_studente);
                break;
            default:
                // @@@ to add pagellinaXX
                if (preg_match('/pagellina(\d+)/', $desc_periodo, $matches)) {
                    $periodo_tmp = ($dati_classe['tipo_indirizzo'] == 4) ? $matches[1] + 20 : $matches[1];
                    ${"voti_pagella_" . $desc_periodo} = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo_tmp, $id_studente);
                } else {
                    $voti_pagella = estrai_voti_pagellina_studente_multi_classe($id_classe, $desc_periodo, $id_studente); // desc periodo has already number periodo valido per la classe
                }
                break;
        }
    }
// echo '<pre>',echo_debug($periodo_tmp);print_r($voti_pagella);exit();
    // estrazione scrutini per le competenze dello studente
    $tab_scrutini_competenze = [];
    $param_nuovo_tabellone = estrai_parametri_singoli('ATTIVA_NUOVO_TABELLONE_COMPETENZE');
    if ( !empty($periodi_competenze_tmp) &&
        $dati_classe['tipo_indirizzo'] == '6' && $param_nuovo_tabellone == 'SI')
    {
        $parametri_comp = [
            "anno_scolastico" =>    $anno_scolastico,
            "estrai_valutazioni_figli"  =>  'SI'
        ];

        // merge dei vari scrutini da estrarre per le competenze
        foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
            $tab_scrutini_competenze[$pc] = nextapi_call('competenze_studente/dati_scrutinio/' . $valperiodo . '/' . $id_studente, 'GET', $parametri_comp, $current_key);
        }
    }

    // metti nel ciclo elenco_materie se esiste variabile da estrarre elenco_materie_normali, nota che ci sono anche i voti e monteore o deroghe
    // estrai materie studente
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $tot_assenze_fine_anno = $tot_ore_fine_anno = $tot_assenze_sel = $tot_ore_sel = $tot_assenze_1qt = $tot_ore_1qt = $tot_assenze_2t = $tot_ore_2t =
        $num_mat_sel = $num_mat_1qt = $num_mat_2t = $num_mat_finale = $somma_voti_sel = $somma_voti_1qt = $somma_voti_2t = $somma_voti_finale = 0;
    $scrutinato = false;
    $da_giudizio_sospeso = false;

    // filtri
    $filtro_scrutinio = $parametri_personalizzati['filtro_studenti_scrutinio']; // null / "TUTTI" / "NEGATIVO"
    switch ($filtro_scrutinio) {
        case "NEGATIVO":
            $stud_da_stampare = false;
            break;
        default:
            $stud_da_stampare = true;
            break;
    }
    $filtro_materie = $parametri_personalizzati['filtro_materie']; // null / NO_FILTRO / SOLO_VOTI
    $filtro_sospesi = $parametri_personalizzati['filtro_giudizio_sospeso']; // null / "TUTTI" / "SOSPESI_PROMOSSI" / "SOSPESI_BOCCIATI" / "NON_SOSPESI" / "SOLO_SOSPESI"

    // setto l'elenco delle materie con appositi voti, etc..
    $stampa_competenze_trasversali = $parametri_personalizzati['stampa_competenze_trasversali'];
    $quali_competenze_stampare = $parametri_personalizzati['quali_competenze_stampare'];
    foreach ($materie_studente as $materia)
    {
        // if ($materia['in_media_pagelle'] == 'NV') { // non stampata e non usata in pagella
        //     continue;
        // }

        $id_mat = $materia['id_materia'];
        $materia_con_voti = false;

        // inizializzo la materia temporanea che sarà poi associato all'array di ritorno
        $materia_tmp = [];
        $materia_tmp = $materia;

        //{{{ <editor-fold defaultstate="collapsed" desc="assegnazione voti, assenze, monteore, recuperi">
        // assegno i valori del tabellone studente per la materia per il periodo di default
        if (!empty($voti_pagella)) {
            // voti numerici
            $materia_tmp['voto_pagellina'] = $voti_pagella[$id_mat]['voto_pagellina'];
            $materia_tmp['voto_pagella'] = $voti_pagella[$id_mat]['voto_pagellina'];
            $materia_tmp['voto_scritto_pagella'] = $voti_pagella[$id_mat]['voto_scritto_pagella'];
            $materia_tmp['voto_orale_pagella'] = $voti_pagella[$id_mat]['voto_orale_pagella'];
            $materia_tmp['voto_pratico_pagella'] = $voti_pagella[$id_mat]['voto_pratico_pagella'];
            // voti tradotti
            foreach ($voti_pagella[$id_mat]['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voti_pagella[$id_mat]['voto_pagellina']) {
                    $materia_tmp['voto_pagellina_tradotto'] = $materia_tmp['voto_pagella_tradotto'] = $materia_tmp['voto_descrizione'] = $significato['valore_pagella'];
                    $materia_tmp['voto_pagellina_lingua'] = $materia_tmp['voto_pagella_lingua'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella[$id_mat]['voto_scritto_pagella']) {
                    $materia_tmp['voto_scritto_pagella_tradotto'] = $significato['valore_pagella'];
                    $materia_tmp['voto_scritto_pagella_lingua'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella[$id_mat]['voto_orale_pagella']) {
                    $materia_tmp['voto_orale_pagella_tradotto'] = $significato['valore_pagella'];
                    $materia_tmp['voto_orale_pagella_lingua'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella[$id_mat]['voto_pratico_pagella']) {
                    $materia_tmp['voto_pratico_pagella_tradotto'] = $significato['valore_pagella'];
                    $materia_tmp['voto_pratico_pagella_lingua'] = $significato['valore_pagella_lingua'];
                }
            }
            // monteore
            $materia_tmp['monteore'] = $voti_pagella[$id_mat]['monteore_totale'];
            $materia_tmp['monteore_totale_tradotto'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['monteore_totale']);
            $materia_tmp['monteore_arrotondato'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['monteore_totale'], 'ORE_ARROTONDATE');
            $materia_tmp['monteore_troncato'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['monteore_totale'], 'ORE_TRONCATE');
            $materia_tmp['monteore_esteso'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['monteore_totale'], 'ETICHETTA_ORE_MINUTI');
            $materia_tmp['monteore_ore_minuti'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['monteore_totale'], 'ORE_MINUTI_ESTESE');
            // assenze
            $materia_tmp['ore_assenza'] = $voti_pagella[$id_mat]['ore_assenza'];
            $materia_tmp['ore_assenza_tradotto'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['ore_assenza']);
            $materia_tmp['ore_assenza_arrotondato'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['ore_assenza'], 'ORE_ARROTONDATE');
            $materia_tmp['ore_assenza_troncato'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['ore_assenza'], 'ORE_TRONCATE');
            $materia_tmp['ore_assenza_esteso'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['ore_assenza'], 'ETICHETTA_ORE_MINUTI');
            $materia_tmp['ore_assenza_ore_minuti'] = stampa_ore_o_minuti($voti_pagella[$id_mat]['ore_assenza'], 'ORE_MINUTI_ESTESE');
            
            // recuperi
            $materia_tmp['tipo_recupero_tradotto'] = $voti_pagella[$id_mat]['tipo_recupero_tradotto'];
            $materia_tmp['esito_recupero'] = $voti_pagella[$id_mat]['esito_recupero'];

            // sommo le assenze e monteore al totale, e le variabili per calcolare la media
            if ($voti_pagella[$id_mat]['in_media_pagelle'] == 'SI') {
                $tot_assenze_sel += intval($voti_pagella[$id_mat]['ore_assenza']);
                $tot_ore_sel += intval($voti_pagella[$id_mat]['monteore_totale']);
                if ($voti_pagella[$id_mat]['voto_pagellina'] > 0) {
                    $somma_voti_sel += $voti_pagella[$id_mat]['voto_pagellina'];
                    $num_mat_sel++;
                }
            }

            if (!$stud_da_stampare &&
                    (
                    $filtro_scrutinio == "NEGATIVO" && $voti_pagella[$id_mat]['voto_pagellina']>0 && $voti_pagella[$id_mat]['voto_pagellina']<6
                    )
                ) {
                $stud_da_stampare = true;
            }

            if ($materia_tmp['voto_pagellina']>0 ||$materia_tmp['voto_scritto_pagella']>0 || $materia_tmp['voto_orale_pagella']>0 || $materia_tmp['voto_pratico_pagella']>0
                    || !empty($materia_tmp['tipo_recupero_tradotto']) || !empty($materia_tmp['esito_recupero']) ) {
                $materia_con_voti = true;
            }


            if ($periodo_default == 9 || $periodo_default == 29) {
                // giudizio sospeso
                if (!empty($voti_pagella[$id_mat]['tipo_recupero']) || !empty($voti_pagella[$id_mat]['esito_recupero'])) {
                    $da_giudizio_sospeso = true;
                    
                    if ($include_elenco_materie_sospese && $materia['in_media_pagelle'] != 'NV') {
                        $array_dati['elenco_materie_sospese'][] = $materia_tmp;
                    }
                }
            }
        }

        // assegno i valori del tabellone studente per la materia per il primo quadrimestre
        if (!empty($voti_pagella_1qt)) {
            // voti numerici
            $materia_tmp['voto_pagellina_1qt'] = $voti_pagella_1qt[$id_mat]['voto_pagellina'];
            $materia_tmp['voto_pagella_1qt'] = $voti_pagella_1qt[$id_mat]['voto_pagellina'];
            $materia_tmp['voto_scritto_pagella_1qt'] = $voti_pagella_1qt[$id_mat]['voto_scritto_pagella'];
            $materia_tmp['voto_orale_pagella_1qt'] = $voti_pagella_1qt[$id_mat]['voto_orale_pagella'];
            $materia_tmp['voto_pratico_pagella_1qt'] = $voti_pagella_1qt[$id_mat]['voto_pratico_pagella'];
            // voti tradotti
            foreach ($voti_pagella_1qt[$id_mat]['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voti_pagella_1qt[$id_mat]['voto_pagellina']) {
                    $materia_tmp['voto_pagellina_tradotto_1qt'] = $materia_tmp['voto_pagella_tradotto_1qt'] = $materia_tmp['voto_descrizione_1qt'] = $significato['valore_pagella'];
                    $materia_tmp['voto_pagellina_lingua_1qt'] = $materia_tmp['voto_pagella_lingua_1qt'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella_1qt[$id_mat]['voto_scritto_pagella']) {
                    $materia_tmp['voto_scritto_pagella_tradotto_1qt'] = $significato['valore_pagella'];
                    $materia_tmp['voto_scritto_pagella_lingua_1qt'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella_1qt[$id_mat]['voto_orale_pagella']) {
                    $materia_tmp['voto_orale_pagella_tradotto_1qt'] = $significato['valore_pagella'];
                    $materia_tmp['voto_orale_pagella_lingua_1qt'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella_1qt[$id_mat]['voto_pratico_pagella']) {
                    $materia_tmp['voto_pratico_pagella_tradotto_1qt'] = $significato['valore_pagella'];
                    $materia_tmp['voto_pratico_pagella_lingua_1qt'] = $significato['valore_pagella_lingua'];
                }
            }
            // monteore
            $materia_tmp['monteore_1qt'] = $voti_pagella_1qt[$id_mat]['monteore_totale'];
            $materia_tmp['monteore_totale_tradotto_1qt'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['monteore_totale']);
            $materia_tmp['monteore_1qt_arrotondato'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['monteore_totale'], 'ORE_ARROTONDATE');
            $materia_tmp['monteore_1qt_troncato'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['monteore_totale'], 'ORE_TRONCATE');
            $materia_tmp['monteore_1qt_esteso'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['monteore_totale'], 'ETICHETTA_ORE_MINUTI');
            $materia_tmp['monteore_1qt_ore_minuti'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['monteore_totale'], 'ORE_MINUTI_ESTESE');
            // assenze
            $materia_tmp['ore_assenza_1qt'] = $voti_pagella_1qt[$id_mat]['ore_assenza'];
            $materia_tmp['ore_assenza_tradotto_1qt'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['ore_assenza']);
            $materia_tmp['ore_assenza_1qt_arrotondato'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['ore_assenza'], 'ORE_ARROTONDATE');
            $materia_tmp['ore_assenza_1qt_troncato'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['ore_assenza'], 'ORE_TRONCATE');
            $materia_tmp['ore_assenza_1qt_esteso'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['ore_assenza'], 'ETICHETTA_ORE_MINUTI');
            $materia_tmp['ore_assenza_1qt_ore_minuti'] = stampa_ore_o_minuti($voti_pagella_1qt[$id_mat]['ore_assenza'], 'ORE_MINUTI_ESTESE');
            // recuperi
            $materia_tmp['tipo_recupero_tradotto_1qt'] = $voti_pagella_1qt[$id_mat]['tipo_recupero_tradotto'];
            $materia_tmp['esito_recupero_1qt'] = $voti_pagella_1qt[$id_mat]['esito_recupero'];

            // sommo le assenze e monteore al totale del periodo
            if ($voti_pagella_1qt[$id_mat]['in_media_pagelle'] == 'SI') {
                $tot_assenze_1qt += intval($voti_pagella_1qt[$id_mat]['ore_assenza']);
                $tot_ore_1qt += intval($voti_pagella_1qt[$id_mat]['monteore_totale']);
                if ($voti_pagella_1qt[$id_mat]['voto_pagellina'] > 0) {
                    $somma_voti_1qt += $voti_pagella_1qt[$id_mat]['voto_pagellina'];
                    $num_mat_1qt++;
                }
            }

            if ($materia_tmp['voto_pagellina_1qt']>0 ||$materia_tmp['voto_scritto_pagella_1qt']>0 || $materia_tmp['voto_orale_pagella_1qt']>0 || $materia_tmp['voto_pratico_pagella_1qt']>0
                    || !empty($materia_tmp['tipo_recupero_tradotto_1qt']) || !empty($materia_tmp['esito_recupero_1qt']) ) {
                $materia_con_voti = true;
            }

            // materie sospese 1° quadrimestre
            if ($include_elenco_materie_sospese && $materia['in_media_pagelle'] != 'NV' && (!empty($voti_pagella_1qt[$id_mat]['tipo_recupero']) || !empty($voti_pagella_1qt[$id_mat]['esito_recupero']))) {
                $array_dati['elenco_materie_sospese_1qt'][] = $materia_tmp;
            }
        }

        // assegno i valori del tabellone studente per la materia per il secondo trimestre
        if (!empty($voti_pagella_2t)) {
            // voti numerici
            $materia_tmp['voto_pagellina_2t'] = $voti_pagella_2t[$id_mat]['voto_pagellina'];
            $materia_tmp['voto_pagella_2t'] = $voti_pagella_2t[$id_mat]['voto_pagellina'];
            $materia_tmp['voto_scritto_pagella_2t'] = $voti_pagella_2t[$id_mat]['voto_scritto_pagella'];
            $materia_tmp['voto_orale_pagella_2t'] = $voti_pagella_2t[$id_mat]['voto_orale_pagella'];
            $materia_tmp['voto_pratico_pagella_2t'] = $voti_pagella_2t[$id_mat]['voto_pratico_pagella'];
            // voti tradotti
            foreach ($voti_pagella_2t[$id_mat]['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voti_pagella_2t[$id_mat]['voto_pagellina']) {
                    $materia_tmp['voto_pagellina_tradotto_2t'] = $materia_tmp['voto_pagella_tradotto_2t'] = $materia_tmp['voto_descrizione_2t'] = $significato['valore_pagella'];
                    $materia_tmp['voto_pagellina_lingua_2t'] = $materia_tmp['voto_pagella_lingua_2t'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella_2t[$id_mat]['voto_scritto_pagella']) {
                    $materia_tmp['voto_scritto_pagella_tradotto_2t'] = $significato['valore_pagella'];
                    $materia_tmp['voto_scritto_pagella_lingua_2t'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella_2t[$id_mat]['voto_orale_pagella']) {
                    $materia_tmp['voto_orale_pagella_tradotto_2t'] = $significato['valore_pagella'];
                    $materia_tmp['voto_orale_pagella_lingua_2t'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella_2t[$id_mat]['voto_pratico_pagella']) {
                    $materia_tmp['voto_pratico_pagella_tradotto_2t'] = $significato['valore_pagella'];
                    $materia_tmp['voto_pratico_pagella_lingua_2t'] = $significato['valore_pagella_lingua'];
                }
            }
            // monteore
            $materia_tmp['monteore_2t'] = $voti_pagella_2t[$id_mat]['monteore_totale'];
            $materia_tmp['monteore_totale_tradotto_2t'] = stampa_ore_o_minuti($voti_pagella_2t[$id_mat]['monteore_totale']);
            $materia_tmp['monteore_2t_arrotondato'] = stampa_ore_o_minuti($voti_pagella_2t[$id_mat]['monteore_totale'], 'ORE_ARROTONDATE');
            $materia_tmp['monteore_2t_troncato'] = stampa_ore_o_minuti($voti_pagella_2t[$id_mat]['monteore_totale'], 'ORE_TRONCATE');
            $materia_tmp['monteore_2t_esteso'] = stampa_ore_o_minuti($voti_pagella_2t[$id_mat]['monteore_totale'], 'ETICHETTA_ORE_MINUTI');
            $materia_tmp['monteore_2t_ore_minuti'] = stampa_ore_o_minuti($voti_pagella_2t[$id_mat]['monteore_totale'], 'ORE_MINUTI_ESTESE');
            // assenze
            $materia_tmp['ore_assenza_2t'] = $voti_pagella_2t[$id_mat]['ore_assenza'];
            $materia_tmp['ore_assenza_tradotto_2t'] = stampa_ore_o_minuti($voti_pagella_2t[$id_mat]['ore_assenza']);
            $materia_tmp['ore_assenza_2t_arrotondato'] = stampa_ore_o_minuti($voti_pagella_2t[$id_mat]['ore_assenza'], 'ORE_ARROTONDATE');
            $materia_tmp['ore_assenza_2t_troncato'] = stampa_ore_o_minuti($voti_pagella_2t[$id_mat]['ore_assenza'], 'ORE_TRONCATE');
            $materia_tmp['ore_assenza_2t_ore_minuti'] = stampa_ore_o_minuti($voti_pagella_2t[$id_mat]['ore_assenza'], 'ORE_MINUTI_ESTESE');
            // recuperi
            $materia_tmp['tipo_recupero_tradotto_2t'] = $voti_pagella_2t[$id_mat]['tipo_recupero_tradotto'];
            $materia_tmp['esito_recupero_2t'] = $voti_pagella_2t[$id_mat]['esito_recupero'];

            // sommo le assenze e monteore al totale del periodo
            if ($voti_pagella_2t[$id_mat]['in_media_pagelle'] == 'SI') {
                $tot_assenze_2t += intval($voti_pagella_2t[$id_mat]['ore_assenza']);
                $tot_ore_2t += intval($voti_pagella_2t[$id_mat]['monteore_totale']);
                if ($voti_pagella_2t[$id_mat]['voto_pagellina'] > 0) {
                    $somma_voti_2t += $voti_pagella_2t[$id_mat]['voto_pagellina'];
                    $num_mat_2t++;
                }
            }

            if ($materia_tmp['voto_pagellina_2t']>0 ||$materia_tmp['voto_scritto_pagella_2t']>0 || $materia_tmp['voto_orale_pagella_2t']>0 || $materia_tmp['voto_pratico_pagella_2t']>0
                    || !empty($materia_tmp['tipo_recupero_tradotto_2t']) || !empty($materia_tmp['esito_recupero_2t']) ) {
                $materia_con_voti = true;
            }

            // materie sospese 2° trimestre
            if ($include_elenco_materie_sospese && $materia['in_media_pagelle'] != 'NV' && (!empty($voti_pagella_2t[$id_mat]['tipo_recupero']) || !empty($voti_pagella_2t[$id_mat]['esito_recupero']))) {
                $array_dati['elenco_materie_sospese_2t'][] = $materia_tmp;
            }
        }

        // assegno i valori del tabellone studente per la materia per il fine anno
        if (!empty($voti_pagella_finale)) {
            // voti numerici
            $materia_tmp['voto_pagellina_finale'] = $voti_pagella_finale[$id_mat]['voto_pagellina'];
            $materia_tmp['voto_pagella_finale'] = $voti_pagella_finale[$id_mat]['voto_pagellina'];
            $materia_tmp['voto_scritto_pagella_finale'] = $voti_pagella_finale[$id_mat]['voto_scritto_pagella'];
            $materia_tmp['voto_orale_pagella_finale'] = $voti_pagella_finale[$id_mat]['voto_orale_pagella'];
            $materia_tmp['voto_pratico_pagella_finale'] = $voti_pagella_finale[$id_mat]['voto_pratico_pagella'];
            // voti tradotti
            foreach ($voti_pagella_finale[$id_mat]['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voti_pagella_finale[$id_mat]['voto_pagellina']) {
                    $materia_tmp['voto_pagellina_tradotto_finale'] = $materia_tmp['voto_pagella_tradotto_finale'] = $materia_tmp['voto_descrizione_finale'] = $significato['valore_pagella'];
                    $materia_tmp['voto_pagellina_lingua_finale'] = $materia_tmp['voto_pagella_lingua_finale'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella_finale[$id_mat]['voto_scritto_pagella']) {
                    $materia_tmp['voto_scritto_pagella_tradotto_finale'] = $significato['valore_pagella'];
                    $materia_tmp['voto_scritto_pagella_lingua_finale'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella_finale[$id_mat]['voto_orale_pagella']) {
                    $materia_tmp['voto_orale_pagella_tradotto_finale'] = $significato['valore_pagella'];
                    $materia_tmp['voto_orale_pagella_lingua_finale'] = $significato['valore_pagella_lingua'];
                }
                if ($significato['voto'] == $voti_pagella_finale[$id_mat]['voto_pratico_pagella']) {
                    $materia_tmp['voto_pratico_pagella_tradotto_finale'] = $significato['valore_pagella'];
                    $materia_tmp['voto_pratico_pagella_lingua_finale'] = $significato['valore_pagella_lingua'];
                }
            }
            // monteore
            $materia_tmp['monteore_finale'] = $voti_pagella_finale[$id_mat]['monteore_totale'];
            $materia_tmp['monteore_totale_tradotto_finale'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['monteore_totale']);
            $materia_tmp['monteore_finale_arrotondato'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['monteore_totale'], 'ORE_ARROTONDATE');
            $materia_tmp['monteore_finale_troncato'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['monteore_totale'], 'ORE_TRONCATE');
            $materia_tmp['monteore_finale_esteso'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['monteore_totale'], 'ETICHETTA_ORE_MINUTI');
            $materia_tmp['monteore_finale_ore_minuti'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['monteore_totale'], 'ETICHETTA_ORE_MINUTI');
            // assenze
            $materia_tmp['ore_assenza_finale'] = $voti_pagella_finale[$id_mat]['ore_assenza'];
            $materia_tmp['ore_assenza_tradotto_finale'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['ore_assenza']);
            $materia_tmp['ore_assenza_finale_arrotondato'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['ore_assenza'], 'ORE_ARROTONDATE');
            $materia_tmp['ore_assenza_finale_troncato'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['ore_assenza'], 'ORE_TRONCATE');
            $materia_tmp['ore_assenza_finale_esteso'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['ore_assenza'], 'ETICHETTA_ORE_MINUTI');
            $materia_tmp['ore_assenza_finale_ore_minuti'] = stampa_ore_o_minuti($voti_pagella_finale[$id_mat]['ore_assenza'], 'ORE_MINUTI_ESTESE');
            // recuperi
            $materia_tmp['tipo_recupero_tradotto_finale'] = $voti_pagella_finale[$id_mat]['tipo_recupero_tradotto'];
            $materia_tmp['esito_recupero_finale'] = $voti_pagella_finale[$id_mat]['esito_recupero'];

            // sommo le assenze e monteore al totale
            if ($voti_pagella_finale[$id_mat]['in_media_pagelle'] == 'SI') {
                $tot_assenze_fine_anno += intval($voti_pagella_finale[$id_mat]['ore_assenza']);
                $tot_ore_fine_anno += intval($voti_pagella_finale[$id_mat]['monteore_totale']);
                if ($voti_pagella_finale[$id_mat]['voto_pagellina'] > 0) {
                    $somma_voti_finale += $voti_pagella_finale[$id_mat]['voto_pagellina'];
                    $num_mat_finale++;
                }
            }
            // scrutinato
            if ($voti_pagella_finale[$id_mat]['voto_pagellina'] != ''){
                $scrutinato = true;
            }

            if ($materia_tmp['voto_pagellina_finale']>0 ||$materia_tmp['voto_scritto_pagella_finale']>0 || $materia_tmp['voto_orale_pagella_finale']>0 || $materia_tmp['voto_pratico_pagella_finale']>0
                    || !empty($materia_tmp['tipo_recupero_tradotto_finale']) || !empty($materia_tmp['esito_recupero_finale']) ) {
                $materia_con_voti = true;
            }

            // giudizio sospeso
            if (!empty($voti_pagella_finale[$id_mat]['tipo_recupero']) || !empty($voti_pagella_finale[$id_mat]['esito_recupero'])) {
                $da_giudizio_sospeso = true;
            }

            // altri dati
            if (isset($materia_tmp['monteore_1qt'])) {
                $materia_tmp['monteore_finale_totali'] = $materia_tmp['monteore_finale'] + $materia_tmp['monteore_1qt'];
                $materia_tmp['monteore_finale_totali_tradotto'] = stampa_ore_o_minuti($materia_tmp['monteore_finale_totali']);
                $materia_tmp['monteore_finale_totali_arrotondato'] = stampa_ore_o_minuti($materia_tmp['monteore_finale_totali'], 'ORE_ARROTONDATE');
                $materia_tmp['monteore_finale_totali_troncato'] = stampa_ore_o_minuti($materia_tmp['monteore_finale_totali'], 'ORE_TRONCATE');
                $materia_tmp['monteore_finale_totali_esteso'] = stampa_ore_o_minuti($materia_tmp['monteore_finale_totali'], 'ETICHETTA_ORE_MINUTI');
                $materia_tmp['monteore_finale_totali_ore_minuti'] = stampa_ore_o_minuti($materia_tmp['monteore_finale_totali'], 'ORE_MINUTI_ESTESE');
            }
            if (isset($materia_tmp['ore_assenza_1qt'])) {
                $materia_tmp['ore_assenza_finale_totali'] = $materia_tmp['ore_assenza_finale'] + $materia_tmp['ore_assenza_1qt'];
                $materia_tmp['ore_assenza_finale_totali_tradotto'] = stampa_ore_o_minuti($materia_tmp['ore_assenza_finale_totali']);
                $materia_tmp['ore_assenza_finale_totali_arrotondato'] = stampa_ore_o_minuti($materia_tmp['ore_assenza_finale_totali'], 'ORE_ARROTONDATE');
                $materia_tmp['ore_assenza_finale_totali_troncato'] = stampa_ore_o_minuti($materia_tmp['ore_assenza_finale_totali'], 'ORE_TRONCATE');
                $materia_tmp['ore_assenza_finale_totali_esteso'] = stampa_ore_o_minuti($materia_tmp['ore_assenza_finale_totali'], 'ETICHETTA_ORE_MINUTI');
                $materia_tmp['ore_assenza_finale_totali_ore_minuti'] = stampa_ore_o_minuti($materia_tmp['ore_assenza_finale_totali'], 'ORE_MINUTI_ESTESE');
            }

            // materie sospese fine anno
            if ($include_elenco_materie_sospese && $materia['in_media_pagelle'] != 'NV' && (!empty($voti_pagella_finale[$id_mat]['tipo_recupero']) || !empty($voti_pagella_finale[$id_mat]['esito_recupero']))) {
                $array_dati['elenco_materie_sospese_finale'][] = $materia_tmp;
            }
        }

        // assegno i valori del tabellone studente per la materia per i periodi infraquadrimestrali
        foreach ($periodi_tmp as $desc_periodo) {
            if (preg_match('/pagellina(\d+)/', $desc_periodo, $matches)) {
                $voti_pagella_infra_tmp = ${"voti_pagella_" . $desc_periodo};

                if (!empty($voti_pagella_infra_tmp)) {
                    // voti numerici
                    $materia_tmp['voto_pagellina_'.$desc_periodo] = $voti_pagella_infra_tmp[$id_mat]['voto_pagellina'];
                    $materia_tmp['voto_pagella_'.$desc_periodo] = $voti_pagella_infra_tmp[$id_mat]['voto_pagellina'];
                    $materia_tmp['voto_scritto_pagella_'.$desc_periodo] = $voti_pagella_infra_tmp[$id_mat]['voto_scritto_pagella'];
                    $materia_tmp['voto_orale_pagella_'.$desc_periodo] = $voti_pagella_infra_tmp[$id_mat]['voto_orale_pagella'];
                    $materia_tmp['voto_pratico_pagella_'.$desc_periodo] = $voti_pagella_infra_tmp[$id_mat]['voto_pratico_pagella'];
                    // voti tradotti
                    foreach ($voti_pagella_infra_tmp[$id_mat]['significati_voto'] as $significato)
                    {
                        if ($significato['voto'] == $voti_pagella_infra_tmp[$id_mat]['voto_pagellina']) {
                            $materia_tmp['voto_pagellina_tradotto_'.$desc_periodo] = $materia_tmp['voto_pagella_tradotto_'.$desc_periodo] = $materia_tmp['voto_descrizione_'.$desc_periodo] = $significato['valore_pagella'];
                            $materia_tmp['voto_pagellina_lingua_'.$desc_periodo] = $materia_tmp['voto_pagella_lingua_'.$desc_periodo] = $significato['valore_pagella_lingua'];
                        }
                        if ($significato['voto'] == $voti_pagella_infra_tmp[$id_mat]['voto_scritto_pagella']) {
                            $materia_tmp['voto_scritto_pagella_tradotto_'.$desc_periodo] = $significato['valore_pagella'];
                            $materia_tmp['voto_scritto_pagella_lingua_'.$desc_periodo] = $significato['valore_pagella_lingua'];
                        }
                        if ($significato['voto'] == $voti_pagella_infra_tmp[$id_mat]['voto_orale_pagella']) {
                            $materia_tmp['voto_orale_pagella_tradotto_'.$desc_periodo] = $significato['valore_pagella'];
                            $materia_tmp['voto_orale_pagella_lingua_'.$desc_periodo] = $significato['valore_pagella_lingua'];
                        }
                        if ($significato['voto'] == $voti_pagella_infra_tmp[$id_mat]['voto_pratico_pagella']) {
                            $materia_tmp['voto_pratico_pagella_tradotto_'.$desc_periodo] = $significato['valore_pagella'];
                            $materia_tmp['voto_pratico_pagella_lingua_'.$desc_periodo] = $significato['valore_pagella_lingua'];
                        }
                    }
                    // monteore
                    $materia_tmp['monteore_'.$desc_periodo] = $voti_pagella_infra_tmp[$id_mat]['monteore_totale'];
                    $materia_tmp['monteore_totale_tradotto_'.$desc_periodo] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['monteore_totale']);
                    $materia_tmp['monteore_'.$desc_periodo.'_arrotondato'] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['monteore_totale'], 'ORE_ARROTONDATE');
                    $materia_tmp['monteore_'.$desc_periodo.'_troncato'] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['monteore_totale'], 'ORE_TRONCATE');
                    $materia_tmp['monteore_'.$desc_periodo.'_esteso'] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['monteore_totale'], 'ETICHETTA_ORE_MINUTI');
                    $materia_tmp['monteore_'.$desc_periodo.'_ore_minuti'] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['monteore_totale'], 'ORE_MINUTI_ESTESE');
                    // assenze
                    $materia_tmp['ore_assenza_'.$desc_periodo] = $voti_pagella_infra_tmp[$id_mat]['ore_assenza'];
                    $materia_tmp['ore_assenza_tradotto_'.$desc_periodo] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['ore_assenza']);
                    $materia_tmp['ore_assenza_'.$desc_periodo.'_arrotondato'] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['ore_assenza'], 'ORE_ARROTONDATE');
                    $materia_tmp['ore_assenza_'.$desc_periodo.'_troncato'] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['ore_assenza'], 'ORE_TRONCATE');
                    $materia_tmp['ore_assenza_'.$desc_periodo.'_esteso'] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['ore_assenza'], 'ETICHETTA_ORE_MINUTI');
                    $materia_tmp['ore_assenza_'.$desc_periodo.'_ore_minuti'] = stampa_ore_o_minuti($voti_pagella_infra_tmp[$id_mat]['ore_assenza'], 'ORE_MINUTI_ESTESE');
                    // recuperi
                    $materia_tmp['tipo_recupero_tradotto_'.$desc_periodo] = $voti_pagella_infra_tmp[$id_mat]['tipo_recupero_tradotto'];
                    $materia_tmp['esito_recupero_'.$desc_periodo] = $voti_pagella_infra_tmp[$id_mat]['esito_recupero'];
        
                    // // sommo le assenze e monteore al totale del periodo
                    // if ($voti_pagella_infra_tmp[$id_mat]['in_media_pagelle'] == 'SI') {
                    //     ${"tot_assenze_".$desc_periodo} += intval($voti_pagella_infra_tmp[$id_mat]['ore_assenza']);
                    //     $tot_ore_1qt += intval($voti_pagella_infra_tmp[$id_mat]['monteore_totale']);
                    //     if ($voti_pagella_infra_tmp[$id_mat]['voto_pagellina'] > 0) {
                    //         $somma_voti_1qt += $voti_pagella_infra_tmp[$id_mat]['voto_pagellina'];
                    //         $num_mat_1qt++;
                    //     }
                    // }

                    if ($materia_tmp['voto_pagellina_'.$desc_periodo]>0 ||$materia_tmp['voto_scritto_pagella_'.$desc_periodo]>0 || $materia_tmp['voto_orale_pagella_'.$desc_periodo]>0 || $materia_tmp['voto_pratico_pagella_'.$desc_periodo]>0
                            || !empty($materia_tmp['tipo_recupero_tradotto_'.$desc_periodo]) || !empty($materia_tmp['esito_recupero_'.$desc_periodo]) ) {
                        $materia_con_voti = true;
                    }
        
                    // materie sospese 1° quadrimestre
                    if ($include_elenco_materie_sospese && $materia['in_media_pagelle'] != 'NV' && (!empty($voti_pagella_infra_tmp[$id_mat]['tipo_recupero']) || !empty($voti_pagella_infra_tmp[$id_mat]['esito_recupero']))) {
                        $array_dati['elenco_materie_sospese_'.$desc_periodo][] = $materia_tmp;
                    }
                }
            }
        }
        //}}} </editor-fold>

        //{{{ <editor-fold defaultstate="collapsed" desc="assegnazione campi liberi">
        // da aggiungere x sistemare bug campi liberi associati a tutte le materie
//echo $materia_tmp['descrizione']." -$id_mat-<br>";
        if (!empty($periodi_cmplib_tmp))
        {
            $materia_tmp['elenco_campi_liberi_popolati_tmp'] = $materia_tmp['elenco_campi_liberi_popolati'] = $struttura_campi_liberi = [];
            foreach ($periodi_cmplib_tmp as $kpf => $pf)
            {
// file_put_contents("/tmp/debug_clb{$materia['descrizione']}_$id_studente.txt", print_r(${"voti_pagella" . $pf}[$id_mat]['campi_liberi'], true));
                foreach (${"voti_pagella" . $pf}[$id_mat]['campi_liberi'] as $campo_libero)
                {
                    if (
                        ( in_array($id_mat, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] === 0
                            || $campo_libero['abbinamenti']['materie'][0] === '0'  // to fix bug: bug campi liberi associati a tutte le materie non passano alla prossima fase -> da verificare retroattivamente su docx già inserite
                        )
                            &&
                        $campo_libero['visibile'] == 'SI')
                    {
                    $giudizio_cmpl = false;
                    $id_cmp = $campo_libero['id_campo_libero'];

                    $campo_libero_tmp = [];
                    $campo_libero_tmp['id_campo_libero'] = $id_cmp;
                    $campo_libero_tmp['nome'] = decode($campo_libero['nome']);
                    $campo_libero_tmp['descrizione'] = decode($campo_libero['descrizione']);
                    $campo_libero_tmp['tipo_stampa'] = $campo_libero['tipo_stampa'];
                    $campo_libero_tmp['stampa_descrizione_campo'] = $campo_libero['stampa_descrizione_campo'];
                    $campo_libero_tmp['separatore_descrizione_valore'] = $campo_libero['separatore_descrizione_valore'];
                    $campo_libero_tmp['tipo_valore'] = $campo_libero['tipo_valore'];
                    $campo_libero_tmp['valore'] = estrai_valore_campo_libero_selezionato($campo_libero);
                    $campo_libero_tmp['valore' . $pf] = $campo_libero_tmp['valore'];
                    $campo_libero_tmp['codice'] = estrai_valore_campo_libero_selezionato($campo_libero, 'codice');
                    $campo_libero_tmp['codice' . $pf] = $campo_libero_tmp['codice'];
                    if ($campo_libero['tipo_valore'] == 'PRECOMPILATO') {
                        foreach($campo_libero['valori_precomp'] as $kprecomp => $valcomp) {
                            if ($valcomp['selezionato'] == 'SI') {
                                // $campo_libero['valori_precomp'][$kprecomp]['selezionato' . $pf] = 'SI';
                                $campo_libero['valori_precomp'][$kprecomp]['selezionato_X'] = 'X';
                                $campo_libero['valori_precomp'][$kprecomp]['selezionato_X'. $pf] = 'X';
                            } else {
                                $campo_libero['valori_precomp'][$kprecomp]['selezionato_X'] = ' ';
                                $campo_libero['valori_precomp'][$kprecomp]['selezionato_X'. $pf] = ' ';
                            }
                            if ( ($kprecomp == "meno_uno" || $kprecomp == -1) && empty($valcomp['valore_testuale'])) {
                                unset($campo_libero['valori_precomp'][$kprecomp]);
                            }
                        }
                        $campo_libero_tmp['valori_precomp'] = $campo_libero['valori_precomp'];
                    }

                    $campo_libero_valorizzato = false;
                    if (strlen($campo_libero_tmp['valore']) > 0) {
                        $campo_libero_valorizzato = true;
                    }
                    
                    // Add to struttura_campi_liberi in base al tipo, non c e da fare altro sulla struttura, anche perchè tutti i campi liberi vengono ciclati e da quelli filtrati 
                    // viene creata l intera struttura campi liberi associata alla materia per il periodo corrente, e $campo_libero_tmp non viene modificato nel codice successivo
                    if ( empty($campo_libero['id_padre']) ) {
                        if (array_key_exists('figli', $struttura_campi_liberi[$id_cmp])) { // se campi figli sono già stati parzialmente deviniti sul padre
                            $campo_libero_tmp['figli'] = $struttura_campi_liberi[$id_cmp]['figli'];
                        }
                        if (isset($struttura_campi_liberi[$id_cmp])) {
                            $struttura_campi_liberi[$id_cmp] = array_merge($struttura_campi_liberi[$id_cmp], $campo_libero_tmp);
                        } else {
                            $struttura_campi_liberi[$id_cmp] = $campo_libero_tmp; 
                        }
                    } else {
                        if (isset($struttura_campi_liberi[$campo_libero['id_padre']]['figli'][$id_cmp])) {
                            $struttura_campi_liberi[$campo_libero['id_padre']]['figli'][$id_cmp] = array_merge($struttura_campi_liberi[$campo_libero['id_padre']]['figli'][$id_cmp], $campo_libero_tmp);
                        } else {
                            $struttura_campi_liberi[$campo_libero['id_padre']]['figli'][$id_cmp] = $campo_libero_tmp; 
                        }
                    }

                    // assegno il campo libero e/o figli agli elenchi e campi richiesti, distinguo anche per tipo di campo libero
                    if ($campo_libero['tipo_valore'] == 'RAGGRUPPAMENTO'){
                        $campi_figli = estrai_campi_liberi_figli($id_cmp);
                        foreach ($campi_figli as $figlio){
                            $campo_libero_tmp_figlio['id_campo_libero'] = $figlio['id_campo_libero'];
                            $campo_libero_tmp_figlio['nome'] = decode($figlio['nome']);
                            $campo_libero_tmp_figlio['descrizione'] = decode($figlio['descrizione']);
                            $campo_libero_tmp_figlio['tipo_stampa'] = $campo_libero['tipo_stampa']; //del padre
                            $campo_libero_tmp_figlio['stampa_descrizione_campo'] = $campo_libero['stampa_descrizione_campo'];
                            $campo_libero_tmp_figlio['separatore_descrizione_valore'] = $campo_libero['separatore_descrizione_valore']; //del padre
                            $campo_libero_tmp_figlio['valore'] = estrai_valore_campo_libero_selezionato(${"voti_pagella" . $pf}['campi_liberi'][$figlio['id_campo_libero']]);
                            $campo_libero_tmp_figlio['valore' . $pf] = $campo_libero_tmp_figlio['valore'];
                            $campo_libero_tmp_figlio['codice'] = estrai_valore_campo_libero_selezionato(${"voti_pagella" . $pf}['campi_liberi'][$figlio['id_campo_libero']], 'codice');
                            $campo_libero_tmp_figlio['codice' . $pf] = $campo_libero_tmp_figlio['codice'];
                            if ($figlio['tipo_valore'] == 'PRECOMPILATO') {
                                $campo_libero_tmp_figlio['valori_precomp'] = $figlio['valori_precomp'];
                            }

                            $campo_libero_valorizzato_figlio = false;
                            if (strlen($campo_libero_tmp_figlio['valore']) > 0) {
                                $campo_libero_valorizzato_figlio = true;
                            }

                            switch ($figlio['tipo_stampa']) {
                                case 'media':
                                    $materia_tmp['elenco_campi_liberi_condotta' . $pf][$figlio['id_campo_libero']] = $campo_libero_tmp_figlio;

                                    if ($campo_libero_valorizzato_figlio) {
                                        $materia_tmp['elenco_campi_liberi_condotta' . $pf . '_popolati'][$figlio['id_campo_libero']] = $campo_libero_tmp_figlio;
                                    }

                                    if ($pf != "" && isset($materia_tmp['elenco_campi_liberi_condotta'][$figlio['id_campo_libero']])) {
                                        $materia_tmp['elenco_campi_liberi_condotta'][$figlio['id_campo_libero']]['valore' . $pf] = $campo_libero_tmp_figlio['valore'];
                                        $materia_tmp['elenco_campi_liberi_condotta'][$figlio['id_campo_libero']]['codice' . $pf] = $campo_libero_tmp_figlio['codice'];

                                        if ($campo_libero_valorizzato_figlio) {
                                        $materia_tmp['elenco_campi_liberi_condotta_popolati'][$figlio['id_campo_libero']]['valore' . $pf] = $campo_libero_tmp_figlio['valore'];
                                        $materia_tmp['elenco_campi_liberi_condotta_popolati'][$figlio['id_campo_libero']]['codice' . $pf] = $campo_libero_tmp_figlio['codice'];
                                        }
                                    }

                                    if ($campo_libero_tmp_figlio['valore']!='') {
                                        if ($campo_libero_tmp_figlio['stampa_descrizione_campo']=='SI') {
                                        if (isset($dati_studente['giudizio_finale' . $pf])) {
                                            $dati_studente['giudizio_finale' . $pf] .=  "\n";
                                        }
                                        $dati_studente['giudizio_finale' . $pf] .=  $campo_libero_tmp_figlio['descrizione']. $campo_libero_tmp_figlio['separatore_descrizione_valore'] . $campo_libero_tmp_figlio['valore'];
                                        } else {
                                        $dati_studente['giudizio_finale' . $pf] .= $campo_libero_tmp_figlio['valore'] . " ";
                                        }
                                    }
                                    break;
                                case 'singola':
                                default:
                                $materia_tmp['elenco_campi_liberi' . $pf][$figlio['id_campo_libero']] = $campo_libero_tmp_figlio;

                                if ($campo_libero_valorizzato_figlio) {
                                    $materia_tmp['elenco_campi_liberi' . $pf . '_popolati'][$figlio['id_campo_libero']] = $campo_libero_tmp_figlio;
                                }

                                if ($pf !="" && isset($materia_tmp['elenco_campi_liberi'][$figlio['id_campo_libero']])) {
                                    $materia_tmp['elenco_campi_liberi'][$figlio['id_campo_libero']]['valore' . $pf] = $campo_libero_tmp_figlio['valore'];
                                    $materia_tmp['elenco_campi_liberi'][$figlio['id_campo_libero']]['codice' . $pf] = $campo_libero_tmp_figlio['codice'];

                                    if ($campo_libero_valorizzato_figlio) {
                                        $materia_tmp['elenco_campi_liberi_popolati_tmp'][$figlio['id_campo_libero']]['valore' . $pf] = $campo_libero_tmp_figlio['valore'];
                                        $materia_tmp['elenco_campi_liberi_popolati_tmp'][$figlio['id_campo_libero']]['codice' . $pf] = $campo_libero_tmp_figlio['codice'];
                                        if ($figlio['tipo_valore'] == 'PRECOMPILATO') {
                                            $materia_tmp['elenco_campi_liberi_popolati_tmp'][$figlio['id_campo_libero']]['valori_precomp' . $pf] = $campo_libero_tmp_figlio['valori_precomp'];
                                        }
                                    }
                                }
                                break;
                            }
                        }
                    }
                    else {
                        switch ($campo_libero['tipo_stampa']) {
                        case 'media':
                            $giudizio_cmpl = true;
                            $materia_tmp['elenco_campi_liberi_condotta' . $pf][$campo_libero['id_campo_libero']] = $campo_libero_tmp;

                            if ($campo_libero_valorizzato) {
                                $materia_tmp['elenco_campi_liberi_condotta' . $pf . '_popolati'][$campo_libero['id_campo_libero']] = $campo_libero_tmp;
                            }

                            if ($pf !="" && isset($materia_tmp['elenco_campi_liberi_condotta'][$campo_libero['id_campo_libero']])) {
                                $materia_tmp['elenco_campi_liberi_condotta'][$campo_libero['id_campo_libero']]['valore' . $pf] = $campo_libero_tmp['valore'];
                                $materia_tmp['elenco_campi_liberi_condotta'][$campo_libero['id_campo_libero']]['codice' . $pf] = $campo_libero_tmp['codice'];

                                if ($campo_libero_valorizzato) {
                                    $materia_tmp['elenco_campi_liberi_condotta_popolati'][$campo_libero['id_campo_libero']]['valore' . $pf] = $campo_libero_tmp['valore'];
                                    $materia_tmp['elenco_campi_liberi_condotta_popolati'][$campo_libero['id_campo_libero']]['codice' . $pf] = $campo_libero_tmp['codice'];
                                }
                            }

                            if ($campo_libero_tmp['valore']!='') {
                                if ($campo_libero_tmp['stampa_descrizione_campo']=='SI') {
                                    if (isset($dati_studente['giudizio_finale' . $pf])) {
                                        $dati_studente['giudizio_finale' . $pf] .=  "\n";
                                    }
                                    $dati_studente['giudizio_finale' . $pf] .= $campo_libero_tmp['descrizione']. $campo_libero_tmp['separatore_descrizione_valore'] . $campo_libero_tmp['valore'] ;
                                } else {
                                    $dati_studente['giudizio_finale' . $pf] .= $campo_libero_tmp['valore'] . " ";
                                }
                            }
                            break;
                        case 'singola':
                        default:
                            $materia_tmp['elenco_campi_liberi' . $pf][$campo_libero['id_campo_libero']] = $campo_libero_tmp;

                            if ($campo_libero_valorizzato) {
                            $materia_tmp['elenco_campi_liberi' . $pf . '_popolati'][$campo_libero['id_campo_libero']] = $campo_libero_tmp;
                            }

                            if ($pf !="" && isset($materia_tmp['elenco_campi_liberi'][$campo_libero['id_campo_libero']])) {
                                $materia_tmp['elenco_campi_liberi'][$campo_libero['id_campo_libero']]['valore' . $pf] = $campo_libero_tmp['valore'];
                                $materia_tmp['elenco_campi_liberi'][$campo_libero['id_campo_libero']]['codice' . $pf] = $campo_libero_tmp['codice'];

                                if ($campo_libero_valorizzato) {
                                    $materia_tmp['elenco_campi_liberi_popolati_tmp'][$campo_libero['id_campo_libero']]['valore' . $pf] = $campo_libero_tmp['valore'];
                                    $materia_tmp['elenco_campi_liberi_popolati_tmp'][$campo_libero['id_campo_libero']]['codice' . $pf] = $campo_libero_tmp['codice'];
                                    if ($campo_libero['tipo_valore'] == 'PRECOMPILATO') {
                                    $materia_tmp['elenco_campi_liberi_popolati_tmp'][$campo_libero['id_campo_libero']]['valori_precomp' . $pf] = $campo_libero_tmp['valori_precomp'];
                                    }
                                }
                            }
                            break;
                        }
                    }

                    if ($giudizio_cmpl===false) {
                        $materia_tmp['elenco_campi_liberi' . $pf][$id_cmp] = $campo_libero_tmp;
                        if ($campo_libero_valorizzato) {
                            $materia_tmp['elenco_campi_liberi_popolati_tmp'][$id_cmp]['nome'] = $campo_libero['nome'];
                            $materia_tmp['elenco_campi_liberi_popolati_tmp'][$id_cmp]['descrizione'] = $campo_libero['descrizione'];
                            $materia_tmp['elenco_campi_liberi_popolati_tmp'][$id_cmp]['tipo_stampa'] = $campo_libero['tipo_stampa'];
                            $materia_tmp['elenco_campi_liberi_popolati_tmp'][$id_cmp]['separatore_descrizione_valore'] = $campo_libero['separatore_descrizione_valore'];
                            $materia_tmp['elenco_campi_liberi_popolati_tmp'][$id_cmp]['valore' . $pf] = $campo_libero_tmp['valore'];
                            $materia_tmp['elenco_campi_liberi_popolati_tmp'][$id_cmp]['codice' . $pf] = $campo_libero_tmp['codice'];
                            if ($campo_libero_tmp['tipo_valore'] == 'PRECOMPILATO') {
                                $materia_tmp['elenco_campi_liberi_popolati_tmp'][$id_cmp]['valori_precomp' . $pf] = $campo_libero_tmp['valori_precomp'];
                            }
                        }
                    }
                    }
                }
            }

            // toglie il bug nella traslazione rimuovendo key come codice e inserendo il conteggio
            $materia_tmp['elenco_campi_liberi_popolati'] = [];
            if (!empty($materia_tmp['elenco_campi_liberi_popolati_tmp'])) {
                $materia_tmp['elenco_campi_liberi_popolati'] = array_values($materia_tmp['elenco_campi_liberi_popolati_tmp']);
                $materia_con_voti = true;
            }
            unset($materia_tmp['elenco_campi_liberi_popolati_tmp']);

            $materia_tmp['elenco_campi_liberi_condotta'] = array_values($materia_tmp['elenco_campi_liberi_condotta']);
            foreach ($periodi_cmplib_tmp as $kpf => $pf) {
                $materia_tmp['elenco_campi_liberi' . $pf] = array_values($materia_tmp['elenco_campi_liberi' . $pf]);
                $materia_tmp['elenco_campi_liberi_condotta' . $pf] = array_values($materia_tmp['elenco_campi_liberi_condotta' . $pf]);
                $materia_tmp['elenco_campi_liberi_condotta' . $pf . '_popolati'] = array_values($materia_tmp['elenco_campi_liberi_condotta' . $pf . '_popolati']);
                $materia_tmp['elenco_campi_liberi' . $pf . '_popolati'] = array_values($materia_tmp['elenco_campi_liberi' . $pf . '_popolati']);

                foreach ($materia_tmp['elenco_campi_liberi_popolati'] as $cont_cmp => $cmp) {
                    if (!isset($cmp['valore' . $pf])) {
                        $materia_tmp['elenco_campi_liberi_popolati'][$cont_cmp]['valore' . $pf] = '';
                        $materia_tmp['elenco_campi_liberi_popolati'][$cont_cmp]['codice' . $pf] = '';
                    }
                    if ($periodo==$kpf) {
                        $materia_tmp['elenco_campi_liberi_popolati'][$cont_cmp]['valore'] = $materia_tmp['elenco_campi_liberi_popolati'][$cont_cmp]['valore' . $pf];
                        $materia_tmp['elenco_campi_liberi_popolati'][$cont_cmp]['codice'] = $materia_tmp['elenco_campi_liberi_popolati'][$cont_cmp]['codice' . $pf];
                    }
                }
            }

            // se gli elenchi non sono valorizzati creo un campo libero vuoto per rimuovere il tag dal modello stampato
            $cmp_empty_tmp = [];
            $cmp_empty_tmp['id_campo_libero'] = '';
            $cmp_empty_tmp['nome'] = '';
            $cmp_empty_tmp['descrizione'] = '';
            $cmp_empty_tmp['tipo_stampa'] = '';
            $cmp_empty_tmp['stampa_descrizione_campo'] = '';
            $cmp_empty_tmp['tipo_valore'] = '';
            $cmp_empty_tmp['separatore_descrizione_valore'] = '';
            $cmp_empty_tmp['valore'] = '';
            $cmp_empty_tmp['valore_1qt'] = '';
            $cmp_empty_tmp['valore_2t'] = '';
            $cmp_empty_tmp['valore_finale'] = '';
            $cmp_empty_tmp['codice'] = '';
            $cmp_empty_tmp['codice_1qt'] = '';
            $cmp_empty_tmp['codice_2t'] = '';
            $cmp_empty_tmp['codice_finale'] = '';
            foreach ($periodi_cmplib_tmp as $kpf => $pf) {
                if (empty($materia_tmp['elenco_campi_liberi' . $pf])) {
                    $materia_tmp['elenco_campi_liberi' . $pf][] = $cmp_empty_tmp;
                }
                if (empty($materia_tmp['elenco_campi_liberi_condotta' . $pf])) {
                    $materia_tmp['elenco_campi_liberi_condotta' . $pf][] = $cmp_empty_tmp;
                }
                if (empty($materia_tmp['elenco_campi_liberi_condotta' . $pf . '_popolati'])) {
                    $materia_tmp['elenco_campi_liberi_condotta' . $pf . '_popolati'][] = $cmp_empty_tmp;
                }
                if (empty($materia_tmp['elenco_campi_liberi' . $pf . '_popolati'])) {
                    $materia_tmp['elenco_campi_liberi' . $pf . '_popolati'][] = $cmp_empty_tmp;
                }
            }

            // popolo delle variabili vuote di default se non popolati
            if (empty($materia_tmp['elenco_campi_liberi_popolati'])){
                $materia_tmp['elenco_campi_liberi_popolati'][] = $cmp_empty_tmp;
            }
            foreach ($periodi_cmplib_tmp as $kpf => $pf) {
                if (empty($materia_tmp['elenco_campi_liberi' . $pf . '_popolati'])){
                    $materia_tmp['elenco_campi_liberi' . $pf . '_popolati'][] = $cmp_empty_tmp;
                }
            }

            // struttura albero campi liberi della materia
            if ($include_struttura_campi_liberi) {
                if (empty($struttura_campi_liberi)) {
                    $struttura_campi_liberi[] = $cmp_empty_tmp;
                }
                // assegno la struttura generale dei campi liberi della materia
                $materia_tmp['struttura_campi_liberi'] = $struttura_campi_liberi;
            }
        }
        //}}} </editor-fold>

        //{{{ <editor-fold defaultstate="collapsed" desc="assegnazione competenze">
        if (!empty($tab_scrutini_competenze))
        {
//            $array_competenze_materia_periodo = [];
            // ciclo per ogni periodo estratto: $chiave_tipo_tabellone_competenze contiene il codice (_1qt, _2t, _finale, vuoto)
            // da creare su $materia_tmp: elenco_competenze _1qt, _2t, _finale, vuoto
            foreach ($tab_scrutini_competenze as $chiave_tipo_tabellone_competenze => $scrutinio_competenze)
            {
                // ricavo l elenco degli id delle competenze per lo scrutinio competenze attuale
                $elenco_id_competenze_scrutinio = array_column($scrutinio_competenze['elenco'], 'id');
                // ciclo le competenze dello scrutinio, ricercando la materia attuale nei vincoli
                foreach ($scrutinio_competenze['elenco'] as $key_comp => $competenza)
                {
                    // variabili usati per la ricerca e setting del periodo
                    $periodo_ric = $periodo_default;
                    switch ($chiave_tipo_tabellone_competenze) {
                        case '_1qt': $periodo_ric=7; break;
                        case '_2t': $periodo_ric=8; break;
                        case '_finale': $periodo_ric=9; break;
                    }

                    // controllo se ha almeno uno schema scrutini, se non lo ha non lo tengo in considerazione
                    $ha_schema_scrutinio = false;
                    foreach ($competenza['schema_valutazioni'] as $key_schema => $val) {
                        if ($val['tipo'] == 'scrutini') {
                            $ha_schema_scrutinio = true;
                            break;
                        }
                    }

                    // se id_materia nei vincoli la competenza fa parte della materia, e quindi continuo
                    $competenza_in_materia = false;
                    if ($materia['in_media_pagelle'] != 'NV') {
                        foreach ($competenza['vincoli']['materie'] as $id_mat_vincolo => $vincolo) {
                            if ($id_mat == $id_mat_vincolo) {
                                $competenza_in_materia = true;
                                break;
                            }
                        }
                        foreach ($competenza['vincoli_ereditati'] as $key => $vincolo_ereditato) {
                            foreach ($vincolo_ereditato['materie'] as $id_mat_vincolo => $vincolo) {
                                if ($id_mat == $id_mat_vincolo) {
                                    $competenza_in_materia = true;
                                    break;
                                }
                            }
                            if ($competenza_in_materia) {break;}
                        }
                    }

                    if ($ha_schema_scrutinio && $competenza_in_materia)
                    {
                        $competenza_tmp = $competenza;
                        if ( !
                                (
                                    $quali_competenze_stampare == 'SOLO_COMPETENZE_CON_VOTI'
                                    &&
                                    empty($competenza_tmp['scrutinio'][$periodo_ric]['voto']['valore_numerico'])
                                )
                        ) {
                            // ------------ VERIFICA LIMITI SU SINGOLA MATERIA
                            $competenza_singola_materia = false;

                            // pulisco dai vincoli le materie che non centrano con la classe di modo da capire in modo piu' efficace se e' trasversale o "finta" perche' comprende materie di altre classi (tipo arte italiano e arte medie)
                            foreach ($competenza_tmp['vincoli']['materie'] as $id_mat_vincolo => $vincolo) {
                                if ($id_mat != $id_mat_vincolo) {
                                    unset($competenza_tmp['vincoli']['materie'][$id_mat_vincolo]);
                                }
                            }
                            foreach ($competenza_tmp['vincoli_ereditati'] as $key => $vincolo_ereditato) {
                                foreach ($vincolo_ereditato['materie'] as $id_mat_vincolo => $vincolo) {
                                    if ($id_mat != $id_mat_vincolo) {
                                        unset($competenza_tmp['vincoli_ereditati'][$key]['materie'][$id_mat_vincolo]);
                                    }
                                }
                                if (count($competenza_tmp['vincoli_ereditati'][$key]['materie']) == 0){
                                    unset($competenza_tmp['vincoli_ereditati'][$key]);
                                }
                            }
                            if (!isset($competenza_tmp['vincoli_ereditati']) || count($competenza_tmp['vincoli_ereditati']) == 0) {
                                if (count($competenza_tmp['vincoli']['materie']) == 1 && isset($competenza_tmp['vincoli']['materie'][$id_mat])) {
                                    $competenza_singola_materia = true;
                                }
                            } else {
                                foreach ($competenza_tmp['vincoli_ereditati'] as $vincoli_padri) {
                                    if (count($vincoli_padri['materie']) == 1 && isset($vincoli_padri['materie'][$id_mat])) {
                                        // almeno un padre nella gerarchia con vincolo su singola materia
                                        $competenza_singola_materia = true;
                                        break;
                                    }
                                }
                            }

                            if ($competenza_singola_materia
                                    &&
                                    (
                                        $competenza_tmp['tipo'] != 'gruppo'
                                            ||
                                        isset($competenza_tmp['scrutinio']) // se è tipo gruppo allora controllo se il nodo scrutinio è impostato (significa che competenza è scrutinata, e quindi va in stampa)
                                    )
                                ) {
                                // competenza con vincolo unico sulla materia
                                $compt_tmp_dati_generali = $competenza_tmp;
                                unset($compt_tmp_dati_generali['valutazioni_figli']);
                                unset($compt_tmp_dati_generali['scrutinio']);
                                foreach ($compt_tmp_dati_generali['schema_valutazioni'] as $key_schema => $val) {
                                    if ($val['tipo'] != 'scrutini') {
                                        unset($compt_tmp_dati_generali['schema_valutazioni'][$key_schema]);
                                    }
                                }

                                // unisco valutazioni della competenze con quelle dei figli raggruppandole per data
                                $tot_valutazioni = [];
                                $tot_valutazioni_tmp = $competenza_tmp['valutazioni_figli'];

                                //pulisco le valutazioni di figli presenti in scrutinio
                                if (count($tot_valutazioni_tmp > 0)) {
                                    foreach ($tot_valutazioni_tmp as $data => $valutazioni) {
                                        foreach ($valutazioni as $key => $valutazione) {
                                            if (in_array($valutazione['dati_competenza']['id'], $elenco_id_competenze_scrutinio)) {
                                                unset($tot_valutazioni_tmp[$data][$key]);
                                                if (count($tot_valutazioni_tmp[$data]) == 0) {
                                                    unset($tot_valutazioni_tmp[$data]);
                                                }
                                            }
                                        }
                                    }
                                }

                                if (count($competenza_tmp['valutazioni']) > 0) {
                                    foreach ($competenza_tmp['valutazioni'] as $valutazione) {
                                        if (!isset($tot_valutazioni_tmp[$valutazione['data']][$competenza_tmp['id']])) {
                                            $tot_valutazioni_tmp[$valutazione['data']][$competenza_tmp['id']]['dati_competenza']['figlia'] = 'NO';
                                        }
                                        $tot_valutazioni_tmp[$valutazione['data']][$competenza_tmp['id']]['valutazioni'][] = $valutazione;
                                    }
                                }
                                ksort($tot_valutazioni_tmp);

                                // imposto le date nel formato italiano
                                foreach ($tot_valutazioni_tmp as $data_iso => $val) {
                                    $tot_valutazioni[date("d/m/Y", strtotime($data_iso))] = $val;
                                }

    //                            if (!isset($array_competenze_materia_periodo[$scrutinio_competenze['id_template_origine']][$competenza_tmp['id']])) {
    //                                $array_competenze_materia_periodo[$scrutinio_competenze['id_template_origine']][$competenza_tmp['id']] = $compt_tmp_dati_generali;
    //                            }
    //                            $array_competenze_materia_periodo[$scrutinio_competenze['id_template_origine']][$competenza_tmp['id']][$chiave_tipo_tabellone_competenze]['valutazioni_totali'] = $tot_valutazioni;
    //                            $array_competenze_materia_periodo[$scrutinio_competenze['id_template_origine']][$competenza_tmp['id']][$chiave_tipo_tabellone_competenze]['scrutinio'] = $competenza_tmp['scrutinio'];

                                // dati generici
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['id'] = $compt_tmp_dati_generali['id'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['tipo'] = $compt_tmp_dati_generali['tipo'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['descrizione'] = $compt_tmp_dati_generali['descrizione'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['codice'] = $compt_tmp_dati_generali['codice'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['descrizione_stampa'] = $compt_tmp_dati_generali['descrizione_stampa'];

                                //voto
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['voto' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa']; //default per voto
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['voto_valore_numerico' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['valore_numerico'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['voto_codice' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['codice'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['voto_descrizione' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['voto_descrizione_stampa' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa'];
                                //proposta
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['proposta' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione_stampa']; //default per proposta
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['proposta_valore_numerico' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['valore_numerico'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['proposta_codice' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['codice'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['proposta_descrizione' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione'];
                                $materia_tmp['elenco_competenze' . $chiave_tipo_tabellone_competenze][$competenza_tmp['id']]['proposta_descrizione_stampa' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione_stampa'];


//                                // solo se valorizzati, elenco_competenze generali -- NON PIU NECESSARIO AVERLO IN CONDIZIONE IF PERCHÈ CREATO IL PARAMETRO DI STAMPA $quali_competenze_stampare
//                                if ($competenza_tmp['scrutinio'][$periodo_ric]['voto']['valore_numerico'] > 0) {
                                    // dati generici
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['id'] = $compt_tmp_dati_generali['id'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['tipo'] = $compt_tmp_dati_generali['tipo'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['descrizione'] = $compt_tmp_dati_generali['descrizione'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['codice'] = $compt_tmp_dati_generali['codice'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['descrizione_stampa'] = $compt_tmp_dati_generali['descrizione_stampa'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['descrizione_1qt'] = $compt_tmp_dati_generali['descrizione'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['codice_1qt'] = $compt_tmp_dati_generali['codice'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['descrizione_stampa_1qt'] = $compt_tmp_dati_generali['descrizione_stampa'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['descrizione_2t'] = $compt_tmp_dati_generali['descrizione'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['codice_2t'] = $compt_tmp_dati_generali['codice'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['descrizione_stampa_2t'] = $compt_tmp_dati_generali['descrizione_stampa'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['descrizione_finale'] = $compt_tmp_dati_generali['descrizione'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['codice_finale'] = $compt_tmp_dati_generali['codice'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['descrizione_stampa_finale'] = $compt_tmp_dati_generali['descrizione_stampa'];
                                    //voto
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa']; //default per voto
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto_valore_numerico' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['valore_numerico'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto_codice' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['codice'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto_descrizione' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto_descrizione_stampa' . $chiave_tipo_tabellone_competenze] = safe_decode($competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa']);
                                    //proposta
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['proposta' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione_stampa']; //default per proposta
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['proposta_valore_numerico' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['valore_numerico'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['proposta_codice' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['codice'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['proposta_descrizione' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione'];
                                    $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['proposta_descrizione_stampa' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione_stampa'];

                                    // se periodo è il default
                                    if (
                                            ($periodo_default==9 && $chiave_tipo_tabellone_competenze=='_finale')
                                            ||
                                            ($periodo_default==8 && $chiave_tipo_tabellone_competenze=='_2t')
                                            ||
                                            ($periodo_default==7 && $chiave_tipo_tabellone_competenze=='_1qt')
                                        ) {
                                        $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto'] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa']; //default per voto
                                        $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto_valore_numerico'] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['valore_numerico'];
                                        $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto_codice'] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['codice'];
                                        $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto_descrizione'] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione'];
                                        $materia_tmp['elenco_competenze'][$competenza_tmp['id']]['voto_descrizione_stampa'] = safe_decode($competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa']);
                                    }
//                                }

                                // elenco competenze totali, anche non valorizzate
                                // dati generici
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['id'] = $compt_tmp_dati_generali['id'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['tipo'] = $compt_tmp_dati_generali['tipo'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['descrizione'] = $compt_tmp_dati_generali['descrizione'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['codice'] = $compt_tmp_dati_generali['codice'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['descrizione_stampa'] = $compt_tmp_dati_generali['descrizione_stampa'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['descrizione_1qt'] = $compt_tmp_dati_generali['descrizione'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['codice_1qt'] = $compt_tmp_dati_generali['codice'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['descrizione_stampa_1qt'] = $compt_tmp_dati_generali['descrizione_stampa'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['descrizione_2t'] = $compt_tmp_dati_generali['descrizione'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['codice_2t'] = $compt_tmp_dati_generali['codice'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['descrizione_stampa_2t'] = $compt_tmp_dati_generali['descrizione_stampa'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['descrizione_finale'] = $compt_tmp_dati_generali['descrizione'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['codice_finale'] = $compt_tmp_dati_generali['codice'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['descrizione_stampa_finale'] = $compt_tmp_dati_generali['descrizione_stampa'];
                                //voto
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['voto' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa']; //default per voto
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['voto_valore_numerico' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['valore_numerico'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['voto_codice' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['codice'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['voto_descrizione' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['voto_descrizione_stampa' . $chiave_tipo_tabellone_competenze] = safe_decode($competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa']);
                                //proposta
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['proposta' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione_stampa']; //default per proposta
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['proposta_valore_numerico' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['valore_numerico'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['proposta_codice' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['codice'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['proposta_descrizione' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione'];
                                $materia_tmp['elenco_competenze_totali'][$competenza_tmp['id']]['proposta_descrizione_stampa' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione_stampa'];

                                // scrutinato solo se valorizzata almeno una competenza nel tabellone finale
                                if ($periodo_default == 9 && $competenza_tmp['scrutinio'][9]['voto']['valore_numerico'] > 0) {
                                    $scrutinato = true;
                                }

                                unset($scrutinio_competenze['elenco'][$key_comp]);
                            }
                        }
                    }
                }

                // trasversali
                if ($stampa_competenze_trasversali == 'SI' && $materia['in_media_pagelle'] != 'NV')
                {
                    if (count($scrutinio_competenze['elenco']) > 0) {
                        $array_dati['elenco_materie'][-1]['id_materia'] = -1;
                        $array_dati['elenco_materie'][-1]['descrizione'] = 'TRASVERSALI';

                        foreach ($scrutinio_competenze['elenco'] as $key_comp => $competenza) {
                            $compt_tmp_dati_generali = $competenza;
                            unset($compt_tmp_dati_generali['valutazioni_figli']);
                            unset($compt_tmp_dati_generali['scrutinio']);
                            foreach ($compt_tmp_dati_generali['schema_valutazioni'] as $key_schema => $val) {
                                if ($val['tipo'] != 'scrutini') {
                                    unset($compt_tmp_dati_generali['schema_valutazioni'][$key_schema]);
                                }
                            }

                            $competenza_tmp = $competenza;

                            // dati generici
                            if (!isset($array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']])) {
                                $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['id'] = $compt_tmp_dati_generali['id'];
                                $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['tipo'] = $compt_tmp_dati_generali['tipo'];
                                $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['descrizione'] = $compt_tmp_dati_generali['descrizione'];
                                $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['codice'] = $compt_tmp_dati_generali['codice'];
                                $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['descrizione_stampa'] = $compt_tmp_dati_generali['descrizione_stampa'];
                            }

                            $periodo_ric = $periodo_default;
                            switch ($chiave_tipo_tabellone_competenze) {
                                case '_1qt': $periodo_ric=7; break;
                                case '_2t': $periodo_ric=8; break;
                                case '_finale': $periodo_ric=9; break;
                            }
                            //voto
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['voto' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa']; //default per voto
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['voto_valore_numerico' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['valore_numerico'];
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['voto_codice' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['codice'];
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['voto_descrizione' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione'];
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['voto_descrizione_stampa' . $chiave_tipo_tabellone_competenze] = safe_decode($competenza_tmp['scrutinio'][$periodo_ric]['voto']['descrizione_stampa']);
                            //proposta
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['proposta' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione_stampa']; //default per proposta
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['proposta_valore_numerico' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['valore_numerico'];
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['proposta_codice' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['codice'];
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['proposta_descrizione' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione'];
                            $array_dati['elenco_materie'][-1]['elenco_competenze'][$competenza_tmp['id']]['proposta_descrizione_stampa' . $chiave_tipo_tabellone_competenze] = $competenza_tmp['scrutinio'][$periodo_ric]['proposta']['descrizione_stampa'];
                        }
                    }
                }
            }

            // convert associative array to normal array, altrimenti la translation non funziona in next-api
            if (!empty($materia_tmp['elenco_competenze'])) {
                $materia_tmp['elenco_competenze'] = array_values($materia_tmp['elenco_competenze']);
            }
            foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
                if (!empty($materia_tmp['elenco_competenze' . $pc])) {
                    $materia_tmp['elenco_competenze' . $pc] = array_values($materia_tmp['elenco_competenze' . $pc]);
                }
            }
            if (!empty($materia_tmp['elenco_competenze_totali'])) {
                $materia_tmp['elenco_competenze_totali'] = array_values($materia_tmp['elenco_competenze_totali']);
            }

            // campo libero vuoto generico per inizializzare liste elenchi competenze usate in pagella ma non state inizializzate precedentemente perchè non presenti in scrutini
            $comp_empty_tmp = [];

            $comp_empty_tmp['descrizione'] = '';
            $comp_empty_tmp['codice'] = '';
            $comp_empty_tmp['descrizione_stampa'] = '';
            $comp_empty_tmp['tipo'] = '';
            $comp_empty_tmp['voto'] = '';
            $comp_empty_tmp['voto_valore_numerico'] = '';
            $comp_empty_tmp['voto_codice'] = '';
            $comp_empty_tmp['voto_descrizione'] = '';
            $comp_empty_tmp['voto_descrizione_stampa'] = '';
            $comp_empty_tmp['proposta'] = '';
            $comp_empty_tmp['proposta_valore_numerico'] = '';
            $comp_empty_tmp['proposta_codice'] = '';
            $comp_empty_tmp['proposta_descrizione'] = '';
            $comp_empty_tmp['proposta_descrizione_stampa'] = '';
            $comp_empty_tmp['voto_1qt'] = '';
            $comp_empty_tmp['voto_valore_numerico_1qt'] = '';
            $comp_empty_tmp['voto_codice_1qt'] = '';
            $comp_empty_tmp['voto_descrizione_1qt'] = '';
            $comp_empty_tmp['voto_descrizione_stampa_1qt'] = '';
            $comp_empty_tmp['voto_2t'] = '';
            $comp_empty_tmp['voto_valore_numerico_2t'] = '';
            $comp_empty_tmp['voto_codice_2t'] = '';
            $comp_empty_tmp['voto_descrizione_2t'] = '';
            $comp_empty_tmp['voto_descrizione_stampa_2t'] = '';
            $comp_empty_tmp['voto_finale'] = '';
            $comp_empty_tmp['voto_valore_numerico_finale'] = '';
            $comp_empty_tmp['voto_codice_finale'] = '';
            $comp_empty_tmp['voto_descrizione_finale'] = '';
            $comp_empty_tmp['voto_descrizione_stampa_finale'] = '';
            // svuoto voti competenze non presenti, cosi da togliere tag voti in doc
            if (!empty($materia_tmp['elenco_competenze'])) {
                foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
                    foreach ($materia_tmp['elenco_competenze'] as $cont_comp => $comp) {
                        if (!isset($comp['voto' . $pc])) {
                            $comp['voto' . $pc] = '';
                            $comp['voto_valore_numerico' . $pc] = '';
                            $comp['voto_codice' . $pc] = '';
                            $comp['voto_descrizione' . $pc] = '';
                            $comp['voto_descrizione_stampa' . $pc] = '';

                            $materia_tmp['elenco_competenze'][$cont_comp] = $comp;
                        }

                        if ($periodo==$valperiodo && empty($comp['voto'])) {
                            $comp['voto'] = '';
                            $comp['voto_valore_numerico'] = '';
                            $comp['voto_codice'] = '';
                            $comp['voto_descrizione'] = '';
                            $comp['voto_descrizione_stampa'] = '';

                            $materia_tmp['elenco_competenze'][$cont_comp] = $comp;
                        }
                    }
                }

                // inizializzo a elenco vuoto se non esiste per le chiavi periodi doc
                foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
                    if (!isset($materia_tmp['elenco_competenze' . $pc])) {
                        $materia_tmp['elenco_competenze' . $pc][] = $comp_empty_tmp;
                    } else {
                        $materia_con_voti = true;
                    }
                }

            } else {
                $materia_tmp['elenco_competenze'][] = $comp_empty_tmp;
                foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
                    $materia_tmp['elenco_competenze' . $pc][] = $comp_empty_tmp;
                }
            }

        }
        //}}} </editor-fold>

        if ($filtro_materie == "SOLO_VOTI" && $materia_con_voti === false) {
            continue;
        }

        // pulizia valori/dati della materia
        $materia_tmp['descrizione'] = decode($materia_tmp['descrizione']);
        $materia_tmp['nome_materia_sito'] = decode($materia_tmp['nome_materia_sito']);

        if ($include_elenco_materie_totali) {
            $array_dati['elenco_materie_totali'][$materia_tmp['codice_ministeriale']] = $materia_tmp;
        }
        if ($materia['in_media_pagelle'] == 'NV') {
            continue;
        }

        switch ($materia['tipo_materia'])
        {
            case 'EDUCAZIONE FISICA':
            case 'LINGUA STRANIERA':
            case 'NORMALE':
                if ($include_elenco_materie_normali) {
                    $array_dati['elenco_materie_normali'][] = $materia_tmp;
                }
                break;
            case 'OPZIONALE':
                if ($include_elenco_materie_opzionali) {
                    $array_dati['elenco_materie_opzionali'][] = $materia_tmp;
                }
                break;
            case 'RELIGIONE':
                if ($include_elenco_materie_religione) {
                    if (isset($materia_tmp['elenco_competenze'])) {
                        $materia_tmp['elenco_competenze_religione'] = $materia_tmp['elenco_competenze'];
                        foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
                            if (!empty($materia_tmp['elenco_competenze' . $pc])) {
                                $materia_tmp['elenco_competenze_religione' . $pc] = $materia_tmp['elenco_competenze' . $pc];
                            }
                        }
                    }
                    $array_dati['elenco_materie_religione'][] = $materia_tmp;

                    if ($dati_studente['esonero_religione'] == 0){
                        if (isset($materia_tmp['elenco_competenze'])) {
                            $materia_tmp['elenco_competenze_religionealternativa'] = $materia_tmp['elenco_competenze'];
                            foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
                                if (!empty($materia_tmp['elenco_competenze' . $pc])) {
                                    $materia_tmp['elenco_competenze_religionealternativa' . $pc] = $materia_tmp['elenco_competenze' . $pc];
                                }
                            }
                        }
                        $array_dati['elenco_materie_religionealternativa'][] = $materia_tmp;
                    }
                }
                break;
            case 'ALTERNATIVA':
                if ($include_elenco_materie_religione) {   
                    if (isset($materia_tmp['elenco_competenze'])) {
                        $materia_tmp['elenco_competenze_alternativa'] = $materia_tmp['elenco_competenze'];
                        foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
                            if (!empty($materia_tmp['elenco_competenze' . $pc])) {
                                $materia_tmp['elenco_competenze_alternativa' . $pc] = $materia_tmp['elenco_competenze' . $pc];
                            }
                        }
                    }
                    $array_dati['elenco_materie_alternativa'][] = $materia_tmp;

                    if ($dati_studente['esonero_religione'] == 1){
                        if (isset($materia_tmp['elenco_competenze'])) {
                            $materia_tmp['elenco_competenze_religionealternativa'] = $materia_tmp['elenco_competenze'];
                            foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
                                if (!empty($materia_tmp['elenco_competenze' . $pc])) {
                                    $materia_tmp['elenco_competenze_religionealternativa' . $pc] = $materia_tmp['elenco_competenze' . $pc];
                                }
                            }
                        }
                        $array_dati['elenco_materie_religionealternativa'][] = $materia_tmp;
                    }
                }
                break;
            case 'CONDOTTA':
                if ($include_elenco_materie_condotta) {
                    if (isset($materia_tmp['elenco_competenze'])) {
                        $materia_tmp['elenco_competenze_condotta'] = $materia_tmp['elenco_competenze'];
                        foreach ($periodi_competenze_tmp as $valperiodo => $pc) {
                            if (!empty($materia_tmp['elenco_competenze' . $pc])) {
                                $materia_tmp['elenco_competenze_condotta' . $pc] = $materia_tmp['elenco_competenze' . $pc];
                            }
                        }
                    }
                    $array_dati['elenco_materie_condotta'][] = $materia_tmp;
                }
                break;
        }

        // Materie in pagella
        if ($include_elenco_materie) {
            $array_dati['elenco_materie'][] = $materia_tmp;
        }
        if ($include_materie) {
            $array_dati['materie'][$materia_tmp['codice_ministeriale']] = $materia_tmp;
        }

        if ($include_discipline_aggregate) { 
            $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['materie'][$materia_tmp['codice_ministeriale']] = $materia_tmp;
            $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['descrizione'] = $materia_tmp['tipologia_aggregamento'];
            if ($materia_tmp['in_media_pagelle'] == 'SI') {
                $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['ore_assenza'] += intval($voti_pagella[$id_mat]['ore_assenza']);
                $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['ore_assenza_1qt'] += intval($voti_pagella_1qt[$id_mat]['ore_assenza']);
                $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['ore_assenza_2t'] += intval($voti_pagella_2t[$id_mat]['ore_assenza']);
                $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['ore_assenza_finale'] += intval($voti_pagella_finale[$id_mat]['ore_assenza']);
                $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['monteore_totale'] += intval($voti_pagella[$id_mat]['monteore_totale']);
                $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['monteore_totale_1qt'] += intval($voti_pagella_1qt[$id_mat]['monteore_totale']);
                $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['monteore_totale_2t'] += intval($voti_pagella_2t[$id_mat]['monteore_totale']);
                $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['monteore_totale_finale'] += intval($voti_pagella_finale[$id_mat]['monteore_totale']);
                if ($voti_pagella[$id_mat]['voto_pagellina'] > 0) {
                    $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['somma_voti'] += $voti_pagella[$id_mat]['voto_pagellina'];
                    $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['num_mat']++;
                }
                if ($voti_pagella_1qt[$id_mat]['voto_pagellina'] > 0) {
                    $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['somma_voti_1qt'] += $voti_pagella_1qt[$id_mat]['voto_pagellina'];
                    $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['num_mat_1qt']++;
                }
                if ($voti_pagella_2t[$id_mat]['voto_pagellina'] > 0) {
                    $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['somma_voti_2t'] += $voti_pagella_2t[$id_mat]['voto_pagellina'];
                    $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['num_mat_2t']++;
                }
                if ($voti_pagella_finale[$id_mat]['voto_pagellina'] > 0) {
                    $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['somma_voti_finale'] += $voti_pagella_finale[$id_mat]['voto_pagellina'];
                    $array_dati['discipline_aggregate'][$materia_tmp['tipologia_aggregamento']]['num_mat_finale']++;
                }
            }
        }
    }

    if ($include_discipline_aggregate) { 
        foreach ($array_dati['discipline_aggregate'] as $tipologia_aggregamento => $area_discipline) { 
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media'] = ($area_discipline['num_mat']>0 ? round($area_discipline['somma_voti'] / $area_discipline['num_mat'], 2) : '');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_1qt'] = ($area_discipline['num_mat_1qt']>0 ? round($area_discipline['somma_voti_1qt'] / $area_discipline['num_mat_1qt'], 2) : '');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_2t'] = ($area_discipline['num_mat_2t']>0 ? round($area_discipline['somma_voti_2t'] / $area_discipline['num_mat_2t'], 2) : '');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_finale'] = ($area_discipline['num_mat_finale']>0 ? round($area_discipline['somma_voti_finale'] / $area_discipline['num_mat_finale'], 2) : '');

            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_arrotondato'] = $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media']!='' ? round($array_dati['discipline_aggregate'][$tipologia_aggregamento]['media']) : '';
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_arrotondato_1qt'] = $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_1qt']!='' ? round($array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_1qt']) : '';
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_arrotondato_2t'] = $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_2t']!='' ? round($array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_2t']) : '';
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_arrotondato_finale'] = $array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_finale']!='' ? round($array_dati['discipline_aggregate'][$tipologia_aggregamento]['media_finale']) : '';

            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['ore_assenza_arrotondato'] += traduci_minuti_in_ore_minuti($area_discipline['ore_assenza'], 'ORE_ARROTONDATE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['ore_assenza_1qt_arrotondato'] += traduci_minuti_in_ore_minuti($area_discipline['ore_assenza_1qt'], 'ORE_ARROTONDATE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['ore_assenza_2t_arrotondato'] += traduci_minuti_in_ore_minuti($area_discipline['ore_assenza_2t'], 'ORE_ARROTONDATE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['ore_assenza_finale_arrotondato'] += traduci_minuti_in_ore_minuti($area_discipline['ore_assenza_finale'], 'ORE_ARROTONDATE');

            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['monteore_totale_arrotondato'] += traduci_minuti_in_ore_minuti($area_discipline['monteore_totale'], 'ORE_ARROTONDATE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['monteore_totale_1qt_arrotondato'] += traduci_minuti_in_ore_minuti($area_discipline['monteore_totale_1qt'], 'ORE_ARROTONDATE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['monteore_totale_2t_arrotondato'] += traduci_minuti_in_ore_minuti($area_discipline['monteore_totale_2t'], 'ORE_ARROTONDATE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['monteore_totale_finale_arrotondato'] += traduci_minuti_in_ore_minuti($area_discipline['monteore_totale_finale'], 'ORE_ARROTONDATE');

            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['ore_assenza_ore_minuti'] += traduci_minuti_in_ore_minuti($area_discipline['ore_assenza'], 'ORE_MINUTI_ESTESE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['ore_assenza_1qt_ore_minuti'] += traduci_minuti_in_ore_minuti($area_discipline['ore_assenza_1qt'], 'ORE_MINUTI_ESTESE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['ore_assenza_2t_ore_minuti'] += traduci_minuti_in_ore_minuti($area_discipline['ore_assenza_2t'], 'ORE_MINUTI_ESTESE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['ore_assenza_finale_ore_minuti'] += traduci_minuti_in_ore_minuti($area_discipline['ore_assenza_finale'], 'ORE_MINUTI_ESTESE');

            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['monteore_totale_ore_minuti'] += traduci_minuti_in_ore_minuti($area_discipline['monteore_totale'], 'ORE_MINUTI_ESTESE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['monteore_totale_1qt_ore_minuti'] += traduci_minuti_in_ore_minuti($area_discipline['monteore_totale_1qt'], 'ORE_MINUTI_ESTESE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['monteore_totale_2t_ore_minuti'] += traduci_minuti_in_ore_minuti($area_discipline['monteore_totale_2t'], 'ORE_MINUTI_ESTESE');
            $array_dati['discipline_aggregate'][$tipologia_aggregamento]['monteore_totale_finale_ore_minuti'] += traduci_minuti_in_ore_minuti($area_discipline['monteore_totale_finale'], 'ORE_MINUTI_ESTESE');
        }
    }

    // convert associative array to normal array, altrimenti la translation non funziona in next-api
    if (!empty($array_dati['elenco_materie'][-1]['elenco_competenze'])) {
        $array_dati['elenco_materie'][-1]['elenco_competenze'] = array_values($array_dati['elenco_materie'][-1]['elenco_competenze']);
    }

    if ($stud_da_stampare === false) {
        continue;
    }

    if (
        ( $filtro_sospesi == 'SOSPESI_PROMOSSI' && 
            !($da_giudizio_sospeso 
                && 
                (
                    strpos($studente['esito_corrente_calcolato'], 'Ammesso') !== false
                    ||
                    strpos($studente['esito_corrente_calcolato'], 'Diplomato') !== false
                    ||
                    strpos($studente['esito_corrente_calcolato'], 'Licenziato') !== false
                    ||
                    strpos($studente['esito_corrente_calcolato'], 'Qualificato') !== false
                )
            ) 
        )
        ||
        ( $filtro_sospesi == 'SOSPESI_BOCCIATI' &&
            !($da_giudizio_sospeso 
                &&
             strpos($studente['esito_corrente_calcolato'], 'Non') !== false
            )
        )
        ||
        ( $filtro_sospesi == 'NON_SOSPESI' && $da_giudizio_sospeso )
        ||
        ( $filtro_sospesi == 'SOLO_SOSPESI' && !$da_giudizio_sospeso )
        ) {
        continue;
    }

    // validazione anno
    if ($tot_ore_fine_anno > 0) {
        $perc = round(($tot_assenze_fine_anno / $tot_ore_fine_anno) * 100);

        if ($perc < 25) {
            $array_dati['ha_non_ha_frequentato'] = 'ha frequentato';
            $array_dati['deroga'] = '';
            $array_dati['croce_frequentato'] = 'X';
        } else {
            if ($scrutinato) {
                $array_dati['ha_non_ha_frequentato'] = 'ha frequentato';
                $array_dati['deroga'] = 'con deroga';
                $array_dati['croce_deroga'] = 'X';
            } else {
                $array_dati['ha_non_ha_frequentato'] = 'non ha frequentato';
                $array_dati['deroga'] = '';
                $array_dati['croce_non_frequentato'] = 'X';
            }
        }
    } else {
        if ($scrutinato) {
            $array_dati['ha_non_ha_frequentato'] = 'ha frequentato';
            $array_dati['deroga'] = '';
            $array_dati['croce_frequentato'] = 'X';
        } else {
            $array_dati['ha_non_ha_frequentato'] = '';
            $array_dati['deroga'] = '';
        }
    }
    
    // conversione/calcolo tag specifici, di default si prende il tag da studenti_completi
    foreach ($variabili_template as $variabile)
    {
        switch ($variabile) {
            // scrutini
            case 'descrizione_periodo':
                switch ($periodo_default) {
                    case 7:
                    case 27:
                        $array_dati[$variabile] = 'QUADRIMESTRE';
                        break;
                    case 8:
                    case 28:
                        $array_dati[$variabile] = 'SECONDO TRIMESTRE';
                        break;
                    case 9:
                    case 29:
                        $array_dati[$variabile] = 'FINE ANNO';
                        break;
                    default:
                        $array_dati[$variabile] = ($periodo_default>20? $periodo_default-20:$periodo_default)."^ INFRAQUADRIMESTRALE";
                        break;
                }
                break;
            case 'consiglio_orientativo':
            case 'consiglio_orientativo2':
            case 'consiglio_orientativo3':
                $param_consigli = [
                    "anno_scolastico" => "{$anno_inizio}_{$anno_fine}"
                ];
                $elenco_consigli = nextapi_call('/school/structure/consiglio_orientativo', 'GET', $param_consigli, $current_key);
                foreach ($elenco_consigli as $consiglio){
                    if ($consiglio['id_consiglio_orientativo_template'] == $dati_studente['id_'.$variabile]) {
                        $array_dati[$variabile] = $consiglio['descrizione'];
                    }
                }
                break;
            case 'elenco_consigli_orientativi':
                $param_consigli = [
                    "anno_scolastico" => "{$anno_inizio}_{$anno_fine}"
                ];
                $elenco_consigli = nextapi_call('/school/structure/consiglio_orientativo', 'GET', $param_consigli, $current_key);
                $consigli_tmp = [];
                foreach ($elenco_consigli as $consiglio){
                    if ($consiglio['id_consiglio_orientativo_template'] == $dati_studente['id_consiglio_orientativo']) {
                        $consigli_tmp[1] = $consiglio['descrizione'];
                    }
                    if ($consiglio['id_consiglio_orientativo_template'] == $dati_studente['id_consiglio_orientativo2']) {
                        $consigli_tmp[2] = $consiglio['descrizione'];
                    }
                    if ($consiglio['id_consiglio_orientativo_template'] == $dati_studente['id_consiglio_orientativo3']) {
                        $consigli_tmp[3] = $consiglio['descrizione'];
                    }
                }
                ksort($consigli_tmp);
                $array_dati[$variabile] = implode(', ', $consigli_tmp);
                break;
            case 'pagella_nota_descrizione_materia_religione_o_alternativa':
                if ($studente['esonero_religione'] == 1) {
                    $array_dati[$variabile] = 'materia alternativa alla Religione Cattolica';
                } else {
                    $array_dati[$variabile] = 'Religione Cattolica';
                }
                break;
            case 'esito_corrente_calcolato_francese':
                switch($dati_studente['esito_corrente_calcolato'])
                {
                    case "Ammesso alla classe successiva":
                        $array_dati[$variabile] = "a été Admis à la classe supérieure";
                    break;
                    case "Non ammesso alla classe successiva":
                        $array_dati[$variabile] = "a été Non admis à la classe supérieure";
                    break;
                    case "Ammessa alla classe successiva":
                        $array_dati[$variabile] = "a été Admise à la classe supérieure";
                    break;
                    case "Non ammessa alla classe successiva":
                        $array_dati[$variabile] = "a été Non admise à la classe supérieure";
                    break;
                    case "Giudizio sospeso":
                        $array_dati[$variabile] = "a été Renvoyé/e au mois de septembre";
                    break;
                    case "Ammesso esame di stato":
                        $array_dati[$variabile] = "a été Admis aux examens";
                    break;
                    case "Ammessa esame di stato":
                        $array_dati[$variabile] = "a été Admise aux examens";
                    break;
                    case "Non ammesso esame di stato":
                        $array_dati[$variabile] = "a été Non admis aux examens";
                    break;
                    case "Non ammessa esame di stato":
                        $array_dati[$variabile] = "a été Non admise aux examens";
                    break;
                    case "Ammesso esame di qualifica":
                        $array_dati[$variabile] = "a été Admis aux examens";
                    break;
                    case "Non ammesso esame di qualifica":
                        $array_dati[$variabile] = "a été Non admis aux examens";
                    break;
                    case "Ammessa esame di qualifica":
                        $array_dati[$variabile] = "a été Admise aux examens";
                    break;
                    case "Non ammessa esame di qualifica":
                        $array_dati[$variabile] = "a été Non admise aux examens";
                    break;
                    case "Qualificato":
                        $array_dati[$variabile] = "a obtenu le diplôme de formation professionnelle";
                    break;
                    case "Non qualificato":
                        $array_dati[$variabile] = "a été refusé";
                    break;
                    case "Qualificata":
                        $array_dati[$variabile] = "a obtenu le diplôme de formation professionnelle";
                    break;
                    case "Non qualificata":
                        $array_dati[$variabile] = "a été refusée";
                    break;
                    default:
                        $array_dati[$variabile] = "";
                    break;
                }
                break;
            case 'elenco_materie_sospese_testo':
                if (!empty($array_dati['elenco_materie_sospese'])) {
                    $array_dati[$variabile] = implode(', ', array_column($array_dati['elenco_materie_sospese'], 'descrizione'));
                } else {
                    $array_dati[$variabile] = '';
                }
                break;
            case 'elenco_materie_sospese_1qt_testo':
                if (!empty($array_dati['elenco_materie_sospese_1qt'])) {
                    $array_dati[$variabile] = implode(', ', array_column($array_dati['elenco_materie_sospese_1qt'], 'descrizione'));
                } else {
                    $array_dati[$variabile] = '';
                }
                break;
            case 'elenco_materie_sospese_2t_testo':
                if (!empty($array_dati['elenco_materie_sospese_2t'])) {
                    $array_dati[$variabile] = implode(', ', array_column($array_dati['elenco_materie_sospese_2t'], 'descrizione'));
                } else {
                    $array_dati[$variabile] = '';
                }
                break;
            case 'elenco_materie_sospese_finale_testo':
                if (!empty($array_dati['elenco_materie_sospese_finale'])) {
                    $array_dati[$variabile] = implode(', ', array_column($array_dati['elenco_materie_sospese_finale'], 'descrizione'));
                } else {
                    $array_dati[$variabile] = '';
                }
                break;
            case 'giudizio_sintetico_esame_terza_media_lettere':
            case 'giudizio_sintetico_esame_terza_media_tradotto':
                $array_dati[$variabile] = traduci_numero_in_lettere($dati_studente['giudizio_sintetico_esame_terza_media']);
                break;

            // dati anagrafici
            case 'suffisso_o_a':
            case 'suffisso_e_essa':
            case 'suffisso_O_A':
            case 'suffisso_E_ESSA':
                if ($dati_studente['sesso'] == 'M') {
                    $array_dati['suffisso_o_a'] = 'o';
                    $array_dati['suffisso_e_essa'] = 'e';
                    $array_dati['suffisso_O_A'] = 'O';
                    $array_dati['suffisso_E_ESSA'] = 'E';
                }
                else {
                    $array_dati['suffisso_o_a'] = 'a';
                    $array_dati['suffisso_e_essa'] = 'essa';
                    $array_dati['suffisso_O_A'] = 'A';
                    $array_dati['suffisso_E_ESSA'] = 'ESSA';
                }
                break;
            case 'descrizione_nascita':
            case 'provincia_nascita_da_comune':
                if ( stripos($dati_studente['descrizione_nascita'], 'ESTERO') !== false  || !$dati_studente['descrizione_nascita']) {
                    $array_dati['descrizione_nascita'] = $dati_studente['citta_nascita_straniera'];
                } else {
                    $array_dati['descrizione_nascita'] = $dati_studente['descrizione_nascita'];
                }
                if ( stripos($dati_studente['provincia_nascita_da_comune'], 'EE') !== false ) {
                    $array_dati['provincia_nascita_da_comune'] = $dati_studente['descrizione_stato_nascita'];
                } else {
                    $array_dati['provincia_nascita_da_comune'] = $dati_studente['provincia_nascita_da_comune'];
                }
                break;
            // conversione timestamp in date
            case 'data_oggi':
                $today = getdate();
                $array_dati[$variabile] = $today['mday'] . '/' . $today['mon'] . '/' . $today['year'];
                break;
            case 'data_nascita':
            case 'data_ritiro':
            case 'data_arrivo_in_italia':
            case 'data_iscrizione':
            case 'data_nascita_padre':
            case 'data_nascita_madre':
            case 'data_nascita_tutore':
                $array_dati[$variabile] = date("d/m/Y", intval($dati_studente[$variabile]));
                break;
            // curriculum e dati scolastici e della classe
            case 'classe_romana':
                switch ($dati_studente['classe']) {
                    case '1': $array_dati[$variabile] = 'I'; break;
                    case '2': $array_dati[$variabile] = 'II'; break;
                    case '3': $array_dati[$variabile] = 'III'; break;
                    case '4': $array_dati[$variabile] = 'IV'; break;
                    case '5': $array_dati[$variabile] = 'V'; break;
                }
                break;
            case 'anno_scolastico': $array_dati[$variabile] = $anno_scolastico; break;
            case 'data_inizio_lezioni': $array_dati[$variabile] = date("d/m/Y", estrai_parametri_singoli('DATA_INIZIO_LEZIONI', $id_classe, 'classe')); break;
            case 'data_inizio_secondo_quadrimestre': $array_dati[$variabile] = date("d/m/Y", estrai_parametri_singoli('DATA_INIZIO_SECONDO_QUADRIMESTRE', $id_classe, 'classe')); break;
            case 'data_inizio_terzo_trimestre': $array_dati[$variabile] = date("d/m/Y", estrai_parametri_singoli('DATA_INIZIO_TERZO_TRIMESTRE', $id_classe, 'classe')); break;
            case 'data_fine_primo_quadrimestre': $array_dati[$variabile] = date("d/m/Y", estrai_parametri_singoli('DATA_INIZIO_SECONDO_QUADRIMESTRE', $id_classe, 'classe') - (60*60*12)); break;
            case 'data_fine_secondo_trimestre': $array_dati[$variabile] = date("d/m/Y", estrai_parametri_singoli('DATA_INIZIO_TERZO_TRIMESTRE', $id_classe, 'classe') - (60*60*12)); break;
            case 'data_fine_lezioni': $array_dati[$variabile] = date("d/m/Y", estrai_parametri_singoli('DATA_FINE_LEZIONI', $id_classe, 'classe')); break;
            case 'num_volte_iscritto_classe_attuale_testo': $array_dati[$variabile] = traduci_classe_in_lettere($dati_studente['num_volte_iscritto_classe_attuale']);break;
            case 'voto_ammissione_medie_lettere': $array_dati[$variabile] = traduci_numero_in_lettere($dati_studente['voto_ammissione_medie']);break;
            case 'voto_qualifica_lettere':
                if ($dati_studente['voto_qualifica'] > 0) {
                    $array_dati[$variabile] = strtoupper(traduci_numero_in_lettere($dati_studente['voto_qualifica']));
                } else {
                    $array_dati[$variabile] = '';
                }
                break;
            case 'esito_classe_destinazione':
            case 'esito_classe_precedente':
            case 'esito_descrizione_classe_precedente':
            case 'esito_numero_volte_iscritto':
            case 'esito_numero_volte_iscritto_testo':
                $array_dati['esito_classe_destinazione'] = $dati_studente['mat_esito']['classe_destinazione'];
                $array_dati['esito_classe_precedente'] = $dati_studente['mat_esito']['esito_precedente'];
                $array_dati['esito_descrizione_classe_precedente'] = $dati_studente['mat_esito']['esito_precedente'];
                $array_dati['esito_numero_volte_iscritto'] = $dati_studente['mat_esito']['numero_volte_iscritto'];
                $array_dati['esito_numero_volte_iscritto_testo'] = traduci_classe_in_lettere($dati_studente['mat_esito']['numero_volte_iscritto']);
                break;
            case 'esito_numero_volte_iscritto_testo_francese':
                $array_dati['esito_numero_volte_iscritto_testo_francese'] = traduci_classe_in_lettere($dati_studente['mat_esito']['numero_volte_iscritto'], 'NO', 'francese');
                break;

            case 'scuola_provenienza':
                $curriculum_studente = estrai_curriculum_studente((int) $id_studente);
                $anno_scolastico_precedente = ($anno_inizio - 1) . "/" . ($anno_fine - 1);
                $scuola_provenienza = '';
                for ($cont_curr = 0; $cont_curr < count($curriculum_studente); $cont_curr++) {
                    if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_precedente) {
                        if ($dati_studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola'] && $dati_studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola']) {
                            $scuola_provenienza = $curriculum_studente[$cont_curr]['nome_scuola'] . $curriculum_studente[$cont_curr]['descrizione_libera_scuola'];
                        } else {
                            $scuola_provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'] . ' ' . $curriculum_studente[$cont_curr]['descrizione_libera_scuola'];
                        }
                    }
                }
                $array_dati['scuola_provenienza'] = $scuola_provenienza;
                break;
            case 'coordinatore':
                $coordinatore = estrai_coordinatore($id_classe);
                $array_dati[$variabile] = $coordinatore['cognome'] . ' ' . $coordinatore['nome'];
                break;
            case 'esito_corrente_calcolato':
                switch($dati_studente['esito_corrente_calcolato']) {
                    case "Ammesso esame di stato":
                        $dati_studente['esito_corrente_calcolato'] = "Ammesso all'esame di stato"; 
                        break;
                    case "Non ammesso esame di stato":
                        $dati_studente['esito_corrente_calcolato'] = "Non ammesso all'esame di stato"; 
                        break;
                    case "Ammesso esame di qualifica":
                        $dati_studente['esito_corrente_calcolato'] = "Ammesso all'esame di qualifica";
                        break;
                    case "Non ammesso esame di qualifica":
                        $dati_studente['esito_corrente_calcolato'] = "Non ammesso all'esame di qualifica";
                        break;
                }
                if ($dati_studente['sesso'] == 'F') {
                    $dati_studente['esito_corrente_calcolato'] = str_replace('scritto', 'scritta', $dati_studente['esito_corrente_calcolato']);
                    $dati_studente['esito_corrente_calcolato'] = str_replace('mmesso', 'mmessa', $dati_studente['esito_corrente_calcolato']);
                    $dati_studente['esito_corrente_calcolato'] = str_replace('alunno', 'alunna', $dati_studente['esito_corrente_calcolato']);
                    $dati_studente['esito_corrente_calcolato'] = str_replace('rasferito', 'rasferita', $dati_studente['esito_corrente_calcolato']);
                    $dati_studente['esito_corrente_calcolato'] = str_replace('itirato', 'itirata', $dati_studente['esito_corrente_calcolato']);
                    $dati_studente['esito_corrente_calcolato'] = str_replace('icenziato', 'icenziata', $dati_studente['esito_corrente_calcolato']);
                    $dati_studente['esito_corrente_calcolato'] = str_replace('iplomato', 'iplomata', $dati_studente['esito_corrente_calcolato']);
                    $dati_studente['esito_corrente_calcolato'] = str_replace('ualificato', 'ualificata', $dati_studente['esito_corrente_calcolato']);
                }

                $array_dati[$variabile] = $dati_studente['esito_corrente_calcolato'];
                break;
            case 'lista_docenti':
                $docenti_classe = estrai_elenco_professori_multi_classe($id_classe);
                $lista_docenti = [];
                foreach ($docenti_classe as $key => $value) {
                    $lista_docenti[] = $value['cognome'] . ' ' . $value['nome'];
                }
                $array_dati['lista_docenti'] = implode(', ', $lista_docenti);
                break;
            // conversione campi in si/no/non disponibile
            case 'esonero_ed_fisica':
            case 'stage_professionali':
            case 'acconsente_aziende':
            case 'necessita_alfabetizzazione':
            case 'qualifica_iefp':
            case 'acconsente':
            case 'servizio_mensa':
            case 'adottato':
            case 'vaccinazioni':
            case 'necessita_sostegno':
            case 'autocertificazione_vaccinazioni':
            case 'sostenuto_prove_invalsi_medie':
            case 'esonero_religione':
            case 'frequenza_asilo_nido':
            case 'frequenza_scuola_materna':
                switch ($variabile) {
                    case 0:
                        $array_dati[$variabile] = 'NO';
                        break;
                    case 1:
                        $array_dati[$variabile] = 'SI';
                        break;
                    default:
                        $array_dati[$variabile] = 'non disponibile';
                        break;
                }
                break;
            // monteore e assenze totali per periodo
            case 'ore_assenza_totali_periodo_attuale': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_sel, 'ORE_MINUTI_ESTESE'); break;
            case 'monteore_totali_periodo_attuale': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_sel, 'ORE_MINUTI_ESTESE'); break;
            case 'ore_assenza_totali_1qt': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_1qt, 'ORE_MINUTI_ESTESE'); break;
            case 'monteore_totali_1qt': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_1qt, 'ORE_MINUTI_ESTESE'); break;
            case 'ore_assenza_totali_2t': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_2t, 'ORE_MINUTI_ESTESE'); break;
            case 'monteore_totali_2t': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_2t, 'ORE_MINUTI_ESTESE'); break;
            case 'ore_assenza_totali_finale': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_fine_anno, 'ORE_MINUTI_ESTESE'); break;
            case 'monteore_totali_finale': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_fine_anno, 'ORE_MINUTI_ESTESE'); break;
            case 'ore_assenza_totali_periodo_attuale_arrotondato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_sel, 'ORE_ARROTONDATE'); break;
            case 'monteore_totali_periodo_attuale_arrotondato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_sel, 'ORE_ARROTONDATE'); break;
            case 'ore_assenza_totali_1qt_arrotondato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_1qt, 'ORE_ARROTONDATE'); break;
            case 'monteore_totali_1qt_arrotondato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_1qt, 'ORE_ARROTONDATE'); break; 
            case 'ore_assenza_totali_2t_arrotondato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_2t, 'ORE_ARROTONDATE'); break;
            case 'monteore_totali_2t_arrotondato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_2t, 'ORE_ARROTONDATE'); break;
            case 'ore_assenza_totali_finale_arrotondato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_fine_anno, 'ORE_ARROTONDATE'); break;
            case 'monteore_totali_finale_arrotondato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_fine_anno, 'ORE_ARROTONDATE'); break;
            case 'ore_assenza_totali_periodo_attuale_troncato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_sel, 'ORE_TRONCATE'); break;
            case 'monteore_totali_periodo_attuale_troncato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_sel, 'ORE_TRONCATE'); break;
            case 'ore_assenza_totali_1qt_troncato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_1qt, 'ORE_TRONCATE'); break;
            case 'monteore_totali_1qt_troncato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_1qt, 'ORE_TRONCATE'); break;
            case 'ore_assenza_totali_2t_troncato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_2t, 'ORE_TRONCATE'); break;
            case 'monteore_totali_2t_troncato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_2t, 'ORE_TRONCATE'); break;
            case 'ore_assenza_totali_finale_troncato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_fine_anno, 'ORE_TRONCATE'); break;
            case 'monteore_totali_finale_troncato': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_fine_anno, 'ORE_TRONCATE'); break;
            case 'ore_assenza_totali_periodo_attuale_esteso': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_sel, 'ETICHETTA_ORE_MINUTI'); break;
            case 'monteore_totali_periodo_attuale_esteso': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_sel, 'ETICHETTA_ORE_MINUTI'); break;
            case 'ore_assenza_totali_1qt_esteso': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_1qt, 'ETICHETTA_ORE_MINUTI'); break;
            case 'monteore_totali_1qt_esteso': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_1qt, 'ETICHETTA_ORE_MINUTI'); break;
            case 'ore_assenza_totali_2t_esteso': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_2t, 'ETICHETTA_ORE_MINUTI'); break;
            case 'monteore_totali_2t_esteso': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_2t, 'ETICHETTA_ORE_MINUTI'); break;
            case 'ore_assenza_totali_finale_esteso': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_fine_anno, 'ETICHETTA_ORE_MINUTI'); break;
            case 'monteore_totali_finale_esteso': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_fine_anno, 'ETICHETTA_ORE_MINUTI'); break;
            case 'ore_assenza_totali_periodo_attuale_ore_minuti': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_sel, 'ORE_MINUTI_ESTESE'); break;
            case 'monteore_totali_periodo_attuale_ore_minuti': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_sel, 'ORE_MINUTI_ESTESE'); break;
            case 'ore_assenza_totali_1qt_ore_minuti': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_1qt, 'ORE_MINUTI_ESTESE'); break;
            case 'monteore_totali_1qt_ore_minuti': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_1qt, 'ORE_MINUTI_ESTESE'); break;
            case 'ore_assenza_totali_2t_ore_minuti': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_2t, 'ORE_MINUTI_ESTESE'); break;
            case 'monteore_totali_2t_ore_minuti': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_2t, 'ORE_MINUTI_ESTESE'); break;
            case 'ore_assenza_totali_finale_ore_minuti': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_assenze_fine_anno, 'ORE_MINUTI_ESTESE'); break;
            case 'monteore_totali_finale_ore_minuti': $array_dati[$variabile] = traduci_minuti_in_ore_minuti($tot_ore_fine_anno, 'ORE_MINUTI_ESTESE'); break;
            case 'ore_assenza_totali_percentuale': $array_dati[$variabile] = round(($tot_assenze_sel/$tot_ore_sel) * 100, 0) . '%'; break;
            case 'ore_assenza_totali_1qt_percentuale': $array_dati[$variabile] = round(($tot_assenze_1qt/$tot_ore_1qt) * 100, 0).'%'; break;
            case 'ore_assenza_totali_2t_percentuale': $array_dati[$variabile] = round(($tot_assenze_2t/$tot_ore_2t) * 100, 0).'%'; break;
            case 'ore_assenza_totali_finale_percentuale': $array_dati[$variabile] = round(($tot_assenze_fine_anno/$tot_ore_fine_anno) * 100, 0).'%'; break;
            case 'attendance_assenze': $array_dati[$variabile] = ($$assenze_classe[$id_studente][1]?$$assenze_classe[$id_studente][1]:0);break;
            case 'attendance_ritardi': $array_dati[$variabile] = ($$assenze_classe[$id_studente][2]?$$assenze_classe[$id_studente][2]:0);break; 
            case 'attendance_uscite': $array_dati[$variabile] = ($$assenze_classe[$id_studente][3]?$$assenze_classe[$id_studente][3]:0);break;
            case 'assenze_totali': $array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_ts, $data_estesa_fine_ts)['assenze_totali'];break;
            case 'entrate_totali':$array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_ts, $data_estesa_fine_ts)['entrate_totali'];break;
            case 'uscite_totali':$array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_ts, $data_estesa_fine_ts)['uscite_totali'];break;
            case 'numero_note_disciplinari_totali': 
                $elenco_note_disciplinari = estrai_note_disciplinari_studente($id_studente, $data_estesa_inizio_ts, $data_estesa_fine_ts);
                $array_dati[$variabile] = count($elenco_note_disciplinari);
                break;
            case 'numero_note_didattiche_totali':
                $elenco_note_didattiche = estrai_annotazioni_studente($id_studente, 'studente', null, null, $data_estesa_inizio_ts, $data_estesa_fine_ts);
                $numero_note_didattiche = 0;
                foreach ($elenco_note_didattiche as $singola_nota) {
                    if($singola_nota['descrizione'] == 'NOTA DIDATTICA') {
                        $numero_note_didattiche++;
                    }
                }
                $array_dati[$variabile] = $numero_note_didattiche;
                break;
            case 'assenze_totali_1qt': $array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_1qt_ts, $data_estesa_fine_1qt_ts)['assenze_totali'];break;
            case 'entrate_totali_1qt':$array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_1qt_ts, $data_estesa_fine_1qt_ts)['entrate_totali'];break;
            case 'uscite_totali_1qt':$array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_1qt_ts, $data_estesa_fine_1qt_ts)['uscite_totali'];break;
            case 'numero_note_disciplinari_totali_1qt': 
                $elenco_note_disciplinari = estrai_note_disciplinari_studente($id_studente, $data_estesa_inizio_1qt_ts, $data_estesa_fine_1qt_ts);
                $array_dati[$variabile] = count($elenco_note_disciplinari);
                break;
            case 'numero_note_didattiche_totali_1qt':
                $elenco_note_didattiche = estrai_annotazioni_studente($id_studente, 'studente', null, null, $data_estesa_inizio_1qt_ts, $data_estesa_fine_1qt_ts);
                $numero_note_didattiche = 0;
                foreach ($elenco_note_didattiche as $singola_nota) {
                    if($singola_nota['descrizione'] == 'NOTA DIDATTICA') {
                        $numero_note_didattiche++;
                    }
                }
                $array_dati[$variabile] = $numero_note_didattiche;
                break;
            case 'assenze_totali_finale': $array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_finale_ts, $data_estesa_fine_finale_ts)['assenze_totali'];break;
            case 'entrate_totali_finale':$array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_finale_ts, $data_estesa_fine_finale_ts)['entrate_totali'];break;
            case 'uscite_totali_finale':$array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_finale_ts, $data_estesa_fine_finale_ts)['uscite_totali'];break;
            case 'numero_note_disciplinari_totali_finale': 
                $elenco_note_disciplinari = estrai_note_disciplinari_studente($id_studente, $data_estesa_inizio_finale_ts, $data_estesa_fine_finale_ts);
                $array_dati[$variabile] = count($elenco_note_disciplinari);
                break;
            case 'numero_note_didattiche_totali_finale':
                $elenco_note_didattiche = estrai_annotazioni_studente($id_studente, 'studente', null, null, $data_estesa_inizio_finale_ts, $data_estesa_fine_finale_ts);
                $numero_note_didattiche = 0;
                foreach ($elenco_note_didattiche as $singola_nota) {
                    if($singola_nota['descrizione'] == 'NOTA DIDATTICA') {
                        $numero_note_didattiche++;
                    }
                }
                $array_dati[$variabile] = $numero_note_didattiche;
                break;
            // in base ad un intervallo di date da parametri stampa
            case 'assenze_totali_intervallo': $array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_intervallo_ts, $data_estesa_fine_intervallo_ts)['assenze_totali'];break;
            case 'entrate_totali_intervallo':$array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_intervallo_ts, $data_estesa_fine_intervallo_ts)['entrate_totali'];break;
            case 'uscite_totali_intervallo':$array_dati[$variabile] = estrai_numero_assenze_studente_periodo((int) $id_studente, $data_estesa_inizio_intervallo_ts, $data_estesa_fine_intervallo_ts)['uscite_totali'];break;
            case 'numero_note_disciplinari_totali_intervallo': 
                $elenco_note_disciplinari = estrai_note_disciplinari_studente($id_studente, $data_estesa_inizio_intervallo_ts, $data_estesa_fine_intervallo_ts);
                $array_dati[$variabile] = count($elenco_note_disciplinari);
                break;
            case 'numero_note_didattiche_totali_intervallo':
                $elenco_note_didattiche = estrai_annotazioni_studente($id_studente, 'studente', null, null, $data_estesa_inizio_intervallo_ts, $data_estesa_fine_intervallo_ts);
                $numero_note_didattiche = 0;
                foreach ($elenco_note_didattiche as $singola_nota) {
                    if($singola_nota['descrizione'] == 'NOTA DIDATTICA') {
                        $numero_note_didattiche++;
                    }
                }
                $array_dati[$variabile] = $numero_note_didattiche;
                break;                

            // crediti
            case 'totale_crediti': $array_dati[$variabile] = $dati_studente['crediti_terza']+$dati_studente['crediti_reintegrati_terza']+$dati_studente['crediti_quarta']+$dati_studente['crediti_reintegrati_quarta']+$dati_studente['crediti_quinta']+$dati_studente['crediti_reintegrati_quinta']+$dati_studente['crediti_finali_agg']; break;
            case 'crediti_anno':
                switch($dati_studente['classe']) {
                    case 3: $array_dati[$variabile] = $dati_studente['crediti_terza']+$dati_studente['crediti_reintegrati_terza']; break;
                    case 4: $array_dati[$variabile] = $dati_studente['crediti_quarta']+$dati_studente['crediti_reintegrati_quarta']; break;
                    case 5: $array_dati[$variabile] = $dati_studente['crediti_quinta']+$dati_studente['crediti_reintegrati_quinta']+$dati_studente['crediti_finali_agg']; break;
                }
                ;break;
            // medie
            case 'media': $array_dati[$variabile] = ($num_mat_sel>0 ? round($somma_voti_sel / $num_mat_sel, 2) : ''); break;
            case 'media_1qt': $array_dati[$variabile] = ($num_mat_1qt>0 ? round($somma_voti_1qt / $num_mat_1qt, 2) : ''); break;
            case 'media_2t': $array_dati[$variabile] = ($num_mat_2t>0 ? round($somma_voti_2t / $num_mat_2t, 2) : ''); break;
            case 'media_finale': $array_dati[$variabile] = ($num_mat_finale>0 ? round($somma_voti_finale / $num_mat_finale, 2) : ''); break;
            case 'media_arrotondato': $array_dati[$variabile] = ($num_mat_sel>0 ? round($somma_voti_sel / $num_mat_sel) : ''); break;
            case 'media_arrotondato_1qt': $array_dati[$variabile] = ($num_mat_1qt>0 ? round($somma_voti_1qt / $num_mat_1qt) : ''); break;
            case 'media_arrotondato_2t': $array_dati[$variabile] = ($num_mat_2t>0 ? round($somma_voti_2t / $num_mat_2t) : ''); break;
            case 'media_arrotondato_finale': $array_dati[$variabile] = ($num_mat_finale>0 ? round($somma_voti_finale / $num_mat_finale) : ''); break;

            // vari
            case 'data_stampa': $array_dati[$variabile] = str_pad($parametri_personalizzati['data_stampa_Day'], 2, '0', STR_PAD_LEFT) . '/'. str_pad($parametri_personalizzati['data_stampa_Month'], 2, '0', STR_PAD_LEFT) . '/'. $parametri_personalizzati['data_stampa_Year']; break;
            case 'data_stampa_1qt': $array_dati[$variabile] = str_pad($parametri_personalizzati['data_stampa_1qt_Day'], 2, '0', STR_PAD_LEFT) . '/'. str_pad($parametri_personalizzati['data_stampa_1qt_Month'], 2, '0', STR_PAD_LEFT) . '/'. $parametri_personalizzati['data_stampa_1qt_Year']; break;
            case 'data_stampa_2t': $array_dati[$variabile] = str_pad($parametri_personalizzati['data_stampa_2t_Day'], 2, '0', STR_PAD_LEFT) . '/'. str_pad($parametri_personalizzati['data_stampa_2t_Month'], 2, '0', STR_PAD_LEFT) . '/'. $parametri_personalizzati['data_stampa_2t_Year']; break;
            case 'data_stampa_finale': $array_dati[$variabile] = str_pad($parametri_personalizzati['data_stampa_finale_Day'], 2, '0', STR_PAD_LEFT) . '/'. str_pad($parametri_personalizzati['data_stampa_finale_Month'], 2, '0', STR_PAD_LEFT) . '/'. $parametri_personalizzati['data_stampa_finale_Year']; break;  
            case 'data_stampa_pagellina1': $array_dati[$variabile] = str_pad($parametri_personalizzati['data_stampa_pagellina1_Day'], 2, '0', STR_PAD_LEFT) . '/'. str_pad($parametri_personalizzati['data_stampa_pagellina1_Month'], 2, '0', STR_PAD_LEFT) . '/'. $parametri_personalizzati['data_stampa_pagellina1_Year']; break;
            case 'data_stampa_pagellina2': $array_dati[$variabile] = str_pad($parametri_personalizzati['data_stampa_pagellina2_Day'], 2, '0', STR_PAD_LEFT) . '/'. str_pad($parametri_personalizzati['data_stampa_pagellina2_Month'], 2, '0', STR_PAD_LEFT) . '/'. $parametri_personalizzati['data_stampa_pagellina2_Year']; break;
            case 'data_stampa_pagellina3': $array_dati[$variabile] = str_pad($parametri_personalizzati['data_stampa_pagellina3_Day'], 2, '0', STR_PAD_LEFT) . '/'. str_pad($parametri_personalizzati['data_stampa_pagellina3_Month'], 2, '0', STR_PAD_LEFT) . '/'. $parametri_personalizzati['data_stampa_pagellina3_Year']; break;
            case 'data_stampa_pagellina4': $array_dati[$variabile] = str_pad($parametri_personalizzati['data_stampa_pagellina4_Day'], 2, '0', STR_PAD_LEFT) . '/'. str_pad($parametri_personalizzati['data_stampa_pagellina4_Month'], 2, '0', STR_PAD_LEFT) . '/'. $parametri_personalizzati['data_stampa_pagellina4_Year']; break;
            case 'data_oggi_estesa':
                $date = DateTime::createFromFormat('d/m/Y', date('d/m/Y'));
                if ($date !== false) {
                    $array_dati[$variabile] = $formatter->format($date);
                }
                break;
            case 'data_stampa_estesa':
                $date = DateTime::createFromFormat('d/m/Y', $parametri_personalizzati['data_stampa_Day'].'/'.$parametri_personalizzati['data_stampa_Month'].'/'.$parametri_personalizzati['data_stampa_Year']);
                if ($date !== false) {
                    $array_dati[$variabile] = $formatter->format($date);
                }
                break;
            case 'data_stampa_1qt_estesa':
                $date = DateTime::createFromFormat('d/m/Y', $parametri_personalizzati['data_stampa_1qt_Day'].'/'.$parametri_personalizzati['data_stampa_1qt_Month'].'/'.$parametri_personalizzati['data_stampa_1qt_Year']);
                if ($date !== false) {
                    $array_dati[$variabile] = $formatter->format($date);
                }
                break;
            case 'data_stampa_2t_estesa': 
                $date = DateTime::createFromFormat('d/m/Y', $parametri_personalizzati['data_stampa_2t_Day'] . '/'. $parametri_personalizzati['data_stampa_2t_Month'] . '/'. $parametri_personalizzati['data_stampa_2t_Year']);
                if ($date !== false) {
                    $array_dati[$variabile] = $formatter->format($date);
                }
                break;
            case 'data_stampa_finale_estesa': 
                $date = DateTime::createFromFormat('d/m/Y', $parametri_personalizzati['data_stampa_finale_Day'] . '/'. $parametri_personalizzati['data_stampa_finale_Month'] . '/'. $parametri_personalizzati['data_stampa_finale_Year']);
                if ($date !== false) {
                    $array_dati[$variabile] = $formatter->format($date);
                }
                break;
            case 'data_stampa_pagellina1_estesa':
                $date = DateTime::createFromFormat('d/m/Y', $parametri_personalizzati['data_stampa_pagellina1_Day'].'/'.$parametri_personalizzati['data_stampa_pagellina1_Month'].'/'.$parametri_personalizzati['data_stampa_pagellina1_Year']);
                if ($date !== false) {
                    $array_dati[$variabile] = $formatter->format($date);
                }
                break;
            case 'data_stampa_pagellina2_estesa':
                $date = DateTime::createFromFormat('d/m/Y', $parametri_personalizzati['data_stampa_pagellina2_Day'].'/'.$parametri_personalizzati['data_stampa_pagellina2_Month'].'/'.$parametri_personalizzati['data_stampa_pagellina2_Year']);
                if ($date !== false) {
                    $array_dati[$variabile] = $formatter->format($date);
                }
                break;
            case 'data_stampa_pagellina3_estesa':
                $date = DateTime::createFromFormat('d/m/Y', $parametri_personalizzati['data_stampa_pagellina3_Day'].'/'.$parametri_personalizzati['data_stampa_pagellina3_Month'].'/'.$parametri_personalizzati['data_stampa_pagellina3_Year']);
                if ($date !== false) {
                    $array_dati[$variabile] = $formatter->format($date);
                }
                break;
            case 'data_stampa_pagellina4_estesa':
                $date = DateTime::createFromFormat('d/m/Y', $parametri_personalizzati['data_stampa_pagellina4_Day'].'/'.$parametri_personalizzati['data_stampa_pagellina4_Month'].'/'.$parametri_personalizzati['data_stampa_pagellina4_Year']);
                if ($date !== false) {
                    $array_dati[$variabile] = $formatter->format($date);
                }
                break;
            case 'intestazione_classe_testo':
                if(intval($dati_classe["id_sede"]) > 0) {
                    $array_dati[$variabile] .= $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"] . chr(13) . chr(10);
                    $array_dati[$variabile] .= $dati_sede["indirizzo"] . " -- " . $dati_sede["descrizione_comune"] . " -- " . $dati_sede["telefono"] . chr(13) . chr(10);
                }
                break;
            case 'data_stampa_lettere':
                $array_dati[$variabile] = traduci_numero_in_lettere($parametri_personalizzati['data_stampa_Day']). ' '. 
                    traduci_mese_in_lettere($parametri_personalizzati['data_stampa_Month'], 'esteso'). ' '.
                    traduci_numero_in_lettere($parametri_personalizzati['data_stampa_Year']);
                break;
            case 'data_stampa_finale_lettere':
                $array_dati[$variabile] = traduci_numero_in_lettere($parametri_personalizzati['data_stampa_finale_Day']). ' '. 
                    traduci_mese_in_lettere($parametri_personalizzati['data_stampa_finale_Month'], 'esteso'). ' '.
                    traduci_numero_in_lettere($parametri_personalizzati['data_stampa_finale_Year']);
                break;
            case 'intestazione_classe_immagine':
                $logo = estrai_parametri_singoli('LOGO_NOME', $id_classe, 'classe');
                if($logo != '' && file_exists("/var/www-source/mastercom/immagini_scuola/" . $logo))
                {
                    $dati_logo_img = [];

                    $dati_logo_img['path'] = "/var/www-source/mastercom/immagini_scuola/" . $logo;
                    $dati_logo_img['ratio'] = false;
                    // prendo la larghezza e altezza in base all'immagine
                    list($dati_logo_img['width'], $dati_logo_img['height']) = getimagesize("/var/www-source/mastercom/immagini_scuola/" . $logo);
                    // // se l'altezza del logo è impostata per la classe e l'immagine allora la uso
                    // $altezza_logo = estrai_parametri_singoli("LOGO_ALTEZZA", $id_classe, "classe");
                    $dati_logo_img['height'] = $altezza_logo > 0 ? $altezza_logo : $dati_logo_img['height'];

                    $array_dati[$variabile] = $dati_logo_img;
                } else {
                    $array_dati[$variabile] = '';
                }
                break;
            case 'intestazione_classe':
                $logo = estrai_parametri_singoli('LOGO_NOME', $id_classe, 'classe');
                if($logo != '' && file_exists("/var/www-source/mastercom/immagini_scuola/" . $logo)) { // immagine
                    $dati_logo_img = [];

                    $dati_logo_img['path'] = "/var/www-source/mastercom/immagini_scuola/" . $logo;
                    $dati_logo_img['ratio'] = false;
                    // prendo la larghezza e altezza in base all'immagine
                    list($dati_logo_img['width'], $dati_logo_img['height']) = getimagesize("/var/www-source/mastercom/immagini_scuola/" . $logo);
                    // // se l'altezza del logo è impostata per la classe e l'immagine allora la uso
                    // $altezza_logo = estrai_parametri_singoli("LOGO_ALTEZZA", $id_classe, "classe");
                    $dati_logo_img['height'] = $altezza_logo > 0 ? $altezza_logo : $dati_logo_img['height'];

                    $array_dati[$variabile] = $dati_logo_img;
                } elseif(intval($dati_classe["id_sede"]) > 0) { // testuale
                    $array_dati[$variabile] .= $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"] . chr(13) . chr(10);
                    $array_dati[$variabile] .= $dati_sede["indirizzo"] . " -- " . $dati_sede["descrizione_comune"] . " -- " . $dati_sede["telefono"] . chr(13) . chr(10);
                } else {
                    $array_dati[$variabile] = '';
                }
                break;            

            default:
                // variabile in tabella studenti
                if ( isset($dati_studente[$variabile]) ) {
                    $array_dati[$variabile] = $dati_studente[$variabile];
                }
                // ricerca se è in parametri personalizzati (dopo aver tolto/elaborato quelli speciali)
                elseif (is_array($parametri_personalizzati) && array_key_exists($variabile,$parametri_personalizzati)) {
                    $array_dati[$variabile] = $parametri_personalizzati[$variabile];
                }
                break;
        }
    }
    $docenti_classe = estrai_elenco_professori_multi_classe($id_classe);
    $lista_docenti = [];
    foreach ($docenti_classe as $key => $value) {
        $lista_docenti[]['docente'] = $value['cognome'] . ' ' . $value['nome'];
    }
    $lista_docenti = ordina_array($lista_docenti, 'docente');
    $array_dati['elenco_docenti'] = $lista_docenti;
    //}}} </editor-fold>
//file_put_contents("/tmp/periodi_competenze_tmp.txt", print_r($periodi_competenze_tmp, true));
//file_put_contents("/tmp/debug_vartmpt.txt", print_r($variabili_template, true));
//file_put_contents("/tmp/debug_$id_studente.txt", print_r($array_dati, true));
//file_put_contents("/tmp/debug_periodi_tmp.txt", print_r($periodi_tmp, true));
//file_put_contents("/tmp/debug_tab_scrutini_competenze_$id_studente.txt", print_r($tab_scrutini_competenze, true));
    if ($motivo == 'struttura_dati') { // stampa a video della struttura da traslare
        echo '<pre>', print_r( $array_dati);
        exit();
    }
    //}}} </editor-fold>

    // per permettere l associazione file-studente ho bisogno in fase futura 
    if ($destinazione_stampa == 'CODA_STAMPA'){
        $array_dati['chiave'] = 'id_studente';
        $array_dati['id_studente'] = $studente['id_studente'];
        $array_dati['tipo'] = $pagella_word['tipo'];
    }
    // aggiunge l'array, con le associazioni parametro-valore da traslare, ai parametri passati alla chiamata api
    $parametri['array_dati'] = $array_dati;
    // traslare campi in valori per lo studente nel modello word
    $filePath = nextapi_call('phpoffice/phpword/translateTemplateVariables/' . $id_template_pagella_word, 'POST', $parametri, $current_key);
    if ($destinazione_stampa == 'ZIP') { 
        // se si sta creando uno zip per la firma digitale cambio il nome del file
        if (file_exists($filePath['href'])) {
            $pathinfo = pathinfo($filePath['href']);
            $new_filename = $studente['id_studente'] 
                . '_' . $studente['codice_fiscale'] 
                . '_' . str_replace([' ', '(', ')'], '_', $pagella_word['nome']) 
                . '_' . $periodo_pagella
                . '_' . $anno_inizio . $anno_fine 
                . '_' . date('Ymd') . '.' . $pathinfo['extension'];
            $new_path = $pathinfo['dirname'] . '/' . $new_filename;
            rename($filePath['href'], $new_path);
            $filePath['href'] = $new_path;
            $filePath['nome_file'] = $new_filename;
        }
    }
    $file_sito = [
        'id_studente' => $studente['id_studente'],
        'cognome' => $studente['cognome'],
        'nome' => $studente['nome'],
        'registro' => $studente['registro'],
        'file'  => $filePath
    ];

    if ($destinazione_stampa == 'ZIP_TEST') {
        $file_nome_strutt = "{$studente['id_studente']}_struttura_dati.txt";
        file_put_contents($dir.$file_nome_strutt, print_r($array_dati, true));
        $file_sito['file_struttura'] = $dir.$file_nome_strutt;
    }

    // assegno i dati necessari negli array per il controllo sulla destinazione
    $array_file_stampa[] = $filePath;
    $array_per_sito[] = $file_sito;

    if (isset($parametri_personalizzati['quanti_studenti_stampare']) &&
            $parametri_personalizzati['quanti_studenti_stampare'] > 0 && count($array_file_stampa) == $parametri_personalizzati['quanti_studenti_stampare']){
        break;
    }
}
//}}} </editor-fold>

//{{{ <editor-fold defaultstate="collapsed" desc="destinazione modello">
switch ($destinazione_stampa){
    case 'CODA_STAMPA':
        // salva lo zip
        $nome_zip = $nome . substr(md5(rand(0, 10000)), 0, 12) . '.zip';
		$nome_zip = str_replace(' ', '_', $nome_zip);
		$nome_zip = str_replace('(', '_', $nome_zip);
		$nome_zip = str_replace(')', '_', $nome_zip);
        $ext = 'pdf';

        // zip i file
        exec('zip -j \'' . $base_dir . $nome_zip . '\' ' . $dir . '*' . "." . $ext);
        // elimina la cartella temporanea
        exec('rm -fr ' . $dir);

        // controllo se lo zip è stato creato e contiene files
        if (file_exists("{$base_dir}{$nome_zip}")) {
            $zip_dim = filesize($base_dir . $nome_zip);
            if ($zip_dim > 0) {
                $upload_id = 'file_upload';
                $_FILES[$upload_id] = [
                    "name" => $nome_zip,
                    "type" => "application/zip",
                    "tmp_name" => $base_dir . $nome_zip,
                    "error" => 0,
                    "size" => filesize($base_dir . $nome_zip)
                ];
                
                $result = [
                    "status" => true,
                    "upload_id" => $upload_id,
                    "message" => "Zip temporaneo creato con successo.",
                ];
                file_put_contents('/tmp/dbg_coda_stampa.txt', "zip temporaneo pronto e con file all'interno. _FILES: " . print_r($_FILES[$upload_id], true) . "\n", FILE_APPEND);
            } else {
                $result = [
                    "status" => false,
                    "message" => "Il file zip creato è vuoto.",
                    "upload_id" => null
                ];
                unlink("{$base_dir}{$nome_zip}");
            }
        } else {
            $result = [
                "status" => false,
                "message" => "Il file zip non è stato creato.",
                "upload_id" => null
            ];
        }

        echo json_encode($result);
        break;
    case 'ZIP':
        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        // zip per firma digitale
        $nome .= substr(md5(rand(0, 10000)), 0, 12).'.zip';
		$nome = str_replace(' ', '_', $nome);
		$nome = str_replace('(', '_', $nome);
		$nome = str_replace(')', '_', $nome);
        $ext = 'pdf';
        exec('zip -j \'' . $base_dir . $nome . '\' ' . $dir . '*' . "." . $ext);
        exec('rm -fr ' . $dir);

        $download_path = $rel_dir . $nome;

        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='{$download_path}';</SCRIPT></HTML>";
        break;
    case 'ZIP_WORD':
        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        $nome .= substr(md5(rand(0, 10000)), 0, 12).'.zip';
        $ext = $pagella_word['estensione'];
        exec('zip -j \'' . $base_dir . $nome . '\' ' . $dir . '*' . "." . $ext);
        exec('rm -fr ' . $dir);

        $download_path = $rel_dir . $nome;

        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='{$download_path}';</SCRIPT></HTML>";
        break;
    case 'ZIP_TEST':
        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        // remove zip files in path $rel_dir
        array_map('unlink', glob("$rel_dir*.zip"));

        // create zip file with all files to download it
        $nome_zip .= 'pagelle_strutture_dati_'. date('Y-m-d_H-i_s') .'.zip';
        $sub_dir = 'sub'.substr(md5(rand(0, 1000000)), 0, 12).'/';

        foreach ($array_per_sito as $studente_pagella) {
            // crea cartella nominato studente_pagella['id_studente'] studente_pagella['cognome'] studente_pagella['nome']
            $student_dir = $dir . $sub_dir . $studente_pagella['id_studente'] . '_' . $studente_pagella['cognome'] . '_' . $studente_pagella['nome'];
            mkdir($student_dir, 0777, true);
            // insert file on path $studente_pagella['file']['href'] inside folder $student_dir
            copy($studente_pagella['file']['href'], $student_dir . '/' . basename($studente_pagella['file']['href']));
            // insert txt file inside folder
            copy($studente_pagella['file_struttura'], $student_dir . '/' . 'struttura_dati.txt');
        }
        // zip
        exec("cd {$dir}{$sub_dir} && zip -r '../../../{$rel_dir}{$nome_zip}' .");

        // Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='{$rel_dir}{$nome_zip}';</SCRIPT></HTML>";

        // sleep(10);
        // unlink($rel_dir . $nome_zip);
        // // cancellare la cartella e tutti gli elementi all'interno della cartella $dir
        // exec('rm -rf ' . escapeshellarg($dir));
        break;
    case 'PDF':
    default:
        $nome .= '.pdf';
        $outputName = $dir . $nome;

        $cmd = "gs -q -dNOPAUSE -dBATCH -sDEVICE=pdfwrite -sOutputFile='$outputName' ";

        foreach ($array_file_stampa as $dati_file) {
            $cmd .= $dati_file['percorso_assoluto'] . $dati_file['nome_file'] . " ";
        }

        $result = shell_exec($cmd);
        $download_path = $rel_dir . $rand_dir . '/' . $nome;

        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='{$download_path}';</SCRIPT></HTML>";
        break;
}

unset($formatter);
//}}} </editor-fold>