<?php

$x_base = $pdf->GetX();

if($valle_aosta_abilitata == 'SI')
{
	//{{{ <editor-fold defaultstate="collapsed" desc="pagella pagella bilingue per Val d'Aosta">
	$pdf->SetX($x_base);
	$pdf->SetFont('Times', 'B', 10);
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['cognome'], 'LT', 0, 'C');
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['nome'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_studenti[$cont]['codice_fiscale'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_sede["codice_meccanografico"], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $anno_scolastico_attuale, 'RT', 1, 'C');

	$pdf->SetX($x_base);
	$pdf->SetFont('Times', '', 6);
	$pdf->CellFitScale(50, 3, 'COGNOME / NOM', 'L', 0, 'C');
	$pdf->CellFitScale(50, 3, 'NOME / PRÉNOM', 0, 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODICE FISCALE', 0, 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODICE ISTITUTO', 0, 0, 'C');
	$pdf->CellFitScale(30, 3, 'ANNO SCOLASTICO', 'R', 1, 'C');
	$pdf->SetX($x_base);
	$pdf->CellFitScale(50, 3, '', 'LB', 0, 'C');
	$pdf->CellFitScale(50, 3, '', 'B', 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODE FISCAL', 'B', 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODE DE L’ÉTABLISSEMENT', 'B', 0, 'C');
	$pdf->CellFitScale(30, 3, 'ANNÉE SCOLAIRE', 'RB', 1, 'C');
	$pdf->ln(10);

	$pdf->SetFont('Times', 'B', 12);
	$y_esami = $pdf->GetY();
	$pdf->SetX($x_base);
	$pdf->CellFitScale(80, 7, '', 'TRL', 0, 'C');
	$pdf->CellFitScale(50, 7, 'SCRUTINIO FINALE', 'TRL', 1, 'C');
	$pdf->SetX($x_base);
	$pdf->CellFitScale(80, 7, 'DISCIPLINE', 'RL', 0, 'C');
	$pdf->CellFitScale(50, 7, 'MOYENNE DE FIN D’ANNÉE', 'BRL', 1, 'C');
	$pdf->SetX($x_base);
	$pdf->CellFitScale(80, 7, 'DISCIPLINES', 'RL', 0, 'C');
	$pdf->SetFont('Times', '', 10);
	$pdf->CellFitScale(25, 7, 'VOTO UNICO', 'TRL', 0, 'C');
	$pdf->CellFitScale(25, 7, 'Totale ore', 'TRL', 1, 'C');
	$pdf->SetX($x_base);
	$pdf->CellFitScale(80, 7, '', 'BRL', 0, 'C');
	$pdf->CellFitScale(25, 7, '(in lettere)', 'RLB', 0, 'C');
	$pdf->CellFitScale(25, 7, 'assenza', 'RLB', 1, 'C');

	foreach($array_finale_voti as $riga)
	{
		if($riga['tipo_materia'] != "CONDOTTA"
//            &&
//            (
//                $riga[1] != '' ||
//                $riga[2] != '' ||
//                $riga[3] != '' ||
//                $riga[4] != ''
//            )
        )
		{
			//{{{ <editor-fold defaultstate="collapsed" desc="stampa riga materia normale">
			if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'SECONDO')
			{
				$pdf->SetX($x_base);
				$pdf->CellFitScale(80, 5, $riga[0], 'RLT',0, 'L');
				$pdf->CellFitScale(25, 5, $riga[6], 'RLT', 0, 'C');
				$pdf->CellFitScale(25, 5, stampa_ore_o_minuti($riga[7]), 'RLT',1, 'C');
				$pdf->SetX($x_base);
				$pdf->CellFitScale(80, 5, $riga['descrizione_materia_straniera'], 'RLB',0, 'L');
				$pdf->CellFitScale(25, 5, $riga['valore_pagella_lingua_voto_unico_fine_anno'], 'RLB', 0, 'C');
				$pdf->CellFitScale(25, 5, '', 'RLB',1, 'C');
			}
			else
			{
				$pdf->SetX($x_base);
				$pdf->CellFitScale(80, 5, '', 'RLT',0, 'L');
				$pdf->CellFitScale(25, 5, '', 'RLT', 0, 'C');
				$pdf->CellFitScale(25, 5, '', 'RLT',1, 'C');
				$pdf->SetX($x_base);
				$pdf->CellFitScale(80, 5, '', 'RLB',0, 'L');
				$pdf->CellFitScale(25, 5, '', 'RLB', 0, 'C');
				$pdf->CellFitScale(25, 5, '', 'RLB',1, 'C');
			}
			/*$pdf->MultiCell(80, 10, $riga[0] . "\n" . $riga['descrizione_materia_straniera'], 1, 'L', 0, 0);
			$pdf->MultiCell(25, 10, $riga[6] . "\n" . $riga['valore_pagella_lingua_voto_unico_fine_anno'], 1, 'L', 0, 0);
			$pdf->CellFitScale(25, 10, stampa_ore_o_minuti($riga[7]), 1, 1, 'C');*/
			//}}} </editor-fold>
		}
		else
		{
			$riga_condotta = $riga;
		}
	}

	//{{{ <editor-fold defaultstate="collapsed" desc="stampa riga condotta">
	if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'SECONDO')
	{
		$pdf->SetX($x_base);
		$pdf->MultiCell(80, 10, "COMPORTAMENTO\nCONDUITE", 1, 'L', 0, 0);
		$pdf->MultiCell(25, 10, $riga_condotta[6] . "\n" . $riga_condotta['valore_pagella_lingua_voto_unico_fine_anno'], 1, 'C', 0, 0);
		$pdf->CellFitScale(25, 10, '', 1, 1, 'C');
	}
	else
	{
		$pdf->SetX($x_base);
		$pdf->MultiCell(80, 10, "", 1, 'L', 0, 0);
		$pdf->MultiCell(25, 10, '', 1, 'C', 0, 0);
		$pdf->CellFitScale(25, 10, '', 1, 1, 'C');
	}
	//}}} </editor-fold>

	$y_esami_fine = $pdf->GetY();
	$x_base_esami = $pdf->GetX() + 120;

	if($voto_esame_terza == 'ZERO')
	{
		$voto_esame_terza = '';
	}
	//{{{ <editor-fold defaultstate="collapsed" desc="traduco il voto in francese">
	switch($dati_studenti[$cont]["voto_qualifica"])
	{
		case "60":
			$voto_esame_terza_fra = "SOIXANTE";
			break;
		case "61":
			$voto_esame_terza_fra = "SOIXANTE-ET-UN";
			break;
		case "62":
			$voto_esame_terza_fra = "SOIXANTE-DEUX";
			break;
		case "63":
			$voto_esame_terza_fra = "SOIXANTE-TROIS";
			break;
		case "64":
			$voto_esame_terza_fra = "SOIXANTE-QUATRE";
			break;
		case "65":
			$voto_esame_terza_fra = "SOIXANTE-CINQ";
			break;
		case "66":
			$voto_esame_terza_fra = "SOIXANTE-SIX";
			break;
		case "67":
			$voto_esame_terza_fra = "SOIXANTE-SEPT";
			break;
		case "68":
			$voto_esame_terza_fra = "SOIXANTE-HUIT";
			break;
		case "69":
			$voto_esame_terza_fra = "SOIXANTE-NEUF";
			break;
		case "70":
			$voto_esame_terza_fra = "SOIXANTE-DIX";
			break;
		case "71":
			$voto_esame_terza_fra = "SOIXANTE-ET-ONZE";
			break;
		case "72":
			$voto_esame_terza_fra = "SOIXANTE-DOUZE";
			break;
		case "73":
			$voto_esame_terza_fra = "SOIXANTE-TREIZE";
			break;
		case "74":
			$voto_esame_terza_fra = "SOIXANTE-QUATORZE";
			break;
		case "75":
			$voto_esame_terza_fra = "SOIXANTE-QUINZE";
			break;
		case "76":
			$voto_esame_terza_fra = "SOIXANTE-QUINZE";
			break;
		case "77":
			$voto_esame_terza_fra = "SOIXANTE-DIX-SEPT";
			break;
		case "78":
			$voto_esame_terza_fra = "SOIXANTE-DIX-HUIT";
			break;
		case "79":
			$voto_esame_terza_fra = "SOIXANTE-DIX-NEUF";
			break;
		case "80":
			$voto_esame_terza_fra = "QUATRE-VINGTS";
			break;
		case "81":
			$voto_esame_terza_fra = "QUATRE-VINGT-UN";
			break;
		case "82":
			$voto_esame_terza_fra = "QUATRE-VINGT-DEUX";
			break;
		case "83":
			$voto_esame_terza_fra = "QUATRE-VINGT-TROIS";
			break;
		case "84":
			$voto_esame_terza_fra = "QUATRE-VINGT-QUATRE";
			break;
		case "85":
			$voto_esame_terza_fra = "QUATRE-VINGT-CINQ";
			break;
		case "86":
			$voto_esame_terza_fra = "QUATRE-VINGT-SIX";
			break;
		case "87":
			$voto_esame_terza_fra = "QUATRE-VINGT-SEPT";
			break;
		case "88":
			$voto_esame_terza_fra = "QUATRE-VINGT-HUIT";
			break;
		case "89":
			$voto_esame_terza_fra = "QUATRE-VINGT-NEUF";
			break;
		case "90":
			$voto_esame_terza_fra = "QUATRE-VINGT-DIX";
			break;
		case "91":
			$voto_esame_terza_fra = "QUATRE-VINGT-ONZE";
			break;
		case "92":
			$voto_esame_terza_fra = "QUATRE-VINGT-DOUZE";
			break;
		case "93":
			$voto_esame_terza_fra = "QUATRE-VINGT-TREIZE";
			break;
		case "94":
			$voto_esame_terza_fra = "QUATRE-VINGT-QUATORZE";
			break;
		case "95":
			$voto_esame_terza_fra = "QUATRE-VINGT-QUINZE";
			break;
		case "96":
			$voto_esame_terza_fra = "QUATRE-VINGT-SEIZE";
			break;
		case "97":
			$voto_esame_terza_fra = "QUATRE-VINGT-DIX-SEPT";
			break;
		case "98":
			$voto_esame_terza_fra = "QUATRE-VINGT-DIX-HUIT";
			break;
		case "99":
			$voto_esame_terza_fra = "QUATRE-VINGT-DIX-NEUF";
			break;
		case "100":
			$voto_esame_terza_fra = "CENT";
			break;
		default:
			$voto_esame_terza_fra = "";
			break;
	}
	//}}} </editor-fold>

	if($crediti_lettere == 'zero')
	{
		$crediti_lettere = '';
	}
	//{{{ <editor-fold defaultstate="collapsed" desc="traduco i crediti in francese">
	switch($crediti_lettere)
	{
		case "uno":
			$crediti_lettere_fra = "un";
			break;
		case "due":
			$crediti_lettere_fra = "deux";
			break;
		case "tre":
			$crediti_lettere_fra = "trois";
			break;
		case "quattro":
			$crediti_lettere_fra = "quatre";
			break;
		case "cinque":
			$crediti_lettere_fra = "cinq";
			break;
		case "sei":
			$crediti_lettere_fra = "six";
			break;
		case "sette":
			$crediti_lettere_fra = "sept";
			break;
		case "otto":
			$crediti_lettere_fra = "huit";
			break;
		case "nove":
			$crediti_lettere_fra = "neuf";
			break;
		case "dieci":
			$crediti_lettere_fra = "dix";
			break;
		case "undici":
			$crediti_lettere_fra = "onze";
			break;
		case "dodici":
			$crediti_lettere_fra = "douze";
			break;
		case "tredici":
			$crediti_lettere_fra = "treize";
			break;
		case "quattordici":
			$crediti_lettere_fra = "quatorze";
			break;
		case "quindici":
			$crediti_lettere_fra = "quinze";
			break;
		case "sedici":
			$crediti_lettere_fra = "seize";
			break;
		case "diciassette":
			$crediti_lettere_fra = "dix-sept";
			break;
		case "diciotto":
			$crediti_lettere_fra = "dix-huit";
			break;
		case "diciannove":
			$crediti_lettere_fra = "dix-neuf";
			break;
		case "venti":
			$crediti_lettere_fra = "vingt";
			break;
		case "ventuno":
			$crediti_lettere_fra = "vingt-et-un";
			break;
		case "ventidue":
			$crediti_lettere_fra = "vingt-deux";
			break;
		case "ventitre":
			$crediti_lettere_fra = "vingt-trois";
			break;
		case "ventiquattro":
			$crediti_lettere_fra = "vingt à quatre";
			break;
		case "venticinque":
			$crediti_lettere_fra = "vingt à cinq";
			break;
		default:
			$crediti_lettere_fra = "";
			break;
	}

	//}}} </editor-fold>

	//{{{ <editor-fold defaultstate="collapsed" desc="parte destra della tabella (dati esami e credito)">
	$pdf->SetXY($x_base + $x_base_esami, $y_esami);
	$pdf->SetFont('Times', 'B', 12);
	$pdf->MultiCell(0, 14, "ESAMI\nEXAMENS", 1, 'C');
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->SetFont('Times', '', 12);

	if($stampa_note == 'SI')
	{
		$pdf->MultiCell(0, 14, "VOTO UNICO (in lettere) (6)\nNOTE UNIQUE (en lettres) (6)", 1, 'C');
	}
	else
	{
		$pdf->MultiCell(0, 14, "VOTO UNICO (in lettere)\nNOTE UNIQUE (en lettres)", 1, 'C');
	}

	$pdf->SetX($x_base + $x_base_esami);
	$pdf->MultiCell(0, 20, $voto_esame_terza . "\n" . $voto_esame_terza_fra, 1, 'C');
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->MultiCell(0, 14, "CREDITO SCOLASTICO\nCRÉDIT SCOLAIRE", 1, 'C');
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->SetFont('Times', '', 8);
	$pdf->MultiCell(0, 14, "Media dei voti conseguiti nello\nscrutinio finale:\nMoyenne des points attribués lors du\ndernier conseil de classe:", 'LRT', 'C');
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->CellFitScale(0, 20, $media_voti, 'LR', 1, 'C');
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->MultiCell(0, 14, "Credito scolastico attribuito\nnell’anno scolastico in corso:\nCrédit attribué au titre de l’année\nscolaire en cours:", 'LR', 'C');
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->CellFitScale(0, 7, $crediti_lettere, 'LR', 1, 'C');
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->CellFitScale(0, 7, $crediti_lettere_fra, 'LRB', 1, 'C');

	$pdf->SetX($x_base + $x_base_esami);
	if(
		(
			intval($dati_classe[2]) == 5
			&& $dati_indirizzo["tipo_indirizzo"] != '1'
			&& $dati_indirizzo["tipo_indirizzo"] != '6'
		)
		||
		(
			intval($dati_classe[2]) == 3
			&& $dati_indirizzo["tipo_indirizzo"] == '1'
		)
		||
		(
			intval($dati_classe[2]) == 4
			&& $dati_indirizzo["tipo_indirizzo"] == '5'
		)
		||
		(
			intval($dati_classe[2]) == 3
			&& ($dati_indirizzo["tipo_indirizzo"] == '2' || $dati_indirizzo["tipo_indirizzo"] == '3')
		)
	)
	{
		if($stampa_note == 'SI')
		{
			$pdf->CellFitScale(0, $y_esami_fine - $pdf->GetY(), $stato_promozione . ' (5)', 1, 1, 'C');
		}
		else
		{
			$pdf->CellFitScale(0, $y_esami_fine - $pdf->GetY(), $stato_promozione, 1, 1, 'C');
		}
	}
	else
	{
		if($stampa_note == 'SI')
		{
			$pdf->CellFitScale(0, $y_esami_fine - $pdf->GetY(), '(5)', 1, 1, 'C');
		}
		else
		{
			$pdf->CellFitScale(0, $y_esami_fine - $pdf->GetY(), '', 1, 1, 'C');
		}
	}
	//}}} </editor-fold>

	$pdf->ln(5);
	$pdf->SetX($x_base);
	$pdf->SetFont('Times', 'B', 12);

	if($stampa_note == 'SI')
	{
		$pdf->CellFitScale($larghezza_max_cella, 5, 'ANNOTAZIONI (4) / ANNOTATIONS (4)', 1, 1, 'C');
	}
	else
	{
		$pdf->CellFitScale($larghezza_max_cella, 5, 'ANNOTAZIONI / ANNOTATIONS', 1, 1, 'C');
	}

	$pdf->SetFont('Times', '', 8);

	if($stampa_commento_annotazioni == 'SI')
	{
		$testo_annotazioni = $commento_esito;
	}
	else
	{
		$testo_annotazioni = '';
	}

	if(($stampa_debiti_fine_anno != 'NO') && (strlen($elenco_materie_sospese) > 0))
	{
		if(strlen($commento_esito) > 0)
		{
			$testo_annotazioni .= ' in:';
		}

		$testo_annotazioni .= $elenco_materie_sospese . chr(13) . chr(10);
	}
	else
	{
		$testo_annotazioni .=  chr(13) . chr(10);
	}

	//if($stampa_carenze_fine_anno != 'NO' && strlen($elenco_materie_carenze) > 0)
	if($stampa_carenze_fine_anno != 'NO' && strlen($materie_carenze) > 0)
	{
        $elenco_carenze = '';
        foreach ($materie_carenze as $value) {
            $elenco_carenze .= chr(13) . chr(10) . $value . " ";
        }

		$testo_annotazioni .= $testo_finale['consolidamento'] . $elenco_carenze . chr(13) . chr(10);
	}

	if($dati_studenti[$cont]['pei'] == 'SI' && $stampa_pei == 'SI')
	{
		$testo_annotazioni .= $commento_pei . chr(13) . chr(10);
	}

	$pdf->SetFont('Times', '', 10);
	$pdf->SetX($x_base);
	$pdf->MultiCell($larghezza_max_cella, 5, $testo_annotazioni, 1, 'L');


    $y_rel = $pdf->GetY();
    $pdf->SetXY($x_base, $y_rel);

    if ($data_luogo_pagina_3 == 'SI') {
        $y_rel = $pdf->GetY() + 10;
        $pdf->SetXY($x_base, $y_rel);
        $pdf->SetFont('Times', '', 10);
        $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella, $citta . ', ' . $data_stampa_attuale, 0, 1, 'L');
        $y_rel = $pdf->GetY();
        $pdf->SetXY($x_base, $y_rel);
        $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'LUOGO E DATA/LIEU ET DATE', 0, 0, 'L');
    }

    $pdf->SetFont('Times', '', 6);
    if($firma_pagina_3 == 'SI')
	{
        $y_rel = $pdf->GetY();
        $pdf->SetXY($x_rel, $y_rel);
        $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella, '______________________________', 0, 0, 'C');

        $y_rel = $y_rel + 5;
        $pdf->SetXY($x_base, $y_rel);
        $pdf->SetXY($x_rel, $y_rel);

        if($stampa_note == 'SI')
        {
            $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, $definizione_dirigente .' (3)', 0, 1, 'C');
            $pdf->SetX($x_rel);
            $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'LE DIRECTEUR GÉNÉRAL (3)', 0, 1, 'C');
        }
        else
        {

            $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, $definizione_dirigente, 0, 1, 'C');
            $pdf->SetX($x_rel);
            $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'LE DIRECTEUR GÉNÉRAL', 0, 1, 'C');
        }
        $pdf->SetX($x_rel);
        $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, $dirigente_scolastico, 0, 1, 'C');

        if ($stampa_firma_digitale == 'SI') {
            $pdf->SetX($x_rel);
            $pdf->CellFitScale($larghezza_max_cella/2 - 40, 0, "(Documento firmato digitalmente)", 0, 1, 'C');
        }
    }
    else
    {
        $y_rel = $pdf->GetY();
        $pdf->SetY($y_rel + 5 + $altezza_cella*2);
    }

	$y_rel = $pdf->GetY() - 5;
	$pdf->SetXY($x_base, $y_rel);
	$pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella, '_________________________________________________________________', 0, 1, 'L');
	$pdf->SetX($x_base);
	$pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'IL /I GENITORE /I O CHI NE FA LE VECI', 0, 1, 'L');
	$pdf->SetX($x_base);
	$pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'LE / LES PARENT/S OU LA PERSONNE QUI EXERCE L’AUTORITÉ PARENTALE', 0, 1, 'L');
	//}}} </editor-fold>
}
else
{
	//{{{ <editor-fold defaultstate="collapsed" desc="pagella ministeriale">
	$pdf->SetX($x_base);
	$pdf->SetFont('Times', 'B', 10);
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['cognome'], 'LT', 0, 'C');
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['nome'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_studenti[$cont]['codice_fiscale'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_sede["codice_meccanografico"], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $anno_scolastico_attuale, 'RT', 1, 'C');

	$pdf->SetX($x_base);
	$pdf->SetFont('Times', '', 6);
    $pdf->CellFitScale(50, 4, $testo_finale['cognome'], 'LB', 0, 'C');
    $pdf->CellFitScale(50, 4, $testo_finale['nome'], 'B', 0, 'C');
    $pdf->CellFitScale(30, 4, $testo_finale['codice_fiscale'], 'B', 0, 'C');
    $pdf->CellFitScale(30, 4, $testo_finale['codice_istituto'], 'B', 0, 'C');
    $pdf->CellFitScale(30, 4, $testo_finale['anno_scolastico'], 'RB', 1, 'C');
	$pdf->ln(10);

	$pdf->SetFont('Times', 'B', 14);
	$y_esami = $pdf->GetY();
	$pdf->SetX($x_base);
	$pdf->CellFitScale(85, 7, '', 'TRL', 0, 'C');
	$pdf->CellFitScale(45, 7, $testo_finale['scrutinio'], 'TRL', 1, 'C');
	$pdf->SetX($x_base);
	$pdf->CellFitScale(85, 7, $testo_finale['discipline'], 'RL', 0, 'C');
	$pdf->CellFitScale(45, 7, $testo_finale['finale'], 'BRL', 1, 'C');
	$pdf->SetX($x_base);
	$pdf->CellFitScale(85, 7, '', 'RL', 0, 'C');
	$pdf->SetFont('Times', '', 12);
	$pdf->CellFitScale(25, 7, $testo_finale['voto_unico'], 'TRL', 0, 'C');
	$pdf->CellFitScale(20, 7, $testo_finale['ore_assenza'], 'TRL', 1, 'C');
	$pdf->SetX($x_base);
	$pdf->CellFitScale(85, 7, '', 'BRL', 0, 'C');
	$pdf->SetFont('Times', '', 12);
	$pdf->CellFitScale(25, 7, $testo_finale['in_lettere'], 'RLB', 0, 'C');
	$pdf->CellFitScale(20, 7, '', 'RLB', 1, 'C');

	foreach($array_finale_voti as $riga)
	{
		if($riga['tipo_materia'] != "CONDOTTA")
		{
			//{{{ <editor-fold defaultstate="collapsed" desc="stampa riga materia normale">
			if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'SECONDO')
			{
				$pdf->SetX($x_base);
				$pdf->CellFitScale(85, 10, $riga[$index_materia], 1, 0, 'C');
				$pdf->CellFitScale(25, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[6]):$riga[6], 1, 0, 'C');
				$pdf->CellFitScale(20, 10, stampa_ore_o_minuti($riga[7]), 1, 1, 'C');
			}
			else
			{
				$pdf->SetX($x_base);
				$pdf->CellFitScale(85, 10, '', 1, 0, 'C');
				$pdf->CellFitScale(25, 10, '', 1, 0, 'C');
				$pdf->CellFitScale(20, 10, '', 1, 1, 'C');
			}
			//}}} </editor-fold>
		}
		else
		{
			$riga_condotta = $riga;
		}
	}

	//{{{ <editor-fold defaultstate="collapsed" desc="stampa riga condotta">
	$pdf->SetX($x_base);

	if($trentino_abilitato == 'SI')
	{
		$pdf->CellFitScale(85, 10, $testo_finale['capacita_relazionale'], 1, 0, 'C');
	}
	else
	{
		$pdf->CellFitScale(85, 10, $testo_finale['condotta'], 1, 0, 'C');
	}

	if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'SECONDO')
	{
		$pdf->CellFitScale(25, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[6]) : $riga_condotta[6], 1, 0, 'C');
	}
	else
	{
		$pdf->CellFitScale(25, 10, '', 1, 0, 'C');
	}

    $altezza_materie = $pdf->GetY();

	$pdf->CellFitScale(20, 10, '', 1, 1, 'C');
	//}}} </editor-fold>


	//{{{ <editor-fold defaultstate="collapsed" desc="parte destra della tabella (dati esami e credito)">
	$y_esami_fine = $pdf->GetY();
	$x_base_esami = $pdf->GetX() + 120;

	if($voto_esame_terza == 'ZERO')
	{
		$voto_esame_terza = '';
	}

	$pdf->SetXY($x_base + $x_base_esami, $y_esami);
	$pdf->SetFont('Times', 'B', 14);
    if ($stampa_esami_pag3=='NO')
    {
        $pdf->CellFitScale(0, 48,  '', 1, 1, 'C');
    }
    else {
        $pdf->CellFitScale(0, 14,  $testo_finale['esami'], 1, 1, 'C');
        $pdf->SetX($x_base + $x_base_esami);

        if($stampa_note == 'SI')
        {
            $pdf->CellFitScale(0, 7,  $testo_finale['voto_unico'] . ' (6)', 'TRL', 1, 'C');
        }
        else
        {
            $pdf->CellFitScale(0, 7, $testo_finale['voto_unico'], 'TRL', 1, 'C');
        }

        $pdf->SetX($x_base + $x_base_esami);
        $pdf->CellFitScale(0, 7, $testo_finale['in_lettere'], 'BRL', 1, 'C');
        $pdf->SetX($x_base + $x_base_esami);
        $pdf->CellFitScale(0, 20, $voto_esame_terza, 1, 1, 'C');
    }
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->CellFitScale(0, 7, $testo_finale['cred_scol_1'], 'TRL', 1, 'C');
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->CellFitScale(0, 7, $testo_finale['cred_scol_2'], 'BRL', 1, 'C');
	$pdf->SetX($x_base + $x_base_esami);
	$pdf->SetFont('Times', '', 8);

	if($trentino_abilitato == 'SI')
	{
		$pdf->CellFitScale(0, 5, $testo_finale['media_voti_1'], 'LRT', 1, 'C');
		$pdf->SetX($x_base + $x_base_esami);
		$pdf->CellFitScale(0, 5, $testo_finale['media_voti_2'], 'LR', 1, 'C');
		$pdf->SetX($x_base + $x_base_esami);
		$pdf->CellFitScale(0, 10, $media_voti, 'LR', 1, 'C');
		$pdf->SetX($x_base + $x_base_esami);
		$pdf->CellFitScale(0, $altezza_riga_crediti, $testo_finale['credito_terzo'] . $crediti_terza, $riquadro_obb_lat, 1, 'L', 0);

        if($dati_studenti[$cont]['crediti_reintegrati_terza'] > 0 )
		{
				$pdf->SetX($x_base + $x_base_esami);
				$pdf->CellFitScale(0, 5, $testo_finale['credito_integrazione'], $riquadro_obb_lat, 1, 'L', 0);
		}

		$pdf->SetX($x_base + $x_base_esami);
		$pdf->CellFitScale(0, 5, $testo_finale['credito_quarto'] . $crediti_quarta, $riquadro_obb_lat, 1, 'L', 0);
		$pdf->SetX($x_base + $x_base_esami);

        if($dati_studenti[$cont]['crediti_reintegrati_quarta'] > 0 )
		{
				$pdf->SetX($x_base + $x_base_esami);
				$pdf->CellFitScale(0, 5, $testo_finale['credito_integrazione'], $riquadro_obb_lat, 1, 'L', 0);
				$pdf->SetX($x_base + $x_base_esami);
		}

		$pdf->CellFitScale(0, 5, $testo_finale['credito_quinto'] . $crediti_quinta, $riquadro_obb_lat, 1, 'L', 0);
		$pdf->SetX($x_base + $x_base_esami);
		$somma_crediti = $crediti_terza + $crediti_quarta +  $crediti_quinta;
		$pdf->CellFitScale(0, 5, $testo_finale['credito_totale'] . $somma_crediti, $riquadro_obb_lat, 1, 'L', 0);
		$pdf->SetX($x_base + $x_base_esami);

		if (estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE') == "2021/2022"){
			$pdf->CellFitScale(0, 5, $testo_finale['credito_totale_om_2022'] . converti_crediti_om_2022($somma_crediti), $riquadro_obb_lat, 1, 'L', 0);
			$pdf->SetX($x_base + $x_base_esami);
		}
	}
	else
    {
        if ($visualizza_media_voti == 'SI'
            ||
            ($visualizza_media_voti == 'PROMOSSI' && strpos($stato_promozione, "Non amm") === false)
            )
        {
            $pdf->CellFitScale(0, 5, $testo_finale['media_voti_1'], 'LRT', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
            $pdf->CellFitScale(0, 5, $testo_finale['media_voti_2'], 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
            $pdf->CellFitScale(0, 20, $media_voti, 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
        }
        else
        {
            $pdf->CellFitScale(0, 5, "", 'LRT', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
            $pdf->CellFitScale(0, 5, "", 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
            $pdf->CellFitScale(0, 18, "", 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
        }


        if (strpos($stato_promozione, "Non amm") !== false || (intval($dati_classe[2]) < 3 && $dati_indirizzo["tipo_indirizzo"] != '1'))
        {
            $pdf->CellFitScale(0, 5, "", 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
            $pdf->CellFitScale(0, 5, "", 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
            $pdf->CellFitScale(0, 18, "", 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
        }
        else
        {
            $pdf->CellFitScale(0, 5, $testo_finale['credito_attribuito_1'], 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);
            $pdf->CellFitScale(0, 5, $testo_finale['credito_attribuito_2'], 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);

            if ($inglese_abilitato == 'SI') {
                $crediti_lettere = traduci_numero_inglese($crediti_lettere);
            }

            $pdf->CellFitScale(0, 20, $crediti_lettere, 'LR', 1, 'C');
            $pdf->SetX($x_base + $x_base_esami);

        }

		if (estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE') == "2021/2022"){
			$pdf->CellFitScale(0, 5, $testo_finale['credito_totale_om_2022'], 'LR', 1, 'C', 0);
			$pdf->SetX($x_base + $x_base_esami);
			$pdf->CellFitScale(0, 12, traduci_numero_in_lettere(converti_crediti_om_2022($crediti_totali_pagella)), 'LRB', 1, 'C', 0);
		}
		else {
			$pdf->CellFitScale(0, 5, $testo_finale['credito_totale'], 'LR', 1, 'C', 0);
			$pdf->SetX($x_base + $x_base_esami);
			$pdf->CellFitScale(0, 12, $crediti_totali_pagella_lettere!='zero'? $crediti_totali_pagella_lettere : '', 'LRB', 1, 'C', 0);
		}
		$pdf->SetX($x_base + $x_base_esami);
    }

    $altezza_promozione = $altezza_materie - $pdf->GetY();

	if(
		(
			intval($dati_classe[2]) == 5
			&& $dati_indirizzo["tipo_indirizzo"] != '1'
			&& $dati_indirizzo["tipo_indirizzo"] != '6'
		)
		||
		(
			intval($dati_classe[2]) == 3
			&& $dati_indirizzo["tipo_indirizzo"] == '1'
		)
		||
		(
			intval($dati_classe[2]) == 4
			&& $dati_indirizzo["tipo_indirizzo"] == '5'
		)
		||
		(
			intval($dati_classe[2]) == 3
			&& ($dati_indirizzo["tipo_indirizzo"] == '2' || $dati_indirizzo["tipo_indirizzo"] == '3')
		)
	)
	{
		if($stampa_note == 'SI')
		{
			$pdf->CellFitScale(0, $altezza_promozione, $stato_promozione . '(5)', "LTR", 1, 'C');
		}
		else
		{
			$pdf->CellFitScale(0, $altezza_promozione, $stato_promozione, "LTR", 1, 'C');
		}
	}
	else
	{
		if($stampa_note == 'SI')
		{
			$pdf->CellFitScale(0, $altezza_promozione, '(5)', "LTR", 1, 'L');
		}
		else
		{
			$pdf->CellFitScale(0, $altezza_promozione, '', "LTR", 1, 'L');
		}
	}

    // campi liberi (attualmente abilitato il campo libero ore Alternanza per il Filzi)
    $testo_campo_libero = "";
    foreach ($campi_liberi as $campo_libero)
    {
        if ($campo_libero['nome'] == 'Ore alternanza')
        {
            $testo_campo_libero = $campo_libero['descrizione'] . ": " . $campo_libero['valore'];
        }
    }

    $pdf->SetFont('Times', '', 10);
    $pdf->SetX($x_base + $x_base_esami);
    $pdf->CellFitScale(0, $y_esami_fine - $pdf->GetY(), $testo_campo_libero, "LBR", 1, 'L');

    //}}} </editor-fold>

	$pdf->ln(5);
	$pdf->SetX($x_base);
	$pdf->SetFont('Times', 'B', 12);

	if($trentino_abilitato != 'SI')
	{
		if($stampa_note == 'SI')
		{
			$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['annotazioni'] . ' (4)', 1, 1, 'C');
		}
		else
		{
			$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['annotazioni'], 1, 1, 'C');
		}
	}
	else
	{
		if($stampa_note == 'SI')
		{
			$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['carenze_as_corrente'] . ' (4)', 1, 1, 'C');
		}
		else
		{
			$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['carenze_as_corrente'], 1, 1, 'C');
		}
	}

	$pdf->SetFont('Times', '', 8);

	$testo_annotazioni = '';

	if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'SECONDO')
	{
		if($dati_studenti[$cont]['pei'] == 'SI' && $stampa_pei == 'SI')
		{
			$testo_annotazioni .= $commento_pei . chr(13) . chr(10);
		}

		if($stampa_commento_annotazioni == 'SI')
		{
			$testo_annotazioni .= $commento_esito;
		}

		if($trentino_abilitato != 'SI')
		{
			if($stampa_debiti_fine_anno != 'NO' && strlen($elenco_materie_sospese) > 0)
			{
				if(strlen($commento_esito) > 0)
				{
					$testo_annotazioni .= ' in:';
				}

				$testo_annotazioni .= $elenco_materie_sospese . chr(13) . chr(10);
			}

//			if($stampa_carenze_fine_anno != 'NO' && strlen($elenco_materie_carenze) > 0)
            if($stampa_carenze_fine_anno != 'NO' && !empty($materie_carenze))
            {
                $elenco_carenze = '';
                foreach ($materie_carenze as $value) {
                    $elenco_carenze .= chr(13) . chr(10) . $value . " ";
                }

				$testo_annotazioni .= $testo_finale['consolidamento'] . $elenco_carenze . chr(13) . chr(10);
			}
		}
		else
		{
			if($stampa_carenze_fine_anno == 'SI' && strpos(strtoupper($frase_finale), 'NON AMMESS') === false)
			{
				foreach($materie_debito as $singola_materia_debito)
				{
					if($singola_materia_debito != '')
					{
						$testo_annotazioni .= $singola_materia_debito . chr(13) . chr(10);
					}
				}
			}
		}
	}

	$pdf->SetFont('Times', '', 10);
	$pdf->SetX($x_base);
	$pdf->MultiCell($larghezza_max_cella, 5, decode($testo_annotazioni), 1, 'L');


    if ($data_luogo_pagina_3 == 'SI') {
        $y_rel = $pdf->GetY() + 10;
        $pdf->SetXY($x_base, $y_rel);
        $pdf->CellFitScale(55, 5, $citta . ', ' . $data_stampa_intermedia, 0, 0, 'L');

    }

	if($firma_pagina_3 == 'SI')
	{
		if($trentino_abilitato == 'SI')
		{
			$x_rel = $x_base +70 ;
			$pdf->SetFont('Times', '', 7);
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 5, '______________________________', 0, 0, 'C');
			$y_rel_2 = $pdf->GetY();
			$y_rel = $y_rel + 5;
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 3, $testo_finale['firma_genitore'], 0, 0, 'C');

			$x_rel = $x_base + 135;
			$pdf->SetFont('Times', '', 7);
			$pdf->SetXY($x_rel, $y_rel_2);
			$pdf->CellFitScale(55, 5, '______________________________', 0, 0, 'C');
			$y_rel = $y_rel_2 + 5;
			$pdf->SetXY($x_rel, $y_rel);

			if($stampa_note == 'SI')
			{
					$pdf->CellFitScale(55, 3, $dir_scol . ' (3)', 0, 0, 'C');
			}
			else
			{
					$pdf->CellFitScale(55, 3, $dir_scol, 0, 0, 'C');
			}

			$y_rel = $y_rel + 3;
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 3, $dirigente_scolastico, 0, 1, 'C');

            if ($stampa_firma_digitale == 'SI') {
                $pdf->SetX($x_rel);
                $pdf->CellFitScale(55, 0, "(Documento firmato digitalmente)", 0, 1, 'C');
            }
		}
	else
		{
			$x_rel = $x_base + 135;
			$pdf->SetFont('Times', '', 7);
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 5, '______________________________', 0, 0, 'C');
			$y_rel = $y_rel + 5;
			$pdf->SetXY($x_rel, $y_rel);

			if($stampa_note == 'SI')
			{
				$pdf->CellFitScale(55, 3, $dir_scol . ' (3)', 0, 0, 'C');
			}
			else
			{
				$pdf->CellFitScale(55, 3, $dir_scol, 0, 0, 'C');
			}

			$y_rel = $y_rel + 3;
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 3, $dirigente_scolastico, 0, 1, 'C');
            if ($stampa_firma_digitale == 'SI') {
                $pdf->SetX($x_rel);
                $pdf->CellFitScale(55, 0, "(Documento firmato digitalmente)", 0, 1, 'C');
            }
            if ($stampa_firma_omessa_dir == 'SI') {
                $pdf->SetX($x_rel);
                $pdf->CellFitScale(55, 0, "(La firma è omessa ai sensi dell’Art. 3, D.to Lgs. 12/02/1993, n. 39)", 0, 1, 'C');
            }
		}
	}
	//}}} </editor-fold>
}
