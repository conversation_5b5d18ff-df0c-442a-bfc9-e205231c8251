<?php

$larghezza_max_cella = 190;

if($valle_aosta_abilitata == 'SI')
{
	//{{{ <editor-fold defaultstate="collapsed" desc="pagella bilingue per Val d'Aosta">
	$pdf->SetFont('Times', 'B', 10);
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['cognome'], 'LT', 0, 'C');
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['nome'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_studenti[$cont]['codice_fiscale'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_sede["codice_meccanografico"], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $anno_scolastico_attuale, 'RT', 1, 'C');

	$pdf->SetFont('Times', '', 6);
	$pdf->CellFitScale(50, 3, 'COGNOME / NOM', 'L', 0, 'C');
	$pdf->CellFitScale(50, 3, 'NOME / PRÉNOM', 0, 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODICE FISCALE', 0, 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODICE ISTITUTO', 0, 0, 'C');
	$pdf->CellFitScale(30, 3, 'ANNO SCOLASTICO', 'R', 1, 'C');
	$pdf->CellFitScale(50, 3, '', 'LB', 0, 'C');
	$pdf->CellFitScale(50, 3, '', 'B', 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODE FISCAL', 'B', 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODE DE L’ÉTABLISSEMENT', 'B', 0, 'C');
	$pdf->CellFitScale(30, 3, 'ANNÉE SCOLAIRE', 'RB', 1, 'C');
	$pdf->ln(10);

	$pdf->SetFont('Times', 'B', 12);
	$pdf->CellFitScale(80, 7, '', 'TRL', 0, 'C');
	$x_voti = $pdf->GetX();
	$pdf->CellFitScale(110, 7, 'VALUTAZIONE PERIODICA - PRIMA FRAZIONE TEMPORALE', 'TRL', 1, 'C');
	$pdf->CellFitScale(80, 7, 'DISCIPLINE', 'RL', 0, 'C');
	$pdf->CellFitScale(110, 7, 'ÉVALUATION PÉRIODIQUE – 1ère  PARTIE DE L’ANNÉE', 'RL', 1, 'C');
	$pdf->CellFitScale(80, 7, 'DISCIPLINES', 'RL', 0, 'C');
	$pdf->SetFont('Times', '', 10);
	$pdf->CellFitScale(27.5, 7, 'SCRITTO', 'TRL', 0, 'C');
	$pdf->CellFitScale(27.5, 7, 'ORALE', 'TRL', 0, 'C');
	$pdf->CellFitScale(27.5, 7, 'PRATICO/ALTRO', 'TRL', 0, 'C');
	$pdf->CellFitScale(27.5, 7, 'ORE ASSENZA', 'TRL', 1, 'C');

	$pdf->CellFitScale(80, 7, '', 'BRL', 0, 'C');
	$pdf->CellFitScale(27.5, 7, 'ÉCRIT', 'RLB', 0, 'C');
	$pdf->CellFitScale(27.5, 7, 'ORAL', 'RLB', 0, 'C');
	$pdf->CellFitScale(27.5, 7, 'PRATIQUE/AUTRE', 'RLB', 0, 'C');
	$pdf->CellFitScale(27.5, 7, 'HEURES D’ABSENCE', 'RLB', 1, 'C');

	foreach($array_finale_voti as $riga)
	{
		if($riga['tipo_materia'] != "CONDOTTA"
                &&
                (
                    $riga[1] != '' ||
                    $riga[2] != '' ||
                    $riga[3] != '' ||
                    $riga[4] != ''
            )
        )
		{
			if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'PRIMO')
			{
				switch($dati_classe['votazione_pagella_fine_quadrimestre'])
				{
					case '1':
						$numero_voto_scritto_primo_quadr = '';
						$numero_voto_orale_primo_quadr = '';
						$numero_voto_pratico_primo_quadr = $riga[1];
						$descrizione_lingua_voto_scritto_primo_quadr = '';
						$descrizione_lingua_voto_orale_primo_quadr = '';
						$descrizione_lingua_voto_pratico_primo_quadr = $riga['valore_pagella_lingua_voto_unico_primo_quadr'];
						break;
					case '2':
						$numero_voto_scritto_primo_quadr = $riga[2];
						$numero_voto_orale_primo_quadr = $riga[3];
						$numero_voto_pratico_primo_quadr = '';
						$descrizione_lingua_voto_scritto_primo_quadr = $riga['valore_pagella_lingua_voto_scritto_primo_quadr'];
						$descrizione_lingua_voto_orale_primo_quadr = $riga['valore_pagella_lingua_voto_orale_primo_quadr'];
						$descrizione_lingua_voto_pratico_primo_quadr = '';
						break;
					case '3':
						$numero_voto_scritto_primo_quadr = $riga[2];
						$numero_voto_orale_primo_quadr = $riga[3];
						$numero_voto_pratico_primo_quadr = $riga[4];
						$descrizione_lingua_voto_scritto_primo_quadr = $riga['valore_pagella_lingua_voto_scritto_primo_quadr'];
						$descrizione_lingua_voto_orale_primo_quadr = $riga['valore_pagella_lingua_voto_orale_primo_quadr'];
						$descrizione_lingua_voto_pratico_primo_quadr = $riga['valore_pagella_lingua_voto_pratico_primo_quadr'];
						break;
					case '4':
						if($riga['tipo_voto_personalizzato'] == 1)
						{
							$numero_voto_scritto_primo_quadr = '';
							$numero_voto_orale_primo_quadr = '';
							$numero_voto_pratico_primo_quadr = $riga[1];
							$descrizione_lingua_voto_scritto_primo_quadr = '';
							$descrizione_lingua_voto_orale_primo_quadr = '';
							$descrizione_lingua_voto_pratico_primo_quadr = $riga['valore_pagella_lingua_voto_unico_primo_quadr'];
						}
						else
						{
							$numero_voto_scritto_primo_quadr = $riga[2];
							$numero_voto_orale_primo_quadr = $riga[3];
							$numero_voto_pratico_primo_quadr = $riga[4];
							$descrizione_lingua_voto_scritto_primo_quadr = $riga['valore_pagella_lingua_voto_scritto_primo_quadr'];
							$descrizione_lingua_voto_orale_primo_quadr = $riga['valore_pagella_lingua_voto_orale_primo_quadr'];
							$descrizione_lingua_voto_pratico_primo_quadr = $riga['valore_pagella_lingua_voto_pratico_primo_quadr'];
						}
						break;
				}

				$pdf->CellFitScale(80, 5, $riga[0], 'RLT',0, 'L');
				$pdf->CellFitScale(27.5, 5, $numero_voto_scritto_primo_quadr, 'RLT', 0, 'C');
				$pdf->CellFitScale(27.5, 5, $numero_voto_orale_primo_quadr, 'RLT', 0, 'C');
				$pdf->CellFitScale(27.5, 5, $numero_voto_pratico_primo_quadr, 'RLT', 0, 'C');
				$pdf->CellFitScale(27.5, 5, stampa_ore_o_minuti($riga[5]), 'RLT',1, 'C');
				$pdf->CellFitScale(80, 5, $riga['descrizione_materia_straniera'], 'RLB',0, 'L');
				$pdf->CellFitScale(27.5, 5, $descrizione_lingua_voto_scritto_primo_quadr, 'RLB', 0, 'C');
				$pdf->CellFitScale(27.5, 5, $descrizione_lingua_voto_orale_primo_quadr, 'RLB', 0, 'C');
				$pdf->CellFitScale(27.5, 5, $descrizione_lingua_voto_pratico_primo_quadr, 'RLB', 0, 'C');
				$pdf->CellFitScale(27.5, 5, '', 'RLB',1, 'C');
			}
			else
			{
				$pdf->CellFitScale(80, 5, '', 'RLT',0, 'L');
				$pdf->CellFitScale(27.5, 5, '', 'RLT', 0, 'C');
				$pdf->CellFitScale(27.5, 5, '', 'RLT', 0, 'C');
				$pdf->CellFitScale(27.5, 5, '', 'RLT', 0, 'C');
				$pdf->CellFitScale(27.5, 5, '', 'RLT',1, 'C');
				$pdf->CellFitScale(80, 5, '', 'RLB',0, 'L');
				$pdf->CellFitScale(27.5, 5, '', 'RLB', 0, 'C');
				$pdf->CellFitScale(27.5, 5, '', 'RLB', 0, 'C');
				$pdf->CellFitScale(27.5, 5, '', 'RLB', 0, 'C');
				$pdf->CellFitScale(27.5, 5, '', 'RLB',1, 'C');
			}
		}
		else
		{
			$riga_condotta = $riga;
		}
	}

	if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'PRIMO')
	{
		switch($dati_classe['votazione_pagella_fine_quadrimestre'])
		{
			case '1':
				$numero_voto_scritto_condotta_primo_quadr = '';
				$numero_voto_orale_condotta_primo_quadr = '';
				$numero_voto_pratico_condotta_primo_quadr = $riga_condotta[1];
				$descrizione_lingua_voto_scritto_condotta_primo_quadr = '';
				$descrizione_lingua_voto_orale_condotta_primo_quadr = '';
				$descrizione_lingua_voto_pratico_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_unico_primo_quadr'];
				break;
			case '2':
				$numero_voto_scritto_condotta_primo_quadr = $riga_condotta[2];
				$numero_voto_orale_condotta_primo_quadr = $riga_condotta[3];
				$numero_voto_pratico_condotta_primo_quadr = '';
				$descrizione_lingua_voto_scritto_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_scritto_primo_quadr'];
				$descrizione_lingua_voto_orale_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_orale_primo_quadr'];
				$descrizione_lingua_voto_pratico_condotta_primo_quadr = '';
				break;
			case '3':
				$numero_voto_scritto_condotta_primo_quadr = $riga_condotta[2];
				$numero_voto_orale_condotta_primo_quadr = $riga_condotta[3];
				$numero_voto_pratico_condotta_primo_quadr = $riga_condotta[4];
				$descrizione_lingua_voto_scritto_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_scritto_primo_quadr'];
				$descrizione_lingua_voto_orale_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_orale_primo_quadr'];
				$descrizione_lingua_voto_pratico_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_pratico_primo_quadr'];
				break;
			case '4':
				if($riga_condotta['tipo_voto_personalizzato'] == 1)
				{
					$numero_voto_scritto_condotta_primo_quadr = '';
					$numero_voto_orale_condotta_primo_quadr = '';
					$numero_voto_pratico_condotta_primo_quadr = $riga_condotta[1];
					$descrizione_lingua_voto_scritto_condotta_primo_quadr = '';
					$descrizione_lingua_voto_orale_condotta_primo_quadr = '';
					$descrizione_lingua_voto_pratico_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_unico_primo_quadr'];
				}
				else
				{
					$numero_voto_scritto_condotta_primo_quadr = $riga_condotta[2];
					$numero_voto_orale_condotta_primo_quadr = $riga_condotta[3];
					$numero_voto_pratico_condotta_primo_quadr = $riga_condotta[4];
					$descrizione_lingua_voto_scritto_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_scritto_primo_quadr'];
					$descrizione_lingua_voto_orale_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_orale_primo_quadr'];
					$descrizione_lingua_voto_pratico_condotta_primo_quadr = $riga_condotta['valore_pagella_lingua_voto_pratico_primo_quadr'];
				}
				break;
		}

		$pdf->MultiCell(80, 10, "COMPORTAMENTO\nCONDUITE", 1, 'L', 0, 0);
		$pdf->MultiCell(27.5, 10, $numero_voto_scritto_condotta_primo_quadr . "\n" . $descrizione_lingua_voto_scritto_condotta_primo_quadr, 1, 'C', 0, 0);
		$pdf->MultiCell(27.5, 10, $numero_voto_orale_condotta_primo_quadr . "\n" . $descrizione_lingua_voto_orale_condotta_primo_quadr, 1, 'C', 0, 0);
		$pdf->MultiCell(27.5, 10, $numero_voto_pratico_condotta_primo_quadr . "\n" . $descrizione_lingua_voto_pratico_condotta_primo_quadr, 1, 'C', 0, 0);
		$pdf->CellFitScale(27.5, 10, '', 1, 1, 'C');
	}
	else
	{
		$pdf->MultiCell(80, 10, '', 1, 'L', 0, 0);
		$pdf->MultiCell(27.5, 10, '', 1, 'C', 0, 0);
		$pdf->MultiCell(27.5, 10, '', 1, 'C', 0, 0);
		$pdf->MultiCell(27.5, 10, '', 1, 'C', 0, 0);
		$pdf->CellFitScale(27.5, 10, '', 1, 1, 'C');
	}

	$pdf->ln(5);
	$pdf->SetFont('Times', 'B', 12);

	if($stampa_note == 'SI')
	{
		$pdf->CellFitScale($larghezza_max_cella, 5, 'ANNOTAZIONI (4) / ANNOTATIONS (4)', 1, 1, 'C');
	}
	else
	{
		$pdf->CellFitScale($larghezza_max_cella, 5, 'ANNOTAZIONI / ANNOTATIONS', 1, 1, 'C');
	}

	$testo_annotazioni = chr(13) . chr(10);

	if($dati_studenti[$cont]['pei'] == 'SI' && $stampa_pei == 'SI')
	{
		$testo_annotazioni .= $commento_pei . chr(13) . chr(10);
	}

	$pdf->SetFont('Times', '', 10);
	$pdf->MultiCell($larghezza_max_cella, 5, decode($testo_annotazioni), 1, 'L');

    $x_base = $pdf->GetX();
//    $y_rel = $pdf->GetY() + 10;
//    $pdf->SetY($y_rel);

    if ($data_luogo_pagina_2 == 'SI') {
        $y_rel = $pdf->GetY() + 10;
        $pdf->SetXY($x_base, $y_rel);
        $pdf->SetFont('Times', '', 10);
        $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella, $citta . ', ' . $data_stampa_primo_quadr, 0, 1, 'L');
        $y_rel = $pdf->GetY();
        $pdf->SetXY($x_base, $y_rel);
        $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'LUOGO E DATA/LIEU ET DATE', 0, 0, 'L');
    }

    $pdf->SetFont('Times', '', 6);
    if($firma_pagina_2 == 'SI')
	{
        $y_rel = $pdf->GetY();
        $pdf->SetFont('Times', '', 6);
        $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella, '______________________________', 0, 0, 'C');

        $y_rel = $y_rel + 5;
        $pdf->SetXY($x_base, $y_rel);
        $pdf->SetXY($x_rel-190, $y_rel);

        if($stampa_note == 'SI')
        {
            $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, $definizione_dirigente .' (3)', 0, 1, 'C');
            $pdf->SetX($x_rel-190);
            $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'LE DIRECTEUR GÉNÉRAL (3)', 0, 1, 'C');
        }
        else
        {

            $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, $definizione_dirigente, 0, 1, 'C');
            $pdf->SetX($x_rel-190);
            $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'LE DIRECTEUR GÉNÉRAL', 0, 1, 'C');
        }
        $pdf->SetX($x_rel-190);
        $pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, $dirigente_scolastico, 0, 1, 'C');
        if ($stampa_firma_digitale == 'SI') {
            $pdf->SetX($x_rel-190);
            $pdf->CellFitScale($larghezza_max_cella/2 - 40, 0, "(Documento firmato digitalmente)", 0, 1, 'C');
        }
    }
    else
    {
        $y_rel = $pdf->GetY();
        $pdf->SetY($y_rel + 5 + $altezza_cella*2);
    }

	$y_rel = $pdf->GetY() - 5;
	$pdf->SetY($y_rel);
	$pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella, '_________________________________________________________________', 0, 1, 'L');
	$pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'IL /I GENITORE /I O CHI NE FA LE VECI', 0, 1, 'L');
	$pdf->CellFitScale($larghezza_max_cella/2 - 40, $altezza_cella-2, 'LE / LES PARENT/S OU LA PERSONNE QUI EXERCE L’AUTORITÉ PARENTALE', 0, 1, 'L');
	//}}} </editor-fold>
}
else
{
	//{{{ <editor-fold defaultstate="collapsed" desc="pagella ministeriale">
	$pdf->SetFont('Times', 'B', 10);
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['cognome'], 'LT', 0, 'C');
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['nome'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_studenti[$cont]['codice_fiscale'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_sede["codice_meccanografico"], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $anno_scolastico_attuale, 'RT', 1, 'C');

	$pdf->SetFont('Times', '', 6);
	$pdf->CellFitScale(50, 4, $testo_finale['cognome'], 'LB', 0, 'C');
	$pdf->CellFitScale(50, 4, $testo_finale['nome'], 'B', 0, 'C');
	$pdf->CellFitScale(30, 4, $testo_finale['codice_fiscale'], 'B', 0, 'C');
	$pdf->CellFitScale(30, 4, $testo_finale['codice_istituto'], 'B', 0, 'C');
	$pdf->CellFitScale(30, 4, $testo_finale['anno_scolastico'], 'RB', 1, 'C');
	$pdf->ln(10);

    $stato_visualizza_pagelle_temp = estrai_parametri_singoli("VOTAZIONE_PAGELLA_FINE_QUADRIMESTRE");

	$pdf->SetFont('Times', 'B', 14);

	if($tipo_periodo == 'trimestri' && $formato_pagina == 'A3')
	{
		//{{{ <editor-fold defaultstate="collapsed" desc="sezione valutazione periodica per due periodi">
		$pdf->CellFitScale(50, 5, '', 'TRL', 0, 'C');
		$pdf->CellFitScale(140, 5, $testo_finale['valutazione_periodica'], 1, 1, 'C');
		$pdf->CellFitScale(50, 5, $testo_finale['discipline'], 'RL', 0, 'C');
		$pdf->CellFitScale(70, 5, $testo_finale['primo_trimestre'], 1, 0, 'C');
		$pdf->CellFitScale(70, 5, $testo_finale['secondo_trimestre'], 1, 1, 'C');
		$pdf->CellFitScale(50, 10, '', 'RLB', 0, 'C');
		$pdf->SetFont('Times', '', 10);

        if ($tipo_visualizzazione_voti_primo_quad == 'voto_singolo') {
            $pdf->CellFitScale(57, 10, $testo_finale['UNICO'], 1, 0, 'C');
        } else {
            $pdf->CellFitScale(19, 10, $testo_finale['scritto'], 1, 0, 'C');
            $pdf->CellFitScale(19, 10, $testo_finale['orale'], 1, 0, 'C');
            $pdf->CellFitScale(19, 10, $testo_finale['pratico'], 1, 0, 'C');
        }
		$pdf->MultiCell(13, 10, $testo_finale['ore_assenza'], 1, 'C', 0, 0);

        if ($tipo_visualizzazione_voti_secondo_trim == 'voto_singolo') {
            $pdf->CellFitScale(57, 10, $testo_finale['UNICO'], 1, 0, 'C');
        } else {
            $pdf->CellFitScale(19, 10, $testo_finale['scritto'], 1, 0, 'C');
            $pdf->CellFitScale(19, 10, $testo_finale['orale'], 1, 0, 'C');
            $pdf->CellFitScale(19, 10, $testo_finale['pratico'], 1, 0, 'C');
        }
		$pdf->MultiCell(13, 10, $testo_finale['ore_assenza'], 1, 'C', 0, 1);

		foreach($array_finale_voti as $riga)
		{
			if($riga['tipo_materia'] != "CONDOTTA")
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="stampa riga materia normale">
				$pdf->CellFitScale(50, 10, $riga[$index_materia], 1, 0, 'C');

				if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'PRIMO')
				{
					//{{{ <editor-fold defaultstate="collapsed" desc="controllo cosa stampare dei vari dati, pulisco dalle informazioni non necessarie">
					if($tipo_visualizzazione_voti_primo_quad == 'personalizzato' && $riga['tipo_voto_personalizzato'] == '0')
					{
						switch(intval($stato_visualizza_pagelle_temp))
						{
							case 1:
								$tipo_visualizzazione_voti_temp = "voto_singolo";
								break;
							case 2:
								$tipo_visualizzazione_voti_temp = "scritto_orale";
								break;
							case 3:
								$tipo_visualizzazione_voti_temp = "scritto_orale_pratico";
								break;
							case 4:
								$tipo_visualizzazione_voti_temp = "personalizzato";
								break;
							default:
								$tipo_visualizzazione_voti_temp = "scritto_orale_pratico";
								break;
						}
					}
					else
					{
						$tipo_visualizzazione_voti_temp = '';
					}

					if($tipo_visualizzazione_voti_primo_quad == 'personalizzato' && $riga['tipo_voto_personalizzato'] != '0')
					{
						if($riga['tipo_voto_personalizzato'] == '1')
						{
							$riga['voto_scritto_primo_quadr'] = '';
							$riga['voto_orale_primo_quadr'] = '';
							$riga['voto_pratico_primo_quadr'] = '';
						}
						elseif($riga['tipo_voto_personalizzato'] == '2')
						{
							$riga['voto_numerico_primo_quadr'] = '';
							$riga['voto_pratico_primo_quadr'] = '';
						}
						else
						{
							$riga['voto_numerico_primo_quadr'] = '';
						}
					}
					elseif($tipo_visualizzazione_voti_primo_quad == 'scritto_orale' || $tipo_visualizzazione_voti_temp == 'scritto_orale')
					{
						$riga['voto_numerico_primo_quadr'] = '';
						$riga['voto_pratico_primo_quadr'] = '';
					}
					elseif($tipo_visualizzazione_voti_primo_quad == 'scritto_orale_pratico' || $tipo_visualizzazione_voti_temp == 'scritto_orale_pratico')
					{
						$riga['voto_numerico_primo_quadr'] = '';
					}
					elseif($tipo_visualizzazione_voti_primo_quad == 'voto_singolo' || $tipo_visualizzazione_voti_temp == 'voto_singolo')
					{
						$riga['voto_scritto_primo_quadr'] = '';
						$riga['voto_orale_primo_quadr'] = '';
						$riga['voto_pratico_primo_quadr'] = '';
					}
					else
					{
						$riga[0] .= " ATTENZIONE: impostazione materia non corretta";
						$riga['voto_numerico_primo_quadr'] = '';
						$riga['voto_scritto_primo_quadr'] = '';
						$riga['voto_orale_primo_quadr'] = '';
						$riga['voto_pratico_primo_quadr'] = '';
					}
					//}}} </editor-fold>

                    if ($tipo_visualizzazione_voti_primo_quad == 'voto_singolo') {

                        if ($modalita_stampa_voti == 'lettere')
                        {
                            $pdf->CellFitScale(57, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[1]) : $riga[1], 1, 0, 'C');
                        }
                        else {
                            $pdf->CellFitScale(20, 10, $riga['voto_numerico_primo_quadr'], 1, 0, 'C');
                            $pdf->CellFitScale(37, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[1]) : $riga[1], 1, 0, 'C');
                        }
                    } else {

                        if ($modalita_stampa_voti == 'lettere')
                        {
                        $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[2]) : $riga[2], 1, 0, 'C');
                        $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[3]) : $riga[3], 1, 0, 'C');
                        $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[4]) : $riga[4], 1, 0, 'C');
                        }
                        else {
                        $pdf->CellFitScale(5, 10, $riga['voto_scritto_primo_quadr'], 1, 0, 'C');
                        $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[2]) :$riga[2], 1, 0, 'C');
                        $pdf->CellFitScale(5, 10, $riga['voto_orale_primo_quadr'], 1, 0, 'C');
                        $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[3]) :$riga[3], 1, 0, 'C');
                        $pdf->CellFitScale(5, 10, $riga['voto_pratico_primo_quadr'], 1, 0, 'C');
                        $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[4]) :$riga[4], 1, 0, 'C');
                        }
                    }
					$pdf->CellFitScale(13, 10, stampa_ore_o_minuti($riga[5]), 1, 0, 'C');

                    if ($tipo_visualizzazione_voti_secondo_trim == 'voto_singolo') {

                        if ($modalita_stampa_voti == 'lettere')
                        {
                        $pdf->CellFitScale(57, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[31]):$riga[31], 1, 0, 'C');
                        }
                        else {
                        $pdf->CellFitScale(20, 10, $riga['voto_numerico_secondo_trim'], 1, 0, 'C');
                        $pdf->CellFitScale(37, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[31]):$riga[31], 1, 0, 'C');
                        }
                    } else {

                        if ($modalita_stampa_voti == 'lettere')
                        {
                        $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[32]):$riga[32], 1, 0, 'C');
                        $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[33]):$riga[33], 1, 0, 'C');
                        $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[34]):$riga[34], 1, 0, 'C');
                        }
                        else {
                        $pdf->CellFitScale(5, 10, $riga['voto_scritto_secondo_trim'], 1, 0, 'C');
                        $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[32]):$riga[32], 1, 0, 'C');
                        $pdf->CellFitScale(5, 10, $riga['voto_orale_secondo_trim'], 1, 0, 'C');
                        $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[33]):$riga[33], 1, 0, 'C');
                        $pdf->CellFitScale(5, 10, $riga['voto_pratico_secondo_trim'], 1, 0, 'C');
                        $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[34]):$riga[34], 1, 0, 'C');
                        }
                    }
					$pdf->CellFitScale(13, 10, stampa_ore_o_minuti($riga[35]), 1, 1, 'C');
				}
				else
				{
					$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(13, 10, '', 1, 0, 'C');

					$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(13, 10, '', 1, 1, 'C');
				}
				//}}} </editor-fold>
			}
			else
			{
				$riga_condotta = $riga;
			}
		}

		//{{{ <editor-fold defaultstate="collapsed" desc="stampa riga condotta">
		$pdf->CellFitScale(50, 10, $testo_finale['condotta'], 1, 0, 'C');

		if($stampa_periodo_voti == 'TUTTI' or $stampa_periodo_voti == 'PRIMO')
		{
            if ($tipo_visualizzazione_voti_primo_quad == 'voto_singolo') {
                if ($modalita_stampa_voti == 'lettere') {
                $pdf->CellFitScale(57, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[1]) : $riga_condotta[1], 1, 0, 'C');
                }
                else {
                $pdf->CellFitScale(20, 10, $riga_condotta['voto_numerico_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale(37, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[1]) : $riga_condotta[1], 1, 0, 'C');
                }
            } else {
                if ($modalita_stampa_voti == 'lettere') {
                $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[2]) : $riga_condotta[2], 1, 0, 'C');
                $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[3]) : $riga_condotta[3], 1, 0, 'C');
                $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[4]) : $riga_condotta[4], 1, 0, 'C');
                } else {
                $pdf->CellFitScale(5, 10, $riga_condotta['voto_scritto_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[2]) : $riga_condotta[2], 1, 0, 'C');
                $pdf->CellFitScale(5, 10, $riga_condotta['voto_orale_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[3]) : $riga_condotta[3], 1, 0, 'C');
                $pdf->CellFitScale(5, 10, $riga_condotta['voto_pratico_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[4]) : $riga_condotta[4], 1, 0, 'C');
                }
            }
			$pdf->CellFitScale(13, 10, '', 1, 0, 'C');

            if ($tipo_visualizzazione_voti_secondo_trim == 'voto_singolo') {
                if ($modalita_stampa_voti == 'lettere') {
                    $pdf->CellFitScale(57, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[31]) : $riga_condotta[31], 1, 0, 'C');
                } else {
                    $pdf->CellFitScale(20, 10, $riga_condotta['voto_numerico_secondo_trim'], 1, 0, 'C');
                    $pdf->CellFitScale(37, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[31]) : $riga_condotta[31], 1, 0, 'C');
                }
            } else {
                if ($modalita_stampa_voti == 'lettere') {
                $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[32]) : $riga_condotta[32], 1, 0, 'C');
                $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[33]) : $riga_condotta[33], 1, 0, 'C');
                $pdf->CellFitScale(19, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[34]) : $riga_condotta[34], 1, 0, 'C');
                } else {
                $pdf->CellFitScale(5, 10, $riga_condotta['voto_scritto_secondo_trim'], 1, 0, 'C');
                $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[32]) : $riga_condotta[32], 1, 0, 'C');
                $pdf->CellFitScale(5, 10, $riga_condotta['voto_orale_secondo_trim'], 1, 0, 'C');
                $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[33]) : $riga_condotta[33], 1, 0, 'C');
                $pdf->CellFitScale(5, 10, $riga_condotta['voto_pratico_secondo_trim'], 1, 0, 'C');
                $pdf->CellFitScale(14, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[34]) : $riga_condotta[34], 1, 0, 'C');
                }
            }
		}
		else
		{
			$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(13, 10, '', 1, 0, 'C');

			$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(5, 10, '', 1, 0, 'C');
			$pdf->CellFitScale(14, 10, '', 1, 0, 'C');
		}

		$pdf->CellFitScale(13, 10, '', 1, 1, 'C');
		//}}} </editor-fold>

		$pdf->ln(5);
		$pdf->SetFont('Times', 'B', 14);

		if($stampa_note == 'SI')
		{
			$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['annotazioni'] .' (4)', 1, 1, 'C');
		}
		else
		{
			$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['annotazioni'], 1, 1, 'C');
		}

		$pdf->CellFitScale($larghezza_max_cella/2, 5, $testo_finale['primo_trimestre'], 1, 0, 'C');
		$pdf->CellFitScale($larghezza_max_cella/2, 5, $testo_finale['secondo_trimestre'], 1, 1, 'C');
		$testo_annotazioni_primo_trim = '';
		$testo_annotazioni_secondo_trim = '';

		if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'PRIMO')
		{
			if($dati_studenti[$cont]['pei'] == 'SI' && $stampa_pei == 'SI')
			{
				$testo_annotazioni_primo_trim .= $commento_pei . chr(13) . chr(10);
				$testo_annotazioni_secondo_trim .= $commento_pei . chr(13) . chr(10);
			}

			if($stampa_debiti_primo_quad != 'NO')
			{
				$testo_annotazioni_primo_trim .= $elenco_materie_recupero_primo_quadr;
			}

			if($stampa_debiti_secondo_trim == 'SI')
			{
				$testo_annotazioni_secondo_trim = $elenco_materie_recupero_secondo_trim;
			}
		}

		$num_righe_primo_trim = $pdf->MultiCellNbLines($larghezza_max_cella/2, $testo_annotazioni_primo_trim);
		$num_righe_secondo_trim = $pdf->MultiCellNbLines($larghezza_max_cella/2, $testo_annotazioni_secondo_trim);
		$num_righe_max = 1;

		if($num_righe_primo_trim > $num_righe_secondo_trim)
		{
			$num_righe_max = $num_righe_primo_trim;
		}
		else
		{
			$num_righe_max = $num_righe_secondo_trim;
		}

		$pdf->SetFont('Times', '', 10);
		$pdf->MultiCell($larghezza_max_cella/2, 5*$num_righe_max, decode($testo_annotazioni_primo_trim), 1, 'L', 0, 0);
		$pdf->MultiCell($larghezza_max_cella/2, 5*$num_righe_max, decode($testo_annotazioni_secondo_trim), 1, 'L', 0, 1);
		//}}} </editor-fold>
	}
	else
	{
		//{{{ <editor-fold defaultstate="collapsed" desc="sezione valutazione periodica per un solo periodo">
		$pdf->CellFitScale(80, 5, '', 'TRL', 0, 'C');
		$pdf->CellFitScale(110, 5, $testo_finale['valutazione_periodica'], 'TRL', 1, 'C');
		$pdf->CellFitScale(80, 5, $testo_finale['discipline'], 'RL', 0, 'C');
		$pdf->CellFitScale(110, 5, ($pers_label_pagina_2!=''?$pers_label_pagina_2:strtoupper($testo_finale[$label_pagina_2])), 'BRL', 1, 'C');
		$pdf->CellFitScale(80, 10, '', 'BRL', 0, 'C');
		$pdf->SetFont('Times', '', 12);

        if ($tipo_visualizzazione_voti_primo_quad == 'voto_singolo')
        {
            $pdf->CellFitScale(95, 10, $testo_finale[$unico_altro], 'TRLB', 0, 'C');
        }
        else
        {
            $pdf->CellFitScale(25, 10, $testo_finale['scritto'], 'TRLB', 0, 'C');
            $pdf->CellFitScale(25, 10, $testo_finale['orale'], 'TRLB', 0, 'C');
            $pdf->CellFitScale(25, 10, $testo_finale['pratico'], 'TRLB', 0, 'C');
            $pdf->CellFitScale(20, 10, $testo_finale[$unico_altro], 'TRLB', 0, 'C');
        }
        $pdf->SetFont('Times', '', 8);
        $pdf->MultiCell(15, 10, $testo_finale['ore_assenza'], 1, 'C', 0, 1);
		$pdf->SetFont('Times', '', 12);

		foreach($array_finale_voti as $riga)
		{
			if($riga['tipo_materia'] != "CONDOTTA")
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="stampa riga materia normale">
				if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'PRIMO')
				{
					//{{{ <editor-fold defaultstate="collapsed" desc="controllo cosa stampare dei vari dati, pulisco dalle informazioni non necessarie">
					if($tipo_visualizzazione_voti_primo_quad == 'personalizzato' && $riga['tipo_voto_personalizzato'] == '0')
					{
						switch(intval($stato_visualizza_pagelle_temp))
						{
							case 1:
								$tipo_visualizzazione_voti_temp = "voto_singolo";
								break;
							case 2:
								$tipo_visualizzazione_voti_temp = "scritto_orale";
								break;
							case 3:
								$tipo_visualizzazione_voti_temp = "scritto_orale_pratico";
								break;
							case 4:
								$tipo_visualizzazione_voti_temp = "personalizzato";
								break;
							default:
								$tipo_visualizzazione_voti_temp = "scritto_orale_pratico";
								break;
						}
					}
					else
					{
						$tipo_visualizzazione_voti_temp = '';
					}

					if($tipo_visualizzazione_voti_primo_quad == 'personalizzato' && $riga['tipo_voto_personalizzato'] != '0')
					{
						if($riga['tipo_voto_personalizzato'] == '1')
						{
							$riga['voto_scritto_primo_quadr'] = '';
							$riga[2] = '';
							$riga['voto_orale_primo_quadr'] = '';
							$riga[3] = '';
							$riga['voto_pratico_primo_quadr'] = '';
							$riga[4] = '';
						}
						elseif($riga['tipo_voto_personalizzato'] == '2')
						{
							$riga['voto_numerico_primo_quadr'] = '';
							$riga[1] = '';
							$riga['voto_pratico_primo_quadr'] = '';
							$riga[4] = '';
						}
						else
						{
							$riga['voto_numerico_primo_quadr'] = '';
							$riga[1] = '';
						}
					}
					elseif($tipo_visualizzazione_voti_primo_quad == 'scritto_orale' || $tipo_visualizzazione_voti_temp == 'scritto_orale')
					{
						$riga['voto_numerico_primo_quadr'] = '';
						$riga[1] = '';
						$riga['voto_pratico_primo_quadr'] = '';
						$riga[4] = '';
					}
					elseif($tipo_visualizzazione_voti_primo_quad == 'scritto_orale_pratico' || $tipo_visualizzazione_voti_temp == 'scritto_orale_pratico')
					{
						$riga['voto_numerico_primo_quadr'] = '';
						$riga[1] = '';
					}
					elseif($tipo_visualizzazione_voti_primo_quad == 'voto_singolo' || $tipo_visualizzazione_voti_temp == 'voto_singolo')
					{
						$riga['voto_scritto_primo_quadr'] = '';
						$riga[2] = '';
						$riga['voto_orale_primo_quadr'] = '';
						$riga[3] = '';
						$riga['voto_pratico_primo_quadr'] = '';
						$riga[4] = '';
					}
					else
					{
						$riga[0] .= " ATTENZIONE: impostazione materia non corretta";
						$riga['voto_numerico_primo_quadr'] = '';
						$riga['voto_scritto_primo_quadr'] = '';
						$riga['voto_orale_primo_quadr'] = '';
						$riga['voto_pratico_primo_quadr'] = '';
					}
					//}}} </editor-fold>


                    if ($tipo_visualizzazione_voti_primo_quad == 'voto_singolo')
                    {
                        $pdf->CellFitScale(80, 10, $riga[$index_materia], 1, 0, 'C');
                        if ($modalita_stampa_voti == 'lettere')
                        {
                            $pdf->CellFitScale(95, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[1]) : $riga[1], 1, 0, 'C');
                        }
                        else
                        {
                            $pdf->CellFitScale(40, 10, $riga['voto_numerico_primo_quadr'], 1, 0, 'C');
                            $pdf->CellFitScale(55, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[1]) : $riga[1], 1, 0, 'C');
                        }
                    }
                    else
                    {
                        $pdf->CellFitScale(80, 10, $riga[$index_materia], 1, 0, 'C');
                        if ($modalita_stampa_voti == 'lettere')
                        {
                            $pdf->CellFitScale(25, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[2]) : $riga[2], 1, 0, 'C');
                            $pdf->CellFitScale(25, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[3]) : $riga[3], 1, 0, 'C');
                            $pdf->CellFitScale(25, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[4]) : $riga[4], 1, 0, 'C');
                            $pdf->CellFitScale(20, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[1]) : $riga[1], 1, 0, 'C');
                        }
                        else
                        {
                            $pdf->CellFitScale(10, 10, $riga['voto_scritto_primo_quadr'], 1, 0, 'C');
                            $pdf->CellFitScale(15, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[2]) : $riga[2], 1, 0, 'C');
                            $pdf->CellFitScale(10, 10, $riga['voto_orale_primo_quadr'], 1, 0, 'C');
                            $pdf->CellFitScale(15, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[3]) : $riga[3], 1, 0, 'C');
                            $pdf->CellFitScale(10, 10, $riga['voto_pratico_primo_quadr'], 1, 0, 'C');
                            $pdf->CellFitScale(15, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[4]) : $riga[4], 1, 0, 'C');
                            $pdf->CellFitScale(8, 10, $riga['voto_numerico_primo_quadr'], 1, 0, 'C');
                            $pdf->CellFitScale(12, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga[1]) : $riga[1], 1, 0, 'C');
                        }
                    }

					$pdf->CellFitScale(15, 10, stampa_ore_o_minuti($riga[5]), 1, 1, 'C');
				}
				else
				{
					$pdf->CellFitScale(80, 10, $riga[$index_materia], 1, 0, 'C');
					$pdf->CellFitScale(10, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(15, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(10, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(15, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(10, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(15, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(8, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(12, 10, '', 1, 0, 'C');
					$pdf->CellFitScale(15, 10, '', 1, 1, 'C');
				}
				//}}} </editor-fold>
			}
			else
			{
				$riga_condotta = $riga;
			}
		}

		//{{{ <editor-fold defaultstate="collapsed" desc="stampa riga condotta">
		if($trentino_abilitato == 'SI')
		{
			$pdf->CellFitScale(80, 10, $testo_finale['capacita_relazionale'], 1, 0, 'C');
		}
		else
		{
			$pdf->CellFitScale(80, 10,$testo_finale['condotta'], 1, 0, 'C');
		}

		if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'PRIMO')
		{
            if ($tipo_visualizzazione_voti_primo_quad == 'voto_singolo')
            {

                if ($modalita_stampa_voti == 'lettere') {
                $pdf->CellFitScale(95, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[1]) : $riga_condotta[1], 1, 0, 'C');
                } else {
                $pdf->CellFitScale(40, 10, $riga_condotta['voto_numerico_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale(55, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[1]) : $riga_condotta[1], 1, 0, 'C');
                }
            }
            else
            {

                if ($modalita_stampa_voti == 'lettere') {
                $pdf->CellFitScale(25, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[2]) : $riga_condotta[2], 1, 0, 'C');
                $pdf->CellFitScale(25, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[3]) : $riga_condotta[3], 1, 0, 'C');
                $pdf->CellFitScale(25, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[4]) : $riga_condotta[4], 1, 0, 'C');
                $pdf->CellFitScale(20, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[1]) : $riga_condotta[1], 1, 0, 'C');
                } else {
    			$pdf->CellFitScale(10, 10, $riga_condotta['voto_scritto_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale(15, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[2]) : $riga_condotta[2], 1, 0, 'C');
                $pdf->CellFitScale(10, 10, $riga_condotta['voto_orale_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale(15, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[3]) : $riga_condotta[3], 1, 0, 'C');
                $pdf->CellFitScale(10, 10, $riga_condotta['voto_pratico_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale(15, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[4]) : $riga_condotta[4], 1, 0, 'C');
                $pdf->CellFitScale(8, 10, $riga_condotta['voto_numerico_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale(12, 10, $inglese_abilitato == 'SI' ? traduci_numero_inglese($riga_condotta[1]) : $riga_condotta[1], 1, 0, 'C');
                }
            }
		}
		else
		{
            if ($tipo_visualizzazione_voti_primo_quad == 'voto_singolo')
            {
                if ($modalita_stampa_voti == 'lettere') {
                $pdf->CellFitScale(95, 10, '', 1, 0, 'C');
                } else {
                $pdf->CellFitScale(40, 10,  '', 1, 0, 'C');
                $pdf->CellFitScale(55, 10, '', 1, 0, 'C');
                }
            }
            else
            {
                $pdf->CellFitScale(10, 10, '', 1, 0, 'C');
                $pdf->CellFitScale(15, 10, '', 1, 0, 'C');
                $pdf->CellFitScale(10, 10, '', 1, 0, 'C');
                $pdf->CellFitScale(15, 10, '', 1, 0, 'C');
                $pdf->CellFitScale(10, 10, '', 1, 0, 'C');
                $pdf->CellFitScale(15, 10, '', 1, 0, 'C');
                $pdf->CellFitScale(8, 10,  '', 1, 0, 'C');
                $pdf->CellFitScale(12, 10, '', 1, 0, 'C');
            }
		}

		$pdf->CellFitScale(15, 10, '', 1, 1, 'C');
		//}}} </editor-fold>

		$pdf->ln(5);
		$pdf->SetFont('Times', 'B', 12);

		if($trentino_abilitato != 'SI')
		{
			if($stampa_note == 'SI')
			{
				$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['annotazioni'] . ' (4)', 1, 1, 'C');
			}
			else
			{
				$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['annotazioni'], 1, 1, 'C');
			}
		}
		else
		{
			if($stampa_note == 'SI')
			{
				$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['carenze_as_precedente'] .' (4)', 1, 1, 'C');
			}
			else
			{
				$pdf->CellFitScale($larghezza_max_cella, 5, $testo_finale['carenze_as_precedente'], 1, 1, 'C');
			}
		}
		$testo_annotazioni = '';
		if($stampa_periodo_voti == 'TUTTI' || $stampa_periodo_voti == 'PRIMO')
		{
			if($dati_studenti[$cont]['pei'] == 'SI' && $stampa_pei == 'SI')
			{
				$testo_annotazioni .= $commento_pei . chr(13) . chr(10);
			}
			if($trentino_abilitato == 'SI')
			{
				if($stampa_carenze_anno_prec == 'SI')
				{
					if (is_array($materie_debito_prec))
					{
						foreach($materie_debito_prec as $singola_materia_debito)
						{
							if($singola_materia_debito != '')
							{
								$testo_annotazioni .= $singola_materia_debito . chr(13) . chr(10);
							}
						}
					}
				}
			}
			else
			{
				if($stampa_debiti_primo_quad != 'NO')
				{
					$testo_annotazioni .= $elenco_materie_recupero_primo_quadr;
				}
			}
		}
		$pdf->SetFont('Times', '', 10);
		$pdf->MultiCell($larghezza_max_cella, 5, decode($testo_annotazioni), 1, 'L');
		//}}} </editor-fold>
	}

    $x_base = $pdf->GetX();
    $y_rel = $pdf->GetY() + 10;
    $pdf->SetY($y_rel);

    if ($data_luogo_pagina_2 == 'SI') {
        $pdf->CellFitScale(55, 5, $citta . ', ' . $data_stampa_primo_quadr, 0, 0, 'L');
    }

	if($firma_pagina_2 == 'SI')
	{
		if($trentino_abilitato == 'SI')
		{
			$x_rel = $x_base +70 ;
			$pdf->SetFont('Times', '', 7);
			 //$y_rel = $y_rel + 10;
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 5, '______________________________', 0, 0, 'C');
			$y_rel_2 = $pdf->GetY();
			$y_rel = $y_rel + 5;
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 3, $testo_finale['firma_genitore'], 0, 0, 'C');

			$x_rel = $x_base + 135;
			$pdf->SetFont('Times', '', 7);
			$pdf->SetXY($x_rel, $y_rel_2);
			$pdf->CellFitScale(55, 5, '______________________________', 0, 0, 'C');
			$y_rel = $y_rel_2 + 5;
			$pdf->SetXY($x_rel, $y_rel);
			if($stampa_note == 'SI')
			{
					$pdf->CellFitScale(55, 3, $definizione_dirigente . ' (3)', 0, 0, 'C');
			}
			else
			{
					$pdf->CellFitScale(55, 3, $definizione_dirigente, 0, 0, 'C');
			}

			$y_rel = $y_rel + 3;
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 3, $dirigente_scolastico, 0, 1, 'C');
            if ($stampa_firma_digitale == 'SI') {
                $pdf->SetX($x_rel);
                $pdf->CellFitScale(55, 0, "(Documento firmato digitalmente)", 0, 1, 'C');
            }
		}
		else
		{
			$x_rel = $x_base + 135;
			$pdf->SetFont('Times', '', 7);
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 5, '______________________________', 0, 0, 'C');
			$y_rel = $y_rel + 5;
			$pdf->SetXY($x_rel, $y_rel);
			if($stampa_note == 'SI')
			{
				$pdf->CellFitScale(55, 3, $dir_scol . ' (3)', 0, 0, 'C');
			}
			else
			{
				$pdf->CellFitScale(55, 3, $dir_scol, 0, 0, 'C');
			}

			$y_rel = $y_rel + 3;
			$pdf->SetXY($x_rel, $y_rel);
			$pdf->CellFitScale(55, 3, $dirigente_scolastico, 0, 1, 'C');
            if ($stampa_firma_digitale == 'SI') {
                $pdf->SetX($x_rel);
                $pdf->CellFitScale(55, 0, "(Documento firmato digitalmente)", 0, 1, 'C');
            }
            if ($stampa_firma_omessa_dir == 'SI') {
                $pdf->SetX($x_rel);
                $pdf->CellFitScale(55, 0, "(La firma è omessa ai sensi dell’Art. 3, D.to Lgs. 12/02/1993, n. 39)", 0, 1, 'C');
            }
		}
	}
	//}}} </editor-fold>
}
