<?php

function estrai_materie($ordinamento = 'ordinamento', $elenco_classi = null) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie dell'istituto">
    if (is_array($elenco_classi) && count($elenco_classi) > 0) {
        $classi = [];

        foreach ($elenco_classi as $classe) {
            $classi[] = $classe['valore'];
        }

        $query = "SELECT DISTINCT materie.*
                  FROM materie
                  INNER JOIN classi_prof_materie ON classi_prof_materie.id_materia = materie.id_materia
                  WHERE materie.flag_canc = 0
                    AND classi_prof_materie.flag_canc = 0
                    AND classi_prof_materie.id_classe IN (" . implode(",", $classi) . ") ";
    } else {
        $query = "SELECT * FROM materie WHERE flag_canc = 0 ";
    }

    $query .= " AND materie.descrizione <> 'MATERIA NON DEFINITA'";
    $query .= " AND materie.tipo_materia <> 'CORSO'";

    if ($ordinamento == 'ordinamento') {
        $query .= " ORDER BY materie.ordinamento, materie.descrizione";
    } else {
        $query .= " ORDER BY materie.descrizione";
    }

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $materie[$cont] = pg_fetch_assoc($result, $cont);
            $materie[$cont][0] = pg_fetch_result($result, $cont, "id_materia");
            $materie[$cont][1] = decode(pg_fetch_result($result, $cont, "codice"));
            $materie[$cont][2] = decode(pg_fetch_result($result, $cont, "descrizione"));
            $materie[$cont]['descrizione_materia_straniera'] = decode(pg_fetch_result($result, $cont, "descrizione_materia_straniera"));
            $materie[$cont]['nome_materia_breve'] = decode(pg_fetch_result($result, $cont, "nome_materia_breve"));
            $materie[$cont][4] = pg_fetch_result($result, $cont, "esonerabile");
            $materie[$cont][5] = pg_fetch_result($result, $cont, "votazione_differenziata");
            $materie[$cont][6] = decode(pg_fetch_result($result, $cont, "codice_itp"));
            $materie[$cont][8] = pg_fetch_result($result, $cont, "scritto");
            $materie[$cont][9] = pg_fetch_result($result, $cont, "orale");
            $materie[$cont][10] = pg_fetch_result($result, $cont, "pratico");
            $materie[$cont][11] = pg_fetch_result($result, $cont, "ordinamento");
            $materie[$cont][12] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $materie[$cont][13] = pg_fetch_result($result, $cont, "tipo_valutazione");
            $materie[$cont]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
            $materie[$cont]["codice_ministeriale"] = decode(pg_fetch_result($result, $cont, "codice_ministeriale"));
            $materie[$cont]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $materie[$cont]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
            $materie[$cont]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
        }
    }

    return $materie;
    //}}} </editor-fold>
}

function estrai_materie_select($ordinamento = 'ordinamento', $elenco_classi = null) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie dell'istituto">
    $materie = estrai_materie($ordinamento, $elenco_classi);

    if (is_array($materie)) {
        foreach ($materie as $materia) {
            $dati[] = [
                'nome'   => $materia['codice_ministeriale'] ? "{$materia['descrizione']} ({$materia['codice_ministeriale']})" : $materia['descrizione'],
                'valore' => $materia['id_materia']
            ];
        }
    }

    return $dati;
    //}}} </editor-fold>
}

function estrai_materie_professore($ordinamento = 'ordinamento', $current_user = null, $elenco_classi = null) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie dell'istituto">
    if ($current_user > 0) {
        if (is_array($elenco_classi) and count($elenco_classi) > 0) {
            $query = "SELECT DISTINCT
							materie.*
						FROM materie
						INNER JOIN classi_prof_materie ON classi_prof_materie.id_materia = materie.id_materia
						WHERE materie.flag_canc = 0
							AND classi_prof_materie.flag_canc = 0
							AND classi_prof_materie.id_professore = $current_user
							AND classi_prof_materie.id_classe in (";

            foreach ($elenco_classi as $classe) {
                $query .= $classe['valore'] . ",";
            }

            $query = substr($query, 0, -1);
            $query .= ") ";
            if ($ordinamento == 'ordinamento') {
                $query .= "
						ORDER BY
							materie.ordinamento,
							materie.descrizione";
            } else {
                $query .= "
						ORDER BY
							materie.descrizione";
            }
        } else {
            if ($ordinamento == 'ordinamento') {
                $query = "SELECT * FROM materie
                            INNER JOIN classi_prof_materie ON classi_prof_materie.id_materia = materie.id_materia
                            INNER JOIN classi_complete ON classi_prof_materie.id_classe = classi_complete.id_classe
							WHERE materie.flag_canc = 0
								AND classi_prof_materie.flag_canc = 0
								AND classi_prof_materie.id_professore = $current_user
							ORDER BY
								materie.ordinamento,
								materie.descrizione";
            } else {
                $query = "SELECT * FROM materie
                            INNER JOIN classi_prof_materie ON classi_prof_materie.id_materia = materie.id_materia
                            INNER JOIN classi_complete ON classi_prof_materie.id_classe = classi_complete.id_classe
							WHERE materie.flag_canc = 0
								AND classi_prof_materie.flag_canc = 0
								AND classi_prof_materie.id_professore = $current_user
							ORDER BY materie.descrizione";
            }
        }
        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);
        if ($numero > 0) {
            for ($cont = 0; $cont < $numero; $cont++) {
                $dati_materie[$cont] = pg_fetch_assoc($result, $cont);
                $dati_materie[$cont][0] = pg_fetch_result($result, $cont, "id_materia");
                $dati_materie[$cont][1] = decode(pg_fetch_result($result, $cont, "codice"));
                $dati_materie[$cont][2] = decode(pg_fetch_result($result, $cont, "descrizione"));
                $dati_materie[$cont]['descrizione_materia_straniera'] = decode(pg_fetch_result($result, $cont, "descrizione_materia_straniera"));
                $dati_materie[$cont][4] = pg_fetch_result($result, $cont, "esonerabile");
                $dati_materie[$cont][5] = pg_fetch_result($result, $cont, "votazione_differenziata");
                $dati_materie[$cont][6] = decode(pg_fetch_result($result, $cont, "codice_itp"));
                $dati_materie[$cont][8] = pg_fetch_result($result, $cont, "scritto");
                $dati_materie[$cont][9] = pg_fetch_result($result, $cont, "orale");
                $dati_materie[$cont][10] = pg_fetch_result($result, $cont, "pratico");
                $dati_materie[$cont][11] = pg_fetch_result($result, $cont, "ordinamento");
                $dati_materie[$cont][12] = pg_fetch_result($result, $cont, "in_media_pagelle");
                $dati_materie[$cont][13] = pg_fetch_result($result, $cont, "tipo_valutazione");
                $dati_materie[$cont]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
                $dati_materie[$cont]["codice_ministeriale"] = decode(pg_fetch_result($result, $cont, "codice_ministeriale"));
                $dati_materie[$cont]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
                $dati_materie[$cont]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
                $dati_materie[$cont]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
            }
        }
    }
    return $dati_materie;
    //}}} </editor-fold>
}

function estrai_materia_professore_classe($current_user, $id_classe) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre il tipo materia">
    if ($current_user > 0 && $id_classe > 0) {
        $query = "SELECT DISTINCT tipo_materia
                    FROM materie
                    WHERE id_materia IN (
                        SELECT id_materia
                        FROM classi_prof_materie
                        WHERE id_professore = {$current_user}
                            AND id_classe = {$id_classe}
                    )
                  ORDER BY tipo_materia DESC
                ";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            return pg_fetch_all($result);
        }
    }
    //}}} </editor-fold>
}

function estrai_materie_professore_select($ordinamento = 'ordinamento', $current_user = null, $elenco_classi = null) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie dell'istituto">
    $mat_materie = estrai_materie_professore($ordinamento, $current_user, $elenco_classi);
    if (is_array($mat_materie)) {
        foreach ($mat_materie as $materia) {
            $dati_materie[] = ['nome' => $materia['descrizione'], 'valore' => $materia['id_materia']];
        }
    }

    return $dati_materie;
    //}}} </editor-fold>
}

function estrai_materie_indicizzate() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie dell'istituto">
    $materie = estrai_materie('ordinamento');

    if (is_array($materie)) {
        foreach ($materie as $materia) {
            $dati_materie[intval($materia['id_materia'])] = $materia;
        }
    }

    return $dati_materie;
    //}}} </editor-fold>
}

function estrai_materie_per_aggiornamento_itp() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie dell'istituto">
    $query = "SELECT * FROM materie
              WHERE flag_canc = 0
              ORDER BY ordinamento, descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $cont2 = 0;

        for ($cont = 0; $cont < $numero; $cont++) {
            $materie[$cont2][0] = pg_fetch_result($result, $cont, "id_materia");
            $materie[$cont2][1] = decode(pg_fetch_result($result, $cont, "codice"));
            $materie[$cont2][2] = decode(pg_fetch_result($result, $cont, "descrizione"));
            $materie[$cont2]['descrizione_materia_straniera'] = decode(pg_fetch_result($result, $cont, "descrizione_materia_straniera"));
            $materie[$cont2][4] = pg_fetch_result($result, $cont, "esonerabile");
            $materie[$cont2][5] = pg_fetch_result($result, $cont, "votazione_differenziata");
            $materie[$cont2][6] = decode(pg_fetch_result($result, $cont, "codice_itp"));
            $materie[$cont2][7] = "materia_principale";
            $materie[$cont2][8] = pg_fetch_result($result, $cont, "scritto");
            $materie[$cont2][9] = pg_fetch_result($result, $cont, "orale");
            $materie[$cont2][10] = pg_fetch_result($result, $cont, "pratico");
            $materie[$cont2][11] = pg_fetch_result($result, $cont, "ordinamento");
            $materie[$cont2][12] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $materie[$cont2][13] = pg_fetch_result($result, $cont, "tipo_valutazione");
            $materie[$cont2]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $materie[$cont2]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
            $materie[$cont2]["codice_ministeriale"] = decode(pg_fetch_result($result, $cont, "codice_ministeriale"));
            $materie[$cont2]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
            $materie[$cont2]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
            $cont2 = $cont2 + 1;
        }
    }

    return $materie;
    //}}} </editor-fold>
}

function estrai_materie_con_itp() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie dell'istituto">
    $query = "SELECT * FROM materie
              WHERE flag_canc = 0
              ORDER BY ordinamento, descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $cont2 = 0;
        for ($cont = 0; $cont < $numero; $cont++) {
            $materie[$cont2][0] = pg_fetch_result($result, $cont, "id_materia");
            $materie[$cont2][1] = decode(pg_fetch_result($result, $cont, "codice"));
            $materie[$cont2][2] = decode(pg_fetch_result($result, $cont, "descrizione"));
            $materie[$cont2]['descrizione_materia_straniera'] = decode(pg_fetch_result($result, $cont, "descrizione_materia_straniera"));
            $materie[$cont2][4] = pg_fetch_result($result, $cont, "esonerabile");
            $materie[$cont2][5] = pg_fetch_result($result, $cont, "votazione_differenziata");
            $materie[$cont2][6] = decode(pg_fetch_result($result, $cont, "codice_itp"));
            $materie[$cont2][7] = "materia_principale";
            $materie[$cont2][8] = pg_fetch_result($result, $cont, "scritto");
            $materie[$cont2][9] = pg_fetch_result($result, $cont, "orale");
            $materie[$cont2][10] = pg_fetch_result($result, $cont, "pratico");
            $materie[$cont2][11] = pg_fetch_result($result, $cont, "ordinamento");
            $materie[$cont2][12] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $materie[$cont2][13] = pg_fetch_result($result, $cont, "tipo_valutazione");
            $materie[$cont2]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
            $materie[$cont2]["codice_ministeriale"] = decode(pg_fetch_result($result, $cont, "codice_ministeriale"));
            $materie[$cont2]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $materie[$cont2]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
            $materie[$cont2]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");

            if ($materie[$cont2][6] != "") {
                $cont2 = $cont2 + 1;
                $materie[$cont2][0] = pg_fetch_result($result, $cont, "id_materia");
                $materie[$cont2][1] = decode(pg_fetch_result($result, $cont, "codice"));
                $materie[$cont2][2] = decode(pg_fetch_result($result, $cont, "descrizione")) . " ITP";
                $materie[$cont2]['descrizione_materia_straniera'] = decode(pg_fetch_result($result, $cont, "descrizione_materia_straniera")) . " ITP";
                $materie[$cont2][4] = pg_fetch_result($result, $cont, "esonerabile");
                $materie[$cont2][5] = pg_fetch_result($result, $cont, "votazione_differenziata");
                $materie[$cont2][6] = decode(pg_fetch_result($result, $cont, "codice_itp"));
                $materie[$cont2][7] = "materia_itp";
                $materie[$cont2][8] = pg_fetch_result($result, $cont, "scritto");
                $materie[$cont2][9] = pg_fetch_result($result, $cont, "orale");
                $materie[$cont2][10] = pg_fetch_result($result, $cont, "pratico");
                $materie[$cont2][11] = pg_fetch_result($result, $cont, "ordinamento");
                $materie[$cont2][12] = pg_fetch_result($result, $cont, "in_media_pagelle");
                $materie[$cont2][13] = pg_fetch_result($result, $cont, "tipo_valutazione");
                $materie[$cont2]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
                $materie[$cont2]["codice_ministeriale"] = decode(pg_fetch_result($result, $cont, "codice_ministeriale"));
                $materie[$cont2]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
                $materie[$cont2]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
                $materie[$cont2]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
            }

            $cont2 = $cont2 + 1;
        }
    }

    return $materie;
    //}}} </editor-fold>
}

function estrai_materie_integrative() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie integrative per chi chiede l'esonero da religione o ed-fisica">
    $query = "SELECT * FROM materie_integrative
              WHERE flag_canc = 0
              ORDER BY id_materia";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $materie[$cont][0] = pg_fetch_result($result, $cont, "id_materia");
            $materie[$cont][1] = decode(pg_fetch_result($result, $cont, "codice"));
            $materie[$cont][2] = decode(pg_fetch_result($result, $cont, "descrizione"));
            $materie[$cont]['id_materia'] = pg_fetch_result($result, $cont, "id_materia");
            $materie[$cont]['codice'] = decode(pg_fetch_result($result, $cont, "codice"));
            $materie[$cont]['descrizione'] = decode(pg_fetch_result($result, $cont, "descrizione"));
        }
    }

    return $materie;
    //}}} </editor-fold>
}

function estrai_materie_integrative_new() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie integrative per chi chiede l'esonero da religione o ed-fisica">
    $query = "SELECT * FROM materie_integrative
				WHERE flag_canc = 0
				ORDER BY id_materia";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $materie[$cont] = [
                'valore' => pg_fetch_result($result, $cont, "id_materia"),
                'nome'   => decode(pg_fetch_result($result, $cont, "codice")) . ' ' . decode(pg_fetch_result($result, $cont, "descrizione"))
            ];
        }
    }

    return $materie;
    //}}} </editor-fold>
}

function estrai_singola_materia_integrativa($id_materia_integrativa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie integrative per chi chiede l'esonero da religione o ed-fisica">
    $query = "SELECT * FROM materie_integrative
              WHERE flag_canc = 0
                AND id_materia = '" . $id_materia_integrativa . "'";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $materia = [
            "id_materia"        => pg_fetch_result($result, 0, "id_materia"),
            "codice"            => decode(pg_fetch_result($result, 0, "codice")),
            "descrizione"       => decode(pg_fetch_result($result, 0, "descrizione")),
            "richiede_presenza" => decode(pg_fetch_result($result, 0, "richiede_presenza"))
        ];
    }

    return $materia;
    //}}} </editor-fold>
}

function estrai_dati_materia($id_materia) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i dati di una materia">
    $id_materia = (int) $id_materia;

    $query = "SELECT DISTINCT materie.*,
                    classi_prof_materie.id_professore,
                    --classi_prof_materie.scritto as cpm_scritto,
                    --classi_prof_materie.orale as cpm_orale,
                    --classi_prof_materie.pratico as cpm_pratico,
                    classi_prof_materie.itp
                FROM materie
                LEFT OUTER JOIN classi_prof_materie ON (classi_prof_materie.id_materia = materie.id_materia)
                WHERE materie.id_materia = {$id_materia}
                    AND materie.flag_canc = 0
                ORDER BY classi_prof_materie.itp";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $materia[0] = pg_fetch_result($result, 0, "id_materia");
        $materia[1] = decode(pg_fetch_result($result, 0, "codice"));
        $materia[2] = decode(pg_fetch_result($result, 0, "descrizione"));
        $materia[4] = pg_fetch_result($result, 0, "esonerabile");
        $materia[5] = pg_fetch_result($result, 0, "votazione_differenziata");
        $materia[6] = decode(pg_fetch_result($result, 0, "codice_itp"));
        $materia[8] = pg_fetch_result($result, 0, "scritto");
        $materia[9] = pg_fetch_result($result, 0, "orale");
        $materia[10] = pg_fetch_result($result, 0, "pratico");
        $materia[11] = pg_fetch_result($result, 0, "ordinamento");
        $materia[12] = pg_fetch_result($result, 0, "in_media_pagelle");
        $materia[13] = pg_fetch_result($result, 0, "tipo_valutazione");
        $materia["id_materia"] = pg_fetch_result($result, 0, "id_materia");
        $materia["codice"] = decode(pg_fetch_result($result, 0, "codice"));
        $materia["descrizione"] = decode(pg_fetch_result($result, 0, "descrizione"));
        $materia["descrizione_materia_straniera"] = decode(pg_fetch_result($result, 0, "descrizione_materia_straniera"));
        $materia["esonerabile"] = pg_fetch_result($result, 0, "esonerabile");
        $materia["votazione_differenziata"] = pg_fetch_result($result, 0, "votazione_differenziata");
        $materia["scritto"] = pg_fetch_result($result, 0, "scritto");
        $materia["orale"] = pg_fetch_result($result, 0, "orale");
        $materia["pratico"] = pg_fetch_result($result, 0, "pratico");

//        $cpm_tipi_voto = estrai_tipi_voto_unificati($id_materia, $id_classe);
//
//        $materia["cpm_scritto"] = pg_fetch_result($result, 0, "cpm_scritto");
//        $materia["cpm_orale"] = pg_fetch_result($result, 0, "cpm_orale");
//        $materia["cpm_pratico"] = pg_fetch_result($result, 0, "cpm_pratico");


        $materia["unico"] = pg_fetch_result($result, 0, "unico");
        $materia["ordinamento"] = pg_fetch_result($result, 0, "ordinamento");
        $materia["in_media_pagelle"] = pg_fetch_result($result, 0, "in_media_pagelle");
        $materia["tipo_valutazione"] = pg_fetch_result($result, 0, "tipo_valutazione");
        $materia["tipo_materia"] = pg_fetch_result($result, 0, "tipo_materia");
        $materia["codice_ministeriale"] = decode(pg_fetch_result($result, 0, "codice_ministeriale"));
        $materia["descrizione_scuola_media"] = decode(pg_fetch_result($result, 0, "descrizione_scuola_media"));
        $materia["id_tipo_voto"] = decode(pg_fetch_result($result, 0, "id_tipo_voto"));
        $materia["tipologia_aggregamento"] = pg_fetch_result($result, 0, "tipologia_aggregamento");
        $materia["tipo_voto_personalizzato"] = pg_fetch_result($result, 0, "tipo_voto_personalizzato");
        $materia["nome_materia_sito"] = decode(pg_fetch_result($result, 0, "nome_materia_sito"));
        $materia["nome_materia_breve"] = decode(pg_fetch_result($result, 0, "nome_materia_breve"));
        $materia["id_materia_riferimento"] = decode(pg_fetch_result($result, 0, "id_materia_riferimento"));

        for ($cont = 0; $cont < $numero; $cont++) {
            $materia["professori"][] = pg_fetch_result($result, $cont, "id_professore");

            $itp =  pg_fetch_result($result, $cont, "itp");
            if ($itp == 'SI'){
                $materia["itp"][] = pg_fetch_result($result, $cont, "id_professore");
            }
        }
    }

    return $materia;
    //}}} </editor-fold>
}

function estrai_id_materia_esonerata($codice_esonero_materia) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre l'id di una materia esonerata">
    $query = "SELECT * FROM materie
				WHERE esonerabile = '$codice_esonero_materia'
					AND flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $dati_materia[0] = pg_fetch_result($result, $cont, "id_materia");
        $dati_materia[1] = pg_fetch_result($result, $cont, "codice");
        $dati_materia[2] = pg_fetch_result($result, $cont, "descrizione");
        $dati_materia['descrizione_materia_straniera'] = pg_fetch_result($result, $cont, "descrizione_materia_straniera");
        $dati_materia[4] = pg_fetch_result($result, $cont, "esonerabile");
        $dati_materia[5] = pg_fetch_result($result, $cont, "votazione_differenziata");
        $dati_materia[6] = pg_fetch_result($result, $cont, "codice_itp");
        $dati_materia[8] = pg_fetch_result($result, $cont, "scritto");
        $dati_materia[9] = pg_fetch_result($result, $cont, "orale");
        $dati_materia[10] = pg_fetch_result($result, $cont, "pratico");
    }
    return $dati_materia;
    //}}} </editor-fold>
}

function estrai_professori_materia($id_materia, $id_classe) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i professori di una materia">
    $query = "SELECT * FROM classi_prof_materie
				WHERE id_classe = {$id_classe} AND id_materia = {$id_materia} AND flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $professori[$cont][0] = pg_fetch_result($result, $cont, "id_professore");
            $professori[$cont]['id_professore'] = $professori[$cont][0];

            $query_int = "SELECT * FROM utenti
							WHERE id_utente = {$professori[$cont][0]} AND tipo_utente = 'P' AND flag_canc = 0";

            $result_int = pgsql_query($query_int) or die("Invalid $query_int");
            $numero_int = pg_num_rows($result_int);

            if ($numero_int > 0) {
                $professori[$cont][1] = pg_fetch_result($result_int, 0, "cognome");
                $professori[$cont][2] = pg_fetch_result($result_int, 0, "nome");
                $professori[$cont]['cognome'] = $professori[$cont][1];
                $professori[$cont]['nome'] = $professori[$cont][2];
            }
        }
    }

    return $professori;
    //}}} </editor-fold>
}

function estrai_professori_materia_tabellone($id_materia, $id_classe) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i professori di una materia">
    $query = "SELECT
                        classi_prof_materie.*,
                        utenti.*
                    FROM
                        classi_prof_materie,
                        utenti
                    WHERE utenti.id_utente = classi_prof_materie.id_professore
                        AND utenti.tipo_utente = 'P'
                        AND classi_prof_materie.id_classe='" . $id_classe . "'" . "
                        AND classi_prof_materie.id_materia='" . $id_materia . "'
                        AND classi_prof_materie.flag_canc = 0
                        AND utenti.flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $professori[$cont] = pg_fetch_assoc($result, $cont);
        }
    }

    return $professori;
    //}}} </editor-fold>
}

function inserisci_materia($codice, $descrizione, $scritto, $orale, $pratico, $unico, $ordinamento, $in_media_pagelle, $tipo_valutazione, $tipo_materia, $codice_ministeriale, $descrizione_scuola_media, $tipologia_aggregamento, $descrizione_materia_straniera, $tipo_voto_personalizzato, $nome_materia_sito, $nome_materia_breve, $current_user, $id_materia_riferimento = -1, $monteore = 0, $id_tipo_corso = -1) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per inserire una nuova materia">
    $codice = strtoupper(preg_replace('/\W/i', '', $codice));

    if (strlen(trim($nome_materia_sito)) <= 0) {
        $nome_materia_sito = $descrizione;
    }

    if (strlen(trim($nome_materia_breve)) <= 0) {
        $nome_materia_breve = $descrizione;
    }

    if (is_null($monteore) || $monteore == '')
    {
        $monteore = 0;
    }

    if (is_null($id_tipo_corso) || $id_tipo_corso == '')
    {
        $id_tipo_corso = -1;
    }

    allinea_sequence('materie_id_seq');

    $query = "INSERT INTO materie
				(
					codice,
					descrizione,
					scritto,
					orale,
					pratico,
					unico,
					ordinamento,
					in_media_pagelle,
					tipo_valutazione,
					tipo_materia,
					codice_ministeriale,
					descrizione_scuola_media,
					tipologia_aggregamento,
					descrizione_materia_straniera,
                    tipo_voto_personalizzato,
                    nome_materia_sito,
                    nome_materia_breve,
                    id_materia_riferimento,
                    monteore,
                    id_tipo_corso
				)
				VALUES
				(
					'" . encode($codice) . "',
					'" . encode($descrizione) . "',
					'" . $scritto . "',
					'" . $orale . "',
					'" . $pratico . "',
					'" . $unico . "',
					'" . $ordinamento . "',
					'" . $in_media_pagelle . "',
					'" . $tipo_valutazione . "',
					'" . $tipo_materia . "',
					'" . encode($codice_ministeriale) . "',
					'" . encode($descrizione_scuola_media) . "',
					'" . $tipologia_aggregamento . "',
					'" . encode($descrizione_materia_straniera) . "',
                    '" . $tipo_voto_personalizzato . "',
					'" . encode($nome_materia_sito) . "',
					'" . encode($nome_materia_breve) . "',
                    '" . $id_materia_riferimento . "',
                    '" . $monteore . "',
                    '" . $id_tipo_corso . "')
				RETURNING id_materia";

    $result = pgsql_query($query) or die("Invalid $query");
    if (pg_num_rows($result) > 0) {
        $id_materia = intval(pg_fetch_result($result, 0, 'id_materia'));
    }

    inserisci_log(["id_materia" => $id_materia], "materie", $current_user, 'INTERFACCIA', 'INSERIMENTO');

    return $id_materia;
    //}}} </editor-fold>
}

function modifica_materia($id_materia, $codice, $descrizione, $scritto, $orale, $pratico, $unico, $ordinamento, $in_media_pagelle, $tipo_valutazione, $tipo_materia, $codice_ministeriale, $descrizione_scuola_media, $tipologia_aggregamento, $descrizione_materia_straniera, $tipo_voto_personalizzato, $nome_materia_sito, $nome_materia_breve, $id_materia_riferimento, $monteore, $id_tipo_corso, $current_user, $superutente_int) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per modificare i dati di una materia">
    $codice = strtoupper(preg_replace('/\W/i', '', $codice));

    $query = "UPDATE classi_prof_materie
				SET codice_materia = '" . encode($codice) . "'
				WHERE id_materia = {$id_materia}";

    pgsql_query($query) or die("Invalid $query");

    $mat_oggetti = [
        "id_materia"     => $id_materia,
        "codice_materia" => $codice
    ];

    inserisci_log($mat_oggetti, "classi_prof_materie", $current_user, 'AUTOMATICO', 'MODIFICA');

    if (strlen(trim($nome_materia_sito)) <= 0) {
        $nome_materia_sito = $descrizione;
    }

    if (strlen(trim($nome_materia_breve)) <= 0) {
        $nome_materia_breve = $descrizione;
    }

    $moodle = moodle_status_check();

    $sql = "SELECT cpm.sync_moodle, m.tipo_materia, m.flag_canc
            FROM classi_prof_materie cpm, materie m
            WHERE cpm.id_materia = m.id_materia
                AND cpm.id_materia = {$id_materia}
            UNION
            SELECT 0 AS sync_moodle, tipo_materia, flag_canc
            FROM materie
            WHERE id_materia = {$id_materia}";

    $result = pgsql_query($sql) or die("Invalid $sql");
    $materie = pg_fetch_all($result);

    foreach ($materie as $materia) {
        if ($materia['sync_moodle'] == 1 && $materia['flag_canc'] == 0 && $materia['tipo_materia'] != 'CONDOTTA' && $materia['descrizione'] != $descrizione) {
            $data = ['name' => formatta_materia($descrizione)];

            $res = $moodle ? \MT\Mastercom\MoodleSync::updateMateria($id_materia, $data) : null;
        }
    }

    if (isset($res->exception)) {
        return $res->exception;
    } else {

        $tipo_indirizzo_materie = estrai_tipo_indirizzo_materia($id_materia);

        $query = "UPDATE materie SET
                    codice						  = '" . encode($codice) . "',
                    --descrizione					  = '" . encode($descrizione) . "',
                    descrizione_materia_straniera = '" . encode($descrizione_materia_straniera) . "',
                    scritto						  = '{$scritto}',
                    orale						  = '{$orale}',
                    pratico						  = '{$pratico}',
                    unico						  = '{$unico}',
                    ordinamento					  = '{$ordinamento}',
                    in_media_pagelle			  = '{$in_media_pagelle}',
                    tipo_valutazione			  = '{$tipo_valutazione}',
                    tipo_materia				  = '{$tipo_materia}',";

        if (!encode($codice_ministeriale) && encode($descrizione) || $superutente_int == 'SI') {
            $query .= " descrizione			  = '" . encode($descrizione) . "',";
        }

        if ($tipo_indirizzo_materie[0]['tipo_indirizzo'] == '6' || $superutente_int == 'SI') {
            $query .= " codice_ministeriale			  = '" . encode($codice_ministeriale) . "',";
        }

        if ((int)$monteore >= 0)
        {
            $query .= " monteore			  = '" . (int)$monteore . "',";
        }
        $query .= "descrizione_scuola_media	  = '" . encode($descrizione_scuola_media) . "',
                    tipologia_aggregamento		  = '{$tipologia_aggregamento}',
                    tipo_voto_personalizzato	  = '{$tipo_voto_personalizzato}',
                    nome_materia_sito			  = '" . encode($nome_materia_sito) . "',
                    nome_materia_breve			  = '" . encode($nome_materia_breve) . "',
                    id_materia_riferimento        = '" . (int)$id_materia_riferimento . "',
                    id_tipo_corso                 = '" . (int)$id_tipo_corso . "'
                  WHERE id_materia = {$id_materia}";

        pgsql_query($query) or die("Invalid $query");

        inserisci_log(["id_materia" => $id_materia], "materie", $current_user, 'INTERFACCIA', 'MODIFICA');

        return $id_materia;
    }
    //}}} </editor-fold>
}

function elimina_materia($id_materia, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per eliminare una materia">
    $query = "UPDATE materie SET flag_canc = " . time() . "
              WHERE id_materia = {$id_materia}";

    $result = pgsql_query($query) or die("Invalid $query");

    inserisci_log(["id_materia" => $id_materia], "materie", $current_user, "INTERFACCIA", "ELIMINAZIONE");

    return $result;
    //}}} </editor-fold>
}

/**
 * Formatta il nome della materia
 *
 * @param string $materia
 * @return string
 */
function formatta_materia($materia = '') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per formattare la descrizione delle materie">
    $materia = ucwords(mb_strtolower($materia, 'utf8'));

    return str_replace(
            [
        " A ", " Al ", " E ", " Et ", " Ed ", " Di ", " Alla ", " Alle ",
        " Del ", " Della ", "&#039;", "'", "’a", "’e", "’i", "’o", "’u",
        " All’", " Dell’", "D’", " Con "
            ], [
        " a ", " al ", " e ", " et ", " ed ", " di ", " alla ", " alle ",
        " del ", " della ", "’", "’", "’A", "’E", "’I", "’O", "’U",
        " all’", " dell’", "d’", " con "
            ], $materia
    );
    //}}} </editor-fold>
}

//cambiata perchè c'erano due decode sullo stesso dato e dava problemi in stampa pagelline
function estrai_materie_classe($id_classe, $estrai_corsi = "NO") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie di appartenenza di una classe">
    $query = "SELECT
					classi_prof_materie.id_professore,
					classi_prof_materie.id_materia,
					classi_prof_materie.ordinamento as ordinamento_principale,
					--classi_prof_materie.scritto as cpm_scritto,
					--classi_prof_materie.orale as cpm_orale,
					--classi_prof_materie.pratico as cpm_pratico,
					materie.scritto,
					materie.orale,
					materie.pratico,
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					materie.codice_ministeriale,
					materie.codice_itp,
					materie.ordinamento,
					materie.tipo_valutazione,
					materie.tipo_materia,
					materie.descrizione_scuola_media,
					materie.in_media_pagelle,
					materie.tipologia_aggregamento,
					materie.tipo_voto_personalizzato,
					materie.nome_materia_sito,
					materie.nome_materia_breve,
                    materie.id_materia_riferimento
				FROM classi_prof_materie
				INNER JOIN materie ON classi_prof_materie.id_materia = materie.id_materia
				INNER JOIN utenti ON utenti.id_utente = classi_prof_materie.id_professore ";

    if($estrai_corsi == 'SI')
    {
        $where_query = "WHERE classi_prof_materie.id_classe = {$id_classe}
                            AND classi_prof_materie.flag_canc = 0
                            AND utenti.flag_canc = 0
                            AND materie.flag_canc = 0
                        ORDER BY
                            classi_prof_materie.ordinamento,
                            coalesce(cast(nullif(materie.ordinamento,'') as integer),0),
                            materie.descrizione";
    }
    else
    {
        $where_query = "WHERE classi_prof_materie.id_classe = {$id_classe}
                            AND classi_prof_materie.flag_canc = 0
                            AND utenti.flag_canc = 0
                            AND materie.tipo_materia <> 'CORSO'
                            AND materie.flag_canc = 0
                        ORDER BY
                            classi_prof_materie.ordinamento,
                            coalesce(cast(nullif(materie.ordinamento,'') as integer),0),
                            materie.descrizione";
    }

    $query .= $where_query;

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $id_materia = pg_fetch_result($result, $cont, "id_materia");
            $dati_materie_temp[$id_materia][0] = $id_materia;
            $dati_materie_temp[$id_materia][1] = decode(pg_fetch_result($result, $cont, "codice"));
            $dati_materie_temp[$id_materia][2] = decode(pg_fetch_result($result, $cont, "descrizione"));
            $dati_materie_temp[$id_materia][3] = decode(pg_fetch_result($result, $cont, "codice_itp"));
            $dati_materie_temp[$id_materia]["id_materia"] = pg_fetch_result($result, $cont, "id_materia");
            $dati_materie_temp[$id_materia]["id_classe"] = $id_classe;
            $dati_materie_temp[$id_materia]["scritto"] = pg_fetch_result($result, $cont, "scritto");
            $dati_materie_temp[$id_materia]["orale"] = pg_fetch_result($result, $cont, "orale");
            $dati_materie_temp[$id_materia]["pratico"] = pg_fetch_result($result, $cont, "pratico");

            $cpm_tipi_voto = estrai_tipi_voto_unificati($id_materia, [$id_classe]);
//            $dati_materie_temp[$id_materia]["cpm_scritto"] = pg_fetch_result($result, $cont, "cpm_scritto");
//            $dati_materie_temp[$id_materia]["cpm_orale"] = pg_fetch_result($result, $cont, "cpm_orale");
//            $dati_materie_temp[$id_materia]["cpm_pratico"] = pg_fetch_result($result, $cont, "cpm_pratico");
            $dati_materie_temp[$id_materia]["cpm_scritto"] = $cpm_tipi_voto['scritto'];
            $dati_materie_temp[$id_materia]["cpm_orale"] = $cpm_tipi_voto['orale'];
            $dati_materie_temp[$id_materia]["cpm_pratico"] = $cpm_tipi_voto['pratico'];

            $dati_materie_temp[$id_materia]["descrizione"] = decode(pg_fetch_result($result, $cont, "descrizione"));
            $dati_materie_temp[$id_materia]["descrizione_materia_straniera"] = decode(pg_fetch_result($result, $cont, "descrizione_materia_straniera"));
            $dati_materie_temp[$id_materia]["tipo_valutazione"] = pg_fetch_result($result, $cont, "tipo_valutazione");
            $dati_materie_temp[$id_materia]["codice"] = decode(pg_fetch_result($result, $cont, "codice"));
            $dati_materie_temp[$id_materia]["codice_ministeriale"] = decode(pg_fetch_result($result, $cont, "codice_ministeriale"));
            $dati_materie_temp[$id_materia]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
            $dati_materie_temp[$id_materia]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $dati_materie_temp[$id_materia]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
            $dati_materie_temp[$id_materia]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
            $dati_materie_temp[$id_materia]["tipo_voto_personalizzato"] = pg_fetch_result($result, $cont, "tipo_voto_personalizzato");
            $dati_materie_temp[$id_materia]["nome_materia_sito"] = decode(pg_fetch_result($result, $cont, "nome_materia_sito"));
            $dati_materie_temp[$id_materia]["nome_materia_breve"] = decode(pg_fetch_result($result, $cont, "nome_materia_breve"));
            $dati_materie_temp[$id_materia]["ordinamento"] = pg_fetch_result($result, $cont, "ordinamento");
            $dati_materie_temp[$id_materia]["ordinamento_principale"] = pg_fetch_result($result, $cont, "ordinamento_principale");
            $dati_materie_temp[$id_materia]["id_materia_riferimento"] = pg_fetch_result($result, $cont, "id_materia_riferimento");
            $dati_materie_temp[$id_materia]["professori"][] = pg_fetch_result($result, $cont, "id_professore");
        }

        $cont = 0;

        foreach ($dati_materie_temp as $materia) {
            $dati_materie[$cont][0] = $materia[0];
            $dati_materie[$cont][1] = $materia[1];
            $dati_materie[$cont][2] = $materia[2];
            $dati_materie[$cont][3] = $materia[3];
            $dati_materie[$cont]["id_materia"] = $materia["id_materia"];
            $dati_materie[$cont]["id_classe"] = $materia["id_classe"];
            $dati_materie[$cont]["scritto"] = $materia["scritto"];
            $dati_materie[$cont]["orale"] = $materia["orale"];
            $dati_materie[$cont]["pratico"] = $materia["pratico"];
            $dati_materie[$cont]["descrizione"] = $materia["descrizione"];
            $dati_materie[$cont]["descrizione_materia_straniera"] = $materia["descrizione_materia_straniera"];
            $dati_materie[$cont]["tipo_valutazione"] = $materia["tipo_valutazione"];
            $dati_materie[$cont]["codice"] = $materia["codice"];
            $dati_materie[$cont]["codice_ministeriale"] = $materia["codice_ministeriale"];
            $dati_materie[$cont]["cpm_scritto"] = $materia["cpm_scritto"];
            $dati_materie[$cont]["cpm_orale"] = $materia["cpm_orale"];
            $dati_materie[$cont]["cpm_pratico"] = $materia["cpm_pratico"];
            $dati_materie[$cont]["tipo_materia"] = $materia["tipo_materia"];
            $dati_materie[$cont]["in_media_pagelle"] = $materia["in_media_pagelle"];
            $dati_materie[$cont]["descrizione_scuola_media"] = $materia["descrizione_scuola_media"];
            $dati_materie[$cont]["tipologia_aggregamento"] = $materia["tipologia_aggregamento"];
            $dati_materie[$cont]["tipo_voto_personalizzato"] = $materia["tipo_voto_personalizzato"];
            $dati_materie[$cont]["nome_materia_sito"] = $materia["nome_materia_sito"];
            $dati_materie[$cont]["nome_materia_breve"] = $materia["nome_materia_breve"];
            $dati_materie[$cont]["ordinamento"] = $materia["ordinamento"];
            $dati_materie[$cont]["ordinamento_principale"] = $materia["ordinamento_principale"];
            $dati_materie[$cont]["id_materia_riferimento"] = $materia["id_materia_riferimento"];
            $dati_materie[$cont]["professori"] = $materia["professori"];
            $cont++;
        }
    }

    // Ordinamento per: Ordinamento in classi_prof_materie, ordinamento in materie
    $ord_arr = [];
    foreach ($dati_materie as $key => $materia) {
        $ord_arr['ordinamento_principale'][$key] = $materia['ordinamento_principale'];
        $ord_arr['ordinamento'][$key] = $materia['ordinamento'];
    }
    array_multisort($ord_arr['ordinamento_principale'], SORT_ASC, $ord_arr['ordinamento'], SORT_ASC, $dati_materie);

    return $dati_materie;
    //}}} </editor-fold>
}

function estrai_materie_multi_classe($id_classe, $id_materia = null, $ordinamento = 'ordinamento', $non_definita = null, $orario_classe = null, $inclusione_corsi = null) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie di appartenenza di una classe">
    // $elenco_studenti_classe = estrai_studenti_classe($id_classe);
    $elenco_studenti_classe = estrai_studenti_classe_registro($id_classe, null, false, 'rapida'); // piu' veloce della precedente

    if (is_array($elenco_studenti_classe) && count($elenco_studenti_classe) > 0) {
        $where_cond = "(";
        $studente_sostegno = false;

        foreach ($elenco_studenti_classe as $studente) {
            $where_cond .= "classi_studenti.id_studente = {$studente["id_studente"]} or ";

            if ($studente['necessita_sostegno'] == 1)
            {
                $studente_sostegno = true;
            }
        }

        if ( is_null($inclusione_corsi) ) {
            $where_cond = substr($where_cond, 0, -4) . ") AND classi_studenti.flag_canc = 0 AND classi_studenti.id_classe IN (SELECT id_classe FROM classi_complete where ordinamento != 'CORSO')";
            $where_corsi = "AND materie.tipo_materia <> 'CORSO'";
        }
        else {
            $where_cond = substr($where_cond, 0, -4) . ") AND classi_studenti.flag_canc = 0 ";
            $where_corsi = " ";
        }

        $query = "SELECT DISTINCT classi_studenti.id_classe
                    FROM classi_studenti, classi
                    WHERE {$where_cond}";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);
        $where_cond_classi = "(";

        $lista_classi = [];

        if ($numero > 0) {
            for ($cont = 0; $cont < $numero; $cont++) {
                $where_cond_classi .= "classi_prof_materie.id_classe=" . pg_fetch_result($result, $cont, "id_classe") . " OR ";
                $lista_classi[] = pg_fetch_result($result, $cont, "id_classe");
            }
        }

        $lista_classi[] = $id_classe;
        $where_cond_classi = substr($where_cond_classi, 0, -4) . ") ";

        if ($orario_classe == 'orario_classe') {
            $where_cond_classi = "classi_prof_materie.id_classe={$id_classe}";
        }

        $where_sostegno = (!$studente_sostegno) ? " AND materie.tipo_materia != 'SOSTEGNO' " : "";

        //Questo caso mi serve per estrarre i dati di una singola materia in formato compatibile alle pagelline

        if ($id_materia > 0) {
            $where_cond_classi .= " AND classi_prof_materie.id_materia = {$id_materia}";
        }

        $query = "SELECT
                        classi_prof_materie.id_professore,
                        classi_prof_materie.id_materia,
                        classi_prof_materie.id_classe,
                        classi_prof_materie.ordinamento AS ordinamento_principale,
                        utenti.cognome,
                        utenti.nome,
                        materie.scritto,
                        materie.orale,
                        materie.pratico,
                        materie.codice,
                        materie.codice_ministeriale,
                        materie.descrizione,
                        materie.descrizione_materia_straniera,
                        materie.codice_itp,
                        materie.ordinamento,
                        materie.tipo_valutazione,
                        materie.tipo_materia,
                        materie.descrizione_scuola_media,
                        materie.in_media_pagelle,
                        materie.tipologia_aggregamento,
                        materie.tipo_voto_personalizzato,
                        materie.nome_materia_sito,
                        materie.nome_materia_breve
                FROM classi_prof_materie
                INNER JOIN materie ON classi_prof_materie.id_materia = materie.id_materia
                INNER JOIN utenti ON utenti.id_utente = classi_prof_materie.id_professore
                WHERE {$where_cond_classi}
                        AND classi_prof_materie.flag_canc = 0
                        AND utenti.flag_canc = 0
                        {$where_corsi}
                        {$where_sostegno}
                        AND materie.flag_canc = 0";

        if ($ordinamento == 'ordinamento') {
            $query .= "
                        ORDER BY
                                classi_prof_materie.ordinamento,
                                coalesce(cast(nullif(materie.ordinamento,'') as integer),0),
                                materie.descrizione,
                                classi_prof_materie.itp DESC";
        } else {
            $query .= " ORDER BY materie.descrizione";
        }

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            for ($cont = 0; $cont < $numero; $cont++) {
                $id_materia = pg_fetch_result($result, $cont, "id_materia");
                $dati_materie_temp[$id_materia][0] = $id_materia;
                $dati_materie_temp[$id_materia][1] = decode(pg_fetch_result($result, $cont, "codice"));
                $dati_materie_temp[$id_materia][2] = decode(pg_fetch_result($result, $cont, "descrizione"));
                $dati_materie_temp[$id_materia][3] = decode(pg_fetch_result($result, $cont, "codice_itp"));
                $dati_materie_temp[$id_materia]["id_materia"] = pg_fetch_result($result, $cont, "id_materia");
                $dati_materie_temp[$id_materia]["id_classe"] = $id_classe;
                $id_classe_orig = intval(pg_fetch_result($result, $cont, "id_classe"));

                if ($id_classe_orig != intval($id_classe)) {
                    $dati_materie_temp[$id_materia]["mat_classi_orig"][$id_classe_orig] = $id_classe_orig;
                }else{
                    $dati_materie_temp[$id_materia]["mat_classi_abb"][$id_classe_orig] = $id_classe_orig;
                }

                $dati_materie_temp[$id_materia]["scritto"] = pg_fetch_result($result, $cont, "scritto");
                $dati_materie_temp[$id_materia]["orale"] = pg_fetch_result($result, $cont, "orale");
                $dati_materie_temp[$id_materia]["pratico"] = pg_fetch_result($result, $cont, "pratico");

                $cpm_tipi_voto = estrai_tipi_voto_unificati($id_materia, $lista_classi);
                $dati_materie_temp[$id_materia]["cpm_scritto"] = $cpm_tipi_voto['scritto'];
                $dati_materie_temp[$id_materia]["cpm_orale"] = $cpm_tipi_voto['orale'];
                $dati_materie_temp[$id_materia]["cpm_pratico"] = $cpm_tipi_voto['pratico'];

                $dati_materie_temp[$id_materia]["descrizione"] = decode(pg_fetch_result($result, $cont, "descrizione"));
                $dati_materie_temp[$id_materia]["descrizione_materia_straniera"] = decode(pg_fetch_result($result, $cont, "descrizione_materia_straniera"));
                $dati_materie_temp[$id_materia]["tipo_valutazione"] = pg_fetch_result($result, $cont, "tipo_valutazione");
                $dati_materie_temp[$id_materia]["codice"] = decode(pg_fetch_result($result, $cont, "codice"));
                $dati_materie_temp[$id_materia]["codice_ministeriale"] = decode(pg_fetch_result($result, $cont, "codice_ministeriale"));
                $dati_materie_temp[$id_materia]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
                $dati_materie_temp[$id_materia]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
                $dati_materie_temp[$id_materia]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
                $dati_materie_temp[$id_materia]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
                $dati_materie_temp[$id_materia]["tipo_voto_personalizzato"] = pg_fetch_result($result, $cont, "tipo_voto_personalizzato");
                $dati_materie_temp[$id_materia]["nome_materia_sito"] = decode(pg_fetch_result($result, $cont, "nome_materia_sito"));
                $dati_materie_temp[$id_materia]["nome_materia_breve"] = decode(pg_fetch_result($result, $cont, "nome_materia_breve"));
                $dati_materie_temp[$id_materia]["ordinamento_principale"] = pg_fetch_result($result, $cont, "ordinamento_principale");
                $dati_materie_temp[$id_materia]["ordinamento"] = pg_fetch_result($result, $cont, "ordinamento");
                $dati_materie_temp[$id_materia]["professori"][] = pg_fetch_result($result, $cont, "id_professore");
                $dati_materie_temp[$id_materia]["nomi_professori"][] = decode(pg_fetch_result($result, $cont, "cognome") . ' ' . pg_fetch_result($result, $cont, "nome"));
            }

            $cont = 0;

            foreach ($dati_materie_temp as $materia) {
                $dati_materie[$cont][0] = $materia[0];
                $dati_materie[$cont][1] = $materia[1];
                $dati_materie[$cont][2] = $materia[2];
                $dati_materie[$cont][3] = $materia[3];
                $dati_materie[$cont]["id_materia"] = $materia["id_materia"];
                $dati_materie[$cont]["id_classe"] = $materia["id_classe"];
                $dati_materie[$cont]["scritto"] = $materia["scritto"];
                $dati_materie[$cont]["orale"] = $materia["orale"];
                $dati_materie[$cont]["pratico"] = $materia["pratico"];
                $dati_materie[$cont]["cpm_scritto"] = $materia["cpm_scritto"];
                $dati_materie[$cont]["cpm_orale"] = $materia["cpm_orale"];
                $dati_materie[$cont]["cpm_pratico"] = $materia["cpm_pratico"];
                $dati_materie[$cont]["descrizione"] = $materia["descrizione"];
                $dati_materie[$cont]["descrizione_materia_straniera"] = $materia["descrizione_materia_straniera"];
                $dati_materie[$cont]["tipo_valutazione"] = $materia["tipo_valutazione"];
                $dati_materie[$cont]["codice"] = $materia["codice"];
                $dati_materie[$cont]["codice_ministeriale"] = $materia["codice_ministeriale"];
                $dati_materie[$cont]["tipo_materia"] = $materia["tipo_materia"];
                $dati_materie[$cont]["in_media_pagelle"] = $materia["in_media_pagelle"];
                $dati_materie[$cont]["descrizione_scuola_media"] = $materia["descrizione_scuola_media"];
                $dati_materie[$cont]["tipologia_aggregamento"] = $materia["tipologia_aggregamento"];
                $dati_materie[$cont]["tipo_voto_personalizzato"] = $materia["tipo_voto_personalizzato"];
                $dati_materie[$cont]["nome_materia_sito"] = $materia["nome_materia_sito"];
                $dati_materie[$cont]["nome_materia_breve"] = $materia["nome_materia_breve"];
                $dati_materie[$cont]["professori"] = $materia["professori"];
                $dati_materie[$cont]["ordinamento_principale"] = $materia["ordinamento_principale"];
                $dati_materie[$cont]["ordinamento"] = $materia["ordinamento"];
                $dati_materie[$cont]["nomi_professori"] = $materia["nomi_professori"];
                $dati_materie[$cont]["mat_classi_orig"] = $materia["mat_classi_orig"];
                $dati_materie[$cont]["mat_classi_abb"] = $materia["mat_classi_abb"];
                $cont++;
            }
        }
    }


    // Ordinamento per: Ordinamento in classi_prof_materie, ordinamento in materie
    $ord_arr = [];
    foreach ($dati_materie as $key => $materia) {
        $ord_arr['ordinamento_principale'][$key] = $materia['ordinamento_principale'];
        $ord_arr['ordinamento'][$key] = $materia['ordinamento'];
    }
    array_multisort($ord_arr['ordinamento_principale'], SORT_ASC, $ord_arr['ordinamento'], SORT_ASC, $dati_materie);

    return $dati_materie;
    //}}} </editor-fold>
}

function estrai_materie_multi_classe_studente($id_studente, $ordinamento = 'ordinamento', $corsi = 'NO') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie di appartenenza di una classe">
    $studente_sostegno = verifica_se_studente_sostegno($id_studente);

    $query = "SELECT DISTINCT classi_studenti.id_classe
              FROM classi_studenti
              INNER JOIN classi_complete ON classi_studenti.id_classe = classi_complete.id_classe
              WHERE classi_studenti.flag_canc = 0
                AND classi_studenti.id_studente = {$id_studente}";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    $dati_materie = [];
    if($corsi == 'SI')
    {
        $where_cond_corsi = " ";
    }
    else
    {
        $where_cond_corsi = " AND classi.ordinamento != 'CORSO' ";
    }

    if ($numero > 0) {

        $lista_classi = [];
        $where_cond_classi = "(";
        for ($cont = 0; $cont < $numero; $cont++) {
            $where_cond_classi .= "classi_prof_materie.id_classe = " . pg_fetch_result($result, $cont, "id_classe") . " or ";
            $lista_classi[] = pg_fetch_result($result, $cont, "id_classe");
        }

        $where_cond_classi = substr($where_cond_classi, 0, -4) . ") ";

        $query = "SELECT DISTINCT
						classi_prof_materie.id_professore,
						classi_prof_materie.id_materia,
						classi_prof_materie.id_classe,
						classi_prof_materie.id_classe AS classId, -- Per il Quaderno
						classi_prof_materie.ordinamento as ordinamento_principale,
                        --classi_prof_materie.scritto AS cpm_scritto,
						--classi_prof_materie.orale AS cpm_orale,
						--classi_prof_materie.pratico AS cpm_pratico,
						utenti.cognome,
						utenti.nome,
						materie.scritto,
						materie.orale,
						materie.pratico,
						materie.codice,
						materie.codice_ministeriale,
						materie.descrizione,
						materie.descrizione_materia_straniera,
						materie.codice_itp,
						coalesce(cast(nullif(materie.ordinamento,'') as integer),0) AS ordinamento,
						materie.tipo_valutazione,
						materie.tipo_materia,
						materie.descrizione_scuola_media,
						materie.in_media_pagelle,
						materie.tipologia_aggregamento,
						materie.tipo_voto_personalizzato,
						materie.nome_materia_sito,
						materie.nome_materia_breve,
                        materie.id_materia_riferimento,
                        classi.ordinamento,
						classi_prof_materie.itp
					FROM classi_prof_materie
                    INNER JOIN materie ON classi_prof_materie.id_materia = materie.id_materia
                    INNER JOIN utenti ON utenti.id_utente = classi_prof_materie.id_professore
                    INNER JOIN classi ON classi.id_classe = classi_prof_materie.id_classe
					WHERE {$where_cond_classi}
						AND classi_prof_materie.flag_canc = 0
						AND utenti.flag_canc = 0
						AND materie.flag_canc = 0
                        {$where_cond_corsi}
                    ";

        if ($ordinamento == 'ordinamento') {
            $query .= " ORDER BY
                        classi.ordinamento,
						classi_prof_materie.ordinamento,
						coalesce(cast(nullif(materie.ordinamento,'') as integer),0),
						materie.descrizione";
        } else {
            $query .= " ORDER BY materie.descrizione";
        }

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);
        $materia_inserita = [];
        if ($numero > 0) {
            for ($cont = 0; $cont < $numero; $cont++) {

                $id_materia = pg_fetch_result($result, $cont, "id_materia");
                $itp_materia = pg_fetch_result($result, $cont, "itp");
                $tipo_materia = pg_fetch_result($result, $cont, "tipo_materia");

                if ($tipo_materia != 'SOSTEGNO' || ($tipo_materia == 'SOSTEGNO' && $studente_sostegno == 'SI'))
                {
                    if (!in_array($id_materia, $materia_inserita) || $itp_materia == 'NO') {
                        $dati_materie_temp[$id_materia][0] = $id_materia;
                        $dati_materie_temp[$id_materia][1] = decode(pg_fetch_result($result, $cont, "codice"));
                        $dati_materie_temp[$id_materia][2] = decode(pg_fetch_result($result, $cont, "descrizione"));
                        $dati_materie_temp[$id_materia][3] = decode(pg_fetch_result($result, $cont, "codice_itp"));
                        $dati_materie_temp[$id_materia]["id_materia"] = pg_fetch_result($result, $cont, "id_materia");
                        $dati_materie_temp[$id_materia]["id_classe"] = $id_classe;
                        $dati_materie_temp[$id_materia]["classId"] = pg_fetch_result($result, $cont, "classId");
                        $id_classe_orig = intval(pg_fetch_result($result, $cont, "id_classe"));

                        if ($id_classe_orig != intval($id_classe)) {
                            $dati_materie_temp[$id_materia]["mat_classi_orig"][$id_classe_orig] = $id_classe_orig;
                        }

                        $dati_materie_temp[$id_materia]["scritto"] = pg_fetch_result($result, $cont, "scritto");
                        $dati_materie_temp[$id_materia]["orale"] = pg_fetch_result($result, $cont, "orale");
                        $dati_materie_temp[$id_materia]["pratico"] = pg_fetch_result($result, $cont, "pratico");

                        $cpm_tipi_voto = estrai_tipi_voto_unificati($id_materia, $lista_classi);
    //                    $dati_materie_temp[$id_materia]["cpm_scritto"] = pg_fetch_result($result, $cont, "cpm_scritto");
    //                    $dati_materie_temp[$id_materia]["cpm_orale"] = pg_fetch_result($result, $cont, "cpm_orale");
    //                    $dati_materie_temp[$id_materia]["cpm_pratico"] = pg_fetch_result($result, $cont, "cpm_pratico");
                        $dati_materie_temp[$id_materia]["cpm_scritto"] = $cpm_tipi_voto['scritto'];
                        $dati_materie_temp[$id_materia]["cpm_orale"] = $cpm_tipi_voto['orale'];
                        $dati_materie_temp[$id_materia]["cpm_pratico"] = $cpm_tipi_voto['pratico'];

                        $dati_materie_temp[$id_materia]["descrizione"] = pg_fetch_result($result, $cont, "descrizione");
                        $dati_materie_temp[$id_materia]["descrizione_materia_straniera"] = pg_fetch_result($result, $cont, "descrizione_materia_straniera");
                        $dati_materie_temp[$id_materia]["tipo_valutazione"] = pg_fetch_result($result, $cont, "tipo_valutazione");
                        $dati_materie_temp[$id_materia]["codice"] = pg_fetch_result($result, $cont, "codice");
                        $dati_materie_temp[$id_materia]["codice_ministeriale"] = pg_fetch_result($result, $cont, "codice_ministeriale");
                        $dati_materie_temp[$id_materia]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
                        $dati_materie_temp[$id_materia]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
                        $dati_materie_temp[$id_materia]["descrizione_scuola_media"] = pg_fetch_result($result, $cont, "descrizione_scuola_media");
                        $dati_materie_temp[$id_materia]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
                        $dati_materie_temp[$id_materia]["tipo_voto_personalizzato"] = pg_fetch_result($result, $cont, "tipo_voto_personalizzato");
                        $dati_materie_temp[$id_materia]["nome_materia_sito"] = pg_fetch_result($result, $cont, "nome_materia_sito");
                        $dati_materie_temp[$id_materia]["nome_materia_breve"] = pg_fetch_result($result, $cont, "nome_materia_breve");
                        $dati_materie_temp[$id_materia]["ordinamento_principale"] = pg_fetch_result($result, $cont, "ordinamento_principale");
                        $dati_materie_temp[$id_materia]["ordinamento"] = pg_fetch_result($result, $cont, "ordinamento");
                        $dati_materie_temp[$id_materia]["itp"] = pg_fetch_result($result, $cont, "itp");
                        $dati_materie_temp[$id_materia]["id_materia_riferimento"] = pg_fetch_result($result, $cont, "id_materia_riferimento");
                        $dati_materie_temp[$id_materia]["professori"][] = pg_fetch_result($result, $cont, "id_professore");
                        $dati_materie_temp[$id_materia]["nomi_professori"][] = pg_fetch_result($result, $cont, "cognome") . ' ' . pg_fetch_result($result, $cont, "nome");

                        $materia_inserita[] = $id_materia;
                    }
                }
            }

            $cont = 0;

            foreach ($dati_materie_temp as $materia) {
                    $dati_materie[$cont][0] = $materia[0];
                    $dati_materie[$cont][1] = $materia[1];
                    $dati_materie[$cont][2] = $materia[2];
                    $dati_materie[$cont][3] = $materia[3];
                    $dati_materie[$cont]["id_materia"] = $materia["id_materia"];
                    $dati_materie[$cont]["id_classe"] = $materia["id_classe"];
                    $dati_materie[$cont]["classId"] = $materia["classId"];
                    $dati_materie[$cont]["scritto"] = $materia["scritto"];
                    $dati_materie[$cont]["orale"] = $materia["orale"];
                    $dati_materie[$cont]["pratico"] = $materia["pratico"];
                    $dati_materie[$cont]["cpm_scritto"] = $materia["cpm_scritto"];
                    $dati_materie[$cont]["cpm_orale"] = $materia["cpm_orale"];
                    $dati_materie[$cont]["cpm_pratico"] = $materia["cpm_pratico"];
                    $dati_materie[$cont]["descrizione"] = decode($materia["descrizione"]);
                    $dati_materie[$cont]["descrizione_materia_straniera"] = decode($materia["descrizione_materia_straniera"]);
                    $dati_materie[$cont]["tipo_valutazione"] = $materia["tipo_valutazione"];
                    $dati_materie[$cont]["codice"] = decode($materia["codice"]);
                    $dati_materie[$cont]["codice_ministeriale"] = decode($materia["codice_ministeriale"]);
                    $dati_materie[$cont]["tipo_materia"] = $materia["tipo_materia"];
                    $dati_materie[$cont]["in_media_pagelle"] = $materia["in_media_pagelle"];
                    $dati_materie[$cont]["descrizione_scuola_media"] = decode($materia["descrizione_scuola_media"]);
                    $dati_materie[$cont]["tipologia_aggregamento"] = $materia["tipologia_aggregamento"];
                    $dati_materie[$cont]["tipo_voto_personalizzato"] = $materia["tipo_voto_personalizzato"];
                    $dati_materie[$cont]["nome_materia_sito"] = $materia["nome_materia_sito"];
                    $dati_materie[$cont]["nome_materia_breve"] = $materia["nome_materia_breve"];
                    $dati_materie[$cont]["ordinamento_principale"] = $materia["ordinamento_principale"];
                    $dati_materie[$cont]["ordinamento"] = $materia["ordinamento"];
                    $dati_materie[$cont]["itp"] = $materia["itp"];
                    $dati_materie[$cont]["id_materia_riferimento"] = $materia["id_materia_riferimento"];
                    $dati_materie[$cont]["professori"] = $materia["professori"];
                    $dati_materie[$cont]["nomi_professori"] = decode($materia["nomi_professori"]);
                    $dati_materie[$cont]["mat_classi_orig"] = $materia["mat_classi_orig"];
                    $cont++;
                }
            }
        }

    // Ordinamento per: Ordinamento in classi_prof_materie, ordinamento in materie
    $ord_arr = [];
    foreach ($dati_materie as $key => $materia) {
        $ord_arr['ordinamento_principale'][$key] = $materia['ordinamento_principale'];
        $ord_arr['ordinamento'][$key] = $materia['ordinamento'];
    }
    array_multisort($ord_arr['ordinamento_principale'], SORT_ASC, $ord_arr['ordinamento'], SORT_ASC, $dati_materie);

    return $dati_materie;
    //}}} </editor-fold>
}

function estrai_materie_classe_itp($id_classe) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie di appartenenza di una classe">
    $query = "SELECT DISTINCT
					classi_prof_materie.id_materia,
					classi_prof_materie.ordinamento as ordinamento_principale,
					materie.scritto,
					materie.orale,
					materie.pratico,
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					materie.codice_itp,
					materie.ordinamento,
					materie.tipo_materia,
					materie.descrizione_scuola_media,
					materie.in_media_pagelle,
					materie.tipologia_aggregamento
				FROM classi_prof_materie
                INNER JOIN materie ON classi_prof_materie.id_materia = materie.id_materia
				WHERE classi_prof_materie.id_classe = " . $id_classe . "
					AND materie.flag_canc = 0
					AND classi_prof_materie.flag_canc = 0
				ORDER BY
					classi_prof_materie.ordinamento,
					materie.ordinamento,
					materie.descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $cont2 = 0;
        for ($cont = 0; $cont < $numero; $cont++) {
            $id_materia = pg_fetch_result($result, $cont, "id_materia");
            $dati_materie[$cont2]["id_materia"] = $id_materia;
            $dati_materie[$cont2]["scritto"] = pg_fetch_result($result, $cont, "scritto");
            $dati_materie[$cont2]["orale"] = pg_fetch_result($result, $cont, "orale");
            $dati_materie[$cont2]["pratico"] = pg_fetch_result($result, $cont, "pratico");
            $dati_materie[$cont2][0] = pg_fetch_result($result, $cont, "id_materia");
            $dati_materie[$cont2][1] = pg_fetch_result($result, $cont, "codice");
            $dati_materie[$cont2][2] = pg_fetch_result($result, $cont, "descrizione");
            $dati_materie[$cont2]['descrizione_materia_straniera'] = pg_fetch_result($result, $cont, "descrizione_materia_straniera");
            $dati_materie[$cont2][3] = pg_fetch_result($result, $cont, "codice_itp");
            $dati_materie[$cont2][7] = "materia_principale";
            $dati_materie[$cont2]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
            $dati_materie[$cont2]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $dati_materie[$cont2]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
            $dati_materie[$cont2]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");

            $dati_materie[$cont2]["obiettivi"] = check_abbinamenti_competenze_obiettivi($id_materia, $id_classe, "OBIETTIVI_VOTI");
            $dati_materie[$cont2]["competenze"] = check_abbinamenti_competenze_obiettivi($id_materia, $id_classe, "COMPETENZE_VOTI");

            if ($dati_materie[$cont2][3] != "") {
                $cont2 = $cont2 + 1;
                $dati_materie[$cont2][0] = pg_fetch_result($result, $cont, "id_materia");
                $dati_materie[$cont2][1] = pg_fetch_result($result, $cont, "codice");
                $dati_materie[$cont2][2] = pg_fetch_result($result, $cont, "descrizione") . " ITP";
                $dati_materie[$cont2]['descrizione_materia_straniera'] = pg_fetch_result($result, $cont, "descrizione_materia_straniera") . " ITP";
                $dati_materie[$cont2][3] = pg_fetch_result($result, $cont, "codice_itp");
                $dati_materie[$cont2][7] = "materia_itp";
                $dati_materie[$cont2]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
            }
            $cont2 = $cont2 + 1;
        }
    }

    return $dati_materie;
    //}}} </editor-fold>
}

//cambiata perchè c'erano due decode sullo stesso dato e dava problemi in stampa pagelline
function estrai_materie_classe_del_professore($id_classe, $id_professore, $includi_corsi = 'NO') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie di appartenenza di una classe">
    $corsi = "";
    if($includi_corsi == 'NO')
    {
        $corsi = " AND materie.tipo_materia <> 'CORSO' ";
    }
    $query = "SELECT
					classi_prof_materie.id_materia,
					classi_prof_materie.id_classe,
					classi_prof_materie.id_professore,
                    --classi_prof_materie.scritto AS cpm_scritto,
                    --classi_prof_materie.orale AS cpm_orale,
                    --classi_prof_materie.pratico AS cpm_pratico,
					classi_prof_materie.ordinamento as ordinamento_principale,
					materie.scritto,
					materie.orale,
					materie.pratico,
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					materie.codice_itp,
					materie.ordinamento,
					materie.tipo_valutazione,
					materie.tipo_materia,
					materie.descrizione_scuola_media,
					materie.in_media_pagelle,
					materie.tipologia_aggregamento,
					materie.tipo_voto_personalizzato,
					materie.nome_materia_sito,
					materie.nome_materia_breve
				FROM materie
                INNER JOIN classi_prof_materie ON materie.id_materia = classi_prof_materie.id_materia
				WHERE classi_prof_materie.id_classe = {$id_classe}
					AND classi_prof_materie.id_professore = {$id_professore}
					AND classi_prof_materie.flag_canc = 0
					AND materie.flag_canc = 0
                    {$corsi}
				ORDER BY
					classi_prof_materie.ordinamento,
					coalesce(cast(nullif(materie.ordinamento,'') as integer),0),
					materie.descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $id_materia = pg_fetch_result($result, $cont, "id_materia");
            $materie_temp[$id_materia][0] = $id_materia;
            $materie_temp[$id_materia][1] = decode(pg_fetch_result($result, $cont, "codice"));
            $materie_temp[$id_materia][2] = decode(pg_fetch_result($result, $cont, "descrizione"));
            $materie_temp[$id_materia]['descrizione_materia_straniera'] = pg_fetch_result($result, $cont, "descrizione_materia_straniera");
            $materie_temp[$id_materia][3] = pg_fetch_result($result, $cont, "codice_itp");
            $materie_temp[$id_materia]["id_materia"] = pg_fetch_result($result, $cont, "id_materia");
            $materie_temp[$id_materia]["id_classe"] = $id_classe;
            $id_classe_orig = intval(pg_fetch_result($result, $cont, "id_classe"));

            if ($id_classe_orig != intval($id_classe)) {
                $materie_temp[$id_materia]["mat_classi_orig"][$id_classe_orig] = $id_classe_orig;
            }

            $materie_temp[$id_materia]["scritto"] = pg_fetch_result($result, $cont, "scritto");
            $materie_temp[$id_materia]["orale"] = pg_fetch_result($result, $cont, "orale");
            $materie_temp[$id_materia]["pratico"] = pg_fetch_result($result, $cont, "pratico");

            $cpm_tipi_voto = estrai_tipi_voto_unificati($id_materia, [$id_classe]);
//            $materie_temp[$id_materia]["cpm_scritto"] = pg_fetch_result($result, $cont, "cpm_scritto");
//            $materie_temp[$id_materia]["cpm_orale"] = pg_fetch_result($result, $cont, "cpm_orale");
//            $materie_temp[$id_materia]["cpm_pratico"] = pg_fetch_result($result, $cont, "cpm_pratico");
            $materie_temp[$id_materia]["cpm_scritto"] = $cpm_tipi_voto['scritto'];
            $materie_temp[$id_materia]["cpm_orale"] = $cpm_tipi_voto['orale'];
            $materie_temp[$id_materia]["cpm_pratico"] = $cpm_tipi_voto['pratico'];

            $materie_temp[$id_materia]["descrizione"] = decode(pg_fetch_result($result, $cont, "descrizione"));
            $materie_temp[$id_materia]["tipo_valutazione"] = pg_fetch_result($result, $cont, "tipo_valutazione");
            $materie_temp[$id_materia]["codice"] = decode(pg_fetch_result($result, $cont, "codice"));
            $materie_temp[$id_materia]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
            $materie_temp[$id_materia]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $materie_temp[$id_materia]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
            $materie_temp[$id_materia]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
            $materie_temp[$id_materia]["tipo_voto_personalizzato"] = pg_fetch_result($result, $cont, "tipo_voto_personalizzato");
            $materie_temp[$id_materia]["nome_materia_sito"] = decode(pg_fetch_result($result, $cont, "nome_materia_sito"));
            $materie_temp[$id_materia]["nome_materia_breve"] = decode(pg_fetch_result($result, $cont, "nome_materia_breve"));
            $materie_temp[$id_materia]["professori"][] = pg_fetch_result($result, $cont, "id_professore");
        }

        $cont = 0;

        foreach ($materie_temp as $materia) {
            $materie[$cont][0] = $materia[0];
            $materie[$cont][1] = $materia[1];
            $materie[$cont][2] = $materia[2];
            $materie[$cont][3] = $materia[3];
            $materie[$cont]["id_materia"] = $materia["id_materia"];
            $materie[$cont]["id_classe"] = $materia["id_classe"];
            $materie[$cont]["scritto"] = $materia["scritto"];
            $materie[$cont]["orale"] = $materia["orale"];
            $materie[$cont]["pratico"] = $materia["pratico"];
            $materie[$cont]["cpm_scritto"] = $materia["cpm_scritto"];
            $materie[$cont]["cpm_orale"] = $materia["cpm_orale"];
            $materie[$cont]["cpm_pratico"] = $materia["cpm_pratico"];
            $materie[$cont]["descrizione"] = $materia["descrizione"];
            $materie[$cont]["descrizione_materia_straniera"] = $materia["descrizione_materia_straniera"];
            $materie[$cont]["tipo_valutazione"] = $materia["tipo_valutazione"];
            $materie[$cont]["codice"] = $materia["codice"];
            $materie[$cont]["tipo_materia"] = $materia["tipo_materia"];
            $materie[$cont]["in_media_pagelle"] = $materia["in_media_pagelle"];
            $materie[$cont]["descrizione_scuola_media"] = $materia["descrizione_scuola_media"];
            $materie[$cont]["tipologia_aggregamento"] = $materia["tipologia_aggregamento"];
            $materie[$cont]["tipo_voto_personalizzato"] = $materia["tipo_voto_personalizzato"];
            $materie[$cont]["nome_materia_sito"] = $materia["nome_materia_sito"];
            $materie[$cont]["nome_materia_breve"] = $materia["nome_materia_breve"];
            $materie[$cont]["professori"] = $materia["professori"];
            $materie[$cont]["mat_classi_orig"] = $materia["mat_classi_orig"];
            $cont++;
        }
    }

    return $materie;
    //}}} </editor-fold>
}

function estrai_materie_multi_classe_del_professore($id_classe = 0, $id_professore = 0) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie di appartenenza di una classe">
    $elenco_studenti_classe =  estrai_studenti_classe_registro($id_classe, null, false);

    if (count($elenco_studenti_classe) > 0)
    {
        $where_cond = "(";
        foreach ($elenco_studenti_classe as $studente) {
            $where_cond .= "classi_studenti.id_studente = " . $studente["id_studente"] . " or ";
        }

        $where_cond = substr($where_cond, 0, -4) . ") and classi.flag_canc = 0 and classi_studenti.flag_canc = 0";

        $query = "SELECT DISTINCT
                        classi_studenti.id_classe
                    FROM
                        classi_studenti,
                        classi
                    WHERE
                        " . $where_cond;

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        $where_cond_classi = "(";
        if ($numero > 0) {
            for ($cont = 0; $cont < $numero; $cont++) {
                $where_cond_classi .= "classi_prof_materie.id_classe = " . pg_fetch_result($result, $cont, "id_classe") . " or ";
            }
        }

        $where_cond_classi = substr($where_cond_classi, 0, -4) . ") ";
    }
    else
    {
        $where_cond_classi = " classi_prof_materie.id_classe = " . $id_classe . " ";
    }

    $dati_classe_verifica = estrai_classe($id_classe);
    if($dati_classe_verifica['ordinamento'] != 'CORSO')
    {
        $where_corso = " AND
                    materie.tipo_materia <> 'CORSO' ";
    }
    else
    {
        $where_corso = " ";
    }

    $query = "SELECT DISTINCT
					classi_prof_materie.id_materia,
					classi_prof_materie.id_classe,
					classi_prof_materie.id_professore,
					--classi_prof_materie.scritto as cpm_scritto,
					--classi_prof_materie.orale as cpm_orale,
					--classi_prof_materie.pratico as cpm_pratico,
					classi_prof_materie.ordinamento as ordinamento_principale,
					materie.scritto,
					materie.orale,
					materie.pratico,
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					materie.codice_itp,
					materie.ordinamento,
					materie.tipo_valutazione,
					materie.tipo_materia,
					materie.descrizione_scuola_media,
					materie.in_media_pagelle,
					materie.tipologia_aggregamento,
					materie.tipo_voto_personalizzato,
					materie.nome_materia_sito,
					materie.nome_materia_breve
				FROM classi_prof_materie
                INNER JOIN materie ON classi_prof_materie.id_materia = materie.id_materia
                INNER JOIN utenti ON utenti.id_utente = classi_prof_materie.id_professore
				WHERE
					" . $where_cond_classi . "
					AND
					classi_prof_materie.flag_canc = 0
					AND
					classi_prof_materie.id_professore=" . $id_professore . "
					AND
					utenti.flag_canc = 0
					AND
                    materie.flag_canc = 0
                    " . $where_corso . "
				ORDER BY
					classi_prof_materie.ordinamento,
					materie.ordinamento,
					materie.descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $id_materia = pg_fetch_result($result, $cont, "id_materia");
            $dati_materie_temp[$id_materia][0] = $id_materia;
            $dati_materie_temp[$id_materia][1] = pg_fetch_result($result, $cont, "codice");
            $dati_materie_temp[$id_materia][2] = pg_fetch_result($result, $cont, "descrizione");
            $dati_materie_temp[$id_materia]['descrizione_materia_straniera'] = pg_fetch_result($result, $cont, "descrizione_materia_straniera");
            $dati_materie_temp[$id_materia][3] = pg_fetch_result($result, $cont, "codice_itp");
            $dati_materie_temp[$id_materia]["id_materia"] = pg_fetch_result($result, $cont, "id_materia");
            $dati_materie_temp[$id_materia]["id_classe"] = $id_classe;
            $id_classe_orig = intval(pg_fetch_result($result, $cont, "id_classe"));
            if ($id_classe_orig != intval($id_classe)) {
                $dati_materie_temp[$id_materia]["mat_classi_orig"][$id_classe_orig] = $id_classe_orig;
            }else{
                $dati_materie_temp[$id_materia]["mat_classi_abb"][$id_classe_orig] = $id_classe_orig;
            }
            $dati_materie_temp[$id_materia]["scritto"] = pg_fetch_result($result, $cont, "scritto");
            $dati_materie_temp[$id_materia]["orale"] = pg_fetch_result($result, $cont, "orale");
            $dati_materie_temp[$id_materia]["pratico"] = pg_fetch_result($result, $cont, "pratico");

            $cpm_tipi_voto = estrai_tipi_voto_unificati($id_materia, [$id_classe]);
//            $dati_materie_temp[$id_materia]["cpm_scritto"] = pg_fetch_result($result, $cont, "cpm_scritto");
//            $dati_materie_temp[$id_materia]["cpm_orale"] = pg_fetch_result($result, $cont, "cpm_orale");
//            $dati_materie_temp[$id_materia]["cpm_pratico"] = pg_fetch_result($result, $cont, "cpm_pratico");
            $dati_materie_temp[$id_materia]["cpm_scritto"] = $cpm_tipi_voto['scritto'];
            $dati_materie_temp[$id_materia]["cpm_orale"] = $cpm_tipi_voto['orale'];
            $dati_materie_temp[$id_materia]["cpm_pratico"] = $cpm_tipi_voto['pratico'];

            $dati_materie_temp[$id_materia]["descrizione"] = pg_fetch_result($result, $cont, "descrizione");
            $dati_materie_temp[$id_materia]["tipo_valutazione"] = pg_fetch_result($result, $cont, "tipo_valutazione");
            $dati_materie_temp[$id_materia]["codice"] = pg_fetch_result($result, $cont, "codice");
            $dati_materie_temp[$id_materia]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
            $dati_materie_temp[$id_materia]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $dati_materie_temp[$id_materia]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
            $dati_materie_temp[$id_materia]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
            $dati_materie_temp[$id_materia]["tipo_voto_personalizzato"] = pg_fetch_result($result, $cont, "tipo_voto_personalizzato");
            $dati_materie_temp[$id_materia]["nome_materia_sito"] = decode(pg_fetch_result($result, $cont, "nome_materia_sito"));
            $dati_materie_temp[$id_materia]["nome_materia_breve"] = decode(pg_fetch_result($result, $cont, "nome_materia_breve"));
            $dati_materie_temp[$id_materia]["professori"][] = pg_fetch_result($result, $cont, "id_professore");
        }
        $cont = 0;
        foreach ($dati_materie_temp as $materia) {
            $dati_materie[$cont][0] = $materia[0];
            $dati_materie[$cont][1] = $materia[1];
            $dati_materie[$cont][2] = $materia[2];
            $dati_materie[$cont][3] = $materia[3];
            $dati_materie[$cont]["id_materia"] = $materia["id_materia"];
            $dati_materie[$cont]["id_classe"] = $materia["id_classe"];
            $dati_materie[$cont]["scritto"] = $materia["scritto"];
            $dati_materie[$cont]["orale"] = $materia["orale"];
            $dati_materie[$cont]["pratico"] = $materia["pratico"];
            $dati_materie[$cont]["cpm_scritto"] = $materia["cpm_scritto"];
            $dati_materie[$cont]["cpm_orale"] = $materia["cpm_orale"];
            $dati_materie[$cont]["cpm_pratico"] = $materia["cpm_pratico"];
            $dati_materie[$cont]["descrizione"] = $materia["descrizione"];
            $dati_materie[$cont]["descrizione_materia_straniera"] = $materia["descrizione_materia_straniera"];
            $dati_materie[$cont]["tipo_valutazione"] = $materia["tipo_valutazione"];
            $dati_materie[$cont]["codice"] = $materia["codice"];
            $dati_materie[$cont]["tipo_materia"] = $materia["tipo_materia"];
            $dati_materie[$cont]["in_media_pagelle"] = $materia["in_media_pagelle"];
            $dati_materie[$cont]["descrizione_scuola_media"] = $materia["descrizione_scuola_media"];
            $dati_materie[$cont]["tipologia_aggregamento"] = $materia["tipologia_aggregamento"];
            $dati_materie[$cont]["tipo_voto_personalizzato"] = $materia["tipo_voto_personalizzato"];
            $dati_materie[$cont]["nome_materia_sito"] = $materia["nome_materia_sito"];
            $dati_materie[$cont]["nome_materia_breve"] = $materia["nome_materia_breve"];
            $dati_materie[$cont]["professori"] = $materia["professori"];
            $dati_materie[$cont]["mat_classi_orig"] = $materia["mat_classi_orig"];
            $dati_materie[$cont]["mat_classi_abb"] = $materia["mat_classi_abb"];
            $cont++;
        }
    }

    return $dati_materie;
    //}}} </editor-fold>
}

function estrai_materie_multi_classe_studente_del_professore($id_studente, $id_professore) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie di uno studente e di un prof">
    $query = "SELECT id_classe
				FROM studenti_completi
				WHERE id_studente =" . $id_studente;

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    $lista_classi = [];
    $where_cond_classi = "(";
    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $where_cond_classi .= "classi_prof_materie.id_classe = " . pg_fetch_result($result, $cont, "id_classe") . " OR ";
            $lista_classi[] = pg_fetch_result($result, $cont, "id_classe");
        }
    }

    $where_cond_classi = substr($where_cond_classi, 0, -4) . ") ";

    $query = "SELECT DISTINCT
					classi_prof_materie.id_materia,
					classi_prof_materie.id_classe,
					classi_prof_materie.id_professore,
					--classi_prof_materie.scritto as cpm_scritto,
					--classi_prof_materie.orale as cpm_orale,
					--classi_prof_materie.pratico as cpm_pratico,
					classi_prof_materie.ordinamento as ordinamento_principale,
					materie.scritto,
					materie.orale,
					materie.pratico,
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					materie.codice_itp,
					materie.ordinamento,
					materie.tipo_valutazione,
					materie.tipo_materia,
					materie.descrizione_scuola_media,
					materie.in_media_pagelle,
					materie.tipologia_aggregamento,
					materie.tipo_voto_personalizzato,
					materie.nome_materia_sito,
					materie.nome_materia_breve
				FROM classi_prof_materie
				INNER JOIN materie ON classi_prof_materie.id_materia = materie.id_materia
				INNER JOIN utenti ON utenti.id_utente = classi_prof_materie.id_professore
				WHERE " . $where_cond_classi . "
					AND classi_prof_materie.flag_canc = 0
					AND classi_prof_materie.id_professore = " . $id_professore . "
					AND utenti.flag_canc = 0
					AND materie.flag_canc = 0
				ORDER BY
					classi_prof_materie.ordinamento,
					materie.ordinamento,
					materie.descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $id_materia = pg_fetch_result($result, $cont, "id_materia");
            $materie_temp[$id_materia][0] = $id_materia;
            $materie_temp[$id_materia][1] = pg_fetch_result($result, $cont, "codice");
            $materie_temp[$id_materia][2] = pg_fetch_result($result, $cont, "descrizione");
            $materie_temp[$id_materia]['descrizione_materia_straniera'] = pg_fetch_result($result, $cont, "descrizione_materia_straniera");
            $materie_temp[$id_materia][3] = pg_fetch_result($result, $cont, "codice_itp");
            $materie_temp[$id_materia]["id_materia"] = pg_fetch_result($result, $cont, "id_materia");
            $materie_temp[$id_materia]["id_classe"] = $id_classe;

            $id_classe_orig = intval(pg_fetch_result($result, $cont, "id_classe"));
            if ($id_classe_orig != intval($id_classe)) {
                $materie_temp[$id_materia]["mat_classi_orig"][$id_classe_orig] = $id_classe_orig;
            }

            $materie_temp[$id_materia]["scritto"] = pg_fetch_result($result, $cont, "scritto");
            $materie_temp[$id_materia]["orale"] = pg_fetch_result($result, $cont, "orale");
            $materie_temp[$id_materia]["pratico"] = pg_fetch_result($result, $cont, "pratico");

            $cpm_tipi_voto = estrai_tipi_voto_unificati($id_materia, $lista_classi);
//            $materie_temp[$id_materia]["cpm_scritto"] = pg_fetch_result($result, $cont, "cpm_scritto");
//            $materie_temp[$id_materia]["cpm_orale"] = pg_fetch_result($result, $cont, "cpm_orale");
//            $materie_temp[$id_materia]["cpm_pratico"] = pg_fetch_result($result, $cont, "cpm_pratico");
            $materie_temp[$id_materia]["cpm_scritto"] = $cpm_tipi_voto['scritto'];
            $materie_temp[$id_materia]["cpm_orale"] = $cpm_tipi_voto['orale'];
            $materie_temp[$id_materia]["cpm_pratico"] = $cpm_tipi_voto['pratico'];

            $materie_temp[$id_materia]["descrizione"] = pg_fetch_result($result, $cont, "descrizione");
            $materie_temp[$id_materia]["tipo_valutazione"] = pg_fetch_result($result, $cont, "tipo_valutazione");
            $materie_temp[$id_materia]["codice"] = pg_fetch_result($result, $cont, "codice");
            $materie_temp[$id_materia]["tipo_materia"] = pg_fetch_result($result, $cont, "tipo_materia");
            $materie_temp[$id_materia]["in_media_pagelle"] = pg_fetch_result($result, $cont, "in_media_pagelle");
            $materie_temp[$id_materia]["descrizione_scuola_media"] = decode(pg_fetch_result($result, $cont, "descrizione_scuola_media"));
            $materie_temp[$id_materia]["tipologia_aggregamento"] = pg_fetch_result($result, $cont, "tipologia_aggregamento");
            $materie_temp[$id_materia]["tipo_voto_personalizzato"] = pg_fetch_result($result, $cont, "tipo_voto_personalizzato");
            $materie_temp[$id_materia]["nome_materia_sito"] = pg_fetch_result($result, $cont, "nome_materia_sito");
            $materie_temp[$id_materia]["nome_materia_breve"] = pg_fetch_result($result, $cont, "nome_materia_breve");
            $materie_temp[$id_materia]["ordinamento_principale"] = pg_fetch_result($result, $cont, "ordinamento_principale");
            $materie_temp[$id_materia]["ordinamento"] = pg_fetch_result($result, $cont, "ordinamento");
            $materie_temp[$id_materia]["professori"][] = pg_fetch_result($result, $cont, "id_professore");
        }

        $cont = 0;

        foreach ($materie_temp as $materia) {
            $materie[$cont][0] = $materia[0];
            $materie[$cont][1] = $materia[1];
            $materie[$cont][2] = $materia[2];
            $materie[$cont][3] = $materia[3];
            $materie[$cont]["id_materia"] = $materia["id_materia"];
            $materie[$cont]["id_classe"] = $materia["id_classe"];
            $materie[$cont]["scritto"] = $materia["scritto"];
            $materie[$cont]["orale"] = $materia["orale"];
            $materie[$cont]["pratico"] = $materia["pratico"];
            $materie[$cont]["cpm_scritto"] = $materia["cpm_scritto"];
            $materie[$cont]["cpm_orale"] = $materia["cpm_orale"];
            $materie[$cont]["cpm_pratico"] = $materia["cpm_pratico"];
            $materie[$cont]["descrizione"] = $materia["descrizione"];
            $materie[$cont]["descrizione_materia_straniera"] = $materia["descrizione_materia_straniera"];
            $materie[$cont]["tipo_valutazione"] = $materia["tipo_valutazione"];
            $materie[$cont]["codice"] = $materia["codice"];
            $materie[$cont]["tipo_materia"] = $materia["tipo_materia"];
            $materie[$cont]["in_media_pagelle"] = $materia["in_media_pagelle"];
            $materie[$cont]["descrizione_scuola_media"] = $materia["descrizione_scuola_media"];
            $materie[$cont]["tipologia_aggregamento"] = $materia["tipologia_aggregamento"];
            $materie[$cont]["tipo_voto_personalizzato"] = $materia["tipo_voto_personalizzato"];
            $materie[$cont]["nome_materia_sito"] = $materia["nome_materia_sito"];
            $materie[$cont]["nome_materia_breve"] = $materia["nome_materia_breve"];
            $materie[$cont]["ordinamento_principale"] = $materia["ordinamento_principale"];
            $materie[$cont]["ordinamento"] = $materia["ordinamento"];
            $materie[$cont]["professori"] = $materia["professori"];
            $materie[$cont]["mat_classi_orig"] = $materia["mat_classi_orig"];
            $cont++;
        }
    }

    // Ordinamento per: Ordinamento in classi_prof_materie, ordinamento in materie
    $ord_arr = [];
    foreach ($materie as $key => $materia) {
        $ord_arr['ordinamento_principale'][$key] = $materia['ordinamento_principale'];
        $ord_arr['ordinamento'][$key] = $materia['ordinamento'];
    }
    array_multisort($ord_arr['ordinamento_principale'], SORT_ASC, $ord_arr['ordinamento'], SORT_ASC, $materie);

    return $materie;
    //}}} </editor-fold>
}

function estrai_materie_indirizzi_plugin($ordinamento = "ORDINAMENTO")
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie dell'istituto divise per indirizzo">
    $query = "SELECT DISTINCT i.id_indirizzo,
                    i.descrizione as descrizione_indirizzo,
                    i.tipo_indirizzo, im.codice
                FROM indirizzi i,
                    indirizzi_ministeriali im
                WHERE i.flag_canc = 0
                  AND i.id_codice_ministeriale = im.id_indirizzo
                  AND i.codice NOT ILIKE '%ZZZ%'
                  AND i.codice NOT ILIKE '%ARTICO%'
                ";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    $indirizzi = pg_fetch_all($result);

    if ($numero > 0)
    {
        $elenco_materie_indirizzo = [];

        foreach ($indirizzi as $indirizzo)
        {
            switch ($indirizzo['tipo_indirizzo'])
            {
                case '0':
                case '1':
                case '2':
                case '3':
                case '5':
                    $query = "SELECT DISTINCT id_studente,
                                    id_quadro_orario_sidi
                                FROM studenti_completi
                                WHERE id_indirizzo = {$indirizzo['id_indirizzo']}
                                    AND id_quadro_orario_sidi IS NOT NULL
                              ";

                    $result = pgsql_query($query) or die("Invalid $query");
                    $studenti = pg_fetch_all($result);

                    foreach ($studenti as $studente)
                    {
                        if ($indirizzo['codice'] != 'FAKE')
                        {
                            $query = "SELECT DISTINCT m.*
                                        FROM abbinamento_materie_piani_studio amps,
                                            materie m
                                        WHERE m.descrizione = amps.dati_sidi->'DESCRIZIONE_MATERIA'
                                            AND m.codice_ministeriale <> ''
                                            AND m.flag_canc = 0
                                            AND m.codice_ministeriale = amps.codice_ministeriale
                                            AND amps.id_quadro_orario = {$studente['id_quadro_orario_sidi']}
                                        ORDER BY m.descrizione
                                    ";
                        } else {
                            $query = "SELECT DISTINCT m.*
                                        FROM classi_prof_materie cpm,
                                            materie m
                                        WHERE cpm.id_materia = m.id_materia
                                            AND m.codice_ministeriale <> ''
                                            AND m.flag_canc = 0
                                            AND cpm.id_classe IN (
                                                SELECT DISTINCT id_classe
                                                FROM classi
                                                WHERE id_indirizzo = {$indirizzo['id_indirizzo']}
                                            )
                                        ORDER BY m.descrizione
                                    ";
                        }

                        $result = pgsql_query($query) or die("Invalid $query");
                        $materie_qo = pg_fetch_all($result);

                        foreach ($materie_qo as $materia_qo)
                        {
                            $chiave_controllo = serialize($materia_qo['codice_ministeriale'] . encode($materia_qo['descrizione']) . $materia_qo['id_materia']);
                            if (!array_key_exists($chiave_controllo, $elenco_materie_indirizzo))
                            {
                                $elenco_materie_indirizzo[$indirizzo['id_indirizzo'] . '@' . $indirizzo['descrizione_indirizzo'] . '@' . $indirizzo['tipo_indirizzo']][$chiave_controllo] = $materia_qo;
                            }
                        }
                    }

                    $query = "SELECT DISTINCT m.*
                                FROM classi_prof_materie cpm,
                                    materie m
                                WHERE cpm.id_materia = m.id_materia
                                    AND m.flag_canc = 0
                                    AND cpm.id_classe IN (
                                        SELECT DISTINCT id_classe
                                        FROM classi
                                        WHERE id_indirizzo = {$indirizzo['id_indirizzo']}
                                    )
                                ORDER BY m.descrizione
                              ";

                    $result = pgsql_query($query) or die("Invalid $query");
                    $materie_qo = pg_fetch_all($result);

                    foreach ($materie_qo as $materia_qo)
                    {
                        $chiave_controllo = serialize($materia_qo['codice_ministeriale'] . encode($materia_qo['descrizione']) . ($materia_qo['id_materia']));
                        if (!array_key_exists($chiave_controllo, $elenco_materie_indirizzo))
                        {
                            $elenco_materie_indirizzo[$indirizzo['id_indirizzo'] . '@' . $indirizzo['descrizione_indirizzo'] . '@' . $indirizzo['tipo_indirizzo']][$chiave_controllo] = $materia_qo;
                        }
                    }
                    break;
                case '4':
                    $materie_ps = [
                        //{{{ <editor-fold defaultstate="collapsed" desc="Materie Ministeriali">
                        0  => [
                            'codice_ministeriale'      => '2001',
                            'descrizione_ministeriale' => 'ITALIANO'
                        ],
                        1  => [
                            'codice_ministeriale'      => '2002',
                            'descrizione_ministeriale' => 'SLOVENO'
                        ],
                        2  => [
                            'codice_ministeriale'      => '2003',
                            'descrizione_ministeriale' => 'LINGUA INGLESE'
                        ],
                        3  => [
                            'codice_ministeriale'      => '2004',
                            'descrizione_ministeriale' => 'STORIA'
                        ],
                        4  => [
                            'codice_ministeriale'      => '2005',
                            'descrizione_ministeriale' => 'GEOGRAFIA'
                        ],
                        5  => [
                            'codice_ministeriale'      => '2006',
                            'descrizione_ministeriale' => 'MATEMATICA'
                        ],
                        6  => [
                            'codice_ministeriale'      => '2007',
                            'descrizione_ministeriale' => 'SCIENZE'
                        ],
                        7  => [
                            'codice_ministeriale'      => '2008',
                            'descrizione_ministeriale' => 'TECNOLOGIA'
                        ],
                        8  => [
                            'codice_ministeriale'      => '2010',
                            'descrizione_ministeriale' => 'ARTE E IMMAGINE'
                        ],
                        9  => [
                            'codice_ministeriale'      => '2011',
                            'descrizione_ministeriale' => 'DISCIPLINE PITTORICHE'
                        ],
                        10 => [
                            'codice_ministeriale'      => '2012',
                            'descrizione_ministeriale' => 'DISCIPLINE PLASTICHE'
                        ],
                        11 => [
                            'codice_ministeriale'      => '2013',
                            'descrizione_ministeriale' => 'MUSICA'
                        ],
                        26 => [
                            'codice_ministeriale'      => '2015',
                            'descrizione_ministeriale' => 'FRANCESE'
                        ],
                        27 => [
                            'codice_ministeriale'      => '2015',
                            'descrizione_ministeriale' => 'INGLESE'
                        ],
                        40 => [
                            'codice_ministeriale'      => '2555',
                            'descrizione_ministeriale' => 'SCIENZE MOTORIE E SPORTIVE'
                        ],
                        41 => [
                            'codice_ministeriale'      => '2666',
                            'descrizione_ministeriale' => 'RELIGIONE CATTOLICA/ATTIVITA ALTERNATIVA'
                        ],
                        42 => [
                            'codice_ministeriale'      => '2777',
                            'descrizione_ministeriale' => 'DISCIPLINA AUTONOMIA'
                        ],
                        43 => [
                            'codice_ministeriale'      => '2999',
                            'descrizione_ministeriale' => 'COMPORTAMENTO'
                        ]
                            //}}} </editor-fold>
                    ];

                    foreach ($materie_ps as $materia_ps)
                    {
                        if ($indirizzo['codice'] != 'FAKE')
                        {
                            $query =  "SELECT m.*
                                        FROM materie m
                                        WHERE m.codice_ministeriale = '{$materia_ps['codice_ministeriale']}'
                                            AND m.descrizione = '".encode($materia_ps['descrizione_ministeriale'])."'
                                            AND m.flag_canc = 0
                                        ORDER BY m.codice_ministeriale DESC, m.descrizione ASC";
                        }
                        else
                        {
                            $query = "SELECT DISTINCT m.*
                                        FROM classi_prof_materie cpm,
                                            materie m
                                        WHERE cpm.id_materia = m.id_materia
                                            AND m.flag_canc = 0
                                            AND cpm.id_classe IN (SELECT DISTINCT id_classe FROM classi WHERE id_indirizzo = {$indirizzo['id_indirizzo']})
                                        ORDER BY m.descrizione
                                    ";
                        }

                        $result = pgsql_query($query) or die("Invalid $query");
                        $materie_qo = pg_fetch_all($result);

                        foreach ($materie_qo as $materia_qo)
                        {
                            $chiave_controllo = serialize($materia_qo['codice_ministeriale'] . encode($materia_qo['descrizione']) . ($materia_qo['id_materia']));
                            if (!array_key_exists($chiave_controllo, $elenco_materie_indirizzo))
                            {
                                $elenco_materie_indirizzo[$indirizzo['id_indirizzo'] . '@' . $indirizzo['descrizione_indirizzo'] . '@' . $indirizzo['tipo_indirizzo']][$chiave_controllo] = $materia_qo;
                            }
                        }
                    }

                    $query = "SELECT m.*
                                FROM classi_prof_materie cpm, materie m
                                WHERE cpm.id_materia = m.id_materia
                                    AND m.flag_canc = 0
                                    AND cpm.id_classe IN (
                                        SELECT DISTINCT id_classe
                                        FROM classi
                                        WHERE id_indirizzo = {$indirizzo['id_indirizzo']}
                                    )
                                ORDER BY m.codice_ministeriale DESC, m.descrizione ASC
                              ";

                    $result = pgsql_query($query) or die("Invalid $query");
                    $materie_qo = pg_fetch_all($result);

                    foreach ($materie_qo as $materia_qo)
                    {
                        $chiave_controllo = serialize($materia_qo['codice_ministeriale'] . encode($materia_qo['descrizione']) . ($materia_qo['id_materia']));
                        if (!array_key_exists($chiave_controllo, $elenco_materie_indirizzo))
                        {
                            $elenco_materie_indirizzo[$indirizzo['id_indirizzo'] . '@' . $indirizzo['descrizione_indirizzo'] . '@' . $indirizzo['tipo_indirizzo']][$chiave_controllo] = $materia_qo;
                        }
                    }
                    break;
                case '6':
                    $materie_primarie = [
                        //{{{ <editor-fold defaultstate="collapsed" desc="Materie Ministeriali">
                        0  => [
                            'codice_ministeriale'      => '1001',
                            'descrizione_ministeriale' => 'ITALIANO'
                        ],
                        1  => [
                            'codice_ministeriale'      => '1002',
                            'descrizione_ministeriale' => 'SLOVENO'
                        ],
                        2  => [
                            'codice_ministeriale'      => '1003',
                            'descrizione_ministeriale' => 'LINGUA INGLESE'
                        ],
                        3  => [
                            'codice_ministeriale'      => '1004',
                            'descrizione_ministeriale' => 'STORIA'
                        ],
                        4  => [
                            'codice_ministeriale'      => '1005',
                            'descrizione_ministeriale' => 'GEOGRAFIA'
                        ],
                        5  => [
                            'codice_ministeriale'      => '1006',
                            'descrizione_ministeriale' => 'MATEMATICA'
                        ],
                        6  => [
                            'codice_ministeriale'      => '1007',
                            'descrizione_ministeriale' => 'SCIENZE'
                        ],
                        7  => [
                            'codice_ministeriale'      => '1008',
                            'descrizione_ministeriale' => 'MUSICA'
                        ],
                        8  => [
                            'codice_ministeriale'      => '1009',
                            'descrizione_ministeriale' => 'ARTE E IMMAGINE'
                        ],
                        9  => [
                            'codice_ministeriale'      => '1010',
                            'descrizione_ministeriale' => 'TECNOLOGIA'
                        ],
                        10 => [
                            'codice_ministeriale'      => '1555',
                            'descrizione_ministeriale' => 'EDUCAZIONE FISICA'
                        ],
                        11 => [
                            'codice_ministeriale'      => '1999',
                            'descrizione_ministeriale' => 'COMPORTAMENTO'
                        ],
                        12 => [
                            'codice_ministeriale'      => '1666',
                            'descrizione_ministeriale' => 'RELIGIONE CATTOLICA/ATTIVITA\' ALTERNATIVA'
                        ],
                        13 => [
                            'codice_ministeriale'      => '1777',
                            'descrizione_ministeriale' => 'AUTONOMIA'
                        ]
                            //}}} </editor-fold>
                    ];

                    foreach ($materie_primarie as $materia_primarie)
                    {
                        $query = "SELECT m.*
                                    FROM materie m
                                    WHERE m.codice_ministeriale = '{$materia_primarie['codice_ministeriale']}'
                                        AND m.descrizione = '".encode($materia_primarie['descrizione_ministeriale'])."'
                                        AND m.flag_canc = 0
                                    ORDER BY m.codice_ministeriale DESC, m.descrizione ASC";

                        $result = pgsql_query($query) or die("Invalid $query");
                        $materie_qo = pg_fetch_all($result);

                        foreach ($materie_qo as $materia_qo)
                        {
                            $chiave_controllo = serialize($materia_qo['codice_ministeriale'] . encode($materia_qo['descrizione']) . ($materia_qo['id_materia']));
                            if (!array_key_exists($chiave_controllo, $elenco_materie_indirizzo))
                            {
                                $elenco_materie_indirizzo[$indirizzo['id_indirizzo'] . '@' . $indirizzo['descrizione_indirizzo'] . '@' . $indirizzo['tipo_indirizzo']][$chiave_controllo] = $materia_qo;
                            }
                        }
                    }

                    $query = "SELECT m.*
                                FROM classi_prof_materie cpm,
                                    materie m
                                WHERE cpm.id_materia = m.id_materia
                                    AND m.flag_canc = 0
                                    AND cpm.id_classe IN (
                                        SELECT DISTINCT id_classe
                                        FROM classi
                                        WHERE id_indirizzo = {$indirizzo['id_indirizzo']}
                                    )
                                ORDER BY m.codice_ministeriale DESC, m.descrizione ASC
                              ";

                    $result = pgsql_query($query) or die("Invalid $query");
                    $materie_qo = pg_fetch_all($result);

                    foreach ($materie_qo as $materia_qo)
                    {
                        $chiave_controllo = serialize($materia_qo['codice_ministeriale'] . encode($materia_qo['descrizione']) . ($materia_qo['id_materia']));
                        if (!array_key_exists($chiave_controllo, $elenco_materie_indirizzo))
                        {
                            $elenco_materie_indirizzo[$indirizzo['id_indirizzo'] . '@' . $indirizzo['descrizione_indirizzo'] . '@' . $indirizzo['tipo_indirizzo']][$chiave_controllo] = $materia_qo;
                        }
                    }
                    break;
                case '7':
                case '8':
                    $query = "SELECT m.*
                                FROM classi_prof_materie cpm,
                                    materie m
                                WHERE cpm.id_materia = m.id_materia
                                    AND m.flag_canc = 0
                                    AND cpm.id_classe IN (
                                        SELECT DISTINCT id_classe
                                        FROM classi
                                        WHERE id_indirizzo = {$indirizzo['id_indirizzo']}
                                    )
                                ORDER BY m.codice_ministeriale DESC, m.descrizione ASC
                              ";

                    $result = pgsql_query($query) or die("Invalid $query");
                    $materie_qo = pg_fetch_all($result);

                    foreach ($materie_qo as $materia_qo)
                        {
                        $chiave_controllo = serialize($materia_qo['codice_ministeriale'] . encode($materia_qo['descrizione']) . ($materia_qo['id_materia']));
                        if (!array_key_exists($chiave_controllo, $elenco_materie_indirizzo))
                                {
                            $elenco_materie_indirizzo[$indirizzo['id_indirizzo'] . '@' . $indirizzo['descrizione_indirizzo'] . '@' . $indirizzo['tipo_indirizzo']][$chiave_controllo] = $materia_qo;
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

    $ord_arr = [];
    $count = 0;

    foreach ($elenco_materie_indirizzo as $key => $materia)
    {
        $count = count($materia);
        $elenco_materie_indirizzo[$key]['numero_materie'] = $count;
    }

    foreach ($elenco_materie_indirizzo as $key => $materia)
    {
        $explode = explode('-', $key);
        $ord_arr['ordinamento_principale'][$key] = $materia['numero_materie'];
        $ord_arr['ordinamento'][$key] = strtoupper($explode[1]);
    }

    array_multisort($ord_arr['ordinamento_principale'], SORT_ASC, $ord_arr['ordinamento'], SORT_ASC, $elenco_materie_indirizzo);

    return $elenco_materie_indirizzo;
    //}}} </editor-fold>
}

function estrai_indirizzi_materia($id_materia) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre gli indirizzi ai quali è associata una materia">
    $query = "SELECT DISTINCT i.descrizione
                    FROM indirizzi i
                    WHERE id_indirizzo IN (
                        SELECT id_indirizzo
                        FROM classi_prof_materie cpm, classi c
                        WHERE id_materia = {$id_materia}
                        AND cpm.id_classe = c.id_classe
                    )
                    ORDER BY i.descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $elenco_indirizzi = pg_fetch_all($result);

    return $elenco_indirizzi;
    //}}} </editor-fold>
}

function estrai_indirizzi_materia_da_descrizione($descrizione) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre gli indirizzi ai quali è associata una materia">
    $query = "SELECT DISTINCT i.descrizione
                    FROM indirizzi i
                    WHERE id_indirizzo IN (
                        SELECT id_indirizzo
                        FROM classi_prof_materie cpm, classi c
                        WHERE id_materia IN (SELECT id_materia FROM materie WHERE descrizione ILIKE '%".encode($descrizione)."%' AND flag_canc = 0)
                        AND cpm.id_classe = c.id_classe
                    )
                    AND i.flag_canc = 0
                    AND i.descrizione NOT ILIKE '%PREISCRIZION%'
                    ORDER BY i.descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $elenco_indirizzi = pg_fetch_all($result);

    return $elenco_indirizzi;
    //}}} </editor-fold>
}

function estrai_tipo_indirizzo_materia($id_materia) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre gli indirizzi ai quali è associata una materia">
    $query = "SELECT DISTINCT i.tipo_indirizzo "
            . "FROM indirizzi i, classi_prof_materie cpm"
            . " WHERE cpm.id_classe IN (SELECT id_classe FROM classi WHERE id_indirizzo = i.id_indirizzo)"
            . " AND id_materia = {$id_materia} AND i.flag_canc = 0 AND i.descrizione NOT ILIKE '%PREISCRIZION%'";

    $result = pgsql_query($query) or die("Invalid $query");
    $indirizzi = pg_fetch_all($result);

    return $indirizzi;
    //}}} </editor-fold>
}

function estrai_materie_musicali_e_linguistiche($codice_ministeriale) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie musicali e linguistiche per le scuole medie">
    $query = "SELECT m.*
                FROM materie m
                  WHERE m.flag_canc = 0
                  AND m.codice_ministeriale = '{$codice_ministeriale}'
                ORDER BY m.descrizione
              ";

    $result = pgsql_query($query) or die("Invalid $query");
    $materie = pg_fetch_all($result);

    return $materie;
    //}}} </editor-fold>
}

function estrai_materie_senza_abbinamento() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie non abbinate ad un indirizzo">
    $query = "SELECT m.*
                FROM materie m
                  WHERE m.flag_canc = 0
                  AND m.codice_ministeriale NOT IN
                  ('1001','1002','1003','1004','1005','1006','1007','1008','1009','1010','1555','1666','1777','1999','2001','2002','2003','2004','2005','2006','2007','2008','2010','2011','2012','2013','2014','2015','2555','2666','2777','2999')
                  AND m.id_materia NOT IN (SELECT DISTINCT id_materia FROM classi_prof_materie)
                  --AND m.codice_ministeriale NOT IN
                  --(SELECT DISTINCT codice_ministeriale FROM abbinamento_materie_piani_studio)
                  AND m.descrizione <> 'MATERIA NON DEFINITA'
                ORDER BY m.descrizione
              ";

    $result = pgsql_query($query) or die("Invalid $query");
    $materie = pg_fetch_all($result);

    return $materie;
    //}}} </editor-fold>
}

function estrai_materia_non_definita() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre la materia non definita">
    $query = "SELECT m.*
                FROM materie m
                  WHERE m.flag_canc = 0
                  AND m.descrizione = 'MATERIA NON DEFINITA'
              ";

    $result = pgsql_query($query) or die("Invalid $query");
    $materie = pg_fetch_all($result);

    return $materie;
    //}}} </editor-fold>
}

function estrai_aree_disciplinari($id_classe, $formato = 'array') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le aree disciplinari">
    $elenco = [];

    $id_classe = $id_classe = '' ? 0 : $id_classe;

    $query = "SELECT ad.id_materia_principale,
                    mp.descrizione as descrizione_principale,
                    mp.tipo_materia as tipo_principale,
                    ad.id_materia_aggregata,
                    ma.descrizione as descrizione_aggregata,
                    ma.tipo_materia as tipo_aggregata,
                    ad.id_classe
                FROM materie_aree_disciplinari ad, materie mp, materie ma
                WHERE
                    ad.id_materia_principale = mp.id_materia AND
                    ad.id_materia_aggregata = ma.id_materia AND
                    ad.id_classe = {$id_classe} AND
                    ad.flag_canc = 0";
    $result = pgsql_query($query) or die ("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {

        $materie = pg_fetch_all($result);
        switch($formato)
        {
            case 'matrice':
                $elenco = $materie;
                break;
            case 'array':
            default:
                foreach($materie as $materia)
                {
                    $elenco[$materia['id_materia_principale']][$materia['id_materia_aggregata']] = $materia['id_materia_aggregata'];
                }
                break;
        }
    }

    return $elenco;
    //}}} </editor-fold>
}

/**
 * Verifica se una materia (opzionale in una classe) fa parte di un'area disciplinare
 *
 * @param int $id_materia
 * @param string $tipo
 * @param int $id_classe
 * @return boolean
 */
function verifica_materie_in_aree_disciplinari($id_materia, $tipo = "aggregata", $id_classe = null) {

    $numero = 0;
    $abilita_aree_disciplinari = estrai_parametri_singoli('ABILITA_AREE_DISCIPLINARI');

    if ($abilita_aree_disciplinari == 'SI')
    {
        $filtro_classe = $id_classe ?"AND id_classe = {$id_classe}" : "";

        switch ($tipo)
        {
            case "pricipale":
                $filtro_materia = "AND id_materia_principale = {$id_materia} ";
                break;
            case "aggregata":
                $filtro_materia = "AND id_materia_aggregata = {$id_materia} ";
                break;
            default:
                $filtro_materia = "";
                break;
        }

        $query = "SELECT id_materia_principale as principale,
                        id_materia_aggregata as aggregata,
                        id_classe
                    FROM materie_aree_disciplinari
                    WHERE id_materia_principale IN (SELECT id_materia FROM materie where flag_canc = 0)
                        AND id_materia_aggregata IN (SELECT id_materia FROM materie where flag_canc = 0)
                        AND flag_canc = 0
                        {$filtro_materia}
                        {$filtro_classe}
                    ";

        $result = pgsql_query($query) or die ("Invalid $query");
        $numero = pg_num_rows($result);
    }

    return $numero > 0 ? true : false;
}

function salva_abbinamenti_aree_disciplinari($aree_disciplinari, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per salvare le aree disciplinari">
    if (is_array($aree_disciplinari) && !empty($aree_disciplinari)) {
        $data_salvataggio = strtotime(date('d-m-Y'));

        foreach ($aree_disciplinari as $area_disciplinare) {
            $query = "INSERT INTO materie_aree_disciplinari (id_materia_principale, id_materia_aggregata, id_classe, chi_inserisce, data_inserimento)
                      VALUES ({$area_disciplinare['id_materia_principale']}, {$area_disciplinare['id_materia_aggregata']}, {$area_disciplinare['id_classe']}, {$current_user}, {$data_salvataggio})
            ";
            pgsql_query($query) or die ("Invalid $query");
        }
    }
    //}}} </editor-fold>
}

function elimina_abbinamenti_aree_disciplinari($aree_disciplinari) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per eliminare le aree disciplinari">
    if (!empty($aree_disciplinari)) {
        $data_del = strtotime(date('d-m-Y'));
        $explode = explode('-', $aree_disciplinari);

        $query = "UPDATE materie_aree_disciplinari SET flag_canc = {$data_del}
                  WHERE id_materia_aggregata = {$explode[0]}
                    AND id_materia_principale = {$explode[1]}
                    AND id_classe = {$explode[2]}
        ";
        pgsql_query($query) or die ("Invalid $query");
}
    //}}} </editor-fold>
}

function estrai_abbinamenti_aree_disciplinari() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le aree disciplinari per abbinamento">
    $elenco = [];

    $query = "SELECT id_materia_principale,
                    id_materia_aggregata, id_classe
                FROM materie_aree_disciplinari
                WHERE flag_canc = 0
                ORDER BY id_materia_principale";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $materie = pg_fetch_all($result);

        foreach ($materie as $materia) {
            $elenco[] = [
                'id_materia_aggregata' => $materia['id_materia_aggregata'],
                'id_materia_principale' => $materia['id_materia_principale'],
                'id_classe' => $materia['id_classe']
            ];
        }
    }

    return $elenco;
    //}}} </editor-fold>
}

function estrai_materie_opzionali() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le materie opzionali">
    $query = "SELECT *
                FROM materie
                  WHERE flag_canc = 0
                  AND tipo_materia = 'OPZIONALE'
                ";

    $result = pgsql_query($query) or die("Invalid $query");
    $materie = pg_fetch_all($result);

    return $materie;
    //}}} </editor-fold>
}

/**
 * Estrae i tipi voto attivi per un abbinamento classe/materia
 * prendendoli da tutti i docenti
 *
 * @param int $id_materia
 * @param array $elenco_classi
 * @return array $tipi
 */
function estrai_tipi_voto_unificati($id_materia, $elenco_classi)
{
    //{{{ <editor-fold defaultstate="collapsed">
    if (empty($elenco_classi))
    {
        $elenco_classi[] = 0;
    }

    $tipi = [
        'scritto'   => 0,
        'orale'     => 0,
        'pratico'   => 0,
    ];

    $classi = implode(", ", $elenco_classi);

    $query = "SELECT id_classe,
                    id_professore,
                    id_materia,
                    scritto,
                    orale,
                    pratico
                FROM classi_prof_materie
                WHERE flag_canc = 0
                    AND id_classe IN ({$classi})
                    AND id_classe in (SELECT id_classe FROM classi WHERE flag_canc = 0)
                    AND id_materia = {$id_materia}
                    AND id_materia in (SELECT id_materia FROM materie WHERE flag_canc = 0)
                    AND id_professore in (SELECT id_utente FROM utenti WHERE tipo_utente = 'P' AND flag_canc = 0)
                ";

    $result = pgsql_query($query) or die("Invalid $query");

    $elenco_abbinamenti = pg_fetch_all($result);

    foreach($elenco_abbinamenti as $abbinamento)
    {
        if (intval($abbinamento['scritto']) == 1)
        {
            $tipi['scritto'] = 1;
        }
        if (intval($abbinamento['orale']) == 1)
        {
            $tipi['orale'] = 1;
        }
        if (intval($abbinamento['pratico']) == 1)
        {
            $tipi['pratico'] = 1;
        }
    }
    return $tipi;
    //}}} </editor-fold>
}

function verifica_se_materia_sostegno($id_materia)
{
    /*{{{ verifica se tipo_materia è sostegno */
	$query = "select
					tipo_materia
				from
					materie
				where
					id_materia = ".intval($id_materia)."
					and
					flag_canc=0
                    and
                    tipo_materia = 'SOSTEGNO'";
    $result = pgsql_query($query);
    $num_rows = pg_num_rows($result);
    if ($num_rows > 0) {
        return true;
    } else {
        return false;
    }

	/*}}}*/
}

function aggiorna_materia_riferimento($id_materia, $id_materia_riferimento)
{
    //{{{ <editor-fold defaultstate="collapsed">
    if ($id_materia && $id_materia_riferimento)
    {
        $materia = estrai_dati_materia($id_materia);
        if ($materia != null)
        {
            $update = " UPDATE materie
                        SET id_materia_riferimento = $id_materia_riferimento
                        WHERE id_materia = $id_materia
                    ";
            pgsql_query($update);
        }
    }
    //}}} </editor-fold>
}

function estrai_classi_materia($id_materia)
{
    //{{{ <editor-fold defaultstate="collapsed">
    $elenco_classi = [];
    $query = " SELECT   id_classe,
                        classe,
                        sezione,
                        id_indirizzo,
                        descrizione_indirizzi,
                        codice_indirizzi
                FROM classi_complete
                WHERE id_classe IN (SELECT DISTINCT id_classe
                                    FROM classi_prof_materie
                                    WHERE id_materia = $id_materia)
                ";
    $result = pgsql_query($query) or die("Invalid $query");
    if (pg_num_rows($result) > 0)
    {
        $elenco_classi = pg_fetch_all($result);
    }
    return $elenco_classi;
    //}}} </editor-fold>
}

function inserisci_abbinamento_studente_materia_riferimento($id_corso, $id_studente, $id_materia, $current_user, $eccezioni = 'NO_RIFERIMENTO')
{
    //{{{ <editor-fold defaultstate="collapsed">
    /*
     * ECCEZIONI:
     * - SEMPRE = l'abbinamento viene creato sempre, senza verificare nè che la materia aggiuntiva non sia già di riferimento per il corso nè che sia tra quelle dello studente
     * - NO_RIFERIMENTO = l'abbinamento viene creato indipendentemente dal fatto che la materia sia tra quelle dello studente o meno, viene però verificato che non sia già di riferimento per il corso (nel caso non la aggiunge)
     * - NO_STUDENTE = l'abbinamento viene creato solo se la materia è tra quelle dello studente e non è già di riferimento per il corso
     *
     */
    if ($id_corso > 0
        && $id_studente > 0
        && $id_materia > 0)
    {
        $inserisci = true;

        if ($eccezioni == 'NO_RIFERIMENTO')
        {
            // verifico che la materia non sia già di riferimento per la classe
            $dati_corso = estrai_classe($id_corso);
            if ($dati_corso['id_materia_riferimento'] == $id_materia)
            {
                $inserisci = false;
            }
        }
        elseif ($eccezioni == 'NO_STUDENTE')
        {
            // verifico che la materia non sia già di riferimento per la classe
            $dati_corso = estrai_classe($id_corso);
            if ($dati_corso['id_materia_riferimento'] == $id_materia)
            {
                $inserisci = false;
            }

            if ($inserisci)
            {
                // verifico che la materia sia tra quelle dello studente
                $materie_studente = estrai_materie_studente($id_studente);
                if (!empty($materie_studente))
                {
                    $materia_presente = false;
                    foreach ($materie_studente as $materia)
                    {
                        if ($materia['id_materia'] == $id_materia)
                        {
                            $materia_presente = true;
                            break;
                        }
                    }
                    if (!$materia_presente)
                    {
                        $inserisci = false;
                    }
                }
            }
        }

        if ($inserisci)
        {
            // verifico che l'abbinamento non sia già presente
            $query = "   SELECT *
                        FROM materia_riferimento_studenti_corsi
                        WHERE   id_corso = $id_corso AND
                                id_studente = $id_studente AND
                                id_materia_riferimento = $id_materia
                                AND flag_canc = 0
                    ";
            $result = pgsql_query($query) or die("Invalid $query");
            if (pg_num_rows($result) == 0)
            {
                $data = time();
                $insert =  "INSERT INTO materia_riferimento_studenti_corsi (id_corso, id_studente, id_materia_riferimento, chi_inserisce, data_inserimento, tipo_inserimento, chi_modifica, data_modifica, tipo_modifica)
                            VALUES ($id_corso, $id_studente, $id_materia, $current_user, $data, 'INTERFACCIA', $current_user, $data, 'INTERFACCIA')";
                pgsql_query($insert);
            }
        }
    }
    //}}} </editor-fold>
}

function elimina_abbinamento_studente_materia_riferimento($id_corso, $id_studente = 0, $id_materia_riferimento = 0, $current_user)
{
    //{{{ <editor-fold defaultstate="collapsed">
    $data = time();

    $update = " UPDATE materia_riferimento_studenti_corsi
                SET flag_canc = $data,
                    chi_modifica = $current_user,
                    data_modifica = $data
                WHERE id_corso = $id_corso
                ";

    if ($id_studente > 0)
    {
        $update .= " AND id_studente = $id_studente";
    }

    if ($id_materia_riferimento > 0)
    {
        $update .= " AND id_materia_riferimento = $id_materia_riferimento";
    }

    $result = pgsql_query($update) or die("Invalid $update");

    return $result;
    //}}} </editor-fold>
}

function estrai_materie_studente($id_studente)
{
    //{{{ <editor-fold defaultstate="collapsed">
    $elenco_materie = [];
    $classi_studente = estrai_classi_studente($id_studente);
    if (!empty($classi_studente))
    {
        foreach ($classi_studente as $classe)
        {
            $id_classi_tmp[$classe['id_classe']] = $classe['id_classe'];
        }

        $id_classi = implode(", ", $id_classi_tmp);

        $query = " SELECT   id_materia,
                            descrizione,
                            codice,
                            ordinamento,
                            in_media_pagelle,
                            codice_ministeriale,
                            nome_materia_sito,
                            nome_materia_breve,
                            unico,
                            id_materia_riferimento
                    FROM materie
                    WHERE   flag_canc = 0 AND
                            id_materia IN ( SELECT DISTINCT id_materia
                                            FROM classi_prof_materie
                                            WHERE id_classe in ($id_classi))
                    ORDER BY descrizione
                    ";
        $result = pgsql_query($query) or die("Invalid $query");
        if (pg_num_rows($result) > 0)
        {
            $elenco_materie = pg_fetch_all($result);
        }
    }
    return $elenco_materie;
    //}}} </editor-fold>
}

function estrai_materie_per_competenze()
{
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrae le materie della scuola con gli indirizzi al quale sono abbinate (se presenti) per la visualizzazione nella gestione delle competenze">
    $elenco_materie_tmp = estrai_materie();
    $elenco_classi = estrai_classi();
    $elenco_materie = [];

    foreach ($elenco_materie_tmp as $materia)
    {
        $materie_id[$materia['id_materia']] = $materia['id_materia'];
        $elenco_materie[$materia['id_materia']] = $materia;
    }

    foreach ($elenco_classi as $classe)
    {
        $materie_classe = estrai_materie_classe($classe['id_classe']);
        if (is_array($materie_classe) && count($materie_classe) > 0)
        {
            foreach ($materie_classe as $materia_classe)
            {
                if (in_array($materia_classe['id_materia'], $materie_id))
                {
                    $elenco_materie[$materia_classe['id_materia']]['indirizzi'][$classe['id_indirizzo']]['id_indirizzo'] = $classe['id_indirizzo'];
                    $elenco_materie[$materia_classe['id_materia']]['indirizzi'][$classe['id_indirizzo']]['codice'] = $classe['codice'];
                    $elenco_materie[$materia_classe['id_materia']]['indirizzi'][$classe['id_indirizzo']]['descrizione'] = $classe['descrizione'];
                }
            }
        }
    }
    return $elenco_materie;
    //}}} </editor-fold>
}

function estrai_materia_sostegno()
{
    $materia_sostegno = null;
    $query_materia_sostegno = "SELECT id_materia,
                                        codice,
                                        descrizione
                                FROM materie
                                WHERE flag_canc = 0 AND
                                        tipo_materia = 'SOSTEGNO'
                                LIMIT 1";
    $result = pgsql_query($query_materia_sostegno) or die ("Invalid $query_materia_sostegno");
    if (pg_num_rows($result) > 0){
        $materia_sostegno = pg_fetch_all($result)[0];
    }

    return $materia_sostegno;
}