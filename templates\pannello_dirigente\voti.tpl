<script type="text/javascript">
    var elenco_materie_classe_professore = JSON.parse('{$elenco_materie_classe_professore_json}');
    var elenco_studenti = JSON.parse('{$elenco_studenti_json}');
    var giorni_festivi = JSON.parse('{$giorni_festivi}');
    var elenco_pesi_competenze = [];
    var peso_default_competenze = null;
    var gestione_pesi_competenze = '{$gestione_pesi_competenze}';

    var oggi;

    var coordinatore = '{$coordinatore}';
    var user_couch_id = '{$user_couch_id}';
    var selezione_abilitata = false;
    var multi_note_didattiche = false;
    var minHeight = 500;
    var token = '';
    var idCompetenza = '';
    var idStudenteCompetenza = '';
    var chiaveTemplateMultivoto = '';
    var revisioneTemplateMultivoto = '';
    var elencoCompetenzeMultivotoAggiunte = {literal}{}{/literal};
    var filtroAlberoCompetenzeProfessore = 'solo_competenze_valutabili';
    var multivotoCompetenzePresenti = {literal}{}{/literal};
    var param_attiva_dimensioni_competenze = '{$param_attiva_dimensioni_competenze}';
    //var filtroAlberoCompetenzeProfessore = 'tutte';

    try {
        elenco_pesi_competenze = JSON.parse('{$elenco_pesi_competenze_json}');
        peso_default_competenze = JSON.parse('{$peso_default_competenze_json}');
    } catch(e){
        console.log(e, 'disabilitato peso competenze');
        gestione_pesi_competenze = 'NO';
    }

    function eliminaVoto(classIdButton, idVoto)
    {
        var current_user = document.getElementById('current_user').value;
        var current_key = document.getElementById('current_key').value;
        var db_key = document.getElementById('db_key').value;
        var tipo_utente = document.getElementById('tipo_utente').value;

        var classi = $('.'+classIdButton).attr('class');
        var p = new Promise((resolve, reject) => {
            if (classi.indexOf('filter_annotazione') !== -1)
            {
                var tipo_voto = 'annotazione';
                resolve(tipo_voto);
            }
            else
            {
                var tipo_voto = 'voto';

                var current_user = document.getElementById('current_user').value;
                var current_key = document.getElementById('current_key').value;
                var db_key = document.getElementById('db_key').value;
                var tipo_utente = document.getElementById('tipo_utente').value;

                var query = {
                    current_user:  current_user,
                    current_key: current_key,
                    db_key: db_key,
                    form_tipo_utente: tipo_utente,
                    id_voto : idVoto,
                    tipo_voto: tipo_voto,
                    form_azione: 'carica_dati_voto_annotazione'
                };

                $.ajax({
                    url: 'ajax_professore.php',
                    type: 'POST',
                    data: query,
                    cache: false,
                    success: function(result) {
                    },
                    complete: function(response) {
                        if (response.responseJSON !== null){
                            var dati_voto = response.responseJSON.dati_voto;

                            var query2 = {
                                "data"  : dati_voto.data,
                                "tipo"  : traduciTipoVoto(dati_voto.tipo),
                                "operazione"    : "del"
                            };

                            {literal}
                            $.ajax({
                                type: 'GET',
                                url: "../next-api/v1/voti/verifica_data",
                                data: query2,
                                cache: false,
                                headers: {'Authorization':token},
                                complete: function(response) {
                                    var r = response.responseJSON;

                                    if (r.del.valore == 'SI'){
                                        resolve(tipo_voto);
                                    } else {
                                        creaToast(r.del.messaggio, 'error');
                                        return;
                                        resolve(tipo_voto);
                                    }
                                }
                            });
                            {/literal}
                        }
                    }
                });
            }
        });

        p.then((tipo_voto) => {
            var toastContainer = $('#toastsContainer');
            switch(tipo_voto) {
                case 'voto':
                    var successDeleteText = '{mastercom_label}Voto eliminato{/mastercom_label}';
                    var errorDeleteText = '{mastercom_label}<b>Errore!</b><br> Voto non eliminato correttamente{/mastercom_label}';
                    break;
                case 'annotazione':
                    var successDeleteText = '{mastercom_label}Nota didattica eliminata{/mastercom_label}';
                    var errorDeleteText = '{mastercom_label}<b>Errore!</b><br> Nota didattica non eliminata correttamente{/mastercom_label}';
                    break;
            }

            var query = {
                current_user:  current_user,
                current_key: current_key,
                db_key: db_key,
                form_tipo_utente: tipo_utente,
                id_voto : idVoto,
                tipo_voto: tipo_voto,
                form_azione: 'elimina_voto_annotazione'
            };

            $.ajax({
                url: 'ajax_professore.php',
                type: 'POST',
                data: query,
                cache: false,
                success: function(result) {
                },
                complete: function(response) {
                    if (response.status === 200)
                    {
                        var toast = $('<div class="btn_pieno sfondo_verde" style="margin-top: 10px; font-weight: normal; padding: 10px 20px; text-align: left;">'+successDeleteText+'</div>');
                        chiudiSfondoOscurato();
                        //$('.'+classIdButton).fadeOut(600, function(){
                        //    $(this).remove();
                        //});

                        $('.'+classIdButton).animate({
                            opacity: 0,
                            height: "toggle"
                        }, 600, function() {
                            this.remove();
                        });
                    } else {
                        var toast = $('<div class="btn_pieno sfondo_rosso" style="margin-top: 10px; font-weight: normal; padding: 10px 20px; text-align: left;">'+errorDeleteText+'</div>');
                        console.log('Errore cancellazione');
                    }

                    toast.hide(function (){
                        toast.appendTo(toastContainer);
                        toast.show().delay(3000).fadeOut(600, function(){
                            $(this).remove();
                        });
                    });
                }
            });
        });
    }

    function eliminaVotiMultipli()
    {
        var voti_da_eliminare = $('.check_voti:checked');
        if (voti_da_eliminare.length > 0)
        {
            if (confirm('{mastercom_label}Eliminare?{/mastercom_label}') === true)
            {
                voti_da_eliminare.each(function (i, el){
                    var strClasses = el.className.trim();
                    strClasses = strClasses.replace(/ +(?= )/g,'');
                    var classiCheckbox = strClasses.split(' ');
                    if (classiCheckbox.length > 0)
                    {
                        classiCheckbox.forEach(function (className){
                            if (className.includes('check_voto') === true)
                            {
                                // elimino il voto
                                var classe_label_da_eliminare = className.replace('check_', '');
                                var id_voto_da_eliminare = className.replace('check_voto', '');
                                eliminaVoto(classe_label_da_eliminare, id_voto_da_eliminare);
                            }
                            else if (className.includes('check_annotazione') === true)
                            {
                                // elimino l'annotazione
                                var classe_label_da_eliminare = className.replace('check_', '');
                                var id_annotazione_da_eliminare = className.replace('check_annotazione', '');
                                eliminaVoto(classe_label_da_eliminare, id_annotazione_da_eliminare);
                            }
                        });
                    }
                });
            }
        }
        $('.check_voti').hide().attr('disabled', true).attr('checked', false);
        $('#btnOperazioni').remove();
        selezione_abilitata = false;
    }

    /*async function eliminaCompetenzeMultiple()
    {
        var competenze_da_eliminare_tmp = $('.check_competenze:checked');
        if (competenze_da_eliminare_tmp.length > 0)
        {
            if (confirm('{mastercom_label}Eliminare?{/mastercom_label}') === true)
            {
                {literal}
                let competenze_da_eliminare = {};
                competenze_da_eliminare_tmp.each(function (i, el){
                    var data = el.dataset;
                    if (!competenze_da_eliminare[data.idStudente]){
                        competenze_da_eliminare[data.idStudente] = [];
                    }
                    competenze_da_eliminare[data.idStudente].push(el);
                });

                for (const index in competenze_da_eliminare){
                    let valutazioniStudente = competenze_da_eliminare[index];
                    for (const val of valutazioniStudente){
                        var data = val.dataset;
                        await eliminaValutazioneDaTabella(data.idStudente, data.idCompetenza, data.idValutazione);
                    }
                }

                {/literal}
            }
        }
        $('.check_competenze').hide().attr('disabled', true).attr('checked', false);
        $('#btnOperazioni').remove();
        selezione_abilitata = false;
    }*/

    async function eliminaCompetenzeMultiple()
    {
        var competenze_da_eliminare_tmp = $('.check_competenze:checked');
        $("#jqxLoader").jqxLoader('open');
        if (competenze_da_eliminare_tmp.length > 0)
        {
            if (confirm('{mastercom_label}Eliminare?{/mastercom_label}') === true)
            {
                {literal}
                let competenze_da_eliminare = {};
                competenze_da_eliminare_tmp.each(function (i, el){
                    var data = el.dataset;
                    if (!competenze_da_eliminare[data.idStudente]){
                        competenze_da_eliminare[data.idStudente] = [];
                    }
                    competenze_da_eliminare[data.idStudente].push(el);
                });

                const promises = Object.values(competenze_da_eliminare).map(eliminaValutazioniAsync);
                //Object.keys(competenze_da_eliminare).forEach(function (index){
                //    let valutazioniStudente = competenze_da_eliminare[index];
                    // non posso chiamare la funzione di eliminazione direttamente qui in quanto posso avere conflitti su eliminazioni
                    // di valutazioni di stessi studenti, quindi per ogni studenti lancio una funzione asincrona che va ad eliminarle una ad una
                //    eliminaValutazioniAsync(valutazioniStudente);
                //});
                await Promise.all(promises);
                {/literal}
            }
        }
        $('.check_competenze').hide().attr('disabled', true).attr('checked', false);
        $('#btnOperazioni').remove();
        selezione_abilitata = false;
        $("#jqxLoader").jqxLoader('close');
    }

    async function eliminaValutazioniAsync(valutazioniStudente)
    {
        for (const val of valutazioniStudente){
            var data = val.dataset;
            await eliminaValutazioneDaTabella(data.idStudente, data.idCompetenza, data.idValutazione);
        }
    }

    function attivaSelezioneVoti(classVotoSelezione = '')
    {
        if (selezione_abilitata === true)
        {
            $('.check_voti').hide().attr('disabled', true).attr('checked', false);
            $('#btnOperazioni').remove();
            selezione_abilitata = false;
        }
        else
        {
            $('.check_voti').show().attr('disabled', false);
            chiudiSfondoOscurato();
            selezione_abilitata = true;
            if (classVotoSelezione !== '')
            {
               $('.'+ classVotoSelezione).prop('checked',true);
            }
            var btnOperazioni = "<div id='btnOperazioni' align='center' style='position: fixed; bottom: 5px; z-index: 1; left: 50%; transform: translateX(-49%);'>"
                                + "<button type='button' class='btn_pieno sfondo_verde testo_bianco ripples'"
                                    + " style='width: 125px;'"
                                    + " onclick='eliminaVotiMultipli();'"
                                + ">Elimina</button>"
                                + "<button type='button' class='btn_pieno sfondo_rosso testo_bianco ripples'"
                                    + " style='width: 125px; margin-left: 5px;'"
                                    + " onclick='attivaSelezioneVoti();'"
                                + ">Annulla</button>"
                                + "<button type='button' class='btn_pieno sfondo_azzurro testo_bianco ripples'"
                                    + " style='width: 125px; margin-left: 5px;'"
                                    + " onclick='spostaVotiMultipli();'"
                                + ">Sposta al &rarr;</button>"
                                + "<input type='date' "
                                    + " style='margin-left: 5px;' "
                                    + " value='{$data_odierna}' "
                                    + " name='data_sposta_voti_multipli' id='data_sposta_voti_multipli' "
                                + ">"
                                + "<input type='hidden' name='id_voti_spostamento_multipli' id='id_voti_spostamento_multipli'>"
                                + "<input type='hidden' name='id_annotazioni_spostamento_multipli' id='id_annotazioni_spostamento_multipli'>"
                                + "<div>";
            //$('body').append(btnOperazioni);
            $('#form_container').append(btnOperazioni);
        }
    }

    function attivaSelezioneCompetenze(classSelezioneCompetenza = '')
    {
        if (selezione_abilitata === true)
        {
            $('.check_competenze').hide().attr('disabled', true).attr('checked', false);
            $('#btnOperazioni').remove();
            selezione_abilitata = false;
        }
        else
        {
            $('.check_competenze').show().attr('disabled', false);
            chiudiSfondoOscurato();
            selezione_abilitata = true;
            if (classSelezioneCompetenza !== '')
            {
                classSelezioneCompetenza = classSelezioneCompetenza.replace(/\./g, "\\.");
                $('.'+ classSelezioneCompetenza).prop('checked',true);
            }
            var btnOperazioni = "<div id='btnOperazioni' align='center' style='position: fixed; bottom: 5px; z-index: 1; left: 50%; transform: translateX(-49%);'>"
                                + "<button type='button' class='btn_pieno sfondo_verde testo_bianco ripples'"
                                    + " style='width: 125px;'"
                                    + " onclick='eliminaCompetenzeMultiple();'"
                                + ">Elimina</button>"
                                + "<button type='button' class='btn_pieno sfondo_rosso testo_bianco ripples'"
                                    + " style='width: 125px; margin-left: 5px;'"
                                    + " onclick='attivaSelezioneCompetenze();'"
                                + ">Annulla</button>"
                                + "<div>";
            $('body').append(btnOperazioni);
        }
    }

    function spostaVotiMultipli(){
    var data = $('#data_sposta_voti_multipli').val();
        if (data == ''){
            alert('{mastercom_label}Selezionare una data{/mastercom_label}');
        } else {
            if (verificaFestivo(data) === false){
                var voti_da_spostare = $('.check_voti:checked');
                if (voti_da_spostare.length > 0)
                {
                    if (confirm('{mastercom_label}Spostare?{/mastercom_label}') === true)
                    {
                        var idVotiDaSpostare = [];
                        var idAnnotazioniDaSpostare = [];
                        voti_da_spostare.each(function (i, el){
                            var strClasses = el.className.trim();
                            strClasses = strClasses.replace(/ +(?= )/g,'');
                            var classiCheckbox = strClasses.split(' ');
                            if (classiCheckbox.length > 0)
                            {
                                classiCheckbox.forEach(function (className){
                                    if (className.includes('check_voto') === true)
                                    {
                                        var id_voto_da_spostare = className.replace('check_voto', '');
                                        idVotiDaSpostare.push(id_voto_da_spostare);
                                    }
                                    else if (className.includes('check_annotazione') === true)
                                    {
                                        var id_annotazione_da_spostare = className.replace('check_annotazione', '');
                                        idAnnotazioniDaSpostare.push(id_annotazione_da_spostare);
                                    }
                                });
                            }
                        });
                    }
                }

                $('#id_voti_spostamento_multipli').val(idVotiDaSpostare.join(','));
                $('#id_annotazioni_spostamento_multipli').val(idAnnotazioniDaSpostare.join(','));

                document.getElementById('form_container').operazione.value = 'sposta_voti_multipli';
                document.getElementById('form_container').submit();
                $('#jqxGeneralLoader').jqxLoader('open');
            } else {
                alert('{mastercom_label}Giorno festivo! Cambiare data{/mastercom_label}');
            }
        }
    }

    function apriModificaVoto(idVoto, tipoVoto)
    {
        var current_user = document.getElementById('current_user').value;
        var current_key = document.getElementById('current_key').value;
        var db_key = document.getElementById('db_key').value;
        var tipo_utente = document.getElementById('tipo_utente').value;

        var query = {
            current_user:  current_user,
            current_key: current_key,
            db_key: db_key,
            form_tipo_utente: tipo_utente,
            id_voto : idVoto,
            tipo_voto: tipoVoto,
            form_azione: 'carica_dati_voto_annotazione'
        };

        $.ajax({
            url: 'ajax_professore.php',
            type: 'POST',
            data: query,
            cache: false,
            success: function(result) {
            },
            complete: function(response) {
                if (response.responseJSON !== null)
                {
                    // compilo tutti i dati della form
                    var dati_voto = response.responseJSON.dati_voto;
                    var modifica_abilitata;

                    if (tipoVoto === 'annotazione')
                    {
                        $('#id_voto_modifica').val('');
                        $('#id_annotazione_modifica').val(idVoto);
                        $('#dati_voto_modifica_voto').hide();
                        $('#note_voto_modifica_voto').hide().attr("disabled", true).val('');
                        $('#peso_modifica_voto').hide().attr("disabled", true).val('');
                        $('#dati_annotazione_modifica_voto').show();
                        $('#note_annotazione_modifica_voto').show().attr("disabled", false).val(dati_voto['note']);
                        $('#modifica_simbolo_assegnato').removeAttr('disabled');
                        $('#tipo_modifica_voto').attr("disabled", true);
                        $('#modifica_voto_assegnato').attr("disabled", true);
                        modifica_abilitata = 'NO';
                        $('#messaggio_modifica_voto_bloccato').hide().text("");
                        $('#btn_salva_modifica_voto').show();
                    }
                    else
                    {
                        $('#id_voto_modifica').val(idVoto);
                        $('#id_annotazione_modifica').val('');
                        $('#dati_voto_modifica_voto').show();
                        $('#note_voto_modifica_voto').show().attr("disabled", false).val(dati_voto['note']);
                        $('#peso_modifica_voto').show().attr("disabled", false);
                        $('#dati_annotazione_modifica_voto').hide();
                        $('#note_annotazione_modifica_voto').hide().attr("disabled", true).val('');
                        $('#modifica_simbolo_assegnato').attr("disabled", true);
                        $('#tipo_modifica_voto').removeAttr('disabled');
                        $('#modifica_voto_assegnato').removeAttr('disabled');

                        if (dati_voto['id_peso'] != null) {
                            $('#peso_modifica_voto').val(dati_voto['id_peso'])
                        } else {
                            selezionaPesoDefault('#peso_modifica_voto');
                        }

                        var query2 = {
                            "data"  : dati_voto.data,
                            "tipo"  : traduciTipoVoto(dati_voto.tipo),
                            "operazione"    : "mod"
                        };

                        {literal}
                        $.ajax({
                            type: 'GET',
                            url: "../next-api/v1/voti/verifica_data",
                            data: query2,
                            cache: false,
                            headers: {'Authorization':token},
                            complete: function(response) {
                                var r = response.responseJSON;

                                if (r.mod.valore == 'SI'){
                                    modifica_abilitata = "SI";

                                    $('#messaggio_modifica_voto_bloccato').hide().text("");
                                    $('#btn_salva_modifica_voto').show();
                                } else {
                                    modifica_abilitata = "NO";

                                    $('#messaggio_modifica_voto_bloccato').show().html("<br>"+r.mod.messaggio);
                                    $('#btn_salva_modifica_voto').hide();
                                }
                            }
                        });
                        {/literal}
                    }
                    $('#studente_modifica_voto').html(dati_voto['descrizione_studente']);
                    $('#materia_modifica_voto').html(dati_voto['descrizione_materia']);
                    //setto la data
                    ts_data = parseInt(dati_voto['data'])*1000;
                    var date = new Date(ts_data);

                    var mese = date.getMonth()+1;
                    var giorno = date.getDate();
                    if (mese < 10)
                    {
                        mese = '0' + mese;
                    }
                    if (giorno < 10)
                    {
                        giorno = '0' + giorno;
                    }

                    var data_valida = date.getFullYear() + '-' + mese + '-' + giorno;
                    $('#data_modifica_voto').val(data_valida);
                    //

                    var id_materia = dati_voto['id_materia'];
                    elenco_materie_classe_professore.professore.forEach(function(materia, key)
                    {
                        if (id_materia === materia['id_materia'])
                        {
                            // logiche riprese da 'include_gestione_voti_professore.tpl' nella parte di inserimento nuovo voto

                            if (tipoVoto === 'voto')
                            {
                                //popolo i tipi voto disponibili
                                var tipi_voto = "";
                                if (materia['voto_unico_normale_attivita'] !== '1' && typeof materia['tipo_voto_abbinamenti'] !== 'undefined' && materia['tipo_voto_abbinamenti'] !== null)
                                {
                                    if (typeof materia['tipo_voto_abbinamenti']['scritto'] !== 'undefined')
                                    {
                                        if (parseInt(materia['tipo_voto_abbinamenti']['scritto']) === 1)
                                        {
                                            tipi_voto += "<option value='0'>Scritto</option>";
                                        }
                                    }
                                    if (typeof materia['tipo_voto_abbinamenti']['orale'] !== 'undefined')
                                    {
                                        if (parseInt(materia['tipo_voto_abbinamenti']['orale']) === 1)
                                        {
                                            tipi_voto += "<option value='1'>Orale</option>";
                                        }
                                    }
                                    if (typeof materia['tipo_voto_abbinamenti']['pratico'] !== 'undefined')
                                    {
                                        if (parseInt(materia['tipo_voto_abbinamenti']['pratico']) === 1)
                                        {
                                            tipi_voto += "<option value='2'>Pratico</option>";
                                        }
                                    }
                                }
                                else if (coordinatore === 'SI')
                                {
                                    if (parseInt(materia['cpm_scritto']) === 1)
                                    {
                                        tipi_voto += "<option value='0'>Scritto</option>";
                                    }
                                    if (parseInt(materia['cpm_orale']) === 1)
                                    {
                                        tipi_voto += "<option value='1'>Orale</option>";
                                    }
                                    if (parseInt(materia['cpm_pratico']) === 1)
                                    {
                                        tipi_voto += "<option value='2'>Pratico</option>";
                                    }
                                }
                                $('#tipo_modifica_voto').html(tipi_voto);
                                $('#tipo_modifica_voto').removeAttr('disabled');
                                $('#tipo_modifica_voto').val(dati_voto['tipo']);

                                //popolo i voti disponibili
                                var voti = "";
                                materia['significati_voto'].forEach(function (significato)
                                {
                                    voti += "<option value='" + significato['voto'] + "'>"+ significato['codice'] + "</option>";
                                });
                                $('#modifica_voto_assegnato').html(voti);
                                $('#modifica_voto_assegnato').val(dati_voto['voto']);

                                $('#id_studente_modifica_voto').val(dati_voto['id_studente']);

                                //bottone competenze
                                {if $optional_competenze_professore == 'OK'}
                                html = '';
                                html += '<button type="button"';
                                html += '        style="margin-top: 10px; background-color: #bbbbff;"';
                                html += '        id="btn_aggiungi_competenze_modifica_voto"';
                                html += '        class="btn_flat ripples"';
                                html += '         onclick="caricaCompetenzeStudenteSingoloVoto(\''+dati_voto['id_studente']+'\', \''+id_materia+'\', this.id, \'containerCompetenzeModificaVoto\', \'spazioNuoveValutazioniModificaVoto\', \'containerValutazioniModificaVoto\');"';
                                html += '         >{mastercom_label}+ Competenze{/mastercom_label}</button>';
                                html += '<div id="spazioNuoveValutazioniModificaVoto" style="display: none;">';
                                html += '    <div style="display: flex; margin-left: 10px;">';
                                html += '        <fieldset style=\'-moz-border-radius:5px; -webkit-border-radius:5px; border-radius:5px; border: 0.5px solid #0077BE; width: fit-content; margin-left: 5px;\'>';
                                html += '        <legend style=\'color: #0077BE;\'>{mastercom_label}Nuove Valutazioni Competenze{/mastercom_label}</legend>';
                                html += '        <div style="max-height: 20vh; overflow: auto; -webkit-overflow-scrolling: touch;">';
                                html += '            <table id="containerValutazioniModificaVoto"></table>';
                                html += '        </div>';
                                html += '        </fieldset>';
                                html += '    </div>';
                                html += '</div>';
                                html += '<div id="containerCompetenzeModificaVoto"></div>';
                                $('#competenze_modifica_voto').html(html);
                                {/if}
                            }
                            else
                            {
                                //popolo le note didattiche
                                var simboli = "";
                                materia['simboli'].forEach(function (simbolo)
                                {
                                    simboli += "<option value='" + simbolo['id_simbolo'] + "'>"+ simbolo['codice'] + "</option>";
                                });
                                $('#modifica_simbolo_assegnato').html(simboli);
                                $('#modifica_simbolo_assegnato').val(dati_voto['id_simbolo']);
                            }
                        }
                    });
                    $('.voto_btn').css("z-index", "0");
                    $('#menu_options_popup').remove();
                    $('#blockHoverVoto').remove();
                    $('#modifica_voto').show();
                }
                else
                {
                    console.log('Errore');
                }
            }
        });
    }

    function apriMenuVoti(label, idVoto)
    {
        if (selezione_abilitata === false)
        {
            $('#menu_options_popup').remove();
            $('#elenco_voti').css('overflow','hidden');
            var classi = label.className;
            if (classi.indexOf('filter_annotazione') !== -1)
            {
                var tipo_voto = 'annotazione';
            }
            else
            {
                var tipo_voto = 'voto';
            }
            var btnPosition = label.getElementsByClassName("voto_btn")[0].getBoundingClientRect();
            var menuWidth = 100;
            var top = btnPosition['top']-30;
            var left = btnPosition['left']-menuWidth - 5;
            var options = "<div id='menu_options_popup' align='left' style='position: absolute; z-index: 11; width: "+menuWidth+"px; top: "+top+"; left: "+left+";' class='div_scheda_generica'>"
                            + "<div class='menu_option evidenzia_menu_option pointer' onclick=\"attivaSelezioneVoti('check_"+tipo_voto+idVoto+"');\">{mastercom_label}Seleziona{/mastercom_label}</div>"
                            + "<div class='menu_option evidenzia_menu_option pointer'"
                                + "onclick='apriModificaVoto("+idVoto+", \""+tipo_voto+"\");'"
                            + ">{mastercom_label}Modifica{/mastercom_label}</div>"
                            + "<div class='menu_option evidenzia_menu_option pointer' "
                                + "onclick=\"if (confirm('{mastercom_label}Eliminare?{/mastercom_label}') === true)"
                                + "{literal}{eliminaVoto('"+tipo_voto+idVoto+"', '"+idVoto+"');}{/literal}\""
                            + ">{mastercom_label}Elimina{/mastercom_label}</div>"
                        + "</div>"
                        + "<div id='blockHoverVoto' style='width: 42px; height: 22px; z-index: 12; background-color: transparent; position: absolute; top: "+btnPosition['top']+"px; left: "+btnPosition['left']+"px'></div>";

            $('#elenco_voti').append(options);
            $('#sfondo_oscurato').show();
            label.getElementsByClassName("voto_btn")[0].style.zIndex = '11';
        }
    }

    function apriMenuCompetenze(label, idStudente, idCompetenza, idValutazione)
    {
        if (selezione_abilitata === false)
        {
            $('#menu_options_popup').remove();
            $('#elenco_voti').css('overflow','hidden');
            var btnPosition = label.getElementsByClassName("voto_btn")[0].getBoundingClientRect();
            var menuWidth = 100;
            var top = btnPosition['top'] - 30;
            var left = btnPosition['left']-menuWidth - 5;
            var options = "<div id='menu_options_popup' align='left' style='position: absolute; z-index: 11; width: "+menuWidth+"px; top: "+top+"; left: "+left+";' class='div_scheda_generica'>"
                            + "<div class='menu_option evidenzia_menu_option pointer' onclick=\"attivaSelezioneCompetenze('check_val_"+idStudente+"_"+idCompetenza+"_"+idValutazione+"');\">{mastercom_label}Seleziona{/mastercom_label}</div>"
                            + "<div class='menu_option evidenzia_menu_option pointer'"
                                + "onclick=\"apriModificaValutazione('"+idStudente+"', '"+idCompetenza+"', '"+idValutazione+"');\""
                            + ">{mastercom_label}Modifica{/mastercom_label}</div>"
                            + "<div class='menu_option evidenzia_menu_option pointer' "
                                + "onclick=\"if (confirm('{mastercom_label}Eliminare?{/mastercom_label}') === true)"
                                + "{literal}{eliminaValutazioneDaTabella('"+idStudente+"', '"+idCompetenza+"', '"+idValutazione+"');}{/literal}\""
                            + ">{mastercom_label}Elimina{/mastercom_label}</div>"
                        + "</div>"
                        + "<div id='blockHoverVoto' style='width: 42px; height: 22px; z-index: 12; background-color: transparent; position: absolute; top: "+btnPosition['top']+"px; left: "+btnPosition['left']+"px'></div>";

            $('#elenco_voti').append(options);
            $('#sfondo_oscurato').show();
            label.getElementsByClassName("voto_btn")[0].style.zIndex = '11';
        }
    }

    function apriModificaValutazione(idStudenteFn, idCompetenzaFn, idValutazioneFn)
    {
        chiudiSfondoOscurato();
        apriChiudiPopup('nuova_competenza', 'sfondo_oscurato', true);
        $('#selectStudenteCompetenze').val(idStudenteFn).trigger('change');

        var document = JSON.parse(sessionStorage.getItem('document'));

        var repeat = setInterval(function(){
            if ($('#jqxTreeComp').length > 0){
                clearTimeout(repeat);
                let items = $('#jqxTreeComp').jqxTree('getItems');

                for (i = 0; i < items.length; i++) {
                    if (idCompetenzaFn == items[i].id){
                        $('#jqxTreeComp').jqxTree('selectItem', items[i]);
                        espandiPadriAlbero(items[i]);
                        break;
                    }
                }
            }
        }, 150);
    }

    function espandiPadriAlbero(item)
    {
        if (item.parentElement !== null){
            $('#jqxTreeComp').jqxTree('expandItem', item);
            espandiPadriAlbero(item.parentElement);
        }
    }

    {literal}
    $('#modal-open').on("change", function (e)
    {
        var body = $('#body')[0];

        if (this.value === 'SI')
        {
            body.style.overflow='hidden';
            $('#btn_menu_utente').css("pointer-events", "none");
            //$('#filtro_id_materia').attr("disabled", true);
            $('.class_type_filter').attr("disabled", true);
            $('#btn_chiudi_studente').attr("disabled", true);
            $('#elenco_voti').css('overflow','hidden');
        }
        else
        {
            body.style.overflow='visible';
            $('#btn_menu_utente').css("pointer-events", "auto");
            //$('#filtro_id_materia').attr("disabled", false);
            $('.class_type_filter').attr("disabled", false);
            $('#btn_chiudi_studente').attr("disabled", false);
            $('#elenco_voti').css('overflow','auto');
            $('#select_studente').val("");
            $('.voto_btn').css("z-index", "0");
            $('#menu_options_popup').remove();
            $('#blockHoverVoto').remove();
            $('#id_voto_modifica').val('');
            $('#id_annotazione_modifica').val('');
        }
    });

    function chiudiSfondoOscurato()
    {
        $('#sfondo_oscurato').hide();
        $('#nuovo_voto').hide();
        $('#multivoto').hide();
        $('#nome_studente_fisso').hide();
        $('#nome_studente_select').hide();
        $('#modifica_voto').hide();
        $('#id_voto').val('');
        $('#nuova_competenza').hide();
        $('#selectStudenteCompetenze').val('0');
        $('#modal-open').val('NO').trigger('change');
        vuotaInterfacciaCompetenze();
        $('#competenze_modifica_voto').html('');
        $('.multi_voto_select').html('');
        $('.multi_voto_select').prop('disabled', 'disabled');
        $('.multi_voto_pesi_select').prop('disabled', 'disabled');
        $('#multi_tipo_voto').html('');
        $('#containerCompetenzeSingoloVoto').html('');
        $('#containerValutazioniSingoloVoto').html('');
        $('#select_elenco_template_studenti').html('');
        $('#select_studente').removeAttr('disabled');
        $('#select_materia').removeAttr('disabled');
        $('#select_materia').val('0');
        $('#select_id_materia_multivoto').removeAttr('disabled');
        $('#select_id_materia_multivoto').val('0');
        $('#btn_aggiungi_competenze_singolo_voto').show();
        $('#btn_aggiungi_competenze_multi_voto').show();
        $('#btn_aggiungi_nota_didattica_multi_voto').show();
        $('#spazioNuoveValutazioniSingoloVoto').hide();
        $('#containerCompetenzeMultivoto').hide();

        $('.cella-nota-didattica').remove();
        $('#multi_commento').css('width', '90%');
        $('#multi_commento_nota').remove();
        multi_note_didattiche = false;

        elencoCompetenzeMultivotoAggiunte = {};
        multivotoCompetenzePresenti = {};
        $('.cella-competenza').remove();
        $('.div-commento-competenze').remove();
        //elimino le colonne aggiunte delle competenze

        //vuoto il local storage
        sessionStorage.removeItem('document');
        sessionStorage.removeItem('elencoTemplateStudenti');
        sessionStorage.removeItem('studentiCompetenze');
    }

    function ottieni_token()
    {
        if ($('#login_centralizzato').val() == 'SI') {
            token = $('#current_key').val();
        } else {
            var query = {
                uid: $('#current_user').val(),
                ukey: $('#current_key').val()
            };

            $.ajax({
                type: 'POST',
                url: "../next-api/v1/login",
                data: query,
                cache: false,
                complete: function(response) {
                    token = response.responseText;
                }
            });
        }
    }

    function cercaCompetenza(elencoCompetenze, valore)
    {
        var datiCompetenza = null;
        var BreakException = {};

        if (Object.keys(elencoCompetenze).length > 0)
        {
            try {
                Object.keys(elencoCompetenze).forEach(function(key){
                    //console.log(String(item['id'])+' --- '+String(valore));
                    if (String(elencoCompetenze[key]['id']) === String(valore))
                    {
                        datiCompetenza = elencoCompetenze[key];
                        throw BreakException;
                    }
                    else if (elencoCompetenze[key]['figli'])
                    {
                        datiCompetenza = cercaCompetenza(elencoCompetenze[key]['figli'], valore);
                        if (datiCompetenza !== null){
                            throw BreakException;
                        }
                    }
                });
            } catch (e){
                if (e !== BreakException) throw e;
            }
        }
        return datiCompetenza;
    }

    function adattaPerAlbero(obj, idMateriaFiltro = '')
    {
        Object.keys(obj).forEach(function(key){
            if (obj[key]['figli'])
            {
                var figli = adattaPerAlbero(obj[key]['figli'], idMateriaFiltro);
                obj[key]['items'] = figli;
            }

            var inserimentoAbilitato = false;
            if (typeof obj[key]['vincoli'] === 'undefined'){
                //caso nessun vincolo
                inserimentoAbilitato = true;
            } else {
                if (typeof obj[key]['vincoli']['materie'] === 'undefined'){
                    //caso nessun vincolo per le materie
                    inserimentoAbilitato = true;
                } else {
                    elenco_materie_classe_professore['professore'].forEach(function (materia){
                        Object.keys(obj[key]['vincoli']['materie']).forEach(function (index){
                            if ((obj[key]['vincoli']['materie'][index]['id'] === materia['id_materia'] && idMateriaFiltro === '') || (idMateriaFiltro == obj[key]['vincoli']['materie'][index]['id'])){
                                inserimentoAbilitato = true;
                            }
                        });
                    });
                }
            }

            if (inserimentoAbilitato && (typeof obj[key]['items'] !== 'undefined' && Object.keys(obj[key]['items']).length == 0)){
                inserimentoAbilitato = false;
                if (typeof obj[key]['schema_valutazioni'] !== 'undefined'){
                    if (obj[key]['schema_valutazioni'].length > 0){
                        inserimentoAbilitato = true;
                    }
                }
            }

            if (!obj[key]['codice']){
                obj[key]['codice'] = '';
            }

            if (!obj[key]['descrizione']){
                obj[key]['descrizione'] = '';
            }

            if (filtroAlberoCompetenzeProfessore === 'solo_competenze_valutabili'){
                if (inserimentoAbilitato){
                    obj[key]['label'] = obj[key]['codice'].replace(/\"/g, "&quot;") + ' - <span style=\'opacity: 0.7;\'>' + obj[key]['descrizione'].replace(/\"/g, "&quot;") + '</span>';
                    obj[key]['value'] = obj[key]['id'];
                    obj[key]['id'] = obj[key]['id'];
                } else {
                    delete obj[key];
                }
            } else {
                if (inserimentoAbilitato){
                    obj[key]['label'] = '<b>'+obj[key]['codice'] + ' - <span style=\'opacity: 0.7;\'>' + obj[key]['descrizione'] + '</span>' + obj[key]['descrizione']+'</b>';
                } else {
                    obj[key]['label'] = obj[key]['codice'];
                }
                obj[key]['value'] = obj[key]['id'];
                obj[key]['id'] = obj[key]['id'];
            }
        });

        return obj;
    }

    function creaInterfacciaCompetenze(){
        var html = '';
        var containerCompetenze = $('#containerCompetenze');
        var document = JSON.parse(sessionStorage.getItem('document'));

        html += "<table width='100%' style='table-layout: fixed;'>";
        html +=     "<tr>";
        html +=         "<td width='40%' align='left' style='padding: 5px; vertical-align: top;'>";
        html +=             "<b>{/literal}{mastercom_label}Elenco{/mastercom_label}{literal}</b>";
        html +=             "<div id='jqxTreeComp'></div>";
        html +=             "<div align='left'>";
        html +=                 "<input type='button' value='Espandi' id='btnEspandiAlbero' style='margin: 5px;' />";
        html +=                 "<input type='button' value='Collassa' id='btnCollassaAlbero'/>";
        html +=             "</div>";
        html +=         "</td>";
        html +=         "<td width='60%' style='padding: 5px; vertical-align: top;'>";
        html +=             "<div id='tabs'>";
        html +=                 "<ul>";
        html +=                     "<li>{/literal}{mastercom_label}Valutazioni{/mastercom_label}{literal}</li>";
        html +=                     "<li>{/literal}{mastercom_label}Informazioni{/mastercom_label}{literal}</li>";
        html +=                 "</ul>";
        html +=                 "<div id='divValutazioniCompetenza'></div>";
        html +=                 "<div id='divInformazioniCompetenza'></div>";
        html +=             "</div>";
        html +=         "</td>";
        html +=     "</tr>";
        html += "</table>";

        containerCompetenze.html(html);

        $('#jqxTreeComp').jqxTree({height: $(window).height()/3*2, allowDrag: false});
        $("#btnEspandiAlbero").jqxButton({theme: 'material', height: '30px', template: 'default'});
        $("#btnCollassaAlbero").jqxButton({theme: 'material', height: '30px', template: 'default'});
        $('#tabs').jqxTabs({theme: 'material', position: 'top'});

        idCompetenza = '';

        if (Object.keys(document['competenze']).length > 0){
            var dataTree = document['competenze'];
            dataTreeAdapted = adattaPerAlbero(dataTree);
            $('#jqxTreeComp').jqxTree({ source: dataTreeAdapted});
        }

        $('#jqxTreeComp').on('select', function (event)
        {
            var args = event.args;
            var item = $('#jqxTreeComp').jqxTree('getItem', args.element);
            var document = JSON.parse(sessionStorage.getItem('document'));

            var datiCompetenza = cercaCompetenza(document['competenze'], item['id']);

            if (datiCompetenza){
                caricaDatiCompetenza(datiCompetenza);
                idCompetenza = item['id'];
            }
        });

        $("#btnEspandiAlbero").on('click', function (){
            $('#jqxTreeComp').jqxTree('expandAll');
        });

        $("#btnCollassaAlbero").on('click', function (){
            $('#jqxTreeComp').jqxTree('collapseAll');
        });
    }

    function creaInterfacciaCompetenzeSingoloVoto(id_container_competenze, id_materia, id_container_nuove_valutazioni){
        var html = '';
        var containerCompetenze = $('#'+id_container_competenze);
        var document = JSON.parse(sessionStorage.getItem('document'));

        html += "<table width='100%' style='table-layout: fixed;'>";
        html +=     "<tr>";
        html +=         "<td width='40%' align='left' style='padding: 5px; vertical-align: top;'>";
        html +=             "<b>{/literal}{mastercom_label}Elenco Competenze{/mastercom_label}{literal}</b>";
        html +=             "<div id='jqxTreeCompSingoloVoto'></div>";
        html +=         "</td>";
        html +=         "<td width='60%' style='padding: 5px; vertical-align: top;'>";
        html +=             "<div id='tabs'>";
        html +=                 "<ul>";
        html +=                     "<li>{/literal}{mastercom_label}Aggiungi Valutazione{/mastercom_label}{literal}</li>";
        html +=                     "<li>{/literal}{mastercom_label}Informazioni{/mastercom_label}{literal}</li>";
        html +=                     "<li>{/literal}{mastercom_label}Valutazioni Presenti{/mastercom_label}{literal}</li>";
        html +=                 "</ul>";
        html +=                 "<div id='divValutazioniCompetenza'></div>";
        html +=                 "<div id='divInformazioniCompetenza'></div>";
        html +=                 "<div id='divValutazioniPresentiCompetenza'></div>";
        html +=             "</div>";
        html +=         "</td>";
        html +=     "</tr>";
        html += "</table>";

        containerCompetenze.html(html);

        $('#jqxTreeCompSingoloVoto').jqxTree({height: $(window).height()/3, allowDrag: false});
        $('#tabs').jqxTabs({theme: 'material', position: 'top'});

        idCompetenza = '';

        if (Object.keys(document['competenze']).length > 0){
            var dataTree = document['competenze'];
            dataTreeAdapted = adattaPerAlbero(dataTree, id_materia);
            $('#jqxTreeCompSingoloVoto').jqxTree({ source: dataTreeAdapted});
            $('#jqxTreeCompSingoloVoto').jqxTree('expandAll');
        }

        $('#jqxTreeCompSingoloVoto').on('select', function (event)
        {
            var args = event.args;
            var item = $('#jqxTreeCompSingoloVoto').jqxTree('getItem', args.element);
            var document = JSON.parse(sessionStorage.getItem('document'));

            var datiCompetenza = cercaCompetenza(document['competenze'], item['id']);

            if (datiCompetenza){
                caricaDatiCompetenzaSingoloVoto(datiCompetenza, id_materia, id_container_nuove_valutazioni);
                idCompetenza = item['id'];
            }
        });
    }

    function caricaDatiCompetenza(datiCompetenza)
    {
        creaInterfacciaCompetenza(datiCompetenza);

        $("#descrizioneCompetenzaVal").html(datiCompetenza['descrizione']);
        $("#descrizioneCompetenza").html(datiCompetenza['descrizione']);
        $("#descrizioneStampaCompetenza").html(datiCompetenza['descrizione_stampa']);
        $("#codiceCompetenza").html(datiCompetenza['codice']);

        var inserimentoAbilitato = false;
        if (typeof datiCompetenza['vincoli'] === 'undefined'){
            //caso nessun vincolo
            inserimentoAbilitato = true;
        } else {
            if (typeof datiCompetenza['vincoli']['materie'] === 'undefined'){
                //caso nessun vincolo per le materie
                inserimentoAbilitato = true;
            } else {
                elenco_materie_classe_professore['professore'].forEach(function (materia){
                    Object.keys(datiCompetenza['vincoli']['materie']).forEach(function (index){
                        if (datiCompetenza['vincoli']['materie'][index]['id'] === materia['id_materia']){
                            inserimentoAbilitato = true;
                        }
                    });
                 });
            }
        }

        var html = "";
        var valDocente = [];
        if (typeof datiCompetenza['valutazioni'] !== 'undefined'){
            html += "<table width='100%' style='table-layout: fixed;'>";
            Object.keys(datiCompetenza['valutazioni']).forEach(function (index){
                //console.log(datiCompetenza['valutazioni'][index]);
                var valutazione = datiCompetenza['valutazioni'][index];
                var btnEliminaValutazione = "";
                var btnModificaValutazione = "";

                if (valutazione['insert_id'] === user_couch_id){
                    valDocente.push(index);
                    btnEliminaValutazione = "<input type='button' value='X' id='jqxButtonDelVal"+index+"' />";
                    btnModificaValutazione = "<input type='button' value='i' id='jqxButtonModVal"+index+"' style='margin-left: 5px;'/>";
                }

                html += "<tr style='border-top: 1px solid lightgray; border-bottom: 1px solid lightgray;'>";
                html +=     "<td class='padding_cella_generica'>"+btnEliminaValutazione+btnModificaValutazione+"</td>";
                html +=     "<td class='padding_cella_generica'><b>"+valutazione['codice']+"</b></td>";

                if (gestione_pesi_competenze == 'SI'){
                    html += "<td class='padding_cella_generica'><span class='annotazione_leggera'>peso: </span>";
                    elenco_pesi_competenze.forEach(function (peso){
                        if (valutazione['id_peso'] && valutazione['id_peso'] == peso.id_peso){
                            html += peso.peso;
                        }
                    });
                    html += "</td>";
                }

                html +=     "<td class='padding_cella_generica'>"+valutazione['data']+"</td>";
                html +=     "<td class='padding_cella_generica' style='white-space: nowrap; overflow: hidden; text-overflow: ellipsis;'>"+valutazione['descrizione_materia']+"</td>";
                html += "</tr>";
                if (typeof valutazione['note'] !== 'undefined'){
                    //html += "<tr>";
                    {/literal}
                    //html +=     "<td></td>";
                    //html +=     "<td class='padding_cella_generica' colspan='3'>{mastercom_label}Note{/mastercom_label}: "+valutazione['note']+"</td>";
                    {literal}
                    //html += "</tr>";
                }
            });
            html += "</table>";
        } else {
            {/literal}
            var html = "<i>{mastercom_label}Non ci sono valutazioni per questa competenza{/mastercom_label}</i>";
            {literal}
        }
        $("#valutazioniCompetenza").html(html);

        if (valDocente.length > 0){
            valDocente.forEach(function(value){
                $("#jqxButtonDelVal"+value).jqxButton({theme: 'material', template: 'danger', width: 25, height: 25});
                $("#jqxButtonModVal"+value).jqxButton({theme: 'material', template: 'primary', width: 25, height: 25});

                $("#jqxButtonDelVal"+value).on('click', function (){
                    if (confirm("{/literal}{mastercom_label}Eliminare la valutazione?{/mastercom_label}{literal}") === true){
                        eliminaValutazione(datiCompetenza['valutazioni'][value]['id']);
                    }
                });
                $("#jqxButtonModVal"+value).on('click', function (){
                    creaModificaValutazione(datiCompetenza, datiCompetenza['valutazioni'][value]);
                });
            });
        }

        if (inserimentoAbilitato){
            //nuova valutazione
            creaNuovaValutazione(datiCompetenza);
        }
    }

    function caricaDatiCompetenzaSingoloVoto(datiCompetenza, id_materia, id_container_nuove_valutazioni)
    {
        creaInterfacciaCompetenzaSingoloVoto(datiCompetenza);

        $("#descrizioneCompetenzaVal").html(datiCompetenza['descrizione']);
        $("#descrizioneCompetenza").html(datiCompetenza['descrizione']);
        $("#descrizioneStampaCompetenza").html(datiCompetenza['descrizione_stampa']);
        $("#codiceCompetenza").html(datiCompetenza['codice']);

        if (typeof datiCompetenza['valutazioni'] !== 'undefined'){
            let valutazioniPresenti = false;
            html = "<table width='100%' style='table-layout: fixed;'>";
            Object.keys(datiCompetenza['valutazioni']).forEach(function (index){
                //console.log(datiCompetenza['valutazioni'][index]);
                var valutazione = datiCompetenza['valutazioni'][index];

                if (valutazione['insert_id'] === user_couch_id && valutazione['id_materia'] == id_materia){
                    valutazioniPresenti = true;

                    html += "<tr style='border-top: 1px solid lightgray; border-bottom: 1px solid lightgray;'>";
                    html +=     "<td class='padding_cella_generica'><b>"+valutazione['codice']+"</b></td>";

                    if (gestione_pesi_competenze == 'SI'){
                        html += "<td class='padding_cella_generica'><span class='annotazione_leggera'>peso: </span>";
                        elenco_pesi_competenze.forEach(function (peso){
                            if (valutazione['id_peso'] && valutazione['id_peso'] == peso.id_peso){
                                html += peso.peso;
                            }
                        });
                        html += "</td>";
                    }

                    html +=     "<td class='padding_cella_generica'>"+valutazione['data']+"</td>";
                    html +=     "<td class='padding_cella_generica' style='white-space: nowrap; overflow: hidden; text-overflow: ellipsis;'>"+valutazione['descrizione_materia']+"</td>";
                    html +=     "<td class='padding_cella_generica' style='white-space: nowrap; overflow: hidden; text-overflow: ellipsis;'>";
                    if (typeof valutazione['note'] !== 'undefined'){
                        html +=    valutazione['note'];
                    }
                    html +=     "</td>";
                    html += "</tr>";
                }
            });
            html += "</table>";

            if (!valutazioniPresenti){
                {/literal}
                html = "<i>{mastercom_label}Non ci sono valutazioni per questa materia{/mastercom_label}</i>";
                {literal}
            }
        } else {
            {/literal}
            var html = "<i>{mastercom_label}Non ci sono valutazioni per questa competenza{/mastercom_label}</i>";
            {literal}
        }
        $("#ValutazioniPresentiCompetenza").html(html);

        creaNuovaValutazioneSingoloVoto(datiCompetenza, id_container_nuove_valutazioni);
    }

    function caricaDatiCompetenzaMultivoto(datiCompetenza)
    {
        creaInterfacciaCompetenzaMultiVoto(datiCompetenza);

        $("#descrizioneCompetenza").html(datiCompetenza['descrizione']);
        $("#descrizioneStampaCompetenza").html(datiCompetenza['descrizione_stampa']);
        $("#codiceCompetenza").html(datiCompetenza['codice']);

        if (typeof datiCompetenza['schema_valutazioni'] !== 'undefined' && (datiCompetenza['schema_valutazioni'].length > 0 || Object.keys(datiCompetenza['schema_valutazioni']).length > 0)){
            $("#btnAggiungiMultiComp").jqxButton({ template: "success", width: 110, height: 35 });

            let classId = 'comp_' + (chiaveTemplateMultivoto + '__' + datiCompetenza['id']).replace(/\./g, "__");
            if (typeof multivotoCompetenzePresenti[classId] !== 'undefined'){
                $('#btnAggiungiMultiComp').jqxButton({disabled: true });
            }

            $('#btnAggiungiMultiComp').on('click', function () {
                aggiungiColonnaCompetenzaMultivoto(datiCompetenza, classId);
                $('#btnAggiungiMultiComp').jqxButton({disabled: true });
            });
        } else {
            $("#btnAggiungiMultiComp").hide();
        }
    }

    function aggiungiColonnaCompetenzaMultivoto(datiCompetenza, classId)
    {
        var idCompetenza = $('#jqxTreeComp').jqxTree('getSelectedItem').value;
        let elencoTemplateStudenti = JSON.parse(sessionStorage.getItem('elencoTemplateStudenti'));
        let studentiCompetenze = JSON.parse(sessionStorage.getItem('studentiCompetenze'));
        let codiceRamo = estraiStringaRamo(elencoTemplateStudenti[chiaveTemplateMultivoto]['competenze'], datiCompetenza);

        $('#elenco_studenti_multivoto tr').each(function (i, row) {
            let tipoColonna = row.getAttribute('data-riga');
            let html = '';
            multivotoCompetenzePresenti[classId] = classId;

            if (tipoColonna === 'titoli'){
                html += '<td align="center" class="padding_cella_generica cella-competenza ' + classId + '">';
                html +=     '<div align="center" class="tooltip">';
                html +=         '<b>' + datiCompetenza['codice'] + '</b>';
                html +=         '<div class="tooltiptext tooltiptext-novisible">' + codiceRamo['tracciato_opacity'] + '</div>';
                html +=     '</div>';
                html +=     '<div align="center">';
                html +=         '<button type="button" class="btn_flat testo_rosso sfondo_bianco" style="padding: 1px 20px; border: 1px solid red;" ';
                html +=             'onclick="eliminaColonnaCompetenzaMultivoto(\'' + classId + '\');"';
                html +=             '>X';
                html +=         '</button>';

                if (gestione_pesi_competenze == 'SI'){
                    var classNamePesoCompetenza = 'select-peso-comp-'+datiCompetenza['id']+'-'+elencoTemplateStudenti[chiaveTemplateMultivoto]['id_template_origine'].replace('.', '');
                    html += '<select class="select" style="margin-left: 10px;" onchange="settaMultiPesi(\''+classNamePesoCompetenza+'\', this.value);">';
                    elenco_pesi_competenze.forEach(function (peso){
                        html += '<option value="'+peso.id_peso+'"';
                        if (peso.id_peso == peso_default_competenze.id_peso){
                            html += ' selected ';
                        }
                        html += '>'+peso.peso+'</option>';
                    });
                    html += '</select>';
                }

                html +=     '</div>';
                html += '</td>';
                $(this).append(html);
            } else {
                let idStudente = row.getAttribute('data-id_studente');
                html += '<td align="center" class="cella-competenza ' + classId + '">';

                if (typeof studentiCompetenze[idStudente] !== 'undefined' && typeof elencoTemplateStudenti[chiaveTemplateMultivoto] !== 'undefined')
                {
                    if (studentiCompetenze[idStudente]['id_template_origine'] == elencoTemplateStudenti[chiaveTemplateMultivoto]['id_template_origine']
                            &&
                        studentiCompetenze[idStudente]['revisione_template_origine'] == elencoTemplateStudenti[chiaveTemplateMultivoto]['revisione_template_origine']){

                        var nuovaValutazione = {};
                        var idMateriaValutazione = $('#select_id_materia_multivoto').val();
                        var descrizioneMateriaValutazione = $.trim($('#select_id_materia_multivoto option:selected').text());

                        html +=     '<select name="multivoto[dati]['+idStudente+'][competenze]['+datiCompetenza['id']+']" class="select select-val-comp">';
                        html +=         '<option value="">---</option>';
                        Object.keys(datiCompetenza['schema_valutazioni']).forEach(function (index){
                            if (datiCompetenza['schema_valutazioni'][index]['tipo'] === 'generale'){
                                nuovaValutazione = {};
                                nuovaValutazione = $.extend(true,{},datiCompetenza['schema_valutazioni'][index]);

                                nuovaValutazione['id_valutazione_schema'] = nuovaValutazione['id'];
                                delete nuovaValutazione['id'];
                                nuovaValutazione['id_materia'] = idMateriaValutazione;
                                nuovaValutazione['descrizione_materia'] = descrizioneMateriaValutazione;

                                html += '<option value="'+JSON.stringify(nuovaValutazione).replace(/\"/g, "&quot;")+'">'+datiCompetenza['schema_valutazioni'][index]['codice']+'</option>';
                            }
                        });
                        html +=     '</select>';

                        if (gestione_pesi_competenze == 'SI'){
                            html += '<select name="multivoto[dati]['+idStudente+'][pesi_competenze]['+datiCompetenza['id']+']" class="select select-peso-comp-'+datiCompetenza['id']+'-'+studentiCompetenze[idStudente]['id_template_origine'].replace('.', '')+'">';
                            elenco_pesi_competenze.forEach(function (peso){
                                html += '<option value="'+peso.id_peso+'"';
                                if (peso.id_peso == peso_default_competenze.id_peso){
                                    html += ' selected ';
                                }
                                html += '>'+peso.peso+'</option>';
                            });
                            html += '</select>';
                        }
                    }
                }

                html += '</td>';
                $(this).append(html);
            }
        });

        htmlCommento = "<div class='bordo_alto_generico padding6 "+classId+" div-commento-competenze' style='margin-top: 5px;' align='left'>" + codiceRamo['tracciato_opacity'] + '<br>';
        htmlCommento +=     "<div align='center' class='padding3'><textarea name='multivoto[commento_competenze]["+datiCompetenza['id']+"]' placeholder='{/literal}{mastercom_label}Commento alla competenza/obiettivo{/mastercom_label}{literal} "+codiceRamo['tracciato']+"' rows='3' style='width: 75%;'></textarea></div>";
        htmlCommento += "</div>";
        $('#commenti_multi_competenze').append(htmlCommento);
    }

    function eliminaColonnaCompetenzaMultivoto(classeHtml)
    {
        //$('.'+classeHtml).fadeOut(500, function() { $(this).remove(); });
        $('.'+classeHtml).animate({
            opacity: 0,
            width: "toggle"
        }, 500, function() {
            this.remove();
        });
        delete multivotoCompetenzePresenti[classeHtml];
    }

    function creaModificaValutazione(datiCompetenza, valutazione){
        {/literal}
        html = '<fieldset style="margin-bottom: 20px; -moz-border-radius:5px; -webkit-border-radius:5px; border-radius:5px; border: 0.5px solid #0077BE;">';
        html += '<legend style="color: #0077BE;">{mastercom_label}Modifica valutazione{/mastercom_label}</legend>';
        html += '<div style="display: flex; flex-wrap: wrap; justify-content: flex-start;">';
        html +=     '<div style="display: flex; padding: 0px 15px 15px 0px;">';
        html +=         '{mastercom_label}Data{/mastercom_label}: ';
        html +=         '<input type="date" value="'+valutazione['data']+'" disabled>';
        html +=     '</div>';
        html +=     '<div style="padding: 0px 15px 15px 0px;">';
        html +=         '{mastercom_label}Valutazione{/mastercom_label}: ';
        html +=         '<select class="select" id="idModificaValutazioneAssegnata">';
        Object.keys(datiCompetenza['schema_valutazioni']).forEach(function (index){
            if (datiCompetenza['schema_valutazioni'][index]['tipo'] === 'generale'){
                var selected = (datiCompetenza['schema_valutazioni'][index]['id'] === valutazione['id_valutazione_schema']) ? ' selected ' : '';
                html +=     '<option value="'+datiCompetenza['schema_valutazioni'][index]['id']+'" '+selected+'>'+datiCompetenza['schema_valutazioni'][index]['codice']+'</option>';
            }
        });
        html +=         '</select>';
        html +=     '</div>';

        if (gestione_pesi_competenze == 'SI'){
            var peso_trovato = false;
            html += '<div style="padding: 0px 15px 15px 0px;">';
            html +=     '{mastercom_label}Peso{/mastercom_label}: ';
            html +=     '<select class="select" id="idPesoModificaValutazioneAssegnata">';
            elenco_pesi_competenze.forEach(function (peso){
                html += '<option value="'+peso.id_peso+'"';
                if (peso.id_peso == peso_default_competenze.id_peso){
                    html += ' selected ';
                }
                if (valutazione['id_peso'] && valutazione['id_peso'] == peso.id_peso){
                    peso_trovato = true;
                }
                html +=     '>'+peso.peso+'</option>';
            });
            html +=     '</select>';
            html += '</div>';
        }

        html +=     '<div style="padding: 0px 15px 15px 0px;">';
        html +=         '{mastercom_label}Materia{/mastercom_label}: ';
        html +=         '<select id="selectMateriaModificaValutazioneCompetenza" class="select">';
        elenco_materie_classe_professore['professore'].forEach(function (materia){
            var selected = (materia['id_materia'] === valutazione['id_materia']) ? ' selected ' : '';
            if (typeof datiCompetenza['vincoli'] !== 'undefined'){
                if (typeof datiCompetenza['vincoli']['materie'] !== 'undefined'){
                    Object.keys(datiCompetenza['vincoli']['materie']).forEach(function (index){
                        if (datiCompetenza['vincoli']['materie'][index]['id'] === materia['id_materia']){
                            html += '<option value="'+materia['id_materia']+'" '+selected+'>'+materia['descrizione']+'</option>';
                        }
                    });
                } else {
                    html +=         '<option value="'+materia['id_materia']+'" '+selected+'>'+materia['descrizione']+'</option>';
                }
            } else {
                html +=             '<option value="'+materia['id_materia']+'" '+selected+'>'+materia['descrizione']+'</option>';
            }
        });
        html +=         '</select>';
        html +=     '</div>';
        html +=     '<div style="padding: 0px 15px 15px 0px;">';
        var note = (typeof valutazione['note'] !== 'undefined') ? valutazione['note'] : '';
        html +=         '<textarea rows="3" cols="30" placeholder="{mastercom_label}Note{/mastercom_label}" id="noteModificaValutazione">'+note+'</textarea>';
        html +=     '</div>';
        html +=     '<div style="padding: 0px 15px 15px 0px;">';
        var noteRiservate = (typeof valutazione['note_riservate'] !== 'undefined') ? valutazione['note_riservate'] : '';
        html +=         '{mastercom_label}Note riservate{/mastercom_label}:<br><textarea rows="3" cols="30" id="noteRiservateModificaValutazione">'+noteRiservate+'</textarea>';
        html +=     '</div>';

        if ((typeof datiCompetenza['dimensioni'] != 'undefined' && Object.keys(datiCompetenza['dimensioni']).length > 0) || (typeof valutazione['dimensioni'] != 'undefined' && Object.keys(valutazione['dimensioni']).length > 0)){
            var dimensioniCompetenza = Object.assign({}, datiCompetenza['dimensioni']);

            try {
                var idDimensioniGenerali = Object.keys(datiCompetenza['dimensioni']);
                var idDimensioniValutate = Object.keys(valutazione['dimensioni']);

                idDimensioniValutate.forEach(function(idDimValutazione){
                    if (!idDimensioniGenerali.includes(idDimValutazione)){
                        dimensioniCompetenza[idDimValutazione] = valutazione['dimensioni'][idDimValutazione];
                    }
                });
            } catch (e){}

            datiCompetenza['dimensioni'] = dimensioniCompetenza;

            Object.keys(dimensioniCompetenza).forEach(function (index){
                var dimensioneValutata = false;
                if (typeof valutazione['dimensioni'] != 'undefined' && typeof valutazione['dimensioni'][index] != 'undefined'){
                    dimensioneValutata = true;
                    var idValorePrecompSel = valutazione['dimensioni'][index]['valori_precomp'][Object.keys(valutazione['dimensioni'][index]['valori_precomp'])[0]]['id_valore_precomp'];
                }
                var campoTesto = false;
                html += '<div style="padding: 0px 15px 15px 0px;">';
                var dim = dimensioniCompetenza[index];
                html += dim['nome'] + ': ';
                html += '<select class="select" data-tipo="dimensione_modifica" data-id-campo-libero="'+dim['id_campo_libero']+'" onchange="toggleTestoLiberoDimensione(this, '+dim['id_campo_libero']+', \'_modifica\');">';
                //if (!dimensioneValutata){
                    html +=     '<option value="0">---</option>';
                //}

                if (dimensioneValutata && typeof dim['valori_precomp'][idValorePrecompSel] == 'undefined'){
                    dim['valori_precomp'][idValorePrecompSel] = valutazione['dimensioni'][index]['valori_precomp'][idValorePrecompSel];
                }

                Object.keys(dim['valori_precomp']).forEach(function (indexVP){
                    html +=     '<option value="'+dim['valori_precomp'][indexVP]['id_valore_precomp']+'" ';
                    if (dimensioneValutata){
                        if (idValorePrecompSel == dim['valori_precomp'][indexVP]['id_valore_precomp']){
                            html += " selected ";
                        }
                    }
                    html +=     '>'+dim['valori_precomp'][indexVP]['descrizione']+'</option>';
                    if (dim['valori_precomp'][indexVP]['id_valore_precomp'] == -1){
                        campoTesto = true;
                    }
                });
                html += '</select>';

                if (campoTesto){
                    html += '<input type="text" data-tipo="dimensione_testo_modifica" data-id-campo-libero="'+dim['id_campo_libero']+'" class="margin-left8" ';
                    if (idValorePrecompSel != '-1'){
                        html += ' style="display: none;" ';
                    } else {
                        html += ' value="'+valutazione['dimensioni'][index]['valori_precomp'][idValorePrecompSel]['valore_testo']+'" ';
                    }
                    html += ">";
                }

                html += '</div>';
            });
        }

        html +=     '<div style="padding: 0px 15px 15px 0px;">';
        html +=         '{mastercom_label}Inserita il {/mastercom_label}'+valutazione['insert_date']+'<br>';
        html +=         '{mastercom_label}Ultima modifica il {/mastercom_label}'+valutazione['modify_date'];
        html +=     '</div>';
        html += '</div>';
        html += '<div align="center" style="padding-top: 10px;">';
        html +=     '<input type="button" value="{mastercom_label}Salva{/mastercom_label}" id="btnSalvaModificaValutazione" style="margin: 5px;" />';
        html +=     '<input type="button" value="{mastercom_label}Annulla{/mastercom_label}" id="btnAnnullaModificaValutazione" style="margin: 5px;" />';
        html += '</div>';
        html += '</fieldset>';
        {literal}
        $("#modificaValutazione").html(html);
        $('#idModificaValutazioneAssegnata').css("max-width", $(window).width()/2);
        $('#selectMateriaModificaValutazioneCompetenza').css("max-width", $(window).width()/2);

        if (gestione_pesi_competenze == 'SI' && peso_trovato){
            $("#idPesoModificaValutazioneAssegnata").val(valutazione['id_peso']).trigger("change");
        }

        $("#btnSalvaModificaValutazione").jqxButton({theme: 'material', template: 'success' });
        $("#btnAnnullaModificaValutazione").jqxButton({theme: 'material', template: 'danger' });

        $("#btnSalvaModificaValutazione").on('click', function (){
            if ($('#selectMateriaModificaValutazioneCompetenza').val() > 0 && $('#idModificaValutazioneAssegnata option:selected').text() !== '---'){
                    modificaValutazione(datiCompetenza, valutazione);
            } else {
                {/literal}
                alert("{mastercom_label}Compilare valutazione e materia{/mastercom_label}");
                {literal}
            }
        });

        $("#btnAnnullaModificaValutazione").on('click', function (){
            $("#modificaValutazione").html('');
        });
    }

    function modificaValutazione(datiCompetenza, valutazione)
    {
        $("#jqxLoader").jqxLoader('open');
        var idValutazioneSchema = $('#idModificaValutazioneAssegnata').val();
        valutazione['id_materia'] = $('#selectMateriaModificaValutazioneCompetenza').val();
        valutazione['descrizione_materia'] = $('#selectMateriaModificaValutazioneCompetenza option:selected').text();
        var noteValutazione = $('#noteModificaValutazione').val();

        if (noteValutazione !== ''){
            valutazione['note'] = noteValutazione;
        } else if (typeof valutazione['note'] !== 'undefined') {
            delete valutazione['note'];
        }

        if (noteRiservateValutazione !== ''){
            valutazione['note_riservate'] = noteRiservateValutazione;
        } else if (typeof valutazione['note_riservate'] !== 'undefined') {
            delete valutazione['note_riservate'];
        }

        if (gestione_pesi_competenze == 'SI'){
            valutazione['id_peso'] = parseInt($("#idPesoModificaValutazioneAssegnata").val());
        }

        Object.keys(datiCompetenza['schema_valutazioni']).forEach(function (index){
            if (datiCompetenza['schema_valutazioni'][index]['id'] === idValutazioneSchema){
                var nuovaValutazione = datiCompetenza['schema_valutazioni'][index];
                valutazione['id_valutazione_schema'] = idValutazioneSchema;
                valutazione['codice'] = nuovaValutazione['codice'];
                valutazione['descrizione'] = nuovaValutazione['descrizione'];
                valutazione['descrizione_stampa'] = nuovaValutazione['descrizione_stampa'];
                valutazione['gruppo'] = nuovaValutazione['gruppo'];
                valutazione['ordinamento'] = nuovaValutazione['ordinamento'];
                valutazione['tipo'] = nuovaValutazione['tipo'];
                valutazione['valore_numerico'] = nuovaValutazione['valore_numerico'];
                valutazione['inseribile_su_assente'] = nuovaValutazione['inseribile_su_assente'];
            }
        });

        //dimensioni
        if ($('[data-tipo="dimensione_modifica"]').length > 0){
            var dimensioniValutabili = $('[data-tipo="dimensione_modifica"]');
            delete valutazione['dimensioni'];
            dimensioniValutabili.each(function(index, dimVal){
                var valore = $(dimVal).val();
                if (valore != '0'){
                    var idCampoLibero = $(dimVal).data().idCampoLibero;

                    if (typeof valutazione['dimensioni'] == 'undefined'){
                        valutazione['dimensioni'] = {};
                    }

                    valutazione['dimensioni'][idCampoLibero] = $.extend(true,{},datiCompetenza['dimensioni'][idCampoLibero]);
                    valutazione['dimensioni'][idCampoLibero]['valori_precomp'][valore]['selezionato'] = 'SI';

                    if (valore == '-1'){
                        valutazione['dimensioni'][idCampoLibero]['valori_precomp'][valore]['valore_testo'] = $('[data-tipo="dimensione_testo_modifica"][data-id-campo-libero="'+idCampoLibero+'"]').val();
                    }

                    //per togliere i valori precomp non selezionati
                    Object.keys(valutazione['dimensioni'][idCampoLibero]['valori_precomp']).forEach(function(ndx){
                        var valPrec = valutazione['dimensioni'][idCampoLibero]['valori_precomp'][ndx];
                        if (valPrec['selezionato'] == 'NO'){
                            delete valutazione['dimensioni'][idCampoLibero]['valori_precomp'][ndx];
                        }
                    });
                }
            });
        }

        if (valutazione['data'] > oggi){
            {/literal}
            alert("{mastercom_label}Non si può inserire una valutazione nel futuro{/mastercom_label}");
            $("#jqxLoader").jqxLoader('close');
            {literal}
        } else {
            var data = {
                "valutazione":  valutazione
            };

            $.ajax({
                type: 'POST',
                url: '../next-api/v1/competenze_studente/'+idStudenteCompetenza+'/'+idCompetenza+'/'+valutazione['id'],
                data: data,
                cache: false,
                headers: {'Authorization':token},
                complete: function(response) {
                    if (response.responseJSON !== null)
                    {
                        if (response.responseJSON === false){
                            console.log('Errore');
                        } else {
                            $.ajax({
                                type: 'GET',
                                url: '../next-api/v1/competenze_studente/'+idStudenteCompetenza,
                                data: '',
                                cache: false,
                                headers: {'Authorization':token},
                                complete: function(response) {
                                    sessionStorage.setItem('document',JSON.stringify(response.responseJSON));
                                    var document = JSON.parse(sessionStorage.getItem('document'));

                                    var datiCompetenzaAggiornata = cercaCompetenza(document['competenze'], idCompetenza);

                                    if (datiCompetenzaAggiornata){
                                        caricaDatiCompetenza(datiCompetenzaAggiornata);

                                        Object.keys(datiCompetenzaAggiornata['valutazioni']).forEach(function (i){
                                            if (String(datiCompetenzaAggiornata['valutazioni'][i]['id']) === String(valutazione['id'])){
                                                let idTmp = 'val_comp_'+idStudenteCompetenza+'_'+idCompetenza+'_'+valutazione['id'];
                                                idTmp = idTmp.replace(/\./g, '\\.');
                                                let el = $('#'+idTmp+' .voto_btn');
                                                el.text(datiCompetenzaAggiornata['valutazioni'][i]['codice']);
                                            }
                                        });
                                    }
                                    $("#jqxLoader").jqxLoader('close');
                                }
                            });
                        }
                    }
                }
            });
        }
    }

    function eliminaValutazione(idValutazione)
    {
        $("#jqxLoader").jqxLoader('open');
        $.ajax({
            type: 'DELETE',
            url: '../next-api/v1/competenze_studente/'+idStudenteCompetenza+'/'+idCompetenza+'/'+idValutazione,
            data: 'wewe',
            cache: false,
            headers: {'Authorization':token},
            complete: function(response) {
                if (response.responseJSON !== null)
                {
                    if (response.responseJSON === false){
                        console.log('Errore');
                    } else {
                        $.ajax({
                            type: 'GET',
                            url: '../next-api/v1/competenze_studente/'+idStudenteCompetenza,
                            data: '',
                            cache: false,
                            headers: {'Authorization':token},
                            complete: function(response) {
                                sessionStorage.setItem('document',JSON.stringify(response.responseJSON));
                                var document = JSON.parse(sessionStorage.getItem('document'));
                                let idTmp = 'val_comp_'+idStudenteCompetenza+'_'+idCompetenza+'_'+idValutazione;
                                idTmp = idTmp.replace(/\./g, '\\.');
                                $('#'+idTmp).fadeOut();

                                var datiCompetenzaAggiornata = cercaCompetenza(document['competenze'], idCompetenza);

                                if (datiCompetenzaAggiornata){
                                    caricaDatiCompetenza(datiCompetenzaAggiornata);
                                }
                                $("#jqxLoader").jqxLoader('close');
                            }
                        });
                    }
                }
            }
        });
    }

    function eliminaValutazioneDaTabella(idStudente, idCompetenza, idValutazione)
    {
        return new Promise((resolve, reject) => {
            $.ajax({
                type: 'DELETE',
                url: '../next-api/v1/competenze_studente/'+idStudente+'/'+idCompetenza+'/'+idValutazione,
                data: '',
                cache: false,
                headers: {'Authorization':token},
                complete: function(response) {
                    var toastContainer = $('#toastsContainer');
                    var successDeleteText = '{/literal}{mastercom_label}Valutazione eliminata{/mastercom_label}';
                    var errorDeleteText = '{mastercom_label}<b>Errore!</b><br> Valutazione non eliminata correttamente{/mastercom_label}{literal}';

                    if (response.responseJSON.ok === true)
                    {
                        var toast = $('<div class="btn_pieno sfondo_verde" style="margin-top: 10px; font-weight: normal; padding: 10px 20px; text-align: left;">'+successDeleteText+'</div>');
                        chiudiSfondoOscurato();

                        let idTmp = 'val_comp_'+idStudente+'_'+idCompetenza+'_'+idValutazione;
                        idTmp = idTmp.replace(/\./g, '\\.');
                        //$('#'+idTmp).fadeOut(600, function(){
                        //    $(this).remove();
                        //});

                        $('#'+idTmp).animate({
                            opacity: 0,
                            height: "toggle"
                        }, 600, function() {
                            this.remove();
                        });
                    }
                    else
                    {
                        var toast = $('<div class="btn_pieno sfondo_rosso" style="margin-top: 10px; font-weight: normal; padding: 10px 20px; text-align: left;">'+errorDeleteText+'</div>');
                        console.log('Errore cancellazione');
                    }
                    resolve(true);

                    toast.hide(function (){
                        toast.appendTo(toastContainer);
                        toast.show().delay(3000).fadeOut(600, function(){
                            $(this).remove();
                        });
                    });
                }
            });
        });
    }

    function creaNuovaValutazione(datiCompetenza)
    {
        {/literal}
        if (datiCompetenza['schema_valutazioni']){
            html = '<fieldset style="-moz-border-radius:5px; -webkit-border-radius:5px; border-radius:5px; border: 0.5px solid #0077BE;">';
            html += '<legend style="color: #0077BE;">{mastercom_label}Nuova valutazione{/mastercom_label}</legend>';
            html += '<div style="display: flex; flex-wrap: wrap; justify-content: flex-start;">';
            html +=     '<div style="padding: 0px 15px 15px 0px;">';
            html +=         '{mastercom_label}Data{/mastercom_label}: ';
            html +=         '<input type="date" value="'+oggi+'" id="dataNuovaValutazione">';
            html +=     '</div>';
            html +=     '<div style="padding: 0px 15px 15px 0px;">';
            html +=         '{mastercom_label}Valutazione{/mastercom_label}: ';
            html +=         '<select class="select" id="idValutazioneAssegnata">';
            html +=             '<option value="0">---</option>';
            Object.keys(datiCompetenza['schema_valutazioni']).forEach(function (index){
                if (datiCompetenza['schema_valutazioni'][index]['tipo'] === 'generale'){
                    html +=     '<option value="'+datiCompetenza['schema_valutazioni'][index]['id']+'">'+datiCompetenza['schema_valutazioni'][index]['codice']+'</option>';
                }
            });
            html +=         '</select>';
            html +=     '</div>';

            if (gestione_pesi_competenze == 'SI'){
                html += '<div style="padding: 0px 15px 15px 0px;">';
                html +=     '{mastercom_label}Peso{/mastercom_label}: ';
                html +=     '<select class="select" id="idPesoValutazioneAssegnata">';
                elenco_pesi_competenze.forEach(function (peso){
                    html += '<option value="'+peso.id_peso+'"';
                    if (peso.id_peso == peso_default_competenze.id_peso){
                        html += ' selected ';
                    }
                    html +=     '>'+peso.peso+'</option>';
                });
                html +=     '</select>';
                html += '</div>';
            }

            html +=     '<div style="padding: 0px 15px 15px 0px;">';
            html +=         '{mastercom_label}Materia{/mastercom_label}: ';
            html +=         '<select id="selectMateriaNuovaValutazioneCompetenza" class="select">';
            html +=             '<option value="0">---</option>';
            elenco_materie_classe_professore['professore'].forEach(function (materia){
                if (typeof datiCompetenza['vincoli'] !== 'undefined'){
                    if (typeof datiCompetenza['vincoli']['materie'] !== 'undefined'){
                        Object.keys(datiCompetenza['vincoli']['materie']).forEach(function (index){
                            if (datiCompetenza['vincoli']['materie'][index]['id'] === materia['id_materia']){
                                html += '<option value="'+materia['id_materia']+'">'+materia['descrizione']+'</option>';
                            }
                        });
                    } else {
                        html +=         '<option value="'+materia['id_materia']+'">'+materia['descrizione']+'</option>';
                    }
                } else {
                    html +=             '<option value="'+materia['id_materia']+'">'+materia['descrizione']+'</option>';
                }
            });
            html +=         '</select>';
            html +=     '</div>';
            html +=     '<div style="display: flex; padding: 0px 15px 15px 0px;">';
            html +=         '<textarea rows="3" cols="30" placeholder="{mastercom_label}Note{/mastercom_label}" id="noteNuovaValutazione"></textarea>';
            html +=     '</div>';
            html +=     '<div style="padding: 0px 15px 15px 0px;">';
            html +=         '{mastercom_label}Note riservate{/mastercom_label}:<br><textarea rows="3" cols="30" id="noteRiservateNuovaValutazione"></textarea>';
            html +=     '</div>';
            html += '</div>';

            if (typeof datiCompetenza['dimensioni'] != 'undefined' && Object.keys(datiCompetenza['dimensioni']).length > 0){
                Object.keys(datiCompetenza['dimensioni']).forEach(function (index){
                    var campoTesto = false;
                    html += '<div style="padding: 0px 15px 15px 0px;">';
                    var dim = datiCompetenza['dimensioni'][index];
                    html += dim['nome'] + ': ';
                    html += '<select class="select" data-tipo="dimensione" data-id-campo-libero="'+dim['id_campo_libero']+'" onchange="toggleTestoLiberoDimensione(this, '+dim['id_campo_libero']+');">';
                    html +=     '<option value="0">---</option>';
                    Object.keys(dim['valori_precomp']).forEach(function (indexVP){
                        html +=     '<option value="'+dim['valori_precomp'][indexVP]['id_valore_precomp']+'">'+dim['valori_precomp'][indexVP]['descrizione']+'</option>';
                        if (dim['valori_precomp'][indexVP]['id_valore_precomp'] == -1){
                            campoTesto = true;
                        }
                    });
                    html += '</select>';

                    if (campoTesto){
                        html += '<input type="text" data-tipo="dimensione_testo" data-id-campo-libero="'+dim['id_campo_libero']+'" style="display: none" class="margin-left8">';
                    }

                    html += '</div>';
                });
            }

            html += '<div align="center" style="padding-top: 10px;">';
            html +=     '<input type="button" value="{mastercom_label}Inserisci{/mastercom_label}" id="btnAggiungiValutazione" style="margin: 5px;" />';
            html +=     '<input type="button" value="{mastercom_label}Annulla{/mastercom_label}" id="btnAnnullaInserimento" style="margin: 5px;" />';
            html += '</div>';
            html += '</fieldset>';
            {literal}
            $("#nuovaValutazione").html(html);
            $('#idValutazioneAssegnata').css("max-width", $(window).width()/2);
            $('#selectMateriaNuovaValutazioneCompetenza').css("max-width", $(window).width()/2);

            $("#btnAggiungiValutazione").jqxButton({theme: 'material', template: 'success' });
            $("#btnAnnullaInserimento").jqxButton({theme: 'material', template: 'danger' });

            $("#btnAggiungiValutazione").on('click', function (){
                var dataValutazione = $('#dataNuovaValutazione').val();
                if (dataValutazione !== '' && $('#selectMateriaNuovaValutazioneCompetenza').val() > 0 && $('#idValutazioneAssegnata option:selected').text() !== '---'){
                    if (dataValutazione > oggi){
                        {/literal}
                        alert("{mastercom_label}Non si può inserire una valutazione nel futuro{/mastercom_label}");
                        {literal}
                    } else {
                        inserisciValutazione(datiCompetenza);
                    }
                } else {
                    {/literal}
                    alert("{mastercom_label}Compilare data, valutazione e materia{/mastercom_label}");
                    {literal}
                }
            });

            $("#btnAnnullaInserimento").on('click', function (){
                $("#jqxLoader").jqxLoader('open');
                creaNuovaValutazione(datiCompetenza);
                $("#jqxLoader").jqxLoader('close');
            });
        }
    }

    function creaNuovaValutazioneSingoloVoto(datiCompetenza, id_container_nuove_valutazioni)
    {
        {/literal}
        if (datiCompetenza['schema_valutazioni']){
            html = '<fieldset style="-moz-border-radius:5px; -webkit-border-radius:5px; border-radius:5px; border: 0.5px solid #0077BE;">';
            html += '<legend style="color: #0077BE;">{mastercom_label}Nuova valutazione{/mastercom_label}</legend>';
            html += '<div style="display: flex; flex-wrap: wrap; justify-content: flex-start;">';
            html +=     '<div style="padding: 0px 15px 15px 0px;">';
            html +=         '{mastercom_label}Valutazione{/mastercom_label}: ';
            html +=         '<select class="select" id="idValutazioneAssegnata">';
            html +=             '<option value="0">---</option>';
            Object.keys(datiCompetenza['schema_valutazioni']).forEach(function (index){
                if (datiCompetenza['schema_valutazioni'][index]['tipo'] === 'generale'){
                    html +=     '<option value="'+datiCompetenza['schema_valutazioni'][index]['id']+'">'+datiCompetenza['schema_valutazioni'][index]['codice']+'</option>';
                }
            });
            html +=         '</select>';
            html +=     '</div>';

            if (gestione_pesi_competenze == 'SI'){
                html += '<div style="padding: 0px 15px 15px 0px;">';
                html +=     '{mastercom_label}Peso{/mastercom_label}: ';
                html +=     '<select class="select" id="idPesoValutazioneAssegnata">';
                elenco_pesi_competenze.forEach(function (peso){
                    html += '<option value="'+peso.id_peso+'"';
                    if (peso.id_peso == peso_default_competenze.id_peso){
                        html += ' selected ';
                    }
                    html +=     '>'+peso.peso+'</option>';
                });
                html +=     '</select>';
                html += '</div>';
            }

            html +=     '<div style="display: flex; padding: 0px 15px 15px 0px;">';
            html +=         '<textarea rows="3" cols="30" placeholder="{mastercom_label}Note{/mastercom_label}" id="noteNuovaValutazione"></textarea>';
            html +=     '</div>';
            html += '</div>';
            html += '<div align="center" style="padding-top: 10px;">';
            html +=     '<input type="button" value="{mastercom_label}Aggiungi{/mastercom_label}" id="btnAggiungiValutazioneSingoloVoto" style="margin: 5px;" />';
            html +=     '<input type="button" value="{mastercom_label}Annulla{/mastercom_label}" id="btnAnnullaInserimentoSingoloVoto" style="margin: 5px;" />';
            html += '</div>';
            html += '</fieldset>';
            {literal}
            $("#nuovaValutazione").html(html);
            $('#idValutazioneAssegnata').css("max-width", $(window).width()/2);

            $("#btnAggiungiValutazioneSingoloVoto").jqxButton({theme: 'material', template: 'success' });
            $("#btnAnnullaInserimentoSingoloVoto").jqxButton({theme: 'material', template: 'danger' });

            $("#btnAggiungiValutazioneSingoloVoto").on('click', function (){
                let valutazioneTesto = $('#idValutazioneAssegnata option:selected').text();
                if (valutazioneTesto !== '---'){
                    var idValutazioneSchema = $('#idValutazioneAssegnata').val();
                    var idMateriaValutazione = $('#select_materia').val();
                    var descrizioneMateriaValutazione = $.trim($('#select_materia option:selected').text());
                    var noteValutazione = $.trim($('#noteNuovaValutazione').val());

                    var nuovaValutazione = {};
                    Object.keys(datiCompetenza['schema_valutazioni']).forEach(function (index){
                        if (datiCompetenza['schema_valutazioni'][index]['id'] === idValutazioneSchema){
                            nuovaValutazione = $.extend(true,{},datiCompetenza['schema_valutazioni'][index]);
                        }
                    });

                    nuovaValutazione['id_valutazione_schema'] = nuovaValutazione['id'];
                    delete nuovaValutazione['id'];
                    nuovaValutazione['id_materia'] = idMateriaValutazione;
                    nuovaValutazione['descrizione_materia'] = descrizioneMateriaValutazione;

                    if (gestione_pesi_competenze == 'SI'){
                        var idPesoValutazione = $('#idPesoValutazioneAssegnata').val();
                        var testoPeso = $('#idPesoValutazioneAssegnata option:selected').text();
                        nuovaValutazione['id_peso'] = idPesoValutazione;
                    }

                    if (noteValutazione !== ''){
                        nuovaValutazione['note'] = noteValutazione;
                    }

                    //aggiungo il voto lato interfaccia
                    var document = JSON.parse(sessionStorage.getItem('document'));
                    let codiceRamo = estraiStringaRamo(document['competenze'], datiCompetenza)['tracciato_opacity'];
                    var html = '';
                    html += '<tr>';
                    html +=     '<td class="padding_cella_generica bordo_alto_generico bordo_basso_generico">';
                    html +=         '<button type="button" class="btn_flat testo_rosso ripples"';
                    html +=             'onclick="this.parentNode.parentNode.remove();"';
                    html +=         '>X</button>';
                    html +=         '<input type="hidden" name="competenze_voto['+datiCompetenza['id']+'][]" value="' + JSON.stringify(nuovaValutazione).replace(/\"/g, "&quot;") + '">';
                    html +=     '</td>';
                    html +=     '<td class="padding_cella_generica bordo_alto_generico bordo_basso_generico" style="padding: 5px 20px;"><b>' + nuovaValutazione['codice'] + '</b></td>';

                    if (gestione_pesi_competenze == 'SI'){
                        html += "<td class='padding_cella_generica bordo_alto_generico bordo_basso_generico'>";
                        html +=     "<span class='annotazione_leggera'>peso: </span>" + testoPeso;
                        html += "</td>";
                    }

                    html +=     '<td class="padding_cella_generica bordo_alto_generico bordo_basso_generico">' + codiceRamo + '</td>';
                    html +=     '<td class="padding_cella_generica bordo_alto_generico bordo_basso_generico annotazione_leggera" style="padding-left: 20px;">' + noteValutazione + '</td>';
                    html += '</tr>';
                    $('#'+id_container_nuove_valutazioni).append(html);
                    creaNuovaValutazioneSingoloVoto(datiCompetenza, id_container_nuove_valutazioni);
                } else {
                    alert('{/literal}Selezionare una valutazione{literal}');
                }
            });

            $("#btnAnnullaInserimentoSingoloVoto").on('click', function (){
                $("#jqxLoader").jqxLoader('open');
                creaNuovaValutazioneSingoloVoto(datiCompetenza, id_container_nuove_valutazioni);
                $("#jqxLoader").jqxLoader('close');
            });
        }
    }

    function estraiStringaRamo(elencoCompetenze, competenza)
    {
        var datiRamo = {'datiCompetenza': null, 'tracciato_opacity': '', 'tracciato': ''};
        var BreakException = {};

        if (Object.keys(elencoCompetenze).length > 0)
        {
            try {
                Object.keys(elencoCompetenze).forEach(function(key){
                    if (String(elencoCompetenze[key]['id']) === String(competenza['id']))
                    {
                        datiRamo['datiCompetenza'] = elencoCompetenze[key];
                        datiRamo['tracciato_opacity'] = elencoCompetenze[key]['codice'];
                        datiRamo['tracciato'] = elencoCompetenze[key]['codice'];
                        throw BreakException;
                    }
                    else if (elencoCompetenze[key]['figli'])
                    {
                        datiRamo = estraiStringaRamo(elencoCompetenze[key]['figli'], competenza);
                        if (datiRamo['datiCompetenza'] !== null){
                            let tracciatoOpacityTmp = '<span style="opacity: 0.7">'+elencoCompetenze[key]['codice']+' - </span>'+datiRamo['tracciato_opacity'];
                            let tracciatoTmp = elencoCompetenze[key]['codice']+' - '+datiRamo['tracciato'];
                            datiRamo['tracciato_opacity'] = tracciatoOpacityTmp;
                            datiRamo['tracciato'] = tracciatoTmp;
                            throw BreakException;
                        }
                    }
                });
            } catch (e){
                if (e !== BreakException) throw e;
            }
        }
        return datiRamo;
    }

    function creaInterfacciaCompetenza(datiCompetenza = '')
    {
        var cellaValutazioniCompetenza = $('#divValutazioniCompetenza');
        var cellaInfoCompetenza = $('#divInformazioniCompetenza');

        {/literal}
        //tab valutazioni
        var html = '';
        html += "<div style='padding-bottom: 20px; padding-top: 10px;'>";
        html +=     "<div id='descrizioneCompetenzaVal'></div>";
        html += "</div>";
        html += "<div style='padding-bottom: 20px;'>";
        html +=     "<fieldset style='-moz-border-radius:5px; -webkit-border-radius:5px; border-radius:5px; border: 0.5px solid #0077BE;'>";
        html +=         "<legend style='color: #0077BE;'>{mastercom_label}Valutazioni{/mastercom_label}</legend>";
        html +=         "<div id='valutazioniCompetenza' align='center'></div>";
        html +=     "</fieldset>";
        html += "</div>";
        html += "<div>";
        html +=     "<div id='modificaValutazione'></div>";
        html += "</div>";
        html += "<div>";
        html +=     "<div id='nuovaValutazione'></div>";
        html += "</div>";
        cellaValutazioniCompetenza.html(html);

        //tab informazioni
        var html = '';
        html += "<div style='padding-bottom: 15px;'>";
        html +=     "<b>{mastercom_label}Descrizione{/mastercom_label}</b>";
        html +=     "<div id='descrizioneCompetenza'></div>";
        html += "</div>";
        html += "<div style='padding-bottom: 15px;'>";
        html +=     "<b>{mastercom_label}Descrizione Stampa{/mastercom_label}</b>";
        html +=     "<div id='descrizioneStampaCompetenza'></div>";
        html += "</div>";
        html += "<div style='padding-bottom: 15px;'>";
        html +=     "<b>{mastercom_label}Codice{/mastercom_label}</b>";
        html +=     "<div id='codiceCompetenza'></div>";
        html += "</div>";
        cellaInfoCompetenza.html(html);
        {literal}
    }

    function creaInterfacciaCompetenzaSingoloVoto(datiCompetenza = '')
    {
        var cellaValutazioniCompetenza = $('#divValutazioniCompetenza');
        var cellaInfoCompetenza = $('#divInformazioniCompetenza');
        var cellaValutazioniPresentiCompetenza = $('#divValutazioniPresentiCompetenza');

        {/literal}
        //tab aggiungi valutazione
        var html = '';
        html += "<div style='padding-bottom: 20px; padding-top: 10px;'>";
        html +=     "<div id='descrizioneCompetenzaVal'></div>";
        html += "</div>";
        html += "<div>";
        html +=     "<div id='nuovaValutazione'></div>";
        html += "</div>";
        cellaValutazioniCompetenza.html(html);

        //tab informazioni
        var html = '';
        html += "<div style='padding-bottom: 15px;'>";
        html +=     "<b>{mastercom_label}Descrizione{/mastercom_label}</b>";
        html +=     "<div id='descrizioneCompetenza'></div>";
        html += "</div>";
        html += "<div style='padding-bottom: 15px;'>";
        html +=     "<b>{mastercom_label}Descrizione Stampa{/mastercom_label}</b>";
        html +=     "<div id='descrizioneStampaCompetenza'></div>";
        html += "</div>";
        html += "<div style='padding-bottom: 15px;'>";
        html +=     "<b>{mastercom_label}Codice{/mastercom_label}</b>";
        html +=     "<div id='codiceCompetenza'></div>";
        html += "</div>";
        cellaInfoCompetenza.html(html);

        //tab valutazioni gia' presenti
        var html = '';
        html += "<div style='padding-bottom: 20px;'>";
        html +=     "{mastercom_label}Valutazioni della competenza già presenti per la materia selezionata{/mastercom_label}";
        html +=     "<fieldset style='-moz-border-radius:5px; -webkit-border-radius:5px; border-radius:5px; border: 0.5px solid #0077BE; margin-top: 5px;'>";
        html +=         "<legend style='color: #0077BE;'>{mastercom_label}Valutazioni{/mastercom_label}</legend>";
        html +=         "<div id='ValutazioniPresentiCompetenza' align='center'></div>";
        html +=     "</fieldset>";
        html += "</div>";
        cellaValutazioniPresentiCompetenza.html(html);
        {literal}
    }

    function creaInterfacciaCompetenzaMultiVoto(datiCompetenza = '')
    {
        var cellaInfoCompetenza = $('#infoCompMulti');

        {/literal}
        //tab informazioni
        var html = '';
        html += "<div style='padding-bottom: 15px;' align='center'>";
        html +=     "<input type='button' id='btnAggiungiMultiComp' value='+ Aggiungi'>";
        html += "</div>";
        html += "<div style='padding-bottom: 15px;'>";
        html +=     "<b>{mastercom_label}Descrizione{/mastercom_label}</b>";
        html +=     "<div id='descrizioneCompetenza'></div>";
        html += "</div>";
        html += "<div style='padding-bottom: 15px;'>";
        html +=     "<b>{mastercom_label}Descrizione Stampa{/mastercom_label}</b>";
        html +=     "<div id='descrizioneStampaCompetenza'></div>";
        html += "</div>";
        html += "<div style='padding-bottom: 15px;'>";
        html +=     "<b>{mastercom_label}Codice{/mastercom_label}</b>";
        html +=     "<div id='codiceCompetenza'></div>";
        html += "</div>";
        cellaInfoCompetenza.html(html);
        {literal}
    }

    function caricaCompetenzeStudente(id_studente)
    {
        if (token !== '' && id_studente > 0)
        {
            idStudenteCompetenza = id_studente;
            $("#jqxLoader").jqxLoader('open');
            $.ajax({
                type: 'GET',
                url: '../next-api/v1/competenze_studente/'+idStudenteCompetenza,
                data: '',
                cache: false,
                headers: {'Authorization':token},
                complete: function(response) {
                    if (response.responseJSON !== null)
                    {
                        //competenze presenti
                        sessionStorage.setItem('document',JSON.stringify(response.responseJSON));
                        creaInterfacciaCompetenze();
                    } else {
                        // nessuna competenza presente
                        $('#containerCompetenze').html("<i>{/literal}{mastercom_label}Nessuna competenza impostata.{/mastercom_label}<br>{mastercom_label}Richiedere l'attivazione in segreteria{/mastercom_label}{literal}</i>");
                        console.log('Nessuna competenza trovata');
                    }
                    $("#jqxLoader").jqxLoader('close');
                }
            });
        }
        else
        {
            vuotaInterfacciaCompetenze();
        }
    }

    function caricaCompetenzeStudenteSingoloVoto(id_studente, id_materia, id_btn_competenze, id_container_competenze, id_spazio_nuove_valutazioni, id_container_nuove_valutazioni)
    {
        if (token !== '' && id_studente > 0 && id_materia > 0)
        {
            $("#jqxLoaderSingoloVoto").jqxLoader('open');
            $.ajax({
                type: 'GET',
                url: '../next-api/v1/competenze_studente/'+id_studente,
                data: '',
                cache: false,
                headers: {'Authorization':token},
                complete: function(response) {
                    if (response.responseJSON !== null)
                    {
                        //competenze presenti
                        sessionStorage.setItem('document',JSON.stringify(response.responseJSON));
                        $('#select_studente').prop('disabled', 'disabled');
                        $('#select_materia').prop('disabled', 'disabled');
                        creaInterfacciaCompetenzeSingoloVoto(id_container_competenze, id_materia, id_container_nuove_valutazioni);
                        $('#'+id_btn_competenze).hide();
                        $('#'+id_spazio_nuove_valutazioni).show();
                    } else {
                        // nessuna competenza presente
                        $('#'+id_container_competenze).html("<i>{/literal}{mastercom_label}Nessuna competenza impostata.{/mastercom_label}<br>{mastercom_label}Richiedere l'attivazione in segreteria{/mastercom_label}{literal}</i>");
                    }
                    $("#jqxLoaderSingoloVoto").jqxLoader('close');
                }
            });
        }
        else
        {
            $('#'+id_container_competenze).html("<i>{/literal}{mastercom_label}Seleziona uno studente e una materia per caricarne l'elenco delle competenze{/mastercom_label}{literal}</i>");
        }
    }

    function creaSelezioneTemplateStudentiMultivoto()
    {
        let elencoTemplateStudenti = JSON.parse(sessionStorage.getItem('elencoTemplateStudenti'));
        let html = '';
        let optVuota = '<option selected value="0"><i>{/literal}{mastercom_label}-- Seleziona un template tra quelli disponibili --{/mastercom_label}{literal}</i></option>';
        if (typeof elencoTemplateStudenti !== 'undefined')
        {
            Object.keys(elencoTemplateStudenti).forEach(function (index){
                html += '<option value="' + index + '">' + elencoTemplateStudenti[index]['nome_template_origine'] + '</option>';
            });
        }

        if (html === ''){
            html = '<option value="0">{/literal}{mastercom_label}Nessun template trovato{/mastercom_label}{literal}</option>';
        } else {
            html = optVuota + html;
        }

        $('#select_elenco_template_studenti').html(html);
    }

    function caricaElencoTemplateClasseMultiVoto(id_classe, id_materia)
    {
        if (token !== '' && id_classe > 0 && id_materia > 0)
        {
            $("#jqxLoaderSingoloVoto").jqxLoader('open');

            let data = {'id_classe': id_classe};
            $.ajax({
                type: 'GET',
                url: '../next-api/v1/competenze_studente/classe/'+id_classe+'/'+id_materia,
                data: data,
                cache: false,
                headers: {'Authorization':token},
                complete: function(response) {
                    if (response.responseJSON !== null)
                    {
                        let elencoTemplateStudenti = response.responseJSON.elenco_template_studenti;
                        let studentiCompetenze = response.responseJSON.studenti_competenze;
                        sessionStorage.setItem('elencoTemplateStudenti',JSON.stringify(elencoTemplateStudenti));
                        sessionStorage.setItem('studentiCompetenze',JSON.stringify(studentiCompetenze));
                        //console.log(response.responseJSON);
                        $('#select_id_materia_multivoto').prop('disabled', 'disabled');
                        creaSelezioneTemplateStudentiMultivoto();
                        $('#btn_aggiungi_competenze_multi_voto').hide();
                        $('#containerCompetenzeMultivoto').show();
                        $('#competenzeMultivoto').html('');
                    } else {
                        // nessuna competenza presente
                        alert("{/literal}{mastercom_label}Nessuna competenza impostata.{/mastercom_label}<br>{mastercom_label}Richiedere l'attivazione in segreteria{/mastercom_label}{literal}</i>");
                    }
                    $('#multivoto_content').animate({ scrollTop: ($('#multivoto_content')[0].scrollHeight) }, "slow");
                    $("#jqxLoaderSingoloVoto").jqxLoader('close');
                }
            });
        }
        else
        {
            alert("{/literal}{mastercom_label}Seleziona prima una materia{/mastercom_label}{literal}");
        }
    }

    function caricaTemplateMultivoto(idTemplate)
    {
        chiaveTemplateMultivoto = idTemplate;
        if (idTemplate == '0'){
            $('#competenzeMultivoto').html('');
        } else {
            creaInterfacciaCompetenzeMultivoto(idTemplate);
        }
    }

    function creaInterfacciaCompetenzeMultivoto()
    {
        var html = '';
        var containerCompetenze = $('#competenzeMultivoto');
        var document = JSON.parse(sessionStorage.getItem('elencoTemplateStudenti'));

        html += "<table width='100%' style='table-layout: fixed; margin-top: 5px;'>";
        html +=     "<tr>";
        html +=         "<td width='40%' align='left' style='padding: 5px; vertical-align: top;'>";
        html +=             "<b>{/literal}{mastercom_label}Elenco{/mastercom_label}{literal}</b>";
        html +=             "<div id='jqxTreeComp'></div>";
        html +=         "</td>";
        html +=         "<td width='60%' style='padding: 5px; vertical-align: top;'>";
        html +=             "<div id='infoCompMulti' style='background-color: white; border: 1px solid lightgray' class='padding_cella_generica'>";
        html +=             "</div>";
        html +=         "</td>";
        html +=     "</tr>";
        html += "</table>";

        containerCompetenze.html(html);

        $('#jqxTreeComp').jqxTree({height: $(window).height()/2, allowDrag: false});

        if (Object.keys(document[chiaveTemplateMultivoto]['competenze']).length > 0){
            var dataTree = document[chiaveTemplateMultivoto]['competenze'];
            dataTreeAdapted = adattaPerAlbero(dataTree, $('#id_materia_multivoto').val());
            $('#jqxTreeComp').jqxTree({ source: dataTreeAdapted});
            $('#jqxTreeComp').jqxTree('expandAll');
        }
        $('#multivoto_content').animate({ scrollTop: ($('#multivoto_content')[0].scrollHeight) }, "slow");

        $('#jqxTreeComp').on('select', function (event)
        {
            var args = event.args;
            var item = $('#jqxTreeComp').jqxTree('getItem', args.element);
            var document = JSON.parse(sessionStorage.getItem('elencoTemplateStudenti'));

            var datiCompetenza = cercaCompetenza(document[chiaveTemplateMultivoto]['competenze'], item['id']);

            if (datiCompetenza){
                caricaDatiCompetenzaMultivoto(datiCompetenza);
            }
        });
    }

    function inserisciValutazione(datiCompetenza)
    {
        $("#jqxLoader").jqxLoader('open');
        var dataValutazione = $('#dataNuovaValutazione').val();
        var idValutazioneSchema = $('#idValutazioneAssegnata').val();
        var idPesoValutazione = $('#idPesoValutazioneAssegnata').val();
        var idMateriaValutazione = $('#selectMateriaNuovaValutazioneCompetenza').val();
        var descrizioneMateriaValutazione = $('#selectMateriaNuovaValutazioneCompetenza option:selected').text();
        var noteValutazione = $('#noteNuovaValutazione').val();

        var nuovaValutazione = {};
        Object.keys(datiCompetenza['schema_valutazioni']).forEach(function (index){
            if (datiCompetenza['schema_valutazioni'][index]['id'] === idValutazioneSchema){
                nuovaValutazione = $.extend(true,{},datiCompetenza['schema_valutazioni'][index]);
            }
        });

        nuovaValutazione['id_valutazione_schema'] = nuovaValutazione['id'];
        delete nuovaValutazione['id'];
        nuovaValutazione['data'] = dataValutazione;
        nuovaValutazione['id_materia'] = idMateriaValutazione;
        nuovaValutazione['descrizione_materia'] = descrizioneMateriaValutazione;
        if (typeof idPesoValutazione != 'undefined'){
            nuovaValutazione['id_peso'] = parseInt(idPesoValutazione);
        }
        if (noteValutazione !== ''){
            nuovaValutazione['note'] = noteValutazione;
        }

        if (noteRiservateValutazione !== ''){
            nuovaValutazione['note_riservate'] = noteRiservateValutazione;
        }

        //dimensioni
        if ($('[data-tipo="dimensione"]').length > 0){
            var dimensioniValutabili = $('[data-tipo="dimensione"]');
            dimensioniValutabili.each(function(index, dimVal){
                var valore = $(dimVal).val();
                if (valore != '0'){
                    var idCampoLibero = $(dimVal).data().idCampoLibero;

                    if (typeof nuovaValutazione['dimensioni'] == 'undefined'){
                        nuovaValutazione['dimensioni'] = {};
                    }

                    nuovaValutazione['dimensioni'][idCampoLibero] = $.extend(true,{},datiCompetenza['dimensioni'][idCampoLibero]);
                    nuovaValutazione['dimensioni'][idCampoLibero]['valori_precomp'][valore]['selezionato'] = 'SI';

                    if (valore == '-1'){
                        nuovaValutazione['dimensioni'][idCampoLibero]['valori_precomp'][valore]['valore_testo'] = $('[data-tipo="dimensione_testo"][data-id-campo-libero="'+idCampoLibero+'"]').val();
                    }

                    //per togliere i valori precomp non selezionati
                    Object.keys(nuovaValutazione['dimensioni'][idCampoLibero]['valori_precomp']).forEach(function(ndx){
                        var valPrec = nuovaValutazione['dimensioni'][idCampoLibero]['valori_precomp'][ndx];
                        if (valPrec['selezionato'] == 'NO'){
                            delete nuovaValutazione['dimensioni'][idCampoLibero]['valori_precomp'][ndx];
                        }
                    });
                }
            });
        }

        var data = {
            "valutazione":  nuovaValutazione
        };

        $.ajax({
            type: 'PUT',
            url: '../next-api/v1/competenze_studente/'+idStudenteCompetenza+'/'+idCompetenza,
            data: data,
            cache: false,
            headers: {'Authorization':token},
            complete: function(response) {
                if (response.responseJSON !== null)
                {
                    if (response.responseJSON === false){
                        console.log('Errore');
                    } else {
                        $.ajax({
                            type: 'GET',
                            url: '../next-api/v1/competenze_studente/'+idStudenteCompetenza,
                            data: '',
                            cache: false,
                            headers: {'Authorization':token},
                            complete: function(response) {
                                sessionStorage.setItem('document',JSON.stringify(response.responseJSON));
                                var document = JSON.parse(sessionStorage.getItem('document'));

                                var datiCompetenzaAggiornata = cercaCompetenza(document['competenze'], idCompetenza);

                                if (datiCompetenzaAggiornata){
                                    caricaDatiCompetenza(datiCompetenzaAggiornata);
                                }
                                $("#jqxLoader").jqxLoader('close');
                            }
                        });
                    }
                }
            }
        });
    }

    function vuotaInterfacciaCompetenze()
    {
        idStudenteCompetenza = '';
        idCompetenza = '';
        $('#containerCompetenze').html('');
    }

    function caricaDataStudenteInserimento(ts_data, id_studente, nome_studente)
    {
        if (id_studente > 0)
        {
            //scrivo il nome dello studente
            $('#nome_studente_nuovo_voto').html(nome_studente);
            $('#id_studente_nuovo_voto').val(id_studente);
            $('#nome_studente_hidden_nuovo_voto').val(nome_studente);
            $('#nome_studente_fisso').show();
        }
        else
        {
            $('#nome_studente_select').show();
        }

        if (ts_data > 0)
        {
            //setto la data
            ts_data = parseInt(ts_data)*1000;
            var date = new Date(ts_data);

            var mese = date.getMonth()+1;
            var giorno = date.getDate();
            if (mese < 10)
            {
                mese = '0' + mese;
            }
            if (giorno < 10)
            {
                giorno = '0' + giorno;
            }

            var data_valida = date.getFullYear() + '-' + mese + '-' + giorno;
            $('#data_nuovo_voto').val(data_valida);
        }

        //imposto anche la materia se ce ne e' una selezionata nel filtro
        var id_materia = parseInt($('#filtro_id_materia').val());
        if (id_materia > 0)
        {
            $('#select_materia').val(id_materia).trigger('change');
        }
        else
        {
            if ($('#select_materia option').length >= 2){ //la prima e' la vuota
                id_materia = parseInt($('#select_materia option:eq(1)').val());
                $('#select_materia').val(id_materia).trigger('change');
            } else {
                $('#select_materia').val('').trigger('change');
                id_materia = '';
            }
        }
        caricaDatiVotoMateriaInserimento(id_materia);
    }

    function caricaMateriaMultivoto()
    {
        //imposto anche la materia se ce ne e' una selezionata nel filtro
        var id_materia = parseInt($('#filtro_id_materia').val());
        if (id_materia > 0)
        {
            $('#select_id_materia_multivoto').val(id_materia).trigger('change');
        }
        else
        {
            if ($('#select_id_materia_multivoto option').length >= 2){ //la prima e' la vuota
                id_materia = parseInt($('#select_id_materia_multivoto option:eq(1)').val());
                $('#select_id_materia_multivoto').val(id_materia).trigger('change');
            } else {
                $('#select_id_materia_multivoto').val('').trigger('change');
                id_materia = '';
            }
        }
        caricaDatiVotoMateriaInserimento(id_materia);
    }

    function caricaDatiVotoMateriaInserimento(id_materia)
    {
        if (id_materia > 0)
        {
            id_materia = parseInt(id_materia);

            elenco_materie_classe_professore.professore.forEach(function(materia, key)
            {
                if (id_materia === parseInt(materia['id_materia']))
                {
                    // logiche riprese da 'include_gestione_voti_professore.tpl' nella parte di inserimento nuovo voto

                    //popolo i tipi voto disponibili
                    //var tipi_voto = "<option value=''></option>";
                    var tipi_voto = "";
                    if (materia['voto_unico_normale_attivita'] !== '1' && typeof materia['tipo_voto_abbinamenti'] !== 'undefined' && materia['tipo_voto_abbinamenti'] !== null)
                    {
                        if (typeof materia['tipo_voto_abbinamenti']['scritto'] !== 'undefined')
                        {
                            if (parseInt(materia['tipo_voto_abbinamenti']['scritto']) === 1)
                            {
                                tipi_voto += "<option value='0'>Scritto</option>";
                            }
                        }
                        if (typeof materia['tipo_voto_abbinamenti']['orale'] !== 'undefined')
                        {
                            if (parseInt(materia['tipo_voto_abbinamenti']['orale']) === 1)
                            {
                                tipi_voto += "<option value='1'>Orale</option>";
                            }
                        }
                        if (typeof materia['tipo_voto_abbinamenti']['pratico'] !== 'undefined')
                        {
                            if (parseInt(materia['tipo_voto_abbinamenti']['pratico']) === 1)
                            {
                                tipi_voto += "<option value='2'>Pratico</option>";
                            }
                        }
                    }
                    else if (coordinatore === 'SI')
                    {
                        if (parseInt(materia['cpm_scritto']) === 1)
                        {
                            tipi_voto += "<option value='0'>Scritto</option>";
                        }
                        if (parseInt(materia['cpm_orale']) === 1)
                        {
                            tipi_voto += "<option value='1'>Orale</option>";
                        }
                        if (parseInt(materia['cpm_pratico']) === 1)
                        {
                            tipi_voto += "<option value='2'>Pratico</option>";
                        }
                    }
                    $('#tipo_nuovo_voto').html(tipi_voto);
                    $('#tipo_nuovo_voto').removeAttr('disabled');

                    //popolo i voti disponibili
                    var voti = "<option value=''></option>";
                    materia['significati_voto'].forEach(function (significato)
                    {
                        voti += "<option value='" + significato['voto'] + "'>"+ significato['codice'] + "</option>";
                    });
                    $('#nuovo_voto_assegnato').html(voti);
                    $('#nuovo_voto_assegnato').removeAttr('disabled');

                    //setto il peso di default
                    selezionaPesoDefault('#peso_nuovo_voto');
                    $('#peso_nuovo_voto').removeAttr('disabled');

                    //popolo le note didattiche
                    var simboli = "<option value=''></option>";
                    materia['simboli'].forEach(function (simbolo)
                    {
                        simboli += "<option value='" + simbolo['id_simbolo'] + "'>"+ simbolo['codice'] + " (" + simbolo['descrizione'] + ")</option>";
                    });
                    $('#nuovo_simbolo_assegnato').html(simboli);
                    $('#nuovo_simbolo_assegnato').removeAttr('disabled');

                    //attivo le note
                    $('#note_nuovo_voto').removeAttr('disabled');
                    $('#note_nuova_nota').removeAttr('disabled');

                    //nascondo gli esonerati da religione se ci sono
                    if (materia.tipo_materia == "RELIGIONE"){
                        $("#nuovo_voto [data-studente-esonero='SI']").hide();
                        if ($('#select_studente option:selected').data().studenteEsonero == "SI"){
                            $('#select_studente').val('');
                        }
                    } else {
                        $("#nuovo_voto [data-studente-esonero='SI']").show();
                    }
                }
            });
        }
        else
        {
            $('#tipo_nuovo_voto').html('');
            $('#tipo_nuovo_voto').prop("disabled", true);
            $('#nuovo_voto_assegnato').html('');
            $('#nuovo_voto_assegnato').prop("disabled", true);
            selezionaPesoDefault('#peso_nuovo_voto');
            $('#peso_nuovo_voto').prop("disabled", true);
            $('#nuovo_simbolo_assegnato').html('');
            $('#nuovo_simbolo_assegnato').prop("disabled", true);
            $('#note_nuovo_voto').prop("disabled", true);
            $('#note_nuova_nota').prop("disabled", true);
        }
    }

    function caricaDatiVotoMateriaInserimentoMultivoto(id_materia)
    {
        $('#multi_tipo_voto').html('');
        $('.multi_voto_select').prop("disabled", true);
        $('.multi_voto_pesi_select').prop("disabled", true);
        $('#multi_commento').prop("disabled", true);
        $('#id_materia_multivoto').val(id_materia);

        if (id_materia > 0)
        {
            id_materia = parseInt(id_materia);

            elenco_materie_classe_professore.professore.forEach(function(materia, key)
            {
                if (id_materia === parseInt(materia['id_materia']))
                {
                    //popolo i voti disponibili
                    var voti = "<option value=''></option>";
                    materia['significati_voto'].forEach(function (significato)
                    {
                        voti += "<option value='" + significato['voto'] + "'>"+ significato['codice'] + "</option>";
                    });

                    //Imposto la selezione di scritto orale pratico in base alle impostazioni della materia
                    var opzioni = '';
                    if (materia['voto_unico_normale_attivita'] !== '1')
                    {
                        if (parseInt(materia['tipo_voto_abbinamenti']['scritto']) === 1)
                        {
                            opzioni += "<option value='scritto'>{/literal}{mastercom_label}Scritto{/mastercom_label}{literal}</option>";
                        }

                        if (parseInt(materia['tipo_voto_abbinamenti']['orale']) === 1)
                        {
                            opzioni += "<option value='orale'>{/literal}{mastercom_label}Orale{/mastercom_label}{literal}</option>";
                        }
                        if (parseInt(materia['tipo_voto_abbinamenti']['pratico']) === 1)
                        {
                            opzioni += "<option value='pratico'>{/literal}{mastercom_label}Pratico{/mastercom_label}{literal}</option>";
                        }
                    }
                    else
                    {
                        opzioni += "<option value='unico'>{/literal}{mastercom_label}Unico{/mastercom_label}{literal}</option>";
                    }
                    $('#multi_tipo_voto').html(opzioni);
                    $('.multi_voto_select').html(voti);
                    $('.multi_voto_select').removeAttr('disabled');
                    $('.multi_voto_pesi_select').removeAttr('disabled');

                    //attivo le note
                    $('#multi_commento').removeAttr('disabled');

                    //nascondo gli esonerati da religione se ci sono
                    if (materia.tipo_materia == "RELIGIONE"){
                        $("#multivoto [data-studente-esonero='SI']").hide();
                        $("#multivoto [data-studente-esonero='SI'] select").attr("disabled", "disabled");
                    } else {
                        $("#multivoto [data-studente-esonero='SI']").show();
                        $("#multivoto [data-studente-esonero='SI'] select").removeAttr("disabled");
                    }
                }
            });
        }
    }

    function selezionaPesoDefault(selector)
    {
        $(selector).val($(selector+" option[data-default='t']").val());
    }

    function aggiungiColonnaMultiNotaDidattica()
    {
        if (multi_note_didattiche === false) {
            $('#elenco_studenti_multivoto tr').each(function (i, row) {
                let tipoColonna = row.getAttribute('data-riga');
                let html = '';

                if (tipoColonna === 'titoli'){
                    html += '<td align="center" class="padding_cella_generica cella-nota-didattica">';
                    html +=     '<b>{/literal}{mastercom_label}Nota didattica{/mastercom_label}{literal}</b>';
                    html += '</td>';
                } else {
                    let idStudente = row.getAttribute('data-id_studente');
                    html += '<td align="center" class="cella-nota-didattica">';
                    html +=     '<select name="multivoto[dati]['+idStudente+'][nota_didattica]" class="select select-multi-nota-didattica">';
                    html +=     '</select>';
                    html += '</td>';
                }
                $(this).append(html);
                multi_note_didattiche = true;
            });

            // aggingo lo spazio per il commento
            $('#multi_commento').css('width', '45%');
            html = "<textarea id='multi_commento_nota' name='multivoto[commento_nota]' placeholder='{/literal}{mastercom_label}Commento alla nota didattica..{/mastercom_label}{literal}' rows='3' disabled style='width: 45%; margin-left: 5px;'></textarea>";
            $('#multi_commento').after(html);
        }
    }

    function caricaElencoNoteDidattiche(idMateria, btn = null)
    {
        if (btn !== null){
            btn.style.display = 'none';
        }
        aggiungiColonnaMultiNotaDidattica();

        var simboli = "";
        $('#multi_commento_nota').prop('disabled', 'disabled');
        if (idMateria > 0)
        {
            elenco_materie_classe_professore.professore.forEach(function(materia, key)
            {
                if (idMateria === materia['id_materia'])
                {
                    //popolo le note didattiche
                    simboli += "<option value=''></option>";
                    materia['simboli'].forEach(function (simbolo)
                    {
                        simboli += "<option value='" + simbolo['id_simbolo'] + "'>"+ simbolo['codice'] + "</option>";
                    });
                    $('#multi_commento_nota').removeAttr('disabled');
                }
            });
        }
        $('.select-multi-nota-didattica').html(simboli);
    }

    function traduciTipoVoto(valore){
        valore = String(valore);

        if (valore == "0"){
            return "scritto";
        } else if (valore == "1"){
            return "orale";
        } else if (valore == "2"){
            return "pratico";
        } else {
            return "scritto";
        }
    }

    function inserisciVoto(btn){
        if (verificaFestivo($('#data_nuovo_voto').val()) === false){
            if ($("#nuovo_voto_assegnato").val() != '' && ($("#tipo_nuovo_voto").val() == ''|| $("#tipo_nuovo_voto").val() == null)){
                creaToast('Tipo voto non presente', 'error');
            } else {
                var query = {
                    "data"  : trasformaDataInTimestamp(new Date($('#data_nuovo_voto').val())),
                    "tipo"  : traduciTipoVoto($('#tipo_nuovo_voto').val()),
                    "operazione"    : "ins"
                };

                $.ajax({
                    type: 'GET',
                    url: "../next-api/v1/voti/verifica_data",
                    data: query,
                    cache: false,
                    headers: {'Authorization':token},
                    complete: function(response) {
                        var r = response.responseJSON;

                        if (r.ins.valore == 'SI' || $("#nuovo_voto_assegnato").val() == ""){
                            document.getElementById('form_container').operazione.value = 'inserisci_nuovo_voto';
                            document.getElementById('form_container').submit();
                            btn.disabled = true;
                            $('#jqxGeneralLoader').jqxLoader('open');
                        } else {
                            creaToast(r.ins.messaggio, 'errore');
                        }
                    }
                });
            }
        } else {
            creaToast('Giorno festivo! Cambiare data', 'error');
        }
    }

    function inserisciMultiVoto(btn){
        if ($('#data_multivoto').val() !== '' && $('#id_materia_multivoto').val() !== '') {
            if (verificaDataFutura($('#data_multivoto').val()) === false){
                if (verificaFestivo($('#data_multivoto').val()) === false){
                    var tipo = $('#multi_tipo_voto').val();
                    var procediComunque = true;

                    $(".multi_voto_select").each(function(index, select){
                        if ($(select).val() != ''){
                            procediComunque = false;
                        }
                    });

                    if (tipo !== null || procediComunque){
                        var query = {
                            "data"  : trasformaDataInTimestamp(new Date($('#data_multivoto').val())),
                            "tipo"  : $('#multi_tipo_voto').val(),
                            "operazione"    : "ins"
                        };

                        $.ajax({
                            type: 'GET',
                            url: "../next-api/v1/voti/verifica_data",
                            data: query,
                            cache: false,
                            headers: {'Authorization':token},
                            complete: function(response) {
                                var r = response.responseJSON;

                                if (r.ins.valore == 'SI' || procediComunque){
                                    document.getElementById('form_container').operazione.value = 'inserisci_multivoto';
                                    btn.disabled = true;
                                    document.getElementById('form_container').submit();
                                    $('#jqxLoader').jqxLoader('open');
                                } else {
                                    creaToast(r.ins.messaggio, 'errore');
                                }
                            }
                        });
                    } else {
                        creaToast('{mastercom_label}Contattare la segreteria didattica in quanto le tipologie di voto non sono impostate{/mastercom_label}', 'errore');
                    }
                } else {
                    alert('{mastercom_label}Giorno festivo! Cambiare data{/mastercom_label}');
                }
            } else {
                alert('{mastercom_label}Non è possibile inserire un voto nel futuro{/mastercom_label}');
            }
        } else {
            alert('{mastercom_label}Sia la data che la materia devono essere compilate{/mastercom_label}');
        }
    }

    function verificaDataFutura(data_input) {
        if (data_input > oggi){
            return true;
        } else {
            return false;
        }
    }

    function verificaFestivo(data_input) {
        if (giorni_festivi.indexOf(data_input) > -1){
            return true;
        } else {
            return false;
        }
    }

    function sistemaCalendario(id){
        var bounding = document.querySelector("#"+id).getBoundingClientRect();
        if (bounding.right > $(window).width())
        {
            document.getElementById(id).style.right = '-125px';
        }
    }

    function applicaFiltri()
    {
        var filtroScritto = $('#filter-scritto')[0].checked;
        var filtroOrale = $('#filter-orale')[0].checked;
        var filtroPratico = $('#filter-pratico')[0].checked;
        var filtroAnnotazione = $('#filter-annotazione')[0].checked;
        if ($('#filter-competenza').length){
            var filtroCompetenza = $('#filter-competenza')[0].checked;
        }

        // materia filtrata con ricaricamento
        //var filtroMateria = parseInt($('#filtro_id_materia').val());

        var elementi = $('.filterable');
        elementi.each(function(index, item){
            var dati =  $(item).data();
            var mostra = true;

            //if (filtroMateria > 0 && parseInt(dati.idMateria) !== filtroMateria){
            //    mostra = false;
            //}

            if (mostra){
                switch (dati.filter){
                    case 'filter_scritto':
                        mostra = (filtroScritto === false) ? false : mostra;
                        break;
                    case 'filter_orale':
                        mostra = (filtroOrale === false) ? false : mostra;
                        break;
                    case 'filter_pratico':
                        mostra = (filtroPratico === false) ? false : mostra;
                        break;
                    case 'filter_annotazione':
                        mostra = (filtroAnnotazione === false) ? false : mostra;
                        break;
                    case 'filter_competenze':
                        mostra = (filtroCompetenza === false) ? false : mostra;
                        break;
                    default:
                        break;
                }
            }

            if (mostra){
                slideUpDown(item, 'down');
            } else {
                slideUpDown(item, 'up');
            }
        });
    }

    function filtraMateria(idMateria){
        $("#form_container").submit();
        $('#jqxGeneralModalLoader').jqxLoader('open');
    }

    function filtraPeriodo(valorePeriodo){
        $("#form_container").submit();
        $('#jqxGeneralModalLoader').jqxLoader('open');
    }

    function settaMultiPesi(className, idPeso){
        $('.'+className).val(idPeso);
    }

    function visualizzazioneDettaglioCompetenza(idCompostoCompetenza){
        $("#form_container").submit();
        $('#jqxGeneralModalLoader').jqxLoader('open');
    }

    function toggleFoto(btn)
    {
        btn.classList.toggle("testo_verde");
        btn.classList.toggle("bordo_verde");
        var foto = $(btn).data().statoFoto;

        if (foto === 'NO'){
            $("[data-colonna='foto']").slideDown(500);
            $(btn).data('statoFoto', 'SI');
        } else {
            $("[data-colonna='foto']").slideUp(500);
            $(btn).data('statoFoto', 'NO');
        }
    }

    function slideUpDown(item, slide){
        if (slide === 'up'){
            $(item)
                .slideUp('slow')
                .animate(
                    { opacity: 1 },
                    { queue: false, duration: 'slow' }
                );
        } else {
            $(item)
                .slideDown('slow')
                .animate(
                    { opacity: 1 },
                    { queue: false, duration: 'slow' }
                );
        }
    }

    function bloccaColonna(classe, x) {
      /**
       * Quando la tabella scrolla sposta di posizione la prima riga e la prima colonna
       */
      $('.'+classe).css('transform', 'translateX(' + x + 'px)');
    }

    function inizializzaOggi(){
        var data = new Date();
        var anno = data.getFullYear();
        var mese = data.getMonth() + 1;
        var giorno = data.getDate();
        if (mese < 10) {
            mese = '0' + mese;
        }
        if (giorno < 10) {
            giorno = '0' + giorno;
        }
        oggi = anno + '-' + mese + '-' + giorno;
    }

    window.addEventListener("orientationchange", function() {
        //$('#elenco_voti').css('height', '');
        //if ($('#elenco_voti').height() > $(window.innerHeight)[0] - $('#elenco_voti').position().top - 10){
        setTimeout(function(){
            let height = $(window.innerHeight)[0] - $('#elenco_voti').position().top - 15;
            if (height < minHeight) {
                height = minHeight;
            }
            $('#elenco_voti').delay(100).height(height);
        }, 250);
        //}
    });

    $(document).ready( function(){
        ottieni_token();

        $('#message').delay(5000).fadeOut();

        if ($('#elenco_voti').length)
        {
            if ($('#elenco_voti').height() > $(window.innerHeight)[0] - $('#elenco_voti').position().top - 10){
                let height = $(window.innerHeight)[0] - $('#elenco_voti').position().top - 10;
                if (height < minHeight){
                    height = minHeight;
                }
                $('#elenco_voti').height(height);
            }
        }

        applicaFiltri();
        inizializzaOggi();

        $('#fixed-headers-studente').scroll(function() {
            bloccaColonna('colonna_mese_voti_studente', $(this).scrollLeft());
        });
        $('#fixed-headers-classe').scroll(function() {
            bloccaColonna('colonna_studente_voti', $(this).scrollLeft());
        });

        $("#jqxLoader").jqxLoader({ text: "", width: 60, height: 60, isModal: true });
        $("#jqxLoaderSingoloVoto").jqxLoader({ text: "", width: 60, height: 60});

        $('.tooltip').on('mouseenter', function () {
            var $this = $(this);
            var $origTooltip = $this.find('.tooltiptext');
            var origWidth = $origTooltip.outerWidth();
            var origHeight = $origTooltip.outerHeight();

            var $tooltip = $origTooltip.clone();
            $tooltip.addClass('tooltip-portal').css({
                'visibility': 'visible',
                'width': origWidth,
                'height': origHeight
            });
            $('body').append($tooltip);

            var offset = $this.offset();
            var spacing = 5;
            var winWidth = $(window).width();
            var winHeight = $(window).height();
            var top;

            // Se l'elemento è nella metà superiore, mostra il tooltip sotto
            // altrimenti sopra
            if (offset.top < winHeight / 2) {
                top = offset.top + $this.outerHeight() + spacing;
            } else {
                top = offset.top - origHeight - spacing;
            }
            top -= 5;

            var left;
            if (offset.left + $this.outerWidth() / 2 < winWidth / 2) {
                left = offset.left + $this.outerWidth() + spacing;
            } else {
                left = offset.left - origWidth - spacing;
            }

            $tooltip.css({
                position: 'absolute',
                top: top,
                left: left,
                zIndex: 9999
            });
            $this.data('tooltip-portal', $tooltip);
        }).on('mouseleave', function () {
            var $tooltip = $(this).data('tooltip-portal');
            if ($tooltip) {
                $tooltip.remove();
                $(this).removeData('tooltip-portal');
            }
        });
    });
    {/literal}
</script>

<br>

<div id="sfondo_oscurato" style="width: 100%; height: 100%; top: 0; left: 0; position: fixed; background-color: #aaa; opacity: 0.5; z-index: 11; display: none;"
     onclick="chiudiSfondoOscurato();"></div>

<div id="toastsContainer" class="" style="position: absolute; z-index: 6; bottom: 0px; left: 0px; padding: 10px;"></div>

{if $messaggio}
    <div id="message" class="messaggio_basso_scomparsa" style="font-size: 110%">
        {$messaggio}
    </div>
{/if}

{if $report != ''}
    {*report inserimento multivoto*}
    <div  class="div_scheda_generica"
            id="report_multivoto"
            style="position: fixed;
                    max-width: 70%;
                    max-height: 95%;
                    min-width: 300px;
                    left: 50%;
                    top: 50%;
                    font-weight: normal;
                    transform: translate(-50%, -50%);
                    display: inline;
                    z-index: 12;
                    padding: 10px;
                    color: black;
                    overflow-y: auto;"
        >
        <h1>{mastercom_label}Report inserimento multivoto{/mastercom_label}</h1>
        {$report}
        <div align='center' class='padding_cella_generica'>
            <button type="button"
                    class="btn_flat_indaco"
                     onclick="$('#report_multivoto').hide();"
                     >{mastercom_label}Chiudi{/mastercom_label}</button>
        </div>
    </div>
{/if}
<form method='post' action='{$SCRIPT_NAME}' id="form_container">
    <div class="div_scheda_generica" align="center" style="width: 98%; color: black; font-weight: normal;">
        <div class="sfondo_scuro bordi_alti_scheda" style="display: flex; align-items: center; justify-content: space-between;">
            <div align="left" class="ombra_testo" style="padding: 14px 10px; font-weight: bold; font-size: 120%;">
                {mastercom_label}Voti{/mastercom_label}
            </div>
            <div>
                {if $id_classe}
                    <div align="right" style="padding-left: 10px;">
                        <div style="display: inline-block;" class="padding_cella_generica ombra_testo">
                            {* Scelta Classe *}
                            {mastercom_label}Classe{/mastercom_label}:<br>
                            <select name="id_classe" class="select" id="select_classe" style="background-color: white; max-width: 35vw; font-weight: bold;" onchange="this.form.submit(); $('#jqxGeneralModalLoader').jqxLoader('open');">
                                <option value="0">---</option>
                                {foreach $elenco_classi as $classe}
                                    <option value="{$classe['id_classe']}"
                                            {if $id_classe == $classe['id_classe']}selected{/if}
                                            >
                                        {if $classe['ordinamento'] == 'CORSO'}
                                            (C) {$classe['descrizione_materia']}
                                        {else}
                                            {$classe['classe']}{$classe['sezione']} {$classe['descrizione_indirizzi']}
                                        {/if}
                                    </option>
                                {/foreach}
                            </select>
                        </div>
                        <div style="display: inline-block;" class="padding_cella_generica ombra_testo">
                            {* Scelta Materia *}
                            {mastercom_label}Materia{/mastercom_label}:<br>
                            <select name="id_materia"
                                    class="select"
                                    id="filtro_id_materia"
                                    style="background-color: white; max-width: 35vw; font-weight: bold;"
                                    onchange="filtraMateria(this.value);"
                                    >
                                <option value="0">{mastercom_label}TUTTE{/mastercom_label}</option>
                                {foreach $elenco_materie_classe_professore['classe'] as $materia}
                                    <option value="{$materia['id_materia']}"
                                            {if $id_materia == $materia['id_materia']}selected{/if}
                                            >
                                        {$materia['descrizione']}
                                    </option>
                                {/foreach}
                            </select>
                        </div>
                        <div style="display: inline-block;" class="padding_cella_generica ombra_testo">
                            {* Scelta Periodo *}
                            {mastercom_label}Periodo{/mastercom_label}:<br>
                            <select name="periodo_selezionato"
                                    class="select bold"
                                    id="filtro_periodo_selezionato"
                                    style="background-color: white;"
                                    onchange="filtraPeriodo(this.value);"
                                    >
                                {foreach $options_periodi as $valore => $periodo}
                                    <option value="{$valore}"
                                            {if $valore == $periodo_selezionato}selected{/if}
                                            >
                                        {$periodo}
                                    </option>
                                {/foreach}
                            </select>
                        </div>
                        <div style="display: inline-block;" class="padding_cella_generica ombra_testo">
                            {* Scelta Visualizzazione *}
                            {mastercom_label}Visual.{/mastercom_label}:<br>
                            <select name="visualizzazione"
                                    class="select bold"
                                    id="filtro_visualizzazione"
                                    style="background-color: white;"
                                    onchange="filtraPeriodo(this.value);"
                                    >
                                    <option value="estesa"
                                            {if $visualizzazione == 'estesa'}selected{/if}
                                            >
                                        Estesa
                                    </option>
                                    <option value="compatta"
                                            {if $visualizzazione == 'compatta'}selected{/if}
                                            >
                                        Compatta
                                    </option>
                            </select>
                        </div>
                    </div>
                {/if}
            </div>
        </div>
        {if !$id_classe}
            <div style="width: 25%; padding: 20px;" class="padding_cella_generica">
                {* Scelta Classe *}
                <select name="id_classe" class="select" style="background-color: white; width: 80%;" onchange="this.form.submit(); $('#jqxGeneralModalLoader').jqxLoader('open');">
                    <option value="0">{mastercom_label}Seleziona una classe{/mastercom_label}</option>
                    {foreach $elenco_classi as $classe}
                        <option value="{$classe['id_classe']}">
                            {if $classe['ordinamento'] == 'CORSO'}
                                (C) {$classe['descrizione_materia']}
                            {else}
                                {$classe['classe']}{$classe['sezione']} {$classe['descrizione_indirizzi']}
                            {/if}
                        </option>
                    {/foreach}
                </select>
            </div>
        {elseif $tabella_voti|@count > 0}
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div align="left" style="margin: 10px 0px; padding-left: 10px;">
                    {if $param_attiva_voti_registro == 'SI'}
                    {* LEGENDA *}
                    <label style="white-space: nowrap;"><input type="checkbox" id='filter-scritto' onclick="applicaFiltri();" checked style='vertical-align: middle;' class="class_type_filter">
                    <div class="btn_pieno_tondo noselect"
                         title="{mastercom_label}Scritto/Grafico{/mastercom_label}"
                         style="display: inline-block;
                                width: 30px;
                                height: 10px;
                                line-height: 10px;
                                color: black;
                                padding: 3px 8px;
                                box-shadow: none;
                                border: 1px solid #4caf50;
                                background: repeating-linear-gradient(
                                            90deg,
                                            #4caf50,
                                            #4caf50 7px,
                                            #FAFAFA 7px,
                                            #FAFAFA 50px
                                          );
                                          ">S/G</div></label>
                    &emsp;
                    <label style="white-space: nowrap;"><input type="checkbox" id='filter-orale'  onclick="applicaFiltri();" checked style='vertical-align: middle;' class="class_type_filter">
                    <div class="btn_pieno_tondo noselect"
                         title="{mastercom_label}Orale{/mastercom_label}"
                         style="display: inline-block;
                                width: 30px;
                                height: 10px;
                                line-height: 10px;
                                color: black;
                                padding: 3px 8px;
                                box-shadow: none;
                                border: 1px solid #ff8f00;
                                background: repeating-linear-gradient(
                                            90deg,
                                            #ff8f00,
                                            #ff8f00 7px,
                                            #FAFAFA 7px,
                                            #FAFAFA 50px
                                          );
                                ">O</div></label>
                    &emsp;
                    <label style="white-space: nowrap;"><input type="checkbox" id='filter-pratico'  onclick="applicaFiltri();" checked style='vertical-align: middle;' class="class_type_filter">
                    <div class="btn_pieno_tondo noselect"
                         title="{mastercom_label}Pratico{/mastercom_label}"
                         style="display: inline-block;
                                width: 30px;
                                height: 10px;
                                line-height: 10px;
                                color: black;
                                padding: 3px 8px;
                                box-shadow: none;
                                border: 1px solid #42a5f5;
                                background: repeating-linear-gradient(
                                            90deg,
                                            #42a5f5,
                                            #42a5f5 7px,
                                            #FAFAFA 7px,
                                            #FAFAFA 50px
                                          );
                                ">P</div></label>
                    &emsp;
                    <label style="white-space: nowrap;"><input type="checkbox" id='filter-annotazione'  onclick="applicaFiltri();" checked style='vertical-align: middle;' class="class_type_filter">
                    <div class="btn_pieno_tondo noselect"
                         title="{mastercom_label}Nota Didattica{/mastercom_label}"
                         style="display: inline-block;
                                width: 30px;
                                height: 10px;
                                line-height: 10px;
                                color: black;
                                padding: 3px 8px;
                                box-shadow: none;
                                border: 1px solid #90a4ae;
                                background: repeating-linear-gradient(
                                            90deg,
                                            #90a4ae,
                                            #90a4ae 7px,
                                            #FAFAFA 7px,
                                            #FAFAFA 50px
                                          );
                                ">N.D.</div></label>
                        {if $optional_competenze_professore == 'OK'}
                        &emsp;
                        <label style="white-space: nowrap;"><input type="checkbox" id='filter-competenza'  onclick="applicaFiltri();" checked style='vertical-align: middle;' class="class_type_filter">
                        <div class="btn_pieno_tondo noselect"
                            title="{mastercom_label}Competenze{/mastercom_label}"
                            style=" display: inline-block;
                                    width: 70px;
                                    height: 10px;
                                    line-height: 10px;
                                    color: black;
                                    padding: 4px 2px;
                                    box-shadow: none;
                                    background-color: #bbbbff;
                                    border-radius: 5px;
                                    overflow: hidden;
                                    text-overflow: clip;
                                    ">Ob./Comp.</div></label>
                        {/if}
                    {else}
                        {if $elenco_competenze_per_dettaglio|@count > 0}
                            {assign var="filtro_vuota" value="NO"}
                            <select class="select" name="id_competenza_per_dettaglio" id="id_competenza_per_dettaglio" onchange="visualizzazioneDettaglioCompetenza(this.value);">
                                <option value='0'>{mastercom_label}Seleziona una competenza per vederne il dettaglio{/mastercom_label}</option>
                                {foreach $elenco_competenze_per_dettaglio as $id => $nome}
                                    <option value='{$id}' {if $id == $id_competenza_per_dettaglio} selected {assign var="filtro_vuota" value="SI"} {/if}>{$nome}</option>
                                {/foreach}
                            </select>
                            {if $filtro_vuota == 'SI'}
                                <button type="button" class="btn_flat sfondo_bianco testo_viola bordo_viola btn_padding_ridotto ripples"
                                    style="border-radius: 32px; margin: 3px; white-space: nowrap;"
                                    title="{mastercom_label}Vuota selezione{/mastercom_label}"
                                    onclick="$('#id_competenza_per_dettaglio').val('0').trigger('change');"
                                    >{mastercom_label}Vuota{/mastercom_label}</button>
                            {/if}
                        {/if}
                    {/if}
                </div>
                <div align="center" style="padding-right: 10px;">
                    <button type="button" class="btn_flat sfondo_bianco bordo_verde testo_verde ripples" style="border-radius: 32px; margin: 3px; white-space: nowrap;" title="{mastercom_label}Aggiorna{/mastercom_label}"
                        onclick="this.form.submit(); $('#jqxGeneralModalLoader').jqxLoader('open');">{mastercom_label}Aggiorna{/mastercom_label}</button>
                    {*{if $optional_competenze_professore == 'OK'}
                    <button type="button" class="btn_pieno sfondo_verde ripples" id="btn_nuovo_voto" style="border-radius: 32px; margin: 3px; white-space: nowrap;" title="{mastercom_label}Inserisci competenza{/mastercom_label}"
                        onclick="$('#data_nuova_competenza').val('{$data_oggi}');
                                apriChiudiPopup('nuova_competenza', 'sfondo_oscurato', true);">{mastercom_label}Comp/Obiett{/mastercom_label}</button>
                    {/if}
                    {if $param_attiva_voti_registro == 'SI'}
                    <button type="button" class="btn_pieno sfondo_verde ripples" id="btn_nuovo_voto" style="border-radius: 32px; margin: 3px; white-space: nowrap;" title="{mastercom_label}Inserisci voto{/mastercom_label}"
                        onclick="caricaDataStudenteInserimento(0, 0, '');
                                $('#data_nuovo_voto').val('{$data_oggi}');
                                apriChiudiPopup('nuovo_voto', 'sfondo_oscurato', true);">{mastercom_label}+ Voto{/mastercom_label}</button>
                    <button type="button" class="btn_pieno sfondo_verde ripples" id="btn_nuovo_voto" style="border-radius: 32px; margin: 3px; white-space: nowrap;" title="{mastercom_label}Inserisci multivoto{/mastercom_label}"
                        onclick="caricaMateriaMultivoto();
                                selezionaPesoDefault('.multi_voto_pesi_select');
                                apriChiudiPopup('multivoto', 'sfondo_oscurato', true);
                                $('#multivoto_content').scrollTop(0);">{mastercom_label}+ Multi{/mastercom_label}</button>
                    {/if}*}
                </div>
            </div>
        {/if}

        {if $visualizza_dettaglio_competenza == 'SI'}
            {* -- Dettaglio competenza selezionata --*}
            <div align="center" style="width: 98%;">
                <div align='left' class='padding8' style="display: flex; align-items: center; justify-content: space-evenly; flex-wrap: wrap;">
                    <div class="padding3">
                        <span class='annotazione_leggera'>{mastercom_label}Tipo{/mastercom_label}:</span> {$array_per_dettaglio_competenza['dati_competenza']['tipo']}
                    </div>
                    <div class="padding3">
                        <span class='annotazione_leggera'>{mastercom_label}Struttura{/mastercom_label}:</span>
                        {foreach $array_per_dettaglio_competenza['dati_competenza']['struttura'] as $key_strutt => $struttura}
                            {if $key_strutt > 0} &rarr; {/if} {$struttura['codice']}
                        {/foreach}
                    </div>
                    <div class="padding3">
                        <span class='annotazione_leggera'>{mastercom_label}Template{/mastercom_label}:</span> {$array_per_dettaglio_competenza['dati_competenza']['nome_template_origine']}
                    </div>
                </div>
                <div align='left' class='testo_nero sfondo_viola_op20' style="border-radius: 32px; padding-left: 10px; height: 27px; display: flex; align-items: center; justify-content: space-between;">
                    <div align="left" class="bold">
                        {mastercom_label}Dettaglio competenza{/mastercom_label}
                    </div>
                    <div align="right">
                        <button type="button"
                            value="Chiudi"
                            title="{mastercom_label}Chiudi{/mastercom_label}"
                            class="btn_flat testo_viola"
                            onclick="$('#id_competenza_per_dettaglio').val('0').trigger('change');"
                            >&times;</button>
                    </div>
                </div>
                <div align="center" style="padding-bottom: 10px;">
                    <div style="overflow: auto;" id="fixed-headers-classe">
                        <table width="100%" style="font-size: 90%;">
                            <tr width="100%">
                                <td class="padding_cella_generica colonna_studente_voti sfondo_bianco"
                                    colspan="3"
                                    style="z-index: 1; position: relative; box-shadow: 2px 0 4px rgba(0,0,0,0.15);">
                                    <button type="button"
                                        class="btn_flat_tondo testo_grigio_scuro bordo_grigio_scuro"
                                        onclick="toggleFoto(this);"
                                        data-stato-foto="NO"
                                        >{mastercom_label}Foto{/mastercom_label}</button>
                                </td>
                                <td align="center" class="bordo_sinistro_generico padding_cella_generica bold">
                                    {mastercom_label}Data{/mastercom_label}
                                </td>
                                <td align="center" class="bordo_sinistro_generico padding_cella_generica bold">
                                    {mastercom_label}Materia{/mastercom_label}
                                </td>
                                <td align="center" class="bordo_sinistro_generico padding_cella_generica bold">
                                    {mastercom_label}Valutazione{/mastercom_label}
                                </td>
                                {foreach $array_per_dettaglio_competenza['dati_competenza']['dimensioni'] as $dimensione}
                                <td align="center" class="bordo_sinistro_generico padding_cella_generica bold">
                                    {$dimensione['nome']}<span class="annotazione_leggera"> ({mastercom_label}dim.{/mastercom_label})</span>
                                </td>
                                {/foreach}
                                <td align="center" class="bordo_sinistro_generico padding_cella_generica bold">
                                    {mastercom_label}Note{/mastercom_label}
                                </td>
                                <td align="center" class="bordo_sinistro_generico padding_cella_generica bold">
                                    {mastercom_label}Note riservate{/mastercom_label}
                                </td>
                            </tr>
                            {foreach $array_per_dettaglio_competenza['studenti'] as $studente}
                                <tr width="100%" class="bordo_alto_generico evidenzia">
                                    <td align="center"
                                        {if $studente['n_val'] > 0}rowspan="{$studente['n_val']}"{/if}
                                        style="z-index: 1; font-weight: bold; position: relative; width: min-content;"
                                        class="colonna_studente_assenze colonna_studente_voti sfondo_bianco">
                                        {if $studente['foto'] != ''}
                                            <img src="foto_studenti/{$studente['foto']}" data-colonna="foto" style="max-width: 14rem; max-height: 14rem; display: none;"/>
                                        {/if}
                                    </td>
                                    <td align="left"
                                        {if $studente['n_val'] > 0}rowspan="{$studente['n_val']}"{/if}
                                        style="z-index: 1; font-weight: bold; position: relative; width: 20px; white-space: normal;"
                                        class="padding_cella_generica colonna_studente_voti sfondo_bianco">
                                        {$studente['registro']}.
                                    </td>
                                    <td align="left"
                                        {if $studente['n_val'] > 0}rowspan="{$studente['n_val']}"{/if}
                                        style="z-index: 1; font-weight: bold; position: relative; background-color: #FAFAFA; box-shadow: 2px 0 4px rgba(0,0,0,0.15);"
                                        class="padding_cella_generica colonna_studente_voti bordo_alto_generico">
                                        {$studente['cognome']} {$studente['nome']}{if $studente['necessita_sostegno'] == '1'}{$param_simbolo_studente_sostegno}{/if}
                                            {if $param_mostra_ammissione_registro == 'SI' && $studente['stampa_esito'] == 'SI'}<i>({$studente['esito_corrente_calcolato']})</i>{/if}
                                    </td>
                                    {assign var='cont_val' value=0}
                                    {foreach $studente['valutazioni'] as $valutazione}
                                    <td width="auto"
                                        align="center"
                                        class="bordo_sinistro_generico bordo_alto_generico padding3"
                                        style="z-index: 0;"
                                        >
                                        {$valutazione['data_ita']}
                                    </td>
                                    <td width="auto"
                                        align="center"
                                        class="bordo_sinistro_generico bordo_alto_generico padding3"
                                        style="z-index: 0;"
                                        >
                                        {$valutazione['descrizione_materia']}
                                    </td>
                                    <td width="auto"
                                        align="center"
                                        class="bordo_sinistro_generico bordo_alto_generico padding3"
                                        style="z-index: 0;"
                                        >
                                        {$valutazione['descrizione']}
                                    </td>
                                    {foreach $array_per_dettaglio_competenza['dati_competenza']['dimensioni'] as $dimensione}
                                    <td width="auto"
                                        align="center"
                                        class="bordo_sinistro_generico bordo_alto_generico padding3"
                                        style="z-index: 0;"
                                        >
                                        {foreach $valutazione['dimensioni'][$dimensione['id_campo_libero']]['valori_precomp'] as $val_precomp}
                                            {if $val_precomp['selezionato'] == 'SI'}
                                                {$val_precomp['descrizione']}
                                            {/if}
                                        {/foreach}
                                    </td>
                                    {/foreach}
                                    <td width="auto"
                                        align="center"
                                        class="bordo_sinistro_generico bordo_alto_generico padding3"
                                        style="z-index: 0;"
                                        >
                                        {$valutazione['note']}
                                    </td>
                                    <td width="auto"
                                        align="center"
                                        class="bordo_sinistro_generico bordo_alto_generico padding3"
                                        style="z-index: 0;"
                                        >
                                        {$valutazione['note_riservate']}
                                    </td>

                                    {assign var='cont_val' value=$cont_val+1}
                                    {if $cont_val < $studente['n_val']}</tr><tr class="evidenzia">{/if}
                                    {/foreach}
                                </tr>
                            {/foreach}
                        </table>
                    </div>
                </div>
            </div>
        {else}
            {if $tabella_voti|@count > 0}
                <div id="elenco_voti" style="overflow: auto; -webkit-overflow-scrolling: touch;">
                {if $voti_studente_selezionato}
                    {* -- Voti dello studente selezionato per tutto l'anno -- *}
                    <div align="center" style="width: 98%; padding-bottom: 60px;" id="voti_studente_specifico">
                        <div style="background-color: #ff8075; border-radius: 32px; color: white; display: flex; align-items: center; justify-content: space-between;">
                            <div align="left" class="ombra_testo" style="padding-left: 10px;">
                                <b>Voti di {$voti_studente_selezionato['cognome']} {$voti_studente_selezionato['nome']}{if $voti_studente_selezionato['necessita_sostegno'] == '1'}{$param_simbolo_studente_sostegno}{/if}</b>
                                    {if $param_mostra_ammissione_registro == 'SI' && $voti_studente_selezionato['stampa_esito'] == 'SI'}<i>({$voti_studente_selezionato['esito_corrente_calcolato']})</i>{/if}
                            </div>
                            <div align="right">
                                <button type="button"
                                    value="Chiudi"
                                    title="{mastercom_label}Chiudi{/mastercom_label}"
                                    class="btn_flat_bianco ombra_testo"
                                    id="btn_chiudi_studente"
                                    onclick="$('#voti_studente_specifico').remove(); $('#id_studente').val('');"
                                    >{mastercom_label}Chiudi{/mastercom_label}</button>
                            </div>
                        </div>
                        <div align="center">
                            <div style="overflow: auto; background-color: lightgray;" id="fixed-headers-studente">
                                <table width="100%" style="font-size: 90%;">
                                    <tr width="100%" class='sfondo_bianco'>
                                        <td class="padding_cella_generica {*colonna_mese_voti_studente*}" style="z-index: 1; background-color: #FAFAFA; position: -webkit-sticky; position: sticky; left:0;"></td>
                                        {for $foo=1 to $numero_giorni_mese}
                                        <td align="center" class="padding_cella_generica bordo_sinistro_generico bordo_basso_generico">
                                            <b>{$foo}</b>
                                        </td>
                                        {/for}
                                    </tr>
                                    {foreach $voti_studente_selezionato['calendario'] as $mese}
                                        <tr class="padding_cella_generica bordo_alto_generico bordo_basso_generico sfondo_bianco">
                                            <td class="{*colonna_mese_voti_studente*} padding_cella_generica bordo_alto_generico" style="z-index: 1; background-color: #FAFAFA; position: relative; position: -webkit-sticky; position: sticky; left:0;">
                                                <b>{$mese['mese_tradotto']}</b>
                                            </td>
                                            {foreach $mese['giorni'] as $giorno}
                                                <td align='center'
                                                    class="bordo_sinistro_generico bordo_alto_generico bordo_basso_generico {if $giorno['voti']|@count == 0 && $funzione_voti_registro == '1'}evidenzia{/if}"
                                                    style="z-index: 0;
                                                            {if $giorno['festivo'] == 'SI'}background-color: lightgray;{elseif $giorno['data_futura'] != 'SI' && $funzione_voti_registro == '1'}cursor: pointer;{/if}"
                                                    {if $giorno['festivo'] != 'SI' && $giorno['voti']|@count == 0 && $giorno['data_futura'] != 'SI' && $funzione_voti_registro == '1'}
                                                        {if $param_attiva_voti_registro == 'SI'}
                                                            title="Inserisci voto"
                                                            onclick="if (selezione_abilitata === false) {
                                                                caricaDataStudenteInserimento('{$giorno['timestamp']}', '{$voti_studente_selezionato['id_studente']}', '{$voti_studente_selezionato['cognome']|escape:'quotes'} {$voti_studente_selezionato['nome']|escape:'quotes'}{if $voti_studente_selezionato['necessita_sostegno'] == '1'}{$param_simbolo_studente_sostegno}{/if}');
                                                                apriChiudiPopup('nuovo_voto', 'sfondo_oscurato', true);}"
                                                        {/if}
                                                    {/if}
                                                        >
                                                    {foreach $giorno['voti'] as $voto}
                                                        <label style='white-space: nowrap; display: block;'
                                                                class='voto {$voto['filter_class']} {$voto['id_materia']}
                                                                        {if $voto['annotazione'] == 'SI'}
                                                                            annotazione{$voto['id_voto']}
                                                                        {else}
                                                                            voto{$voto['id_voto']}
                                                                        {/if}'
                                                                {if $voto['id_professore'] == $current_user && $funzione_voti_registro == '1'}
                                                                    onclick="apriMenuVoti(this, '{$voto['id_voto']}');"
                                                                {/if}
                                                            >
                                                            {if $voto['id_professore'] == $current_user && $funzione_voti_registro == '1'}
                                                                <input type='checkbox' style='vertical-align: middle; display: none;'
                                                                    class='check_voti
                                                                                {if $voto['annotazione'] == 'SI'}
                                                                                    check_annotazione{$voto['id_voto']}
                                                                                {else}
                                                                                    check_voto{$voto['id_voto']}
                                                                                {/if}'
                                                                        disabled>
                                                            {/if}
                                                            <div class="tooltip
                                                                        btn_pieno_tondo
                                                                        voto_btn
                                                                        noselect
                                                                        {if $voto['insufficiente'] == 'SI'}
                                                                            ombra_testo
                                                                        {/if}
                                                                        "
                                                                style=" background: repeating-linear-gradient(
                                                                                    90deg,
                                                                                    {$voto['colore_sfondo1']},
                                                                                    {$voto['colore_sfondo1']} 7px,
                                                                                    {$voto['colore_sfondo2']} 7px,
                                                                                    {$voto['colore_sfondo2']} 33px,
                                                                                    {$voto['colore_sfondo3']} 33px,
                                                                                    {$voto['colore_sfondo3']} 40px
                                                                                );
                                                                        color: {$voto['colore_testo']};
                                                                        width: 24px;
                                                                        margin: 2px 0px;
                                                                        padding: 3px 8px;
                                                                        display: inline-block;
                                                                        {if $voto['id_professore'] == $current_user}
                                                                            font-weight: bold;
                                                                        {else}
                                                                            font-weight: normal;
                                                                        {/if}
                                                                        {if $voto['insufficiente'] != 'SI'}
                                                                        border: 1px solid {$voto['colore_sfondo1']};
                                                                        {/if}
                                                                        box-shadow: none;">
                                                                {$voto['codice_voto']}<span class="tooltiptext tooltiptext-novisible">{$voto['descrizione_materia']}
                                                                                    <div class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>({$voto['professore']})</div>
                                                                                        {if $voto['annotazione'] == 'SI'}
                                                                                            <div align='center' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>({$voto['descrizione']|escape})</div>
                                                                                        {/if}
                                                                                        {if $voto['note'] !== ''}
                                                                                            <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>{$voto['note']|escape}</div>
                                                                                        {/if}
                                                                                        {if $voto['descrizione_tag'] !== ''}
                                                                                            <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'># {$voto['descrizione_tag']}</div>
                                                                                        {/if}
                                                                                        {if $voto['lista_prese_visioni']|@count > 0}
                                                                                            <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>
                                                                                            {mastercom_label}Prese visioni{/mastercom_label}:
                                                                                                <ul>
                                                                                                {foreach $voto['lista_prese_visioni'] as $presa_visione}
                                                                                                    <li>{$presa_visione['nome']} {$presa_visione['data']}</li>
                                                                                                {/foreach}
                                                                                                </ul>
                                                                                            </div>
                                                                                        {/if}
                                                                                </span>
                                                            </div>
                                                        </label>
                                                    {/foreach}
                                                </td>
                                            {/foreach}
                                        </tr>
                                    {/foreach}
                                    <tr width="100%" class='sfondo_bianco'>
                                        <td class="padding_cella_generica {*colonna_mese_voti_studente*}" style="z-index: 1; background-color: #FAFAFA; position: -webkit-sticky; position: sticky; left:0;"></td>
                                        {for $foo=1 to 31}
                                        <td align="center" class="padding_cella_generica bordo_sinistro_generico bordo_basso_generico">
                                            <b>{$foo}</b>
                                        </td>
                                        {/for}
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                {/if}

                {* -- Voti della classe --*}
                <div align="center" style="width: 98%;">
                    <div align='left' class='ombra_testo sfondo_verde_chiaro' style="border-radius: 32px; padding-left: 10px; height: 27px; color: white; display: flex; align-items: center;">
                        <b>Elenco voti della classe</b>
                    </div>
                    <div align="center" style="padding-bottom: 10px;">
                        <div style="overflow: auto;" id="fixed-headers-classe">
                            <table width="100%" style="font-size: 90%;">
                                <tr width="100%">
                                    <td class="padding_cella_generica colonna_studente_voti sfondo_bianco"
                                        {if $optional_competenze_professore == 'OK' && $elenco_giorni|count > 0 && $visualizzazione == 'estesa'}
                                            rowspan="2"
                                        {/if}
                                        colspan="3"
                                        style="z-index: 1; position: relative; box-shadow: 2px 0 4px rgba(0,0,0,0.15);">
                                        <button type="button"
                                            class="btn_flat_tondo testo_grigio_scuro bordo_grigio_scuro"
                                            onclick="toggleFoto(this);"
                                            data-stato-foto="NO"
                                            >{mastercom_label}Foto{/mastercom_label}</button>
                                    </td>
                                    {if $elenco_giorni|count > 0}
                                        {if $visualizzazione == 'estesa'}
                                            {foreach $elenco_giorni as $giorno}
                                                <td align="center"
                                                    class="bordo_sinistro_generico padding_cella_generica"
                                                    {if $optional_competenze_professore == 'OK'}
                                                        {if $param_attiva_voti_registro == 'SI'}
                                                            colspan="{$giorno['competenze']|count + 1}"
                                                        {else}
                                                            colspan="{$giorno['competenze']|count}"
                                                        {/if}
                                                    {/if}
                                                    style="{if $giorno['festivo'] == 'SI'}background-color: lightgray;{/if} border-left: 2px solid #868686;">
                                                    <div
                                                        {if $giorno['oggi'] == 'SI'}
                                                            class="ombra_testo"
                                                            style="color:white; background-color: #663399; border-radius: 20px; display:block; padding:1px 7px;"
                                                        {/if}
                                                    >
                                                        {$giorno['giorno_tradotto']}
                                                        {*<br>*}
                                                        <span style='white-space: nowrap; font-weight: bold; font-size: 120%'>{$giorno['giorno']} {$giorno['mese_tradotto']|substr:0:3} {$giorno['anno']|substr:2}</span>
                                                    </div>
                                                </td>
                                            {/foreach}
                                        {else}
                                            <td></td>
                                        {/if}
                                    {else}
                                        <td align="center" rowspan="{$tabella_voti|count + 2}" style="vertical-align: middle;">
                                            <i>
                                                {mastercom_label}Non sono ancora stati inseriti voti nella classe{/mastercom_label}<br>
                                                {mastercom_label}Puoi aggiungere voti con i tasti verdi in alto a destra{/mastercom_label}
                                            </i>
                                        </td>
                                    {/if}
                                </tr>
                                {if $optional_competenze_professore == 'OK' && $elenco_giorni|count > 0 && $visualizzazione == 'estesa'}
                                    <tr>
                                        {foreach $elenco_giorni as $giorno}
                                            {if $param_attiva_voti_registro == 'SI'}
                                            <td align="center"
                                                class="bordo_sinistro_generico padding_cella_generica"
                                                style="{if $giorno['festivo'] == 'SI'}background-color: lightgray;{/if} border-left: 2px solid #868686;">
                                                <div>Voti</div>
                                            </td>
                                            {/if}
                                            {if $giorno['competenze']|count > 0}
                                                {if $param_attiva_voti_registro == 'NO'}
                                                    {assign var='i' value=0}
                                                {/if}
                                                {foreach $giorno['competenze'] as $comp}
                                                    {if $param_attiva_voti_registro == 'NO'}
                                                        {assign var='i' value=$i+1}
                                                    {/if}
                                                    <td align="center"
                                                        class="bordo_sinistro_generico padding_cella_generica noselect"
                                                        style="
                                                        {if $i == 1} border-left: 2px solid #868686; {/if}
                                                        {if $giorno['festivo'] == 'SI'} background-color: lightgray; {/if}
                                                        ">
                                                        <div class="tooltip">
                                                            {$comp['dati_competenza']['codice']}
                                                            <span class="tooltiptext tooltiptext-novisible">{$comp['dati_competenza']['descrizione']}</span>
                                                        </div>
                                                    </td>
                                                {/foreach}
                                            {/if}
                                        {/foreach}
                                    </tr>
                                {/if}
                                {assign var='cont_stud' value=0}
                                {foreach $tabella_voti as $studente}
                                    <tr width="100%" class="bordo_alto_generico">
                                        <td align="center"
                                            style="z-index: 1; font-weight: bold; position: relative; width: min-content;"
                                            class="colonna_studente_assenze colonna_studente_voti sfondo_bianco">
                                            {if $studente['foto'] != ''}
                                                <img src="foto_studenti/{$studente['foto']}" data-colonna="foto" style="max-width: 14rem; max-height: 14rem; display: none;"/>
                                            {/if}
                                        </td>
                                        <td align="left"
                                            style="z-index: 1; font-weight: bold; position: relative; width: 20px; white-space: normal;"
                                            class="padding_cella_generica colonna_studente_voti sfondo_bianco">
                                            {$studente['registro']}.
                                        </td>
                                        <td align="left"
                                            style="z-index: 1; font-weight: bold; position: relative; background-color: #FAFAFA; box-shadow: 2px 0 4px rgba(0,0,0,0.15);"
                                            class="padding_cella_generica colonna_studente_voti bordo_alto_generico">
                                            <span   {if $optional_competenze_professore != 'OK'}
                                                    style="cursor: pointer;"
                                                    title="Apri dettaglio voti dello studente"
                                                    onclick="document.getElementById('form_container').id_studente.value='{$studente['id_studente']}';
                                                            document.getElementById('form_container').operazione.value='voti_studente';
                                                            document.getElementById('form_container').submit();
                                                            $('#jqxGeneralModalLoader').jqxLoader('open');"
                                                    {/if}
                                                >{$studente['cognome']} {$studente['nome']}{if $studente['necessita_sostegno'] == '1'}{$param_simbolo_studente_sostegno}{/if}
                                                {if $param_mostra_ammissione_registro == 'SI' && $studente['stampa_esito'] == 'SI'}<i>({$studente['esito_corrente_calcolato']})</i>{/if}
                                            </span>
                                        </td>
                                        {if $visualizzazione == 'estesa'}
                                            {foreach $elenco_giorni as $data => $giorno}
                                                {if $param_attiva_voti_registro == 'SI'}
                                                <td width="auto"
                                                    align="center"
                                                    {if $studente['calendario'][$data]['voti']|@count == 0}
                                                        class="bordo_sinistro_generico {if $funzione_voti_registro == '1'}evidenzia{/if}"
                                                        style="z-index: 0; border-left: 2px solid #868686; {if $giorno['festivo'] == 'SI'}background-color: lightgray;{elseif $giorno['data_futura'] != 'SI' && $funzione_voti_registro == '1'} cursor: pointer;{/if}"
                                                        {if $giorno['festivo'] != 'SI' && $giorno['data_futura'] != 'SI' && $funzione_voti_registro == '1' && $funzione_voti_registro == '1'}
                                                            {if $param_attiva_voti_registro == 'SI'}
                                                                title="Inserisci voto"
                                                                onclick="if (selezione_abilitata === false) {
                                                                    caricaDataStudenteInserimento('{$giorno['timestamp']}', '{$studente['id_studente']}', '{$studente['cognome']|escape:'quotes'} {$studente['nome']|escape:'quotes'}{if $studente['necessita_sostegno'] == '1'}{$param_simbolo_studente_sostegno}{/if}');
                                                                    apriChiudiPopup('nuovo_voto', 'sfondo_oscurato', true);}"
                                                            {/if}
                                                        {/if}
                                                    {else}
                                                        class="bordo_sinistro_generico"
                                                        style="z-index: 0; border-left: 2px solid #868686; {if $giorno['festivo'] == 'SI'}background-color: lightgray;{/if}"
                                                    {/if}
                                                    >
                                                    {* VOTI *}
                                                    {if $studente['calendario'][$data]['voti']|@count > 0}
                                                        {foreach $studente['calendario'][$data]['voti'] as $voto}
                                                            <label style='white-space: nowrap; display: inline-block;'
                                                                class='voto {$voto['filter_class']} {$voto['id_materia']} filterable
                                                                            {if $voto['annotazione'] == 'SI'}
                                                                                annotazione{$voto['id_voto']}
                                                                            {else}
                                                                                voto{$voto['id_voto']}
                                                                            {/if}'
                                                                    data-id-materia='{$voto['id_materia']}'
                                                                    data-filter='{$voto['filter_class']}'
                                                                    {if $voto['id_professore'] == $current_user && $funzione_voti_registro == '1'}
                                                                        onclick="apriMenuVoti(this, '{$voto['id_voto']}');"
                                                                    {/if}
                                                                >
                                                                {if $voto['id_professore'] == $current_user}
                                                                    <input type='checkbox'
                                                                        style='vertical-align: middle; display: none;'
                                                                        class='check_voti
                                                                                {if $voto['annotazione'] == 'SI'}
                                                                                    check_annotazione{$voto['id_voto']}
                                                                                {else}
                                                                                    check_voto{$voto['id_voto']}
                                                                                {/if}'
                                                                        disabled>
                                                                {/if}
                                                                <div class="tooltip
                                                                            btn_pieno_tondo
                                                                            voto_btn
                                                                            noselect
                                                                            {if $voto['insufficiente'] == 'SI'}
                                                                                ombra_testo
                                                                            {/if}
                                                                            {if $voto['peso_default'] !== 't' && $gestione_pesi_voti == 'SI'}
                                                                                underline
                                                                            {/if}
                                                                            "
                                                                    style=" background: repeating-linear-gradient(
                                                                                90deg,
                                                                                {$voto['colore_sfondo1']},
                                                                                {$voto['colore_sfondo1']} 7px,
                                                                                {$voto['colore_sfondo2']} 7px,
                                                                                {$voto['colore_sfondo2']} 33px,
                                                                                {$voto['colore_sfondo3']} 33px,
                                                                                {$voto['colore_sfondo3']} 40px
                                                                            );
                                                                            color: {$voto['colore_testo']};
                                                                            width: 24px;
                                                                            margin: 2px 0px;
                                                                            padding: 3px 8px;
                                                                            display: inline-block;
                                                                            {if $voto['id_professore'] == $current_user}
                                                                                font-weight: bold;
                                                                            {else}
                                                                                font-weight: normal;
                                                                            {/if}
                                                                            {if $voto['insufficiente'] != 'SI'}
                                                                            border: 1px solid {$voto['colore_sfondo1']};
                                                                            {/if}
                                                                            box-shadow: none;
                                                                            cursor: pointer;">
                                                                    {$voto['codice_voto']}<div class="tooltiptext tooltiptext-novisible">{$voto['descrizione_materia']}
                                                                        <div class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>({$voto['professore']})</div>
                                                                        {if $voto['annotazione'] == 'SI'}
                                                                            <div align='center' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>({$voto['descrizione']|escape})</div>
                                                                        {/if}
                                                                        {if $voto['peso'] !== '' && $gestione_pesi_voti == 'SI' && $voto['annotazione'] != 'SI'}
                                                                            <div align='center' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'
                                                                                >{mastercom_label}Peso{/mastercom_label}: {$voto['peso']}</div>
                                                                        {/if}
                                                                        {if $voto['note'] !== ''}
                                                                            <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>{$voto['note']|escape}</div>
                                                                        {/if}
                                                                        {if $voto['descrizione_tag'] !== ''}
                                                                            <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'># {$voto['descrizione_tag']}</div>
                                                                        {/if}
                                                                        {if $voto['lista_prese_visioni']|@count > 0}
                                                                            <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>
                                                                            {mastercom_label}Prese visioni{/mastercom_label}:
                                                                                <ul>
                                                                                {foreach $voto['lista_prese_visioni'] as $presa_visione}
                                                                                    <li>{$presa_visione['nome']} {$presa_visione['data']}</li>
                                                                                {/foreach}
                                                                                </ul>
                                                                            </div>
                                                                        {/if}
                                                                    </div>
                                                                </div>
                                                            </label>
                                                        {/foreach}
                                                    {/if}
                                                </td>
                                                {/if}

                                                {* COMPETENZE *}
                                                {if $giorno['competenze']|@count > 0}
                                                    {if $param_attiva_voti_registro == 'NO'}
                                                        {assign var='i' value=0}
                                                    {/if}
                                                    {foreach $giorno['competenze'] as $competenza_generale}
                                                        {if $param_attiva_voti_registro == 'NO'}
                                                            {assign var='i' value=$i+1}
                                                        {/if}
                                                        <td width="auto"
                                                            align="center"
                                                            class="bordo_sinistro_generico"
                                                            style="
                                                                z-index: 0;
                                                                {if $giorno['festivo'] == 'SI'}background-color: lightgray;{/if}
                                                                {if $i == 1} border-left: 2px solid #868686; {/if}
                                                            "
                                                            >
                                                            {if $studente['calendario'][$data]['competenze']|@count > 0}
                                                                {foreach $studente['calendario'][$data]['competenze'] as $dati_competenza}
                                                                    {if $dati_competenza['dati_competenza']['id'] == $competenza_generale['dati_competenza']['id'] && $dati_competenza['valutazioni']|count > 0}
                                                                        {foreach $dati_competenza['valutazioni'] as $valutazione}
                                                                            <label style='white-space: nowrap; display: inline-block;'
                                                                                    {if $valutazione['insert_id'] == $user_couch_id && $funzione_voti_registro == '1'}
                                                                                        onclick="apriMenuCompetenze(this, '{$studente['id_studente']}', '{$dati_competenza['dati_competenza']['id']}', '{$valutazione['id']}');"
                                                                                    {/if}
                                                                                    data-id-materia='{$valutazione['id_materia']}'
                                                                                    data-filter='filter_competenze'
                                                                                    class="filter_competenze competenza {$valutazione['id_materia']} filterable"
                                                                                    id="val_comp_{$studente['id_studente']}_{$dati_competenza['dati_competenza']['id']}_{$valutazione['id']}"
                                                                                >
                                                                                {if $valutazione['insert_id'] == $user_couch_id}
                                                                                    <input type='checkbox'
                                                                                        style='vertical-align: middle; display: none;'
                                                                                        data-id-studente="{$studente['id_studente']}"
                                                                                        data-id-competenza="{$dati_competenza['dati_competenza']['id']}"
                                                                                        data-id-valutazione="{$valutazione['id']}"
                                                                                        class='check_competenze check_val_{$studente['id_studente']}_{$dati_competenza['dati_competenza']['id']}_{$valutazione['id']}'
                                                                                        disabled>
                                                                                {/if}
                                                                                <div class="tooltip
                                                                                            btn_pieno_tondo
                                                                                            voto_btn
                                                                                            noselect
                                                                                            "
                                                                                    style=" background-color: #bbbbff;
                                                                                            color: black;
                                                                                            position: relative;
                                                                                            width: 36px;
                                                                                            margin: 2px 0px;
                                                                                            padding: 3px 2px;
                                                                                            display: inline-block;
                                                                                            {if $valutazione['insert_id'] == $user_couch_id}
                                                                                                font-weight: bold;
                                                                                            {else}
                                                                                                font-weight: normal;
                                                                                            {/if}
                                                                                            box-shadow: none;
                                                                                            border-radius: 5px;
                                                                                            white-space: nowrap;
                                                                                            *overflow: hidden;
                                                                                            text-overflow: clip;
                                                                                            cursor: pointer;">
                                                                                    <div style='width: 36px; overflow: hidden;
                                                                                        {if $valutazione['colore'] != ""}
                                                                                            color: {$valutazione['colore']};
                                                                                        {/if}
                                                                                    '>
                                                                                        {$valutazione['codice']}
                                                                                    </div>
                                                                                    <div class="tooltiptext tooltiptext-novisible">{$valutazione['descrizione_materia']}
                                                                                        <div class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>({$valutazione['insert_name']})</div>
                                                                                        {if $gestione_pesi_competenze == 'SI'}
                                                                                            {foreach $elenco_pesi_competenze as $peso}
                                                                                                {if $peso['id_peso'] == $valutazione['id_peso']}
                                                                                                        <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>{mastercom_label}Peso{/mastercom_label}: {$peso['peso']}</div>
                                                                                                    {break}
                                                                                                {/if}
                                                                                            {/foreach}
                                                                                        {/if}
                                                                                        {if $valutazione['note'] !== ''}
                                                                                            <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>{$valutazione['note']}</div>
                                                                                        {/if}
                                                                                        {if $valutazione['note_riservate'] !== ''}
                                                                                            <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>{$valutazione['note_riservate']} <i>({mastercom_label}riservata{/mastercom_label})</i></div>
                                                                                        {/if}
                                                                                        {if $valutazione['dimensioni']|@count > 0}
                                                                                            {foreach $valutazione['dimensioni'] as $dimensione}
                                                                                                <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>
                                                                                                {$dimensione['nome']}:
                                                                                                {foreach $dimensione['valori_precomp'] as $valore_precomp}
                                                                                                    <b>
                                                                                                        {if $valore_precomp['selezionato'] == 'SI'}
                                                                                                            {if $valore_precomp['id_valore_precomp'] == -1}
                                                                                                                {$valore_precomp['valore_testo']}
                                                                                                            {else}
                                                                                                                {$valore_precomp['descrizione']}
                                                                                                            {/if}
                                                                                                            {break}
                                                                                                        {/if}
                                                                                                    </b>
                                                                                                {/foreach}
                                                                                                </div>
                                                                                            {/foreach}
                                                                                        {/if}
                                                                                    </div>
                                                                                </div>
                                                                            </label>
                                                                        {/foreach}
                                                                    {/if}
                                                                {/foreach}
                                                            {/if}
                                                        </td>
                                                    {/foreach}
                                                {/if}
                                            {/foreach}
                                        {else}
                                        {* Visualizzazione compatta*}
                                        <td class="nowrap">
                                            {if $param_attiva_voti_registro == 'SI'}
                                                {if $studente['media_aritmetica'] != ""}
                                                    <div align="center" class="inline bordo_destro_generico">
                                                        <div class="padding3 font90p">
                                                            Media {if $gestione_pesi_voti == 'SI'}pes.{else}aritm.{/if}
                                                        </div>
                                                        <div class="padding3 bold" style="margin-top: 4px;">
                                                            {if $gestione_pesi_voti == 'SI'}
                                                                {$studente['media_pesata']}
                                                            {else}
                                                                {$studente['media_aritmetica']}
                                                            {/if}
                                                        </div>
                                                    </div>
                                                {/if}
                                            {/if}
                                            {foreach $studente['calendario'] as $giorno}
                                                {if $giorno['voti']|@count > 0 && $param_attiva_voti_registro == 'SI'}
                                                    {foreach $giorno['voti'] as $voto}
                                                        <div align="center"
                                                            class='bordo_destro_generico inline
                                                                    voto {$voto['filter_class']} {$voto['id_materia']} filterable
                                                                    {if $voto['annotazione'] == 'SI'}
                                                                        annotazione{$voto['id_voto']}
                                                                    {else}
                                                                        voto{$voto['id_voto']}
                                                                    {/if}'
                                                            data-id-materia='{$voto['id_materia']}'
                                                            data-filter='{$voto['filter_class']}'
                                                        >
                                                            <div class="padding3 font90p">
                                                                {$voto['giorno']}/{$voto['mese']}
                                                            </div>
                                                            <div class="padding3">
                                                                <label style='white-space: nowrap; display: inline-block;'
                                                                        {if $voto['id_professore'] == $current_user && $funzione_voti_registro == '1'}
                                                                            onclick="apriMenuVoti($(this).parent().parent()[0], '{$voto['id_voto']}');"
                                                                        {/if}
                                                                    >
                                                                    {if $voto['id_professore'] == $current_user}
                                                                        <input type='checkbox'
                                                                            style='vertical-align: middle; display: none;'
                                                                            class='check_voti
                                                                                    {if $voto['annotazione'] == 'SI'}
                                                                                        check_annotazione{$voto['id_voto']}
                                                                                    {else}
                                                                                        check_voto{$voto['id_voto']}
                                                                                    {/if}'
                                                                            disabled>
                                                                    {/if}
                                                                    <div class="tooltip
                                                                                btn_pieno_tondo
                                                                                voto_btn
                                                                                noselect
                                                                                {if $voto['insufficiente'] == 'SI'}
                                                                                    ombra_testo
                                                                                {/if}
                                                                                {if $voto['peso_default'] !== 't' && $gestione_pesi_voti == 'SI'}
                                                                                    underline
                                                                                {/if}
                                                                                "
                                                                        style=" background: repeating-linear-gradient(
                                                                                    90deg,
                                                                                    {$voto['colore_sfondo1']},
                                                                                    {$voto['colore_sfondo1']} 7px,
                                                                                    {$voto['colore_sfondo2']} 7px,
                                                                                    {$voto['colore_sfondo2']} 33px,
                                                                                    {$voto['colore_sfondo3']} 33px,
                                                                                    {$voto['colore_sfondo3']} 40px
                                                                                );
                                                                                color: {$voto['colore_testo']};
                                                                                width: 24px;
                                                                                margin: 2px 0px;
                                                                                padding: 3px 8px;
                                                                                display: inline-block;
                                                                                {if $voto['id_professore'] == $current_user}
                                                                                    font-weight: bold;
                                                                                {else}
                                                                                    font-weight: normal;
                                                                                {/if}
                                                                                {if $voto['insufficiente'] != 'SI'}
                                                                                border: 1px solid {$voto['colore_sfondo1']};
                                                                                {/if}
                                                                                box-shadow: none;
                                                                                cursor: pointer;">
                                                                        {$voto['codice_voto']}<div class="tooltiptext tooltiptext-novisible">{$voto['descrizione_materia']}
                                                                            <div class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>({$voto['professore']})</div>
                                                                            {if $voto['annotazione'] == 'SI'}
                                                                                <div align='center' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>({$voto['descrizione']|escape})</div>
                                                                            {/if}
                                                                            {if $voto['peso'] !== '' && $gestione_pesi_voti == 'SI' && $voto['annotazione'] != 'SI'}
                                                                                <div align='center' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'
                                                                                    >{mastercom_label}Peso{/mastercom_label}: {$voto['peso']}</div>
                                                                            {/if}
                                                                            {if $voto['note'] !== ''}
                                                                                <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>{$voto['note']|escape}</div>
                                                                            {/if}
                                                                            {if $voto['descrizione_tag'] !== ''}
                                                                                <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'># {$voto['descrizione_tag']}</div>
                                                                            {/if}
                                                                            {if $voto['lista_prese_visioni']|@count > 0}
                                                                                <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>
                                                                                {mastercom_label}Prese visioni{/mastercom_label}:
                                                                                    <ul>
                                                                                    {foreach $voto['lista_prese_visioni'] as $presa_visione}
                                                                                        <li>{$presa_visione['nome']} {$presa_visione['data']}</li>
                                                                                    {/foreach}
                                                                                    </ul>
                                                                                </div>
                                                                            {/if}
                                                                        </div>
                                                                    </div>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    {/foreach}
                                                {/if}
                                                {if $giorno['competenze']|@count > 0}
                                                    {foreach $giorno['competenze'] as $dati_competenza}
                                                        {if $dati_competenza['valutazioni']|@count > 0}
                                                            {foreach $dati_competenza['valutazioni'] as $valutazione}
                                                                <div align="center"
                                                                    class='bordo_destro_generico inline
                                                                        filter_competenze competenza {$valutazione['id_materia']} filterable'
                                                                    data-id-materia='{$valutazione['id_materia']}'
                                                                    data-filter='filter_competenze'
                                                                >
                                                                    <div class="padding3 font90p">
                                                                        {$valutazione['giorno']}/{$valutazione['mese']}
                                                                    </div>
                                                                    <div class="padding3">
                                                                        <label style='white-space: nowrap; display: inline-block;'
                                                                                {if $valutazione['insert_id'] == $user_couch_id && $funzione_voti_registro == '1'}
                                                                                    onclick="apriMenuCompetenze(this, '{$studente['id_studente']}', '{$dati_competenza['dati_competenza']['id']}', '{$valutazione['id']}');"
                                                                                {/if}
                                                                                id="val_comp_{$studente['id_studente']}_{$dati_competenza['dati_competenza']['id']}_{$valutazione['id']}"
                                                                            >
                                                                            {if $valutazione['insert_id'] == $user_couch_id}
                                                                                <input type='checkbox'
                                                                                    style='vertical-align: middle; display: none;'
                                                                                    data-id-studente="{$studente['id_studente']}"
                                                                                    data-id-competenza="{$dati_competenza['dati_competenza']['id']}"
                                                                                    data-id-valutazione="{$valutazione['id']}"
                                                                                    class='check_competenze check_val_{$studente['id_studente']}_{$dati_competenza['dati_competenza']['id']}_{$valutazione['id']}'
                                                                                    disabled>
                                                                            {/if}
                                                                            <div class="tooltip
                                                                                        btn_pieno_tondo
                                                                                        voto_btn
                                                                                        noselect
                                                                                        "
                                                                                style=" background-color: #bbbbff;
                                                                                        color: black;
                                                                                        position: relative;
                                                                                        width: 36px;
                                                                                        margin: 2px 0px;
                                                                                        padding: 3px 2px;
                                                                                        display: inline-block;
                                                                                        {if $valutazione['insert_id'] == $user_couch_id}
                                                                                            font-weight: bold;
                                                                                        {else}
                                                                                            font-weight: normal;
                                                                                        {/if}
                                                                                        box-shadow: none;
                                                                                        border-radius: 5px;
                                                                                        white-space: nowrap;
                                                                                        *overflow: hidden;
                                                                                        text-overflow: clip;
                                                                                        cursor: pointer;">
                                                                                <div style='width: 36px; overflow: hidden;
                                                                                    {if $valutazione['colore'] != ""}
                                                                                        color: {$valutazione['colore']};
                                                                                    {/if}
                                                                                '>
                                                                                    {$valutazione['codice']}
                                                                                </div>
                                                                                <div class="tooltiptext tooltiptext-novisible">{$valutazione['descrizione_materia']}
                                                                                        <div class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>({$valutazione['insert_name']})</div>
                                                                                        <div align='left' class='padding_cella_generica' style='font-weight: normal;'>{$dati_competenza['dati_competenza']['codice']}</div>
                                                                                        <div align='left' class='padding_cella_generica' style='font-weight: normal;'>{$dati_competenza['dati_competenza']['descrizione']}</div>
                                                                                    {if $gestione_pesi_competenze == 'SI'}
                                                                                        {foreach $elenco_pesi_competenze as $peso}
                                                                                            {if $peso['id_peso'] == $valutazione['id_peso']}
                                                                                                    <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>{mastercom_label}Peso{/mastercom_label}: {$peso['peso']}</div>
                                                                                                {break}
                                                                                            {/if}
                                                                                        {/foreach}
                                                                                    {/if}
                                                                                    {if $valutazione['note'] !== ''}
                                                                                        <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>{$valutazione['note']}</div>
                                                                                    {/if}
                                                                                    {if $valutazione['note_riservate'] !== ''}
                                                                                        <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>{$valutazione['note_riservate']} <i>({mastercom_label}riservata{/mastercom_label})</i></div>
                                                                                    {/if}
                                                                                    {if $valutazione['dimensioni']|@count > 0}
                                                                                        {foreach $valutazione['dimensioni'] as $dimensione}
                                                                                            <div align='left' class='padding_cella_generica' style='font-weight: normal; color: #aaa;'>
                                                                                            {$dimensione['nome']}:
                                                                                            {foreach $dimensione['valori_precomp'] as $valore_precomp}
                                                                                                <b>
                                                                                                    {if $valore_precomp['selezionato'] == 'SI'}
                                                                                                        {if $valore_precomp['id_valore_precomp'] == -1}
                                                                                                            {$valore_precomp['valore_testo']}
                                                                                                        {else}
                                                                                                            {$valore_precomp['descrizione']}
                                                                                                        {/if}
                                                                                                        {break}
                                                                                                    {/if}
                                                                                                </b>
                                                                                            {/foreach}
                                                                                            </div>
                                                                                        {/foreach}
                                                                                    {/if}
                                                                                </div>
                                                                            </div>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            {/foreach}
                                                        {/if}
                                                    {/foreach}
                                                {/if}
                                            {/foreach}
                                        </td>
                                        {/if}
                                    </tr>
                                    {assign var='cont_stud' value=$cont_stud+1}
                                {/foreach}
                                {if $optional_competenze_professore == 'OK' && $elenco_giorni|count > 0 && $visualizzazione == 'estesa'}
                                    <tr class="bordo_alto_generico">
                                        <td class="padding_cella_generica colonna_studente_voti sfondo_bianco"
                                            colspan="3"
                                            style="z-index: 1; position: relative; ">
                                        </td>
                                        {foreach $elenco_giorni as $giorno}
                                            {if $param_attiva_voti_registro == 'SI'}
                                            <td align="center"
                                                class="bordo_sinistro_generico padding_cella_generica"
                                                style="{if $giorno['festivo'] == 'SI'}background-color: lightgray;{/if} border-left: 2px solid #868686;">
                                                <div>Voti</div>
                                            </td>
                                            {/if}
                                            {if $giorno['competenze']|count > 0}
                                                {if $param_attiva_voti_registro == 'NO'}
                                                    {assign var='i' value=0}
                                                {/if}
                                                {foreach $giorno['competenze'] as $comp}
                                                    {if $param_attiva_voti_registro == 'NO'}
                                                        {assign var='i' value=$i+1}
                                                    {/if}
                                                    <td align="center"
                                                        class="bordo_sinistro_generico padding_cella_generica noselect"
                                                        style="
                                                        {if $i == 1} border-left: 2px solid #868686; {/if}
                                                        {if $giorno['festivo'] == 'SI'} background-color: lightgray; {/if}
                                                        ">
                                                        <div class="tooltip">
                                                            {$comp['dati_competenza']['codice']}
                                                            <span class="tooltiptext tooltiptext-novisible">{$comp['dati_competenza']['descrizione']}</span>
                                                        </div>
                                                    </td>
                                                {/foreach}
                                            {/if}
                                        {/foreach}
                                    </tr>
                                {/if}
                                {if $visualizzazione == 'estesa'}
                                    <tr width="100%" {if $optional_competenze_professore != 'OK'}class="bordo_alto_generico"{/if}>
                                        <td class="padding_cella_generica colonna_studente_voti sfondo_bianco"
                                            colspan="3"
                                            style="z-index: 1; position: relative; box-shadow: 2px 0 4px rgba(0,0,0,0.15);">
                                        </td>
                                        {foreach $elenco_giorni as $giorno}
                                            <td align="center"
                                                class="bordo_sinistro_generico padding_cella_generica"
                                                {if $optional_competenze_professore == 'OK'}
                                                    {if $param_attiva_voti_registro == 'SI'}
                                                        colspan="{$giorno['competenze']|count + 1}"
                                                    {else}
                                                        colspan="{$giorno['competenze']|count}"
                                                    {/if}
                                                {/if}
                                                style="{if $giorno['festivo'] == 'SI'}background-color: lightgray;{/if} border-left: 2px solid #868686;">
                                                <div
                                                    {if $giorno['oggi'] == 'SI'}
                                                        class="ombra_testo"
                                                        style="color:white; background-color: #663399; border-radius: 20px; display:block; padding:1px 7px;"
                                                    {else}

                                                    {/if}
                                                >
                                                    {$giorno['giorno_tradotto']}
                                                    {*<br>*}
                                                    <span style='white-space: nowrap; font-weight: bold; font-size: 120%'>{$giorno['giorno']} {$giorno['mese_tradotto']|substr:0:3} {$giorno['anno']|substr:2}</span>
                                                </div>
                                            </td>
                                        {/foreach}
                                    </tr>
                                {/if}
                            </table>
                        </div>
                    </div>
                </div>
            {elseif $id_classe > 0}
                <div class="annotazione_leggera" style="padding: 10px;">
                    {mastercom_label}Non ci sono studenti nella classe{/mastercom_label}
                </div>
            {/if}
            </div>
        {/if}

        {*form inserimento nuovo voto*}
        <div  class="div_scheda_generica"
                id="nuovo_voto"
                style="position: fixed;
                      width: 70%;
                      min-width: 300px;
                      max-height: 95%;
                      left: 50%;
                      top: 50%;
                      transform: translate(-50%, -50%);
                      display: none;
                      z-index: 12;
                      overflow-y: auto;"
            >
            <div align="center" style="width: 100%; height: 100%;">
                <table width="95%" align="center">
                    <tr>
                        <td align="center" colspan="2" style="padding: 15px 10px 3px 10px; font-size: 120%;">
                            <b>{mastercom_label}INSERISCI VOTO{/mastercom_label}</b><br>
                        </td>
                    </tr>
                    <tr>
                        <td align="center" colspan="2" style="padding: 3px 10px 20px 10px;" class='annotazione_leggera'>
                            {mastercom_label}Puoi inserire un voto, una nota didattica o entrambe{/mastercom_label}
                        </td>
                    </tr>
                    <tr>
                        <td align="center" colspan="2">
                            <div width="100%" style="display: flex; align-items: center; justify-content: space-around; flex-wrap: wrap;">
                                <div class="padding_cella_generica" id="nome_studente_fisso" style="display: none;">
                                    {mastercom_label}Studente{/mastercom_label} <div id='nome_studente_nuovo_voto' style="font-weight: bold; padding-top: 3px;"></div>
                                </div>
                                <div class="padding_cella_generica" id="nome_studente_select" style="display: none;">
                                    {mastercom_label}Studente{/mastercom_label}<br>
                                    <select id="select_studente" class="select" onchange="$('#nome_studente_hidden_nuovo_voto').val($('#select_studente option:selected').text());
                                            $('#id_studente_nuovo_voto').val(this.value);">
                                            <option value="">---</option>
                                        {foreach $elenco_studenti as $studente}
                                            <option value="{$studente['id_studente']}"
                                                {if $studente['esonero_religione'] == '1'}data-studente-esonero="SI"{/if}
                                            >
                                            {$studente['cognome']} {$studente['nome']}{if $studente['necessita_sostegno'] == '1'}{$param_simbolo_studente_sostegno}{/if}
                                            {if $param_mostra_ammissione_registro == 'SI' && $studente['stampa_esito'] == 'SI'}<i>({$studente['esito_corrente_calcolato']})</i>{/if}
                                            </option>
                                        {/foreach}
                                    </select>
                                </div>
                                <input type="hidden" name="nuovo_voto[id_studente]" id="id_studente_nuovo_voto">
                                <input type="hidden" name="nuovo_voto[nome_studente]" id="nome_studente_hidden_nuovo_voto">
                                <div class="padding_cella_generica">
                                    {mastercom_label}Materia{/mastercom_label}</br>
                                    <select class='select'
                                            id="select_materia"
                                            style='width: 250px;'
                                            onchange="$('#id_materia_nuovo_voto').val(this.value); caricaDatiVotoMateriaInserimento(this.value);"
                                            >
                                        <option value=''>-</option>
                                        {foreach $elenco_materie_classe_professore['professore'] as $materia}
                                            <option value='{$materia['id_materia']}'>
                                                {$materia['descrizione']}
                                            </option>
                                        {/foreach}
                                    </select>
                                    <input type="hidden" name="nuovo_voto[id_materia]" id='id_materia_nuovo_voto'>
                                </div>
                                <div class='padding_cella_generica'>
                                    {mastercom_label}Data{/mastercom_label}<br>
                                    <input type='date'
                                           name="nuovo_voto[data]"
                                           id="data_nuovo_voto"
                                           style="border: 0.5px solid #ccc; padding: 2px;">
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align="center" class='padding_cella_generica bordo_destro_generico'>
                            <div width="100%" style="display: flex; align-items: center; justify-content: space-around; flex-wrap: wrap;">
                                <div class="padding_cella_generica">
                                    {mastercom_label}Tipo voto{/mastercom_label}</br>
                                    <select name='nuovo_voto[tipo_voto]' id='tipo_nuovo_voto' class='select' disabled>
                                    </select>
                                </div>
                                <div class="padding_cella_generica">
                                    {mastercom_label}Voto{/mastercom_label}</br>
                                    <select name='nuovo_voto[voto]' id='nuovo_voto_assegnato' class='select' disabled>
                                    </select>
                                </div>
                                {if $gestione_pesi_voti == 'SI'}
                                    <div class="padding_cella_generica">
                                        {mastercom_label}Peso{/mastercom_label}</br>
                                        <select name='nuovo_voto[id_peso]' id='peso_nuovo_voto' class='select' disabled>
                                            {foreach $elenco_pesi_voti as $peso}
                                                <option value="{$peso['id_peso']}"
                                                    data-default="{$peso['peso_default']}"
                                                    {if $peso['peso_default'] == 't'}selected{/if}
                                                >{$peso['peso']}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                {/if}
                            </div>
                        </td>
                        <td align="center" class='padding_cella_generica'>
                                <div class="padding_cella_generica">
                                    {mastercom_label}Nota Didattica{/mastercom_label}</br>
                                    <select name='nuovo_voto[id_simbolo]' id='nuovo_simbolo_assegnato' class='select' disabled>
                                    </select>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align="center" class='padding_cella_generica bordo_destro_generico'>
                            <textarea name="nuovo_voto[note_voto]"
                                        id="note_nuovo_voto"
                                        rows="5"
                                        style="width: 95%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                        placeholder="{mastercom_label}Note del voto didattico..{/mastercom_label}"
                                        disabled
                                        ></textarea>
                        </td>
                        <td align="center" class='padding_cella_generica bordo_basso_generico'>
                            <textarea name="nuovo_voto[note_nota_didattica]"
                                        id="note_nuova_nota"
                                        rows="5"
                                        style="width: 95%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                        placeholder="{mastercom_label}Note della nota didattica..{/mastercom_label}"
                                        disabled
                                        ></textarea>
                        </td>
                    </tr>
                </table>
                {if $optional_competenze_professore == 'OK'}
                <button type="button"
                        style="margin-top: 10px; background-color: #bbbbff;"
                        id="btn_aggiungi_competenze_singolo_voto"
                        class="btn_flat ripples"
                         onclick="caricaCompetenzeStudenteSingoloVoto($('#id_studente_nuovo_voto').val(), $('#id_materia_nuovo_voto').val(), this.id, 'containerCompetenzeSingoloVoto', 'spazioNuoveValutazioniSingoloVoto', 'containerValutazioniSingoloVoto');"
                         >{mastercom_label}+ Competenze{/mastercom_label}</button>
                {/if}
                <div id="spazioNuoveValutazioniSingoloVoto" style="display: none;">
                    <div style="display: flex; margin-left: 10px;">
                        <fieldset style='-moz-border-radius:5px; -webkit-border-radius:5px; border-radius:5px; border: 0.5px solid #0077BE; width: fit-content; margin-left: 5px;'>
                        <legend style='color: #0077BE;'>{mastercom_label}Nuove Valutazioni Competenze{/mastercom_label}</legend>
                        <div style="max-height: 20vh; overflow: auto; -webkit-overflow-scrolling: touch;">
                            <table id="containerValutazioniSingoloVoto"></table>
                        </div>
                        </fieldset>
                    </div>
                </div>
                <div id="containerCompetenzeSingoloVoto"></div>
                <div class="padding_cella_generica">
                    <br>
                    <button type="button"
                           class="btn_flat_indaco"
                            onclick="apriChiudiPopup('nuovo_voto', 'sfondo_oscurato', true); chiudiSfondoOscurato();"
                            >{mastercom_label}Chiudi{/mastercom_label}</button>
                    <button type="button"
                            class="btn_pieno sfondo_scuro ripples"
                            onclick="inserisciVoto(this);"
                        >{mastercom_label}Inserisci{/mastercom_label}</button>
                </div>
            </div>
        </div>

        <div id="jqxLoaderSingoloVoto"></div>

        {*form modifica voto*}
        <div  class="div_scheda_generica"
                id="modifica_voto"
                style="position: fixed;
                      width: 50%;
                      min-width: 300px;
                      max-height: 95%;
                      left: 50%;
                      top: 50%;
                      transform: translate(-50%, -50%);
                      display: none;
                      z-index: 12;
                      overflow-y: auto;"
            >
            <div align="center" style="width: 100%; height: 100%;">
                <table width="95%" align="center">
                    <tr>
                        <td align="center" colspan="2" style="padding: 15px 10px 20px 10px; font-size: 120%;">
                            <b>{mastercom_label}MODIFICA VOTO{/mastercom_label}</b><br>
                        </td>
                    </tr>
                    <tr>
                        <td align="center" colspan="2">
                            <div width="100%" style="display: flex; align-items: center; justify-content: space-around; flex-wrap: wrap;">
                                <div class="padding_cella_generica">
                                    {mastercom_label}Studente{/mastercom_label}<br>
                                    <span id="studente_modifica_voto" style="font-weight: bold;"></span>
                                </div>
                                <div class="padding_cella_generica">
                                    {mastercom_label}Materia{/mastercom_label}</br>
                                    <span id="materia_modifica_voto" style="font-weight: bold;"></span>
                                </div>
                                <div class='padding_cella_generica'>
                                    {mastercom_label}Data{/mastercom_label}<br>
                                    <input type='date'
                                           name="modifica_voto[data]"
                                           id="data_modifica_voto"
                                           style="border: 0.5px solid #ccc; padding: 2px;">
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align="center" colspan="2" class='padding_cella_generica'>
                            <div width="100%" id="dati_voto_modifica_voto" style="display: flex; align-items: center; justify-content: space-around; flex-wrap: wrap;">
                                <div class="padding_cella_generica">
                                    {mastercom_label}Tipo voto{/mastercom_label}</br>
                                    <select name='modifica_voto[tipo_voto]' id='tipo_modifica_voto' class='select bold' disabled>
                                    </select>
                                </div>
                                <div class="padding_cella_generica">
                                    {mastercom_label}Voto{/mastercom_label}</br>
                                    <select name='modifica_voto[voto]' id='modifica_voto_assegnato' class='select bold' disabled>
                                    </select>
                                </div>
                                {if $gestione_pesi_voti == 'SI'}
                                    <div class="padding_cella_generica">
                                        {mastercom_label}Peso{/mastercom_label}</br>
                                        <select name='modifica_voto[id_peso]' id='peso_modifica_voto' class='select bold' disabled>
                                            {foreach $elenco_pesi_voti as $peso}
                                                <option value="{$peso['id_peso']}"
                                                    data-default="{$peso['peso_default']}"
                                                >{$peso['peso']}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                {/if}
                            </div>
                            <div class="padding_cella_generica" id="dati_annotazione_modifica_voto">
                                {mastercom_label}Nota Didattica{/mastercom_label}</br>
                                <select name='modifica_voto[id_simbolo]' id='modifica_simbolo_assegnato' class='select' disabled style="font-weight: bold;">
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align="center" colspan="2" class='padding_cella_generica'>
                            <textarea name="modifica_voto[note_voto]"
                                        id="note_voto_modifica_voto"
                                        rows="5"
                                        style="width: 95%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                        placeholder="{mastercom_label}Note del voto didattico..{/mastercom_label}"
                                        disabled
                                        ></textarea>
                            <textarea name="modifica_voto[note_nota_didattica]"
                                        id="note_annotazione_modifica_voto"
                                        rows="5"
                                        style="width: 95%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                        placeholder="{mastercom_label}Note della nota didattica..{/mastercom_label}"
                                        disabled
                                        ></textarea>
                        </td>
                    </tr>
                </table>
                <input type="hidden" name="modifica_voto[id_studente]" id="id_studente_modifica_voto" value="">
                <div id="competenze_modifica_voto"></div>
                <div class = "padding_cell_-generica" id="messaggio_modifica_voto_bloccato"></div>
                <div class="padding_cella_generica">
                    <br>
                    <button type="button"
                           class="btn_flat_indaco"
                            onclick="apriChiudiPopup('modifica_voto', 'sfondo_oscurato', true); chiudiSfondoOscurato();"
                        >{mastercom_label}Chiudi{/mastercom_label}</button>
                    <button type="button"
                            class="btn_pieno sfondo_scuro ripples"
                            id="btn_salva_modifica_voto"
                            onclick="
                                if (verificaFestivo($('#data_modifica_voto').val()) === false){
                                    document.getElementById('form_container').operazione.value = 'modifica_voto';
                                    document.getElementById('form_container').submit();
                                    this.disabled = true;
                                    $('#jqxGeneralLoader').jqxLoader('open');
                                } else {
                                    alert('{mastercom_label}Giorno festivo! Cambiare data{/mastercom_label}');
                                }"
                        >{mastercom_label}Salva{/mastercom_label}</button>
                </div>
            </div>
        </div>

        {*form inserimento multivoto*}
        <div  class="div_scheda_generica"
                id="multivoto"
                style="position: fixed;
                      width: 70%;
                      min-width: 300px;
                      max-height: 95%;
                      left: 50%;
                      top: 50%;
                      transform: translate(-50%, -50%);
                      display: none;
                      z-index: 12;
                      overflow-y: auto;"
            >
            <div align="center" style="width: 100%; height: 100%;">
                <div align="center" style="padding: 15px 10px 20px 10px; font-size: 120%;">
                    <b>{mastercom_label}INSERISCI MULTIVOTO{/mastercom_label}</b>
                </div>
                <div align="center" id="multivoto_content" style="margin: 10px; max-height: 75vh; overflow: auto; -webkit-overflow-scrolling: touch;">
                    {if $elenco_studenti|@count > 0}
                        <div width="100%" style="display: flex; align-items: center; justify-content: space-around; flex-wrap: wrap;">
                            <div class='padding_cella_generica'>
                                {mastercom_label}Data{/mastercom_label}<br>
                                <input type='date'
                                       name="multivoto[data]"
                                       id="data_multivoto"
                                       value='{$data_oggi}'
                                       style="border: 0.5px solid #ccc; padding: 2px;">
                            </div>
                            <div class="padding_cella_generica">
                                {mastercom_label}Materia{/mastercom_label}</br>
                                <select id='select_id_materia_multivoto'
                                        class='select'
                                        style='width: 250px;'
                                        onchange="$('#id_materia_multivoto').val(this.value);
                                                    caricaDatiVotoMateriaInserimentoMultivoto(this.value);
                                                    if (multi_note_didattiche === true){
                                                    caricaElencoNoteDidattiche(this.value);}"
                                        >
                                    <option value=''>-</option>
                                    {foreach $elenco_materie_classe_professore['professore'] as $materia}
                                        <option value='{$materia['id_materia']}'>
                                            {$materia['descrizione']}
                                        </option>
                                    {/foreach}
                                </select>
                                <input type="hidden" name="multivoto[id_materia]" id="id_materia_multivoto">
                            </div>
                            <button type="button"
                                style="margin-top: 10px; background-color: #90a4ae;"
                                id="btn_aggiungi_nota_didattica_multi_voto"
                                class="btn_flat testo_bianco ombra_testo ripples"
                                 onclick="caricaElencoNoteDidattiche($('#select_id_materia_multivoto').val(), this);"
                                 >{mastercom_label}+ Nota Didattica{/mastercom_label}</button>
                            {if $optional_competenze_professore == 'OK'}
                            <button type="button"
                                style="margin-top: 10px; background-color: #bbbbff;"
                                id="btn_aggiungi_competenze_multi_voto"
                                class="btn_flat ripples"
                                 onclick="caricaElencoTemplateClasseMultiVoto({$id_classe}, $('#id_materia_multivoto').val(), this.id, 'containerCompetenzeSingoloVoto', 'spazioNuoveValutazioniSingoloVoto', 'containerValutazioniSingoloVoto');"
                                 >{mastercom_label}+ Competenze{/mastercom_label}</button>
                            {/if}
                        </div>
                        <table width="98%" id="elenco_studenti_multivoto">
                            <tr data-riga="titoli">
                                <td align="center" class="padding_cella_generica"><b>{mastercom_label}Studente<{/mastercom_label}/b></td>
                                <td align="center" class="padding_cella_generica">
                                    <b>{mastercom_label}Voto{/mastercom_label}</b><br><select class="select" id="multi_tipo_voto" name="multivoto[tipo_voto]" style="width: 80px;">
                                    </select>
                                </td>
                                {if $gestione_pesi_voti == 'SI'}
                                    <td align="center" class="padding_cella_generica bold">
                                        {mastercom_label}Peso{/mastercom_label}<br>
                                        <select class="select multi_voto_pesi_select" id="multi_peso_voto" onchange="settaMultiPesi('multi_voto_pesi_select', this.value);" disabled>
                                            {foreach $elenco_pesi_voti as $peso}
                                                <option value="{$peso['id_peso']}"
                                                    data-default="{$peso['peso_default']}"
                                                >{$peso['peso']}</option>
                                            {/foreach}
                                        </select>
                                    </td>
                                {/if}
                            </tr>
                        {foreach $elenco_studenti as $studente}
                            <tr class="bordo_alto_generico evidenzia" data-riga="studente" data-id_studente="{$studente['id_studente']}" {if $studente['esonero_religione'] == '1'}data-studente-esonero="SI"{/if}>
                                <td style="width: max-content;">
                                    {$studente['cognome']} {$studente['nome']}{if $studente['necessita_sostegno'] == '1'}{$param_simbolo_studente_sostegno}{/if}</b>
                            {if $param_mostra_ammissione_registro == 'SI' && $studente['stampa_esito'] == 'SI'}<i>({$studente['esito_corrente_calcolato']})</i>{/if}
                                </td>
                                <td align="center">
                                    <select class="select multi_voto_select" name="multivoto[dati][{$studente['id_studente']}][voto]" disabled></select>
                                </td>
                                {if $gestione_pesi_voti == 'SI'}
                                    <td align="center">
                                        <select name='multivoto[dati][{$studente['id_studente']}][id_peso]' class='select multi_voto_pesi_select' disabled>
                                            {foreach $elenco_pesi_voti as $peso}
                                                <option value="{$peso['id_peso']}"
                                                    data-default="{$peso['peso_default']}"
                                                >{$peso['peso']}</option>
                                            {/foreach}
                                        </select>
                                    </td>
                                {/if}
                            </tr>
                        {/foreach}
                        </table>
                        <div align='center' style='margin-top: 10px;'>
                            <textarea id='multi_commento' name="multivoto[commento]" placeholder='{mastercom_label}Commento al voto didattico..{/mastercom_label}' rows='3' disabled style='width: 90%;'></textarea>
                        </div>
                        <div align='center' id="commenti_multi_competenze"></div>
                        <div align="left" id="containerCompetenzeMultivoto" style="display: none;" class="padding_cella_generica">
                            <h2>Competenze</h2>
                            <select id="select_elenco_template_studenti" class="select" onchange="caricaTemplateMultivoto(this.value);"></select>
                            <div id="competenzeMultivoto"></div>
                        </div>
                    {else}
                        {mastercom_label}Non ci sono studenti nella classe{/mastercom_label}
                    {/if}
                </div>
                <div class="padding_cella_generica">
                    <br>
                    <button type="button"
                           class="btn_flat_indaco"
                            onclick="apriChiudiPopup('multivoto', 'sfondo_oscurato', true); chiudiSfondoOscurato(); caricaDatiVotoMateriaInserimentoMultivoto('');"
                        >{mastercom_label}Chiudi{/mastercom_label}</button>
                    <button type="button"
                            class="btn_pieno sfondo_scuro ripples"
                            onclick="inserisciMultiVoto(this);"
                        >{mastercom_label}Inserisci{/mastercom_label}</button>
                </div>
            </div>
        </div>

        {*form nuova competenza*}
        <div  class="div_scheda_generica"
                id="nuova_competenza"
                style="position: fixed;
                      width:{if $is_mobile == 1}95%{else}70%{/if};
                      min-width: 300px;
                      max-height: 95%;
                      left: 50%;
                      top: 50%;
                      transform: translate(-50%, -50%);
                      display: none;
                      z-index: 12;
                      overflow-y: auto;"
            >
            <div align="center" style="width: 100%; height: 100%;">
                <div align="center" style="padding: 15px 10px 20px 10px; font-size: 120%;">
                    <b>{mastercom_label}OBIETTIVI/COMPETENZE{/mastercom_label}</b><br>
                </div>
                <div align="center" class="padding_cella_generica">
                    {mastercom_label}Studente{/mastercom_label}
                    <select class="select" id="selectStudenteCompetenze" onchange="caricaCompetenzeStudente(this.value);">
                        <option value="0">---</option>
                        {foreach $elenco_studenti as $studente}
                            <option value="{$studente['id_studente']}">{$studente['cognome']} {$studente['nome']}{if $studente['necessita_sostegno'] == '1'}{$param_simbolo_studente_sostegno}{/if}
                                {if $param_mostra_ammissione_registro == 'SI' && $studente['stampa_esito'] == 'SI'}<i>({$studente['esito_corrente_calcolato']})</i>{/if}
                            </option>
                        {/foreach}
                    </select>
                </div>
                <div style="margin-top: 10px;">
                    <div id="containerCompetenze"></div>
                </div>
                <div class="padding_cella_generica">
                    <br>
                    <button type="button"
                           class="btn_flat_indaco"
                            onclick="apriChiudiPopup('nuova_competenza', 'sfondo_oscurato', true); chiudiSfondoOscurato();"
                            >{mastercom_label}Chiudi{/mastercom_label}</button>
                </div>
            </div>
        </div>

    <div id="jqxLoader"></div>

    <input type='hidden' name='form_stato' value='{$form_stato}'>
    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
    <input type='hidden' name='stato_secondario' id='stato_secondario' value='{$stato_secondario}'>
    <input type='hidden' name='operazione' value=''>
    <input type='hidden' name='id_studente' id='id_studente' value='{$id_studente}'>
    <input type='hidden' name='id_voto' id='id_voto' value=''>
    <input type='hidden' name='id_voto_modifica' id='id_voto_modifica' value=''>
    <input type='hidden' name='id_annotazione_modifica' id='id_annotazione_modifica' value=''>
    <input type='hidden' name='current_user' id='current_user' value='{$current_user}'>
    <input type='hidden' name='current_key' id='current_key' value='{$current_key}'>
    <input type='hidden' id='db_key' value='{$db_key}'>
    <input type='hidden' id='tipo_utente' value='{$form_stato}'>
</form>