<?php
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'ee_12345_generica_gajo_mi_01', 'Pagella Scuola Primaria', 1, 'gajo mi', 1);

UPDATE parametri set valore = 'SI' where nome ='ABILITA_STAMPE_PERSONALIZZATE';
 */


$orientamento = 'P';


switch ($periodo)
{
    case 1:
        $periodo_pagella = 'pagellina1';
        $periodo_desc = 'METÀ PRIMO PERIODO';
        $formato = 'A4';
        break;
    case 2:
        $periodo_pagella = 'pagellina2';
        $periodo_desc = 'METÀ SECONDO PERIODO';
        $formato = 'A4';
        break;
    case 7:
        $periodo_pagella = 'intermedia';
        $periodo_desc = 'PRIMO QUADRIMESTRE';
        $formato = 'A4';
        break;
    case 9:
        $periodo_pagella = 'finale';
        $periodo_desc = 'FINE ANNO';
        $formato = $formato_pagina_stampa;
        break;
}

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    $id_studente = $studente['id_studente'];
    $as_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $id_classe = $parametri_stampa['id_classe'];
    $periodo = $parametri_stampa['periodo'];
    $data_p1 = "{$parametri_stampa['data_p1_day']}/{$parametri_stampa['data_p1_month']}/{$parametri_stampa['data_p1_year']}";
    $data_p2 = "{$parametri_stampa['data_p2_day']}/{$parametri_stampa['data_p2_month']}/{$parametri_stampa['data_p2_year']}";
    $formato = $parametri_stampa['formato'];
    $periodo_desc = $parametri_stampa['periodo_desc'];

    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $comune_nascita = $studente['citta_nascita_straniera'];
        $provincia_nascita = '';
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $provincia_nascita = $stato['descrizione'];
        }
    }
    else
    {
        $comune_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }
    $luogo_nascita = "$comune_nascita ($provincia_nascita)";

    $dati_classe = estrai_classe((int) $id_classe);
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);

    // Estrazione voti
    $voti_p1 = estrai_voti_pagellina_studente_multi_classe($id_classe, 7, $id_studente);
    $voti_p2 = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $id_studente);
    $arr_voti = $religione = $condotta = [];
    $giudizi_gen = '';

    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            && (
                    !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'RELIGIONE', 'SOSTEGNO', 'CONDOTTA'])
                    ||
                    (
                    $studente['esonero_religione'] == 0 && $materia['tipo_materia'] == 'RELIGIONE'
                    ||
                    $studente['esonero_religione'] == 1 && $materia['tipo_materia'] == 'ALTERNATIVA'
                     )
                )
            )
        {
            $arr_voti[$id_materia]['p1'] = $voti_p1[$id_materia];
            $arr_voti[$id_materia]['p2'] = $voti_p2[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV'
            &&
                (
                $studente['esonero_religione'] == 0 && $materia['tipo_materia'] == 'RELIGIONE'
                ||
                $studente['esonero_religione'] == 1 && $materia['tipo_materia'] == 'ALTERNATIVA'
                 )
        )
        {
            $religione[$id_materia]['p1'] = $voti_p1[$id_materia];
            $religione[$id_materia]['p2'] = $voti_p2[$id_materia];
        }

        // Giudizio globale
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {
            $condotta[$id_materia]['p1'] = $voti_p1[$id_materia];
            $condotta[$id_materia]['p2'] = $voti_p2[$id_materia];

            foreach ($voti_p1[$id_materia]['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($campo_libero['id_padre'] == '') {
                        if ($value !== '') {
                            $giudizi_gen['p1'] .= "$value \n";
                        }
                    }
                }
            }
            foreach ($voti_p2[$id_materia]['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($campo_libero['id_padre'] == '') {
                        if ($value !== '') {
                            $giudizi_gen['p2'] .= "$value \n";
                        }
                    }
                }
            }
        }
    }

    if ($periodo != 9) {$arr_voti += $religione;}
    if ($periodo == 9) {$arr_voti += $condotta;}

    $labels = [
        "intestazione"              => "Ministero dell'Istruzione",

        "titolo_1"                  => "VALUTAZIONE PRIMO QUADRIMESTRE",

        "classe"                    => "Classe:",
        "sezione"                   => "Sezione:",
        "alunno"               => "Alunn||min_oa||:",
        "alunno_max"               => "ALUNN||max_oa||:",
        "nascita_data"              => "Data Nascita:",

        "disciplina"                => "DISCIPLINA",
        "valutazione"               => "VALUTAZIONE",

        "valutazione_intermedia"    => "VALUTAZIONE INTERMEDIA",

        "firma_dirigente"           => "La coordinatrice delle attività didattico-educative",
        "firma_docenti"             => "",

        "legenda"                   => "Avanzato: l'alunno porta a termine compiti in situazioni note e non note, mobilitando una varietà di risorse sia fornite dal docente.
sia reperite altrove, in modo autonomo e con continuità.\n
Intermedio: l'alunno porta a termine compiti in situazioni note in modo autonomo e continuo; risolve compiti in situazioni non note,
utilizzando le risorse fornite dal docente o reperite altrove, anche se in modo discontinuo e non del tutto autonomo.\n
Base: l'alunno porta a termine compiti solo in situazioni note e utilizzando le risorse fomite dal docente, sia in modo autonomo ma
discontinuo, sia in modo non autonomo, ma con continuità.\n
In via di prima acquisizione: l'alunno porta a termine compiti solo in situazioni note e unicamente con il supporto del docente e di
risorse fornite appositamente.",
        "legenda_p"  => '<table cellpadding="3" border="0.1px">
            <tr align="center">
                <td width="13%"><b>Giudizio sintetico</b></td>
                <td width="87%"><b>Descrizione</b></td>
            </tr>
            <tr>
                <td align="center"><b>Ottimo</b></td>
                <td>L’alunno svolge e porta a termine le attività con autonomia e consapevolezza, riuscendo ad affrontare anche situazioni complesse e non proposte in precedenza.<br>
È in grado di utilizzare conoscenze, abilità e competenze per svolgere con continuità compiti e risolvere problemi, anche difficili, in modo originale e personale.<br>
Si esprime correttamente, con particolare proprietà di linguaggio, capacità critica e di argomentazione, in modalità adeguate al contesto.</td>
            </tr>
            <tr>
                <td align="center"><b>Distinto</b></td>
                <td>L’alunno svolge e porta a termine le attività con autonomia e consapevolezza, riuscendo ad affrontare anche situazioni complesse.<br>
È in grado di utilizzare conoscenze, abilità e competenze per svolgere con continuità compiti e risolvere problemi anche difficili.<br>
Si esprime correttamente, con proprietà di linguaggio e capacità di argomentazione, in modalità adeguate al contesto.</td>
            </tr>
            <tr>
                <td align="center"><b>Buono</b></td>
                <td>L’alunno svolge e porta a termine le attività con autonomia e consapevolezza.<br>
È in grado di utilizzare conoscenze, abilità e competenze per svolgere con continuità compiti e risolvere problemi.<br>
Si esprime correttamente, collegando le principali informazioni e usando un linguaggio adeguato al contesto.</td>
            </tr>
            <tr>
                <td align="center"><b>Discreto</b></td>
                <td>L’alunno svolge e porta a termine le attività con parziale autonomia e consapevolezza.<br>
È in grado di utilizzare alcune conoscenze, abilità e competenze per svolgere compiti e risolvere problemi non particolarmente complessi.<br>
Si esprime correttamente, con un lessico semplice e adeguato al contesto.</td>
            </tr>
            <tr>
                <td align="center"><b>Sufficiente</b></td>
                <td>L’alunno svolge le attività principalmente sotto la guida e con il supporto del docente.<br>
È in grado di applicare alcune conoscenze e abilità per svolgere semplici compiti e problemi, solo se già affrontati in precedenza.<br>
Si esprime con un lessico limitato e con qualche incertezza.</td>
            </tr>
            <tr>
                <td align="center"><b>Non sufficiente</b></td>
                <td>L’alunno non riesce abitualmente a svolgere le attività proposte, anche se guidato dal docente.<br>
Applica solo saltuariamente conoscenze e abilità per svolgere alcuni semplici compiti.<br>
Si esprime con incertezza e in maniera non adeguata al contesto.</td>
            </tr>
        </table>',

        ####
        "p4_tit"        => "RILEVAZIONE DEI PROGRESSI NELL'APPRENDIMENTO E NELLO SVILUPPO PERSONALE<br>E SOCIALE DELL'ALUNN||max_oa||",
        "attestato_valutazione"     => "Visti gli atti d'ufficio e la valutazione dei docenti della classe, si attesta che<br>l'alunn||min_oa|| è stat||min_oa|| ",

        "p4_note_sub" =>
        "(1) La firma può essere omessa ai sensi dell'art. 3, D.to Lgs. 12.02.1993, n. 39.<br>"
        . "(2) Specifica nota illustrativa di cui all'art. 2, comma 8, del D.P.R. n. 122/2009.<br>"
        . "(3) \"ammesso/a (ovvero non ammesso/a) alla classe successiva\" oppure \"ammesso/a (ovvero non ammesso/a) al successivo grado dell'istruzione obbligatoria\"<br>"
        . "(4) art. 3, OM 4 Dicembre 2020, n 172.",
        "iscritto_classe"           => "Iscritt||min_oa|| alla classe {$studente['classe']}",

    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }
    //}}} </editor-fold>

    $f = 'Times';
    if ($periodo != 9) {
    $pdf->AddPage('P');
    $pdf->SetAutoPageBreak('off', 1);
    inserisci_intestazione_pdf($pdf, (int) $id_classe);
    $pdf->CellFitScale(0, 5, '', 'T', 1, 'R');

    $pdf->SetY(55);
	$pdf->SetFont($f, 'B', 20);
    $pdf->CellFitScale(0, 7, "DOCUMENTO DI VALUTAZIONE", 0, 1, 'C');
	$pdf->SetFont($f, '', 16);
    $pdf->CellFitScale(0, 0, "PER L'ANNO SCOLASTICO $as_attuale", 0, 1, 'C');
    $pdf->CellFitScale(0, 0, $periodo_desc, 0, 1, 'C');
    $pdf->ln(4);

	$pdf->SetFont($f, '', 8);
	$pdf->Cell(95, 3, 'Cognome', 'TL', 0, 'L');
	$pdf->Cell(95, 3, 'Nome', 'TR', 1, 'L');
	$pdf->SetFont($f, 'B', 12);
	$pdf->CellFitScale(95, 6, $studente['cognome'], 'BL', 0, 'L');
	$pdf->CellFitScale(95, 6, $studente['nome'], 'BR', 1, 'L');

    $pdf->SetFont($f, '', 8);
    $pdf->Cell(95,3, 'Nato a', 'TL', 0, 'L');
    $pdf->Cell(95,3, 'Il', 'TR', 1, 'L');
    $pdf->SetFont($f, 'B', 12);
    $pdf->CellFitScale(95, 6, $luogo_nascita, 'BL', 0, 'L');
    $pdf->CellFitScale(95, 6,date('d/m/Y', $studente['data_nascita']), 'BR', 1, 'L');

    $pdf->SetFont($f, '', 8);
    $pdf->Cell(30,3, 'Classe', 'TL', 0, 'L');
    $pdf->Cell(30,3, 'Sezione', 'T', 0, 'L');
    $pdf->Cell(65,3, 'Corso', 'T', 0, 'L');
    $pdf->Cell(65,3, '', 'TR', 1, 'L');
    $pdf->SetFont($f, 'B', 10);
    $pdf->CellFitScale(30, 6, $dati_classe[2], 'BL', 0, 'L');
    $pdf->CellFitScale(30, 6, $dati_classe[3], 'B', 0, 'L');
    $pdf->CellFitScale(65, 6, $dati_classe[5], 'B', 0, 'L');
    $pdf->CellFitScale(65, 6, 'Anno Scolastico ' . $as_attuale, 'BR', 1, 'L');

    //{{{ <editor-fold defaultstate="collapsed" desc="Tabella VOTI">
    $pdf->ln(4);
	$pdf->SetFont($f, 'B', 12);
    $pdf->CellFitScale(0, 0, "VALUTAZIONI PERIODICHE DEGLI APPRENDIMENTI NELLE DISCIPLINE E DEL COMPORTAMENTO", 0, 1, 'C');
    $pdf->ln(2);
	$pdf->SetFont($f, '', 8);

//    T => 0
//    R => 1.000125
//    B => 0
//    L => 1.000125
//    $pdf->setCellPaddings( 1, 0.5, 1, 0.5 );

    $page_width = $pdf->getPageWidth()-10-10;
    foreach ($arr_voti as $vt)
    {
        $voto = $vt['p1'];
        $pdf->SetFont($f, '', 8);

        $desc = $giudizio_mt = $voto_stampa = '';
        $cmp_st = [];
        $alt_mt_clstampa= 0;

        // voto
        $desc = $voto['descrizione'];
        foreach ($voto['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $voto['voto_pagellina'])
            {
                $voto_stampa = decode($significato['valore']);
            }
        }

        // raccolta dei campi liberi da stampare
//        if ($voto['tipo_materia'] != 'RELIGIONE')
//        {
            foreach ($voto['campi_liberi'] as $campo_libero) {
                if (in_array($voto['id_materia'], $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '')
                    {
                        if ($campo_libero['nome'] == 'GIUDIZIO DESCRITTIVO') {
                            $giudizio_mt .= $value;
                            $alt_mt_clstampa += $pdf->getStringHeight($page_width, $giudizio_mt,false,true,'',1);
                        }
                        elseif (strtoupper($campo_libero['nome']) != 'DESCRITTORE' &&
                                ( strpos($campo_libero['nome'], 'OBIETTIVI') === false )) {
                            $alt_cella = max($pdf->getStringHeight($page_width*0.85, $campo_libero['nome'],false,true,'',1),
                                    $pdf->getStringHeight($page_width*0.15, $value,false,true,'',1));
                            $alt_mt_clstampa += $alt_cella;
                            $cmp_st[] = [$campo_libero['nome'], $value, $alt_cella];
                        }
                    }
                }
            }
//        }

        if ($pdf->GetY()+12+$alt_mt_clstampa > 293) {
            $pdf->AddPage();
        }

        if ($voto['tipo_materia'] != 'RELIGIONE')
        {
            $pdf->SetFont($f, 'B', 8);
            $pdf->CellFitScale($page_width*0.85, 6, $desc, 1, 0, 'L');
            $pdf->CellFitScale(0, 6, 'Livello', 1, 1, 'C');
            $pdf->SetFont($f, '', 8);

            foreach ($cmp_st as $cmp_stampa) {
                $pdf->MultiCell( $page_width*0.85, $cmp_stampa[2], $cmp_stampa[0], 1, 'L', false, 0, '', '', true, 1, true, true, $cmp_stampa[2], 'M', true);
                $pdf->MultiCell( 0, $cmp_stampa[2], $cmp_stampa[1], 1, 'C', false, 1, '', '', true, 1, true, true, $cmp_stampa[2], 'M', true);
            }

            if ($giudizio_mt != '') {
                $pdf->MultiCell( $page_width, 0, $giudizio_mt, 1, 'L', false, 1, '', '', true, 1, false, true, 0, 'T', true);
            }
        }
        else
        {
            $pdf->SetFont($f, 'B', 8);
            $pdf->CellFitScale($page_width*0.75, 8, $desc, 1, 0, 'L');
            $pdf->CellFitScale(0, 8, $voto_stampa, 1, 1, 'C');
            $pdf->SetFont($f, '', 8);
        }
    }
	$pdf->SetFont($f, '', 8);

    // condotta
    $pdf->ln(4);
    foreach ($condotta as $cond_vt) {
        $cond_voto = $cond_vt['p1'];
        $vc = '';
        foreach ($cond_voto['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $cond_voto['voto_pagellina'])
            {
                $vc = decode($significato['valore']);
            }
        }
        $pdf->SetFont($f, 'B', 8);
        $pdf->CellFitScale($page_width*0.75, 8, $cond_voto['descrizione'], 1, 0, 'L');
        $pdf->CellFitScale(0, 8, $vc, 1, 1, 'C');
    }
    $pdf->SetFont($f, '', 8);
    //}}} </editor-fold>

    $pdf->AddPage();
    $pdf->ln(6);

    $pdf->SetFont('Times', 'B', 10);
    $pdf->CellFitScale(0, 0, 'GIUDIZIO ANALITICO SUL LIVELLO GLOBALE DI MATURAZIONE', 0, 1, 'C', 0);
    $pdf->CellFitScale(0, 0, "RAGGIUNTO DALL'".$labels['alunno_max'], 0, 1, 'C', 0);
    $pdf->ln(7);

    $pdf->SetFont('Times', 'B', 10);
    $pdf->CellFitScale(0, 10, "VALUTAZIONE $periodo_desc", 1, 1, 'C');

    $pdf->SetFont('Times', '', 10);
    $pdf->MultiCell(0, 40, $giudizi_gen['p1'], 1, 'L', false, 1, "", "", true, 0 );

//    $pdf->ln(8);
//    if ($giudizi_gen['p2'] != '') {
//        $pdf->SetFont('Times', 'B', 10);
//        $pdf->CellFitScale(0, 10, 'VALUTAZIONE FINALE', 1, 1, 'C');
//
//        $pdf->SetFont('Times', '', 10);
//        $pdf->MultiCell(0, 40, $giudizi_gen['p2'], 1, 'L', false, 1, "", "", true, 0 );
//    }

    $pdf->ln(12);
    $firme = '<table>
            <tr>
                <td colspan="3">'.$studente['descrizione_comuni'] . ", lì " . $data_p1 .'</td>
            </tr>
            <tr align="center">
                <td width="40%"><br><br><br></td>
                <td width="20%"></td>
                <td width="40%"><b>IL DIRIGENTE SCOLASTICO<br><i>'.$studente['nome_dirigente'].'</i></b><br><small>Firma autografa sostituita a mezzo stampa<br>
ai sensi dell’art. 3. Comma 2, del D.Lgs. n. 39 del 1993</small></td>
            </tr>
        </table>';
    $pdf->writeHTMLCell(0, 0, '', '', $firme, 0, 1);

    $pdf->SetFont('Times', '', 8);
    $pdf->writeHTMLCell(0, 0, '', 230, $labels['legenda_p'], 0, 1);
    }
    else // pagella finale (A3 o A4)
    {
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf">
        if ($formato == 'A3') {
            $pdf->AddPage('L', 'A3');
            $page_width = $pdf->getPageWidth();
            $page_dims = $pdf->getPageDimensions();
            $m_sinistro = $page_dims['lm'];
            $m_top = $page_dims['tm'];
            $m_destro = $page_dims['rm'];
            $half_page = $page_width / 2;
            $wp = $half_page - $m_destro - $m_sinistro;
            $x2 = $half_page + $m_sinistro;
        } else {
            $pdf->AddPage('P');
            $page_width = $pdf->getPageWidth();
            $page_dims = $pdf->getPageDimensions();
            $m_sinistro = $page_dims['lm'];
            $m_top = $page_dims['tm'];
            $m_destro = $page_dims['rm'];
            $half_page = $page_width;
            $wp = $half_page - $m_destro - $m_sinistro;
            $x2 = $m_sinistro;
        }
        $pdf->SetAutoPageBreak("off", 1);
        $fp = '<table>'
                . '<tr >'
                    . '<td align="left"   width="50%">' ."{$studente['descrizione_comuni']}, $data_p2". '</td>'
                    . '<td width="30%">' . '</td>'
                    . '<td align="center" width="20%">Il Dirigente Scolastico (1)<br>'.$studente['nome_dirigente'].'</td>'
                . '</tr>'
            . '</table>';

        //{{{ <editor-fold defaultstate="collapsed" desc="######   PAGINA 4   ######">
        if ($formato == 'A3') {

        $pdf->SetFont($f, 'B', 9);
        $pdf->CellFitScale($wp*0.24, 0, $studente['cognome'], 'TL', 0, 'C');
        $pdf->CellFitScale($wp*0.24, 0, $studente['nome'] , 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, $studente['codice_fiscale'], 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, $studente['codice_meccanografico'], 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.12, 0, $as_attuale, 'TR', 1, 'C');
        $pdf->SetFont($f, '', 9);
        $pdf->CellFitScale($wp*0.24, 0, 'COGNOME', 'BL', 0, 'C');
        $pdf->CellFitScale($wp*0.24, 0, 'NOME', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, 'CODICE FISCALE', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, 'CODICE ISTITUTO', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.12, 0, 'ANNO SCOLASTICO', 'RB', 1, 'C');

        $pdf->ln(3);
        $pdf->SetFont($f, 'B', 10);
        $pdf->writeHTMLCell($wp, 0, '', '', $labels['p4_tit'], 0, 1, false, true, 'C');
        $pdf->ln(3);
        $pdf->SetFont($f, '', 10);
        $pdf->writeHTMLCell($wp, 0, '', '', "VALUTAZIONE INTERMEDIA", 0, 1, false, true, 'C');
        $pdf->SetFont($f, '', 9);
        $pdf->ln(1);
        $pdf->writeHTMLCell($wp, 35, '', '', $giudizi_gen['p1'], 1, 1, false, true, 'L');
        $firme_p1 =
            '<table>'
                . '<tr>'
                    . '<td>' ."{$studente['descrizione_comuni']}, $data_p1<br><br>". '</td>'
                . '</tr>'

                . '<tr >'
                    . '<td align="left"   width="50%">___________________________________<br>Il (i) genitore (i) o chi ne fa le veci</td>'
                    . '<td width="30%">' . '</td>'
                    . '<td align="center" width="20%"><br>Il Dirigente Scolastico (1)<br>'.$studente['nome_dirigente'].'</td>'
                . '</tr>'
            . '</table>';
        $pdf->ln(2);
        $pdf->writeHTMLCell($wp, 0, '', '', $firme_p1, 0, 1, false, true, 'L');
        $pdf->ln(5);
        $pdf->SetFont($f, '', 10);
        $pdf->writeHTMLCell($wp, 0, '', '', "VALUTAZIONE FINALE", 0, 1, false, true, 'C');
        $pdf->SetFont($f, '', 9);
        $pdf->ln(1);
        $pdf->writeHTMLCell($wp, 35, '', '', $giudizi_gen['p2'], 1, 1, false, true, 'L');
        $firme_p2 =
            '<table>'
                . '<tr>'
                    . '<td>' ."{$studente['descrizione_comuni']}, $data_p2<br><br>". '</td>'
                . '</tr>'

                . '<tr >'
                    . '<td align="left"   width="50%">___________________________________<br>Il (i) genitore (i) o chi ne fa le veci</td>'
                    . '<td width="30%">' . '</td>'
                    . '<td align="center" width="20%"><br>Il Dirigente Scolastico (1)<br>'.$studente['nome_dirigente'].'</td>'
                . '</tr>'
            . '</table>';
        $pdf->ln(2);
        $pdf->writeHTMLCell($wp, 0, '', '', $firme_p2, 0, 1, false, true, 'L');
        $pdf->ln(5);
        $pdf->SetFont($f, 'B', 10);
        $pdf->CellFitScale($wp, 10, "ATTESTAZIONE", 0, 1, 'C');
        $pdf->SetFont($f, '', 9);
        $pdf->writeHTMLCell($wp, 0, '', '', $labels['attestato_valutazione'].$studente['esito_corrente_calcolato'].'<sup>(3)</sup>', 0, 1, false, true, 'C');
        $pdf->ln(2);
        $pdf->writeHTMLCell($wp, 0, '', '', $fp, 0, 1, false, true, 'L');
        $pdf->SetFont($f, '', 7);
        $pdf->writeHTMLCell($wp, 0, '', 205, $labels['legenda_p'], 0, 1);
        $pdf->ln(3);
        $pdf->SetFont($f, '', 7);
        $pdf->writeHTMLCell($wp, 0, '', '', $labels['p4_note_sub'], 0, 1);
        }
        //}}} </editor-fold>

        //{{{ <editor-fold defaultstate="collapsed" desc="######   PAGINA 1   ######">
        $pdf->Image('immagini_scuola/logo_repubblica.jpg', $x2 + ($wp/2) - 10, $m_top+15, 25, 25, 'JPG', false);
        $pdf->setY($m_top+34);
        $pdf->Ln(10);
        $pdf->SetFont($f, 'B', 17);
        $pdf->SetX($x2);
        $pdf->MultiCell($wp, 0, "Ministero dell'Istruzione", 0, 'C');
        $pdf->Ln(15);
        $pdf->SetX($x2);
        $pdf->SetFont($f, 'B', 10);
        $pdf->MultiCell($wp*0.35, 14, "Istituzione\nscolastica", 1, 'C', false, 0, '', '', true, 0, false, true, 14, 'M');
        $pdf->SetFont($f, '', 10);
        $pdf->MultiCell($wp*0.65, 14, "SCUOLA ELEMENTARE\n{$studente['descrizione_comuni']}, {$studente['provincia_comuni']}", 1, 'C', false, 1, '', '', true, 0, false, true, 14, 'M');
        $pdf->Ln(5);
        $pdf->SetX($x2);
        $pdf->SetFont($f, 'B', 10);
        $pdf->MultiCell($wp*0.35, 24, "Scuola Primaria\nParitaria", 1, 'C', false, 0, '', '', true, 0, false, true, 24, 'M');
        $pdf->SetFont($f, '', 10);
        $pdf->MultiCell($wp*0.65, 24, "\n"
                . "Sede principale\n"
                . "{$studente['codice_meccanografico']}\n"
                . "\n"
                . "{$studente['descrizione_comuni']}, {$studente['cap_comuni']}, {$studente['provincia_comuni']}", 1, 'C', false, 1, '', '', true, 0, false, true, 24, 'M');
        $pdf->ln(12);
        $pdf->SetFont($f, 'B', 22);
        $pdf->SetX($x2);
        $pdf->MultiCell($wp, 0, "Documento di valutazione\nAnno scolastico $as_attuale", 1, 'C', false, 1, '', '', true, 0, false, true, 0, 'M');
        $pdf->ln(10);
        $altc = 5;
        $pdf->SetX($x2);
        $pdf->SetFont($f, 'B', 14);
        $pdf->CellFitScale($wp, $altc, 'Dati anagrafici dello studente', 'TRL', 1, 'C');
        $pdf->SetX($x2);
        $pdf->CellFitScale($wp, 0, '', 'RL', 1, 'C');
        $pdf->SetFont($f, '', 8);

        $pdf->SetX($x2);
        $pdf->SetFont($f, 'B', 10);
        $pdf->CellFitScale($wp/3, $altc, $studente['cognome'], 'L', 0, 'C');
        $pdf->CellFitScale($wp/3, $altc, $studente['nome'], 0, 0, 'C',FALSE,'',0, FALSE,'T', 'B');
        $pdf->CellFitScale($wp/3, $altc, $studente['codice_fiscale'], 'R', 1, 'C');

        $pdf->SetX($x2);
        $pdf->SetFont($f, '', 8);
        $pdf->CellFitScale($wp/3, $altc+3, 'COGNOME', 'L', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
        $pdf->CellFitScale($wp/3, $altc+3, 'NOME', 0, 0, 'C',FALSE,'',0, FALSE,'T', 'T');
        $pdf->CellFitScale($wp/3, $altc+3, 'CODICE FISCALE', 'R', 1, 'C',FALSE,'',0, FALSE,'T', 'T');

        $pdf->SetFont($f, 'B', 10);
        $pdf->SetX($x2);
        $pdf->CellFitScale($wp, 3, '', 'RL', 1, 'C');
        $pdf->SetX($x2);
        $pdf->CellFitScale($wp/3, $altc, $studente['data_nascita_ext'], 'L', 0, 'C');
        $pdf->CellFitScale($wp/3, $altc, $comune_nascita, 0, 0, 'C');
        $pdf->CellFitScale($wp/3, $altc, $provincia_nascita, 'R', 1, 'C');

        $pdf->SetX($x2);
        $pdf->SetFont($f, '', 8);
        $pdf->CellFitScale($wp/3, $altc+3, 'DATA DI NASCITA', 'LB', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
        $pdf->CellFitScale($wp/3, $altc+3, 'COMUNE DI NASCITA', 'B', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
        $pdf->CellFitScale($wp/3, $altc+3, 'PROV .O STATO ESTERO', 'RB', 1, 'C',FALSE,'',0, FALSE,'T', 'T');
        $pdf->ln(10);

        $pdf->SetX($x2);
        $pdf->SetFont($f, 'B', 10);
        $pdf->CellFitScale(100, 8, $labels['iscritto_classe'], 'TLB', 0, 'L');
        $pdf->CellFitScale(0, 8, 'Sezione '.$studente['sezione'], 'TRB', 1, 'L');

        $pdf->ln(7);
        $pdf->SetFont($f, '', 8);
        $pdf->SetX($x2);
        $pdf->writeHTMLCell($wp, 0, '', '', $fp, 0, 1, false, true, 'L');
        //}}} </editor-fold>

        //{{{ <editor-fold defaultstate="collapsed" desc="######   PAGINA 2 - 3  ######">
        if ($formato == 'A3') { $pdf->AddPage('L', 'A3'); } else { $pdf->AddPage('P'); }
        $pdf->SetFont($f, 'B', 9);
        $pdf->CellFitScale($wp*0.24, 0, $studente['cognome'], 'TL', 0, 'C');
        $pdf->CellFitScale($wp*0.24, 0, $studente['nome'] , 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, $studente['codice_fiscale'], 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, $studente['codice_meccanografico'], 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.12, 0, $as_attuale, 'TR', 0, 'C');
        $pdf->SetX($x2);
        $pdf->CellFitScale($wp*0.24, 0, $studente['cognome'], 'TL', 0, 'C');
        $pdf->CellFitScale($wp*0.24, 0, $studente['nome'] , 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, $studente['codice_fiscale'], 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, $studente['codice_meccanografico'], 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.12, 0, $as_attuale, 'TR', 1, 'C');
        $pdf->SetFont($f, '', 9);
        $pdf->CellFitScale($wp*0.24, 0, 'COGNOME', 'BL', 0, 'C');
        $pdf->CellFitScale($wp*0.24, 0, 'NOME', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, 'CODICE FISCALE', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, 'CODICE ISTITUTO', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.12, 0, 'ANNO SCOLASTICO', 'RB', 0, 'C');
        $pdf->SetX($x2);
        $pdf->CellFitScale($wp*0.24, 0, 'COGNOME', 'BL', 0, 'C');
        $pdf->CellFitScale($wp*0.24, 0, 'NOME', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, 'CODICE FISCALE', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, 'CODICE ISTITUTO', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.12, 0, 'ANNO SCOLASTICO', 'RB', 1, 'C');

        $pdf->ln(4);
        $pdf->SetFont($f, 'B', 12);
        $pdf->CellFitScale($wp, 0, 'VALUTAZIONI PERIODICHE', 1, 0, 'C');
        $pdf->SetX($x2);
        $pdf->CellFitScale($wp, 0, 'VALUTAZIONI PERIODICHE', 1, 1, 'C');
        $y_tab = $pdf->GetY();
        $new_pg = false;
        $pdf->SetFont($f, '', 10);
        foreach ($arr_voti as $id_materia => $voto)
        {
            $pdf->SetFont($f, '', 10);
            $vp1 = $voto['p1'];
            $vp2 = $voto['p2'];

            $cmp_st = $giudizi = [];
            $alt_mat = 0; $val_finale = '';
            foreach ($vp1['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($value !== '')
                    {
                        if(stripos($campo_libero['nome'], 'GIUDIZIO DESCRITTIVO') !== false) {
                            $giudizi['p1'] ="GIUDIZIO DESCRITTIVO 1° Quadrimestre: $value";
                        } else {
                            $id_cmp = $campo_libero['id_campo_libero'];
                            $cmp_st[$id_cmp]['desc'] = $campo_libero['nome'];
                            $cmp_st[$id_cmp]['p1'] = $value;
                            $cmp_st[$id_cmp]['alt'] = $pdf->getStringHeight( $wp*0.70, $cmp_st[$id_cmp]['desc'], false, true, '', 0 );
                            $alt_mat += $cmp_st[$id_cmp]['alt'];
                        }
                    }
                }
            }
            foreach ($vp2['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($value !== '')
                    {
                        if(stripos($campo_libero['nome'], 'GIUDIZIO DESCRITTIVO') !== false) {
                            $giudizi['p2'] = "GIUDIZIO DESCRITTIVO Fine Anno: $value";
                        } else {
                            $val_finale = $value;
                        }
                    }
                }
            }

            if ($pdf->GetY() + $alt_mat + 15 > 275) {
                if ($formato == 'A4') { 
                    $pdf->SetY(280);
                    $pdf->SetFont($f, '', 8);
                    $pdf->writeHTMLCell($wp, 0, '', '', $fp, 0, 0, false, true, 'L');
                    $pdf->AddPage('P');
                }
                $pdf->SetY($y_tab);
                $new_pg = true;
            }
            $new_pg ? $pdf->SetX($x2) : '';
            $pdf->MultiCell( $wp*0.70, 6, $vp1['descrizione'], 1, 'L', false, 0, '', '', true, 1, false, true, 6, 'M', true);
            $pdf->MultiCell( $wp*0.15, 6, '1° Quadrimestre', 1, 'C', false, 0, '', '', true, 1, false, true, 6, 'M', true);
            $pdf->MultiCell( $wp*0.15, 6, 'Fine Anno', 1, 'C', false, 1, '', '', true, 1, false, true, 6, 'M', true);
            $new_pg ? $pdf->SetX($x2) : '';
            $yst = $pdf->GetY();
            $xst = $wp*0.85 + $pdf->GetX();

                if ($vp1['tipo_materia'] != 'CONDOTTA') {
                foreach ($cmp_st as $id_cmp => $cmp)
                {
                    $new_pg ? $pdf->SetX($x2) : '';
                    $pdf->MultiCell( $wp*0.70, $cmp['alt'], $cmp['desc'], 1, 'L', false, 0, '', '', true, 1, true, true, $cmp['alt'], 'M', true);
                    $pdf->MultiCell( $wp*0.15, $cmp['alt'], $cmp['p1'], 1, 'C', false, 1, '', '', true, 1, false, true, $cmp['alt'], 'M', true);   
                }
                $pdf->MultiCell( $wp*0.15, $pdf->GetY()-$yst, $val_finale, 1, 'C', false, 1, $xst, $yst, true, 1, false, true, $pdf->GetY()-$yst, 'M', true);
                $new_pg ? $pdf->SetX($x2) : '';
                if ($giudizi['p1'] != '') {
                    $pdf->MultiCell( $wp, 0, $giudizi['p1'], 1, 'L', false, 1, '', '', true, 1, false, true, 0, 'T', true);
                    $new_pg ? $pdf->SetX($x2) : '';
                }
                if ($giudizi['p2'] != '') {
                    $pdf->MultiCell( $wp, 0, $giudizi['p2'], 1, 'L', false, 1, '', '', true, 1, false, true, 0, 'T', true);
                    $new_pg ? $pdf->SetX($x2) : '';
                }
            } else {
                // condotta
                $vp1_voto = $vp1['voto_pagellina'];
                $vp2_voto = $vp2['voto_pagellina'];
                $vc = '';
                foreach ($vp1['significati_voto'] as $significato) {
                    if ($significato['voto'] == $vp1_voto) {
                        $vp1_voto = decode($significato['valore']);
                    }
                    if ($significato['voto'] == $vp2_voto) {
                        $vp2_voto = decode($significato['valore']);
                    }
                }
                $pdf->MultiCell( $wp*0.70, 6, $vp1['descrizione'], 1, 'L', false, 0, '', '', true, 1, false, true, 6, 'M', true);
                $pdf->MultiCell( $wp*0.15, 6, $vp1_voto, 1, 'C', false, 0, '', '', true, 1, false, true, 6, 'M', true);
                $pdf->MultiCell( $wp*0.15, 6, $vp2_voto, 1, 'C', false, 1, '', '', true, 1, false, true, 6, 'M', true);
            }
        }

        $pdf->SetY(280);
        $pdf->SetFont($f, '', 8);
        $pdf->writeHTMLCell($wp, 0, '', '', $fp, 0, 0, false, true, 'L');
        if ($formato == 'A3') {
        $pdf->writeHTMLCell($wp, 0, $x2, '', $fp, 0, 1, false, true, 'L');
        }


        if ($formato == 'A4') {
        $pdf->AddPage('P');

        $pdf->SetFont($f, 'B', 9);
        $pdf->CellFitScale($wp*0.24, 0, $studente['cognome'], 'TL', 0, 'C');
        $pdf->CellFitScale($wp*0.24, 0, $studente['nome'] , 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, $studente['codice_fiscale'], 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, $studente['codice_meccanografico'], 'T', 0, 'C');
        $pdf->CellFitScale($wp*0.12, 0, $as_attuale, 'TR', 1, 'C');
        $pdf->SetFont($f, '', 9);
        $pdf->CellFitScale($wp*0.24, 0, 'COGNOME', 'BL', 0, 'C');
        $pdf->CellFitScale($wp*0.24, 0, 'NOME', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, 'CODICE FISCALE', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.20, 0, 'CODICE ISTITUTO', 'B', 0, 'C');
        $pdf->CellFitScale($wp*0.12, 0, 'ANNO SCOLASTICO', 'RB', 1, 'C');

        $pdf->ln(3);
        $pdf->SetFont($f, 'B', 10);
        $pdf->writeHTMLCell($wp, 0, '', '', $labels['p4_tit'], 0, 1, false, true, 'C');
        $pdf->ln(3);
        $pdf->SetFont($f, '', 10);
        $pdf->writeHTMLCell($wp, 0, '', '', "VALUTAZIONE INTERMEDIA", 0, 1, false, true, 'C');
        $pdf->SetFont($f, '', 9);
        $pdf->ln(1);
        $pdf->writeHTMLCell($wp, 35, '', '', $giudizi_gen['p1'], 1, 1, false, true, 'L');
        $firme_p1 =
            '<table>'
                . '<tr>'
                    . '<td>' ."{$studente['descrizione_comuni']}, $data_p1<br><br>". '</td>'
                . '</tr>'

                . '<tr >'
                    . '<td align="left"   width="50%">___________________________________<br>Il (i) genitore (i) o chi ne fa le veci</td>'
                    . '<td width="30%">' . '</td>'
                    . '<td align="center" width="20%"><br>Il Dirigente Scolastico (1)<br>'.$studente['nome_dirigente'].'</td>'
                . '</tr>'
            . '</table>';
        $pdf->ln(2);
        $pdf->writeHTMLCell($wp, 0, '', '', $firme_p1, 0, 1, false, true, 'L');
        $pdf->ln(5);
        $pdf->SetFont($f, '', 10);
        $pdf->writeHTMLCell($wp, 0, '', '', "VALUTAZIONE FINALE", 0, 1, false, true, 'C');
        $pdf->SetFont($f, '', 9);
        $pdf->ln(1);
        $pdf->writeHTMLCell($wp, 35, '', '', $giudizi_gen['p2'], 1, 1, false, true, 'L');
        $firme_p2 =
            '<table>'
                . '<tr>'
                    . '<td>' ."{$studente['descrizione_comuni']}, $data_p2<br><br>". '</td>'
                . '</tr>'

                . '<tr >'
                    . '<td align="left"   width="50%">___________________________________<br>Il (i) genitore (i) o chi ne fa le veci</td>'
                    . '<td width="30%">' . '</td>'
                    . '<td align="center" width="20%"><br>Il Dirigente Scolastico (1)<br>'.$studente['nome_dirigente'].'</td>'
                . '</tr>'
            . '</table>';
        $pdf->ln(2);
        $pdf->writeHTMLCell($wp, 0, '', '', $firme_p2, 0, 1, false, true, 'L');
        $pdf->ln(5);
        $pdf->SetFont($f, 'B', 10);
        $pdf->CellFitScale($wp, 10, "ATTESTAZIONE", 0, 1, 'C');
        $pdf->SetFont($f, '', 9);
        $pdf->writeHTMLCell($wp, 0, '', '', $labels['attestato_valutazione'].$studente['esito_corrente_calcolato'].'<sup>(3)</sup>', 0, 1, false, true, 'C');
        $pdf->ln(2);
        $pdf->writeHTMLCell($wp, 0, '', '', $fp, 0, 1, false, true, 'L');
        $pdf->SetFont($f, '', 7);
        $pdf->writeHTMLCell($wp, 0, '', 205, $labels['legenda_p'], 0, 1);
        $pdf->ln(3);
        $pdf->SetFont($f, '', 7);
        $pdf->writeHTMLCell($wp, 0, '', '', $labels['p4_note_sub'], 0, 1);
        }
        //}}} </editor-fold>
        //}}} </editor-fold>
    }
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'             => $id_classe,
    'data_p1_day'              => $data_p1_Day,
    'data_p1_month'            => $data_p1_Month,
    'data_p1_year'             => $data_p1_Year,
    'data_p2_day'              => $data_p2_Day,
    'data_p2_month'            => $data_p2_Month,
    'data_p2_year'             => $data_p2_Year,
    'periodo_pagella'       => $periodo_pagella,
    'periodo'               => $periodo,
    'periodo_desc'          => $periodo_desc,
    'orientamento'          => $orientamento,
    'formato'               => $formato
];


switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;