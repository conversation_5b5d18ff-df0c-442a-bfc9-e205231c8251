<?php

/*
 * Tipo: pagella trimestre
 * mm_123_generica_A3_donbosco_bs_01
 * Richie<PERSON> da: don bosco bs
 * Data: 2020/11/18
 *
 * Materie particolari:
 */

/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H','mm_123_generica_A3_donbosco_bs_01','Pagella Scuola Secondaria di I Grado', 1, 'donbosco-bs', 2);

 */

$orientamento = 'L';
$formato = 'A3';
$periodo = ($periodo_pagella == "intermedia" ? 27 : 29);


function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">

    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $orientamento = $parametri_stampa['orientamento'];
    $id_studente = $studente['id_studente'];
    $id_classe = $parametri_stampa['id_classe'];
    $classe = $studente['classe'];
    $sezione = $studente['sezione'];
    $periodo = $parametri_stampa['periodo'];

    $comune_data = ucwords(strtolower($studente['descrizione_comuni'])) . ',  ' . $parametri_stampa['data_day']."/".$parametri_stampa['data_month']."/".$parametri_stampa['data_year'];

    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $comune_nascita = $studente['citta_nascita_straniera'];
        $provincia_nascita = '';
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $provincia_nascita = $stato['descrizione'];
        }
    }
    else
    {
        $comune_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    if ($periodo == 27)
    {
        $voti_pagellina = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo, $studente['id_studente']);
        $voti_pagella_pentamestre = [];
    }
    else    // pentamestre
    {
        $voti_pagellina = estrai_voti_pagellina_studente_multi_classe($id_classe, 27, $studente['id_studente']);
        $voti_pagella_pentamestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 29, $studente['id_studente']);
    }

    $arr_voti_finale = [];

    $assenze_finale = $monteore_finale = 0;
    $scrutinato = false;
    $comportamento = [];
    $religione = [];
    $richieste_consiglio_classe = '';
    $osservazioni = '';
    $condotta = $applicazione = $partecipazione = $recuperi = '';

    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $monteore_finale += $voti_pagella_pentamestre[$id_materia]['monteore_totale'];
        $assenze_finale += $voti_pagella_pentamestre[$id_materia]['ore_assenza'];

        // Materie normali e attività opzionali
        if ($materia['in_media_pagelle'] != 'NV' && //$materia['in_media_pagelle'] != 'NO' &&
            ( !in_array($materia['tipo_materia'], ['RELIGIONE','ALTERNATIVA', 'CONDOTTA', 'SOSTEGNO', 'OPZIONALE']) )
            )
        {
            $arr_voti_finale[$id_materia]['1P'] = $voti_pagellina[$id_materia];
            $arr_voti_finale[$id_materia]['2P'] = $voti_pagella_pentamestre[$id_materia];
        }

        // RELIGIONE
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'RELIGIONE') {
            $religione[$id_materia]['1P'] = $voti_pagellina[$id_materia];
            $religione[$id_materia]['2P'] = $voti_pagella_pentamestre[$id_materia];
        }

        // CONDOTTA
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA')
        {
            $comportamento[$id_materia]['1P'] = $voti_pagellina[$id_materia];
            $comportamento[$id_materia]['2P'] = $voti_pagella_pentamestre[$id_materia];

            if ($periodo == 27)
            {
                foreach ($voti_pagellina[$id_materia]['campi_liberi'] as $campo_libero) {
                    // Richieste del consiglio di classe
                    if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                        if  (strtoupper($campo_libero['nome']) == 'RICHIESTE DEL CONSIGLIO DI CLASSE')
                        {
                            $campi_richieste = estrai_campi_liberi_figli($campo_libero['id_campo_libero']);
                            foreach ($campi_richieste as $campo_richiesto) {
                                $campo_richiesto_def = $voti_pagellina[$id_materia]['campi_liberi'][$campo_richiesto['id_campo_libero']];
                                $value = estrai_valore_campo_libero_selezionato($campo_richiesto_def);
                                if ($value != '') {
                                    $richieste_consiglio_classe[$campo_richiesto['id_campo_libero']] = $campo_richiesto_def['nome'] . ($value!=='SI'? " $value": '')  ;
                                }
                            }
                        }

                        if  (strtoupper($campo_libero['nome']) == 'OSSERVAZIONI')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $osservazioni = $value ;
                            }
                        }

                        if  (strtoupper($campo_libero['nome']) == 'CONDOTTA (I MEDIE)')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $condotta = $value ;
                            }
                        }
                        if  (strtoupper($campo_libero['nome']) == 'APPLICAZIONE (I MEDIE)')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $applicazione = $value ;
                            }
                        }
                        if  (strtoupper($campo_libero['nome']) == 'PARTECIPAZIONE (I MEDIE)')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $partecipazione = $value ;
                            }
                        }
                        if  (strtoupper($campo_libero['nome']) == 'RECUPERI')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $recuperi = $value ;
                            }
                        }
                    }
                }
            }
            else {
                foreach ($voti_pagella_pentamestre[$id_materia]['campi_liberi'] as $campo_libero) {
                    // Richieste del consiglio di classe
                    if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                        if  (strtoupper($campo_libero['nome']) == 'RICHIESTE DEL CONSIGLIO DI CLASSE')
                        {
                            $campi_richieste = estrai_campi_liberi_figli($campo_libero['id_campo_libero']);
                            foreach ($campi_richieste as $campo_richiesto) {
                                $campo_richiesto_def = $voti_pagellina[$id_materia]['campi_liberi'][$campo_richiesto['id_campo_libero']];
                                $value = estrai_valore_campo_libero_selezionato($campo_richiesto_def);
                                if ($value != '') {
                                    $richieste_consiglio_classe[$campo_richiesto['id_campo_libero']] = $campo_richiesto_def['nome'] . ($value!=='SI'? " $value": '')  ;
                                }
                            }
                        }

                        if  (strtoupper($campo_libero['nome']) == 'OSSERVAZIONI')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $osservazioni = $value ;
                            }
                        }

                        if  (strtoupper($campo_libero['nome']) == 'CONDOTTA (I MEDIE)')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $condotta = $value ;
                            }
                        }
                        if  (strtoupper($campo_libero['nome']) == 'APPLICAZIONE (I MEDIE)')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $applicazione = $value ;
                            }
                        }
                        if  (strtoupper($campo_libero['nome']) == 'PARTECIPAZIONE (I MEDIE)')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $partecipazione = $value ;
                            }
                        }
                        if  (strtoupper($campo_libero['nome']) == 'RECUPERI')
                        {
                            $value = estrai_valore_campo_libero_selezionato($campo_libero);
                            if ($value != '') {
                                $recuperi = $value ;
                            }
                        }
                    }
                }
            }
        }


        // scrutinio
        if ($voti_pagella_pentamestre[$id_materia]['voto_pagellina'] > 0 && $voti_pagella_pentamestre[$id_materia]['voto_pagellina'] != '')
        {
            $scrutinato = true;
        }
    }
    $arr_voti_tot = $religione + $arr_voti_finale + $comportamento;
//    echo_debug($richieste_consiglio_classe);


    // Validazione anno
    if ($monteore_finale > 0)
    {
        //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati assenze - codice di L7 per il calcolo della % assenze">
        $totale_minuti_monteore = 0;
        $totale_minuti_assenza = 0;
        $totale_minuti_assenza_esclusi = 0;
        $totale_minuti_ritardi = 0;
        $totale_minuti_assenza_dad = 0;
        $totale_minuti_ritardi_dad = 0;
        $inizio_anno = estrai_parametri_singoli('DATA_INIZIO_LEZIONI');
        $fine_anno = estrai_parametri_singoli('DATA_FINE_LEZIONI');
        $assenze_nuovo = estrai_ore_assenza_studente_periodo_nuovo((int) $id_studente, $inizio_anno, $fine_anno); // materie != NV

        // Calcolo del totale del monteore
        foreach ($assenze_nuovo as $id_materia_assenze => $assenza_materia)
        {
            $tipo_materia = estrai_dati_materia((int) $id_materia_assenze);
            if ($assenza_materia['monteore_totale'] > 0)
            {
                if (
                        (($studente['esonero_religione'] == 0 && $tipo_materia['tipo_materia'] == 'RELIGIONE') || $tipo_materia['tipo_materia'] != 'RELIGIONE')
                    )
                {
                    $ore_monteore = intval($assenza_materia['totale_secondi'] / 3600);
                    $minuti_monteore_complemento = round(($assenza_materia['totale_secondi'] / 60) % 60, 0);
                    $monteore_tradotto = $ore_monteore . ' ore ' . $minuti_monteore_complemento . ' min';

                    $ore_ritardo = intval($assenza_materia['secondi_solo_ritardo'] / 3600);
                    $minuti_ritardo_complemento = round(($assenza_materia['secondi_solo_ritardo'] / 60) % 60, 0);
                    $ritardo_tradotto = $ore_ritardo . ' ore ' . $minuti_ritardo_complemento . ' min';

                    $totale_minuti_monteore += $assenza_materia['monteore_totale'];
                    $totale_minuti_assenza += $assenza_materia['ore_assenza'];
                    $totale_minuti_ritardi += $assenza_materia['ore_solo_ritardo'];
                    $totale_minuti_assenza_esclusi += $assenza_materia['ore_assenza_escluse'];

                    $totale_minuti_assenza_dad += $assenza_materia['di_cui_dad']['ore_assenza'];
                    $totale_minuti_ritardi_dad += $assenza_materia['di_cui_dad']['ore_solo_ritardo'];

                    if ($assenza_materia['ore_assenza'] > 0)
                    {
                        $percentuale = round($assenza_materia['ore_assenza'] / $assenza_materia['monteore_totale'] * 100, 0);
                    }
                    else
                    {
                        $percentuale = 0;
                    }
                }
            }
        }
        $tot_ore_monteore = intval($totale_minuti_monteore / 60);
        $tot_minuti_monteore = round($totale_minuti_monteore % 60, 0);
        $tot_tradotto_monteore = $tot_ore_monteore . ' ore ' . $tot_minuti_monteore . ' min';

        $tot_ore_assenza = intval($totale_minuti_assenza / 60);
        $tot_minuti_assenza = round($totale_minuti_assenza % 60, 0);
        $tot_tradotto_assenza = $tot_ore_assenza . ' ore ' . $tot_minuti_assenza . ' min';

        $tot_ore_ritardi = intval($totale_minuti_ritardi / 60);
        $tot_minuti_ritardi = round($totale_minuti_ritardi % 60, 0);
        $tot_tradotto_ritardi = $tot_ore_ritardi . ' ore ' . $tot_minuti_ritardi . ' min';

        $tot_ore_assenza_dad = intval($totale_minuti_assenza_dad / 60);
        $tot_minuti_assenza_dad = round($totale_minuti_assenza_dad % 60, 0);
        $tot_tradotto_assenza_dad = $tot_ore_assenza_dad . ' ore ' . $tot_minuti_assenza_dad . ' min';

        $tot_ore_ritardi_dad = intval($totale_minuti_ritardi_dad / 60);
        $tot_minuti_ritardi_dad = round($totale_minuti_ritardi_dad % 60, 0);
        $tot_tradotto_ritardi_dad = $tot_ore_ritardi_dad . ' ore ' . $tot_minuti_ritardi_dad . ' min';

        $tot_ore_assenza_escluse = intval($totale_minuti_assenza_esclusi / 60);
        $tot_minuti_assenza_esclusi = round($totale_minuti_assenza_esclusi % 60, 0);
        $tot_tradotto_assenza_escuse = $tot_ore_assenza_escluse . ' ore ' . $tot_minuti_assenza_esclusi . ' min';

        $perc_assenza = round($totale_minuti_assenza / $totale_minuti_monteore * 100);
    //        echo "{$studente['nome']} {$studente['cognome']}: $percentuale_totmin<br>";
        //}}} </editor-fold>

//        $perc_assenza = round($assenze_finale / $monteore_finale, 2) * 100;
        if ($perc_assenza < 25)
        {
            $validazione_anno = "SI";
        }
        else
        {
            $validazione_anno = ($scrutinato) ? "DEROGA" : "NO";
        }
    }
    else
    {
        $validazione_anno = "SI";
    }


    // CURRICULUM
    $anno_scolastico_corrente = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $a = explode('/', $anno_scolastico_corrente);
    $anno_inizio = (int) $a[0];
    $anno_fine = (int) $a[1];
    $curriculum_studente = estrai_curriculum_studente((int) $id_studente);
    $anno_scolastico_corrente = $anno_inizio . "/" . $anno_fine;
    $anno_scolastico_precedente = ($anno_inizio - 1) . "/" . ($anno_fine - 1);
    //estraggo i dati di provenienza e di quante volte è ripetente
    $provenienza = '';
    for($cont_curr=0; $cont_curr <count($curriculum_studente); $cont_curr++)
    {
        if($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_precedente)
        {
            if($studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                    $provenienza = $curriculum_studente[$cont_curr]['classe_desc'].' '.$curriculum_studente[$cont_curr]['nome_scuola'];
            }
            else
            {
                    $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
            }
        }
        else {
            $classe_attuale = $curriculum_studente[$cont_curr]['classe'];

            if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_corrente
                && $classe_attuale == $curriculum_studente[$cont_curr]['classe']
                && $studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola']
                && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                if (($curriculum_studente[$cont_curr]['esito'] == 'Iscritto') || ($cont_curr <count($curriculum_studente) -1)) {

                    // Nel caso nel curriculum non sia stata inserita la scuola di provenienza
                    if($curriculum_studente[$cont_curr]['nome_scuola'] != "") {
                        $provenienza = $curriculum_studente[$cont_curr]['nome_scuola'];
                    } else {
                        $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
                    }
                }
            }
        }
    }

    // titolo di ammissione
    $esito_precedente = estrai_esito_e_volte_iscritto_da_curriculum_studente((int) $studente['id_studente'], $anno_scolastico_precedente);
    $titolo_ammissione = $esito_precedente['esito'];

    // numero volte iscritto alla classe
    $numero_volte_iscritto_studente_classe = $studente['mat_esito']['numero_volte_iscritto'];
    $numero_volte_iscritto_studente_classe_trad = '';
    switch ($numero_volte_iscritto_studente_classe) {
        case 1: $numero_volte_iscritto_studente_classe_trad = 'prima';break;
        case 2: $numero_volte_iscritto_studente_classe_trad = 'seconda';break;
        case 3: $numero_volte_iscritto_studente_classe_trad = 'terza';break;
    }


    //{{{ <editor-fold defaultstate="collapsed" desc="dizionario">
//    echo_debug($studente);
    $labels = [
        "txt_recuperi"              => "Il consiglio di classe invita l'alunn||min_oa|| a partecipare al corso di recupero della seguente/delle seguenti materie: ",

        "validita"                  => "VALIDITÀ DELL’ANNO SCOLASTICO",
        "validita_dpr"              => "(Art. 3, O.M. n 11 del 16-05-2020 in deroga a: Art. 2, comma 10 del D.P.R. n. 122/2009)",
        "validita_fini"             => "Ai fini della validità dell’anno e dell’ammissione allo scrutinio finale, l’alunn||min_oa||*:",

        "scrutinio_tit_1"           => "VALIDITÀ DELL’ANNO SCOLASTICO",
        "scrutinio_tit_2"           => "(Art. 2, comma 10 del D.P.R. n. 122/2009)",
        "scrutinio_desc"            => "Ai fini della validità dell’anno e dell’ammissione allo scrutinio finale, l’alunn||min_oa||:",
        "scrutinio_desc_val_1"      => "ha frequentato per almeno tre quarti dell’orario annuale;",
        "scrutinio_desc_val_2"      => "non ha frequentato per almeno tre quarti dell’orario annuale, ma ha usufruito della deroga;",
        "scrutinio_desc_val_3"      => "non ha frequentato per almeno tre quarti dell’orario annuale.",

        "dirigente"                 => "Il Dirigente scolastico\n{$studente['nome_dirigente']}",
        "titolo"                    => "Ministero dell’Istruzione \n e del Merito",
        "scuola_tipo"               => "Istituzione\nscolastica",
        "scuola_tipo_val"           => "Scuola Secondaria di I grado\nBrescia, BS",
        "scuola_tipo_2"             => "Scuola Secondaria di I Grado Paritaria",
        "scuola_tipo_2_val"         => '"DON UMBERTO PASINI"'."\n"
                . $studente['codice_meccanografico']."\n"
                . "SCUOLA PARITARIA – Decr. Dir. 981/02\n"
                . $studente['indirizzo_sedi']."\n"
                . "25125 {$studente['descrizione_comuni']} ({$studente['provincia_comuni']})",

        "titolo_scheda"             => "Documento di valutazione",
        "anno_scolastico"           => "Anno scolastico $anno_scolastico_attuale",

        "iscritto_classe"           => "Iscritt||min_oa|| alla classe: $classe $sezione",

        "alunno"                    => "dell’alunn||min_oa|| ",
        "alunno_1"                  => "L'alunn||min_oa|| ",

        "attestato_titolo"          => "ATTESTATO",
        "attestato_desc"            => "Visti gli atti d’ufficio e la valutazione dei docenti della classe,",
        "attestato_ammissione_1"    => "si attesta che l’alunn||min_oa|| ",
        "attestato_ammissione_2"    => "è stat||min_oa|| ",

        "valutazioni"               => "VALUTAZIONI PERIODICHE",

        "attestato_att"             => "si attesta che l'alunn||min_oa|| è ",
        "attestato_att2_terze"      => "ammess||min_oa|| all'Esame di Stato conclusivo del primo ciclo di istruzione",

        "nota_1pag"                 => "Il presente certificato non può essere prodotto agli organi della pubblica amministrazione o ai privati gestori di pubblici servizi (art. 15, comma 1, L. 183/11)",

        "dati_titolo"               => "Posizione scolastica dello studente",
        "dati_anno"                 => "Anno Scolastico ",
        "dati_registro"             => "N. REGISTRO GEN.",
        "dati_classe"               => "CLASSE",
        "dati_provenienza"          => "PROVENIENZA",
        "dati_ammissione"           => "TITOLO DI AMMISSIONE <sup>(1)</sup>",
        "dati_indirizzo"            => "INDIRIZZO",
        "iscrizione_n_volte"        => "ISCRIZIONE PER LA <b>" . $numero_volte_iscritto_studente_classe .  "</b> VOLTA <sup>(2)</sup>",
        "dati_firma_dirigente"      => "Il Dir. Serv. Gen. E Amm. <sup>(3)</sup>",


        "note_titolo"               => "NOTE",
        "note_tot"                  =>
            "<table>
                <tr>
                  <td width=\"4%\"><i>(1)</i></td>
                  <td width=\"96%\">PROMOZIONE; IDONEITA’; QUALIFICA; Idoneità all’ultima classe a seguito di esito positivo dell’esame preliminare e mancato superamento esami di Stato.</td>
                </tr>
                <tr>
                  <td><i>(2)</i></td>
                  <td>PRIMA; SECONDA; TERZA.</td>
                </tr>
                <tr>
                  <td><i>(3)</i></td>
                  <td>La firma è omessa ai sensi dell’Art. 3, D.to Lgs. 12/02/1993, n. 39.</td>
                </tr>
                <tr>
                  <td><i>(4)</i></td>
                  <td>“Il riquadro può essere utilizzato anche:<br>
              - per l’annotazione delle materie Art. 4, comma 6 del D.P.R. 122/2009;<br>
              - per l’annotazione prevista dall’Art. 9, comma 1 del D.P.R. 122/2009;<br>
              - per eventuali altre annotazioni o indicazione di rilascio di certificazione”.</td>
                </tr>
                <tr>
                  <td><i>(5)</i></td>
                  <td>Per le classi terminali indicare: ammess||min_oa|| agli esami – non ammess||min_oa|| agli esami.</td>
                </tr>
                <tr>
                  <td><i>(6)</i></td>
                  <td>Solo per esami di qualifica professionale.</td>
                </tr>
                <tr>
                  <td><i>(7)</i></td>
                  <td>promoss||min_oa|| – non promoss||min_oa||.<br>Per le classi terminali indicare: ammess||min_oa|| – non ammess||min_oa||.</td>
                </tr>
          </table>",

    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
        $studente['esito'] = str_replace('Licenziato', 'Licenziata', $studente['esito']);
        $studente['esito'] = str_replace('licenziato', 'licenziata', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k=>$label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }
    //}}} </editor-fold>
    //}}} </editor-fold>

    // Validazione ammissione scrutinio


    if ((int)$classe == 3) {
        if ( strpos( strtoupper($studente['esito']), 'NON' ) !== false ) {
            $attestato_esito = $labels['attestato_att'] . 'Non ' . $labels['attestato_att2_terze'];
        }
        else {
            $attestato_esito = $labels['attestato_att'] . $labels['attestato_att2_terze']; // promosso
        }
    }
    else {
        $attestato_esito = $labels['attestato_att'] . $studente['esito']; // 1 e 2, ammesso e non
    }


    switch ($validazione_anno) {
        case 'SI':
            $labels['scrutinio_desc_val_1'] = "[ X ] " . $labels['scrutinio_desc_val_1'];
            $labels['scrutinio_desc_val_2'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_2'];
            $labels['scrutinio_desc_val_3'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_3'];
            break;
        case 'DEROGA':
            $labels['scrutinio_desc_val_1'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_1'];
            $labels['scrutinio_desc_val_2'] = "[ X ] " . $labels['scrutinio_desc_val_2'];
            $labels['scrutinio_desc_val_3'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_3'];
            break;
        case 'NO':
            $labels['scrutinio_desc_val_1'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_1'];
            $labels['scrutinio_desc_val_2'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_2'];
            $labels['scrutinio_desc_val_3'] = "[ X ] " . $labels['scrutinio_desc_val_3'];
            break;
    }
    $tbl_validazione = '
        <div align="center"><br><b>'
            . $labels['scrutinio_tit_1'] . '<br>' . $labels['scrutinio_tit_2'] .
        '</b></div>
            <div>'.$labels['scrutinio_desc'].'</div>
        <div style="line-height: 13pt;">'
            . $labels['scrutinio_desc_val_1'] . '<br>'
            . $labels['scrutinio_desc_val_2'] . '<br>'
            . $labels['scrutinio_desc_val_3'] . '<br>' .
        '</div>        ';


    // Attestato
    $esito = $studente['esito'];
    switch(strtolower($studente['esito']))
    {
        case "licenziato":
        case "licenziata":
            $esito = " Ammess||min_oa|| al secondo Ciclo di Istruzione";
            break;
        case "non licenziato":
        case "non licenziata":
            $esito = " NON Ammess||min_oa|| al secondo Ciclo di Istruzione";
            break;
    }
    $esito = str_replace("||min_oa||", $min_oa, $esito);

    $firma_img = '';
    if (file_exists('immagini_scuola/firma_dirigente.png')) {
        $firma_img = '<img src="immagini_scuola/firma_dirigente.png" height="30" width="130">';
    }

    $timbro_img = '';
    if (file_exists('immagini_scuola/timbro.png')) {
        $timbro_img = '<img src="immagini_scuola/timbro.png" height="40" width="40">';
    }

    // PDF parametri
    $font = 'helvetica';
    $font_dim = 10;
    $h_header = 10;
    $img_logo = '';
    if (file_exists('immagini_scuola/logo_poloni.png'))        {
        $img_logo = 'immagini_scuola/logo_poloni.png';
    }

    $pdf->AddPage($orientamento, 'A3');
    $pdf->SetAutoPageBreak("off", 0);
    $pdf->SetFont($font, '', $font_dim);
    $y_start = $pdf->getY();
    $page_width = $pdf->getPageWidth();

    $page_dims = $pdf->getPageDimensions();
    $m_sinistro = $page_dims['lm'];
    $m_top = $page_dims['tm'];
    $m_destro = $page_dims['rm'];

    $half_page = $page_width / 2;
    $w_page_parts = $half_page - $m_destro - $m_sinistro;
    $x_start2 = $half_page + $m_sinistro;

    // ######   PAGINA 1   ######
    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1: Informazioni Pagella, Giudizi, Firme">
    // validita anno

    if ($periodo == 27)
    {    // intermedia
        $pdf->ln(20);
        $pdf->SetFont($font, 'B', $font_dim+3);
        $pdf->writeHTMLCell($w_page_parts, 0, '', '', "RICHIESTE DEL CONSIGLIO DI CLASSE",0, 1);
        $pdf->SetFont($font, 'B', $font_dim);
        $pdf->ln(7);
        $pdf->writeHTMLCell($w_page_parts, 0, '', '', "Preso atto della situazione attuale, gli insegnanti ritengono opportuno:",0, 1);
        $pdf->SetFont($font, '', $font_dim);
    //    $pdf->ln(2);

        $tbl = '<table border="0" cellpadding="2">';
        foreach($richieste_consiglio_classe as $consiglio) {

            $tbl .= "<tr>";
            if (strpos($consiglio['desc'], 'educativo') == true) {
                $tbl .=   "<td>".$consiglio.".</td>";
            }
            else {
                $tbl .=   "<td>".$consiglio.".</td>";
            }
            $tbl .= "</tr>";
        }
        $tbl .= "</table>";
        $pdf->writeHTMLCell($w_page_parts, 0, '', '', $tbl,0, 1);
        $pdf->ln(6);
        if ($osservazioni != '') {
            $pdf->writeHTMLCell($w_page_parts, 0, '', '', "<b>OSSERVAZIONI:</b> $osservazioni", 0, 1);
        }
        // recuperi
        if ($recuperi != '') {
            $pdf->ln(6);
            $pdf->writeHTMLCell($w_page_parts, 0, '', '', "<b>RECUPERI</b><br>".$labels['txt_recuperi']." $recuperi", 0, 1);
        }

        $pdf->ln(15);
    }
    else
    {   // finale
        $pdf->ln(12);

        $pdf->SetFont('helvetica', '', 10);
        $pdf->writeHTMLCell($w_page_parts, 0, '', '', $tbl_validazione, 1, 1, false, true, 'L');

        if ($studente['classe'] == 3) {
            $pdf->ln(9);

            $pdf->writeHTMLCell($w_page_parts, 0, '', '', "<b>SOLO PER LE CLASSI TERZE:</b>", 0, 1, false, true, 'C');
            $pdf->ln(2);
            $tbl_amm = '<table align="center" cellpadding="3">
                <tr>
                    <td><b>VOTO DI AMMISSIONE</b><br>Voto (in cifre e in lettere)</td>
                    <td>'.$studente['voto_ammissione_medie'].'/10</td>
                    <td>'.strtoupper(traduci_numero_in_lettere($studente['voto_ammissione_medie'])) .'/decimi</td>
                </tr>
            </table>
            ';
            $pdf->writeHTMLCell($w_page_parts, 0, '', '', $tbl_amm, 1, 1, false, true, 'L');
        }

        $pdf->ln(20);
    }

    $pdf->SetFont($font, '', $font_dim);
    $firme = '<table align="center">
            <tr>
                <td width="70%" align="left">'.$comune_data.'</td>
                <td width="30%">_______________________<br>Il Coordinatore didattico<sup>(1)</sup><br>'.$studente['nome_dirigente'].'</td>
            </tr>
        </table>';
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $firme,0, 1);
    $pdf->ln(18);
    $pdf->SetFont($font, 'I', $font_dim);
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', "Il presente modello non può essere prodotto agli organi della pubblica amministrazione o ai privati gestori di pubblici servizi (art.15, comma1, L. 183/11)",0, 1);
    $pdf->SetFont($font, '', $font_dim);
    $pdf->Ln(12);
    $pdf->SetFont($font, '', $font_dim-1);
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', "(1) Firma autografa sostituita a mezzo stampa, ai sensi dell’art. 3, comma 2, del D.Lgs. 39/93",0, 1);
    $pdf->SetFont($font, '', $font_dim);
//    $pdf->SetY(190);
//    $curr_x_ln = $pdf->GetX() + 5;
//    $curr_y_ln = $pdf->GetY();
//    $pdf->Line($curr_x_ln, $curr_y_ln, $curr_x_ln + $w_page_parts-5, $curr_y_ln);
//    $pdf->Ln(4);
//    $pdf->SetFont($font, 'B', $font_dim + 1);
//    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $labels['note_titolo'], 0, 1, false, true, 'C');
//    $pdf->Ln(4);
//    $pdf->SetFont($font, '', $font_dim-1);
//    $pdf->writeHTMLCell($w_page_parts-20, 0, $m_sinistro+15, '', $labels['note_tot'], 0, 1, false, true, 'L');
//    $pdf->SetFont($font, '', $font_dim);
//    $pdf->Ln(8);
//
//    $pdf->SetFont($font, 'I', $font_dim-1);
//    $pdf->MultiCell($w_page_parts, 0, $labels['nota_1pag'], 0, 'L');
//    $pdf->SetFont($font, '', $font_dim);


    if ($periodo != 27)
    {    // finale

        $pdf->ln(20);
        $pdf->SetFont($font, 'B', $font_dim+4);
        $pdf->CellFitScale($w_page_parts, 0, "ATTESTAZIONE", 0, 1, 'C');
        $pdf->SetFont($font, '', $font_dim);
        $pdf->ln(2);
        $pdf->writeHTMLCell($w_page_parts, 0, '', '', $labels['attestato_desc'].'<br><b>'.$labels['attestato_ammissione_1'].$labels['attestato_ammissione_2'].$esito.'</b>', 0, 1, false, true, 'C');
        $pdf->SetFont($font, '', $font_dim);
        $pdf->ln(18);

        $pdf->writeHTMLCell($w_page_parts, 0, '', '', $firme,0, 1);
    }

    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1, Parte 2: Informazioni Pagella">
    //{{{ <editor-fold defaultstate="collapsed" desc="Intestazione">
    $pdf->Image('immagini_scuola/logo_repubblica.jpg', $x_start2 + ($w_page_parts/2) - 10, $m_top+15, 25, 25, 'JPG', false);
    $pdf->setY($m_top+$h_header+24);
    $pdf->Ln(10);
    $pdf->SetFont($font, '', $font_dim + 9);
    $pdf->SetX($x_start2);
    $pdf->MultiCell($w_page_parts, 0, $labels['titolo'], 0, 'C');
    $pdf->Ln(9);

//    echo_debug($studente);
    $pdf->SetFont($font, 'B', $font_dim + 3);
    $pdf->SetX($x_start2);
    $pdf->MultiCell($w_page_parts*0.35, 20, $labels['scuola_tipo'], 1, 'C', false, 0, '', '', true, 0, false, true, 20, 'M');
    $pdf->SetFont($font, 'B', $font_dim + 2);
    $pdf->MultiCell($w_page_parts*0.65, 20, $labels['scuola_tipo_val'], 1, 'C', false, 1, '', '', true, 0, false, true, 20, 'M');;
    $pdf->Ln(6);
    $pdf->SetFont($font, 'B', $font_dim + 3);
    $pdf->SetX($x_start2);
    $pdf->MultiCell($w_page_parts*0.35, 26, $labels['scuola_tipo_2'], 1, 'C', false, 0, '', '', true, 0, false, true, 26, 'M');
    $pdf->SetFont($font, 'B', $font_dim + 2);
    $pdf->MultiCell($w_page_parts*0.65, 26, $labels['scuola_tipo_2_val'], 1, 'C', false, 1, '', '', true, 0, false, true, 26, 'M');

    $pdf->SetCellHeightRatio(1.25);
    $pdf->Ln(14);
    $pdf->SetFont($font, 'B', $font_dim + 10);
    $pdf->SetX($x_start2);
    $pdf->CellFitScale(0, 0, $labels['titolo_scheda'], 0, 1, 'C');
    $pdf->SetX($x_start2);
    $pdf->CellFitScale(0, 0, $labels['anno_scolastico'], 0, 1, 'C');
    $pdf->Ln(14);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Anagrafica">
    $larghezza_max_cella = $w_page_parts;
	$altezza_cella = 5;

	$pdf->SetX($x_start2);
    $pdf->SetCellPadding( 1 );
    $pdf->SetFont($font, 'B', $font_dim + 4);
	$pdf->CellFitScale($larghezza_max_cella, $altezza_cella+4, 'Dati anagrafici dello studente', 'TRL', 1, 'C');
    $pdf->SetFont($font, '', $font_dim);

	$pdf->SetX($x_start2);
    $pdf->SetFont($font, 'B', $font_dim + 2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['cognome'], 'L', 0, 'C');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['nome'], 0, 0, 'C',FALSE,'',0, FALSE,'T', 'B');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['codice_fiscale'], 'R', 1, 'C');

	$pdf->SetX($x_start2);
    $pdf->SetFont($font, '', $font_dim-2);
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'COGNOME', 'L', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'NOME', 0, 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'CODICE FISCALE', 'R', 1, 'C',FALSE,'',0, FALSE,'T', 'T');

	$pdf->SetX($x_start2);
    $pdf->SetFont($font, 'B', $font_dim + 2);
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $studente['data_nascita_ext'], 'L', 0, 'C');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $comune_nascita, 0, 0, 'C');
	$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella, $provincia_nascita, 'R', 1, 'C');

	$pdf->SetX($x_start2);
    $pdf->SetFont($font, '', $font_dim-2);
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'DATA DI NASCITA', 'LB', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'COMUNE DI NASCITA', 'B', 0, 'C',FALSE,'',0, FALSE,'T', 'T');
	$pdf->Cell($larghezza_max_cella/3, $altezza_cella+3, 'PROV. O STATO ESTERO', 'RB', 1, 'C',FALSE,'',0, FALSE,'T', 'T');

    $pdf->SetFont($font, '', $font_dim);
    $pdf->setCellHeightRatio( 1.25 );
    $pdf->ln(9);

    $pdf->SetX($x_start2);
    $pdf->SetFont($font, 'B', $font_dim);
    $pdf->CellFitScale(0, 0, $labels['iscritto_classe'], 1, 1);
    $pdf->SetFont($font, '', $font_dim);

//    $pdf->SetFont($font, '', $font_dim);
//    $pdf->ln(12);
////    $firme = '<table>
////            <tr>
////                <td width="35%">'.$comune_data.'</td>
////                <td width="15%"align="right">'.$timbro_img.'</td>
////                <td width="50%" align="center">'.$firma_img.'</td>
////            </tr>
////                <td width="50%"></td>
////                <td width="50%" align="center">Il Coordinatore didattico<br>'.$studente['nome_dirigente'].'</td>
////            </tr>
////        </table>';
//	$pdf->SetX($x_start2);
//    $pdf->writeHTMLCell(0, 0, $x_start2, '',$firme);
    //}}} </editor-fold>
   //}}} </editor-fold>
    //}}} </editor-fold>

    // ######   PAGINA 2   ######
    $pdf->AddPage($orientamento, 'A3');

//    $pdf->SetFont($font, 'B');
//    $pdf->CellFitScale($w_page_parts*0.24, 0, $studente['cognome'], 1, 0, 'C');
//    $pdf->CellFitScale($w_page_parts*0.24, 0, $studente['nome'] , 1, 0, 'C');
//    $pdf->CellFitScale($w_page_parts*0.20, 0, $studente['codice_fiscale'], 1, 0, 'C');
//    $pdf->CellFitScale($w_page_parts*0.20, 0, $studente['codice_meccanografico'], 1, 0, 'C');
//    $pdf->CellFitScale($w_page_parts*0.12, 0, $anno_scolastico_attuale, 1, 1, 'C');
//
//    $pdf->SetFont($font, '', $font_dim-3);
//    $pdf->CellFitScale($w_page_parts*0.24, 0, 'COGNOME', 1, 0, 'C');
//    $pdf->CellFitScale($w_page_parts*0.24, 0, 'NOME', 1, 0, 'C');
//    $pdf->CellFitScale($w_page_parts*0.20, 0, 'CODICE FISCALE', 1, 0, 'C');
//    $pdf->CellFitScale($w_page_parts*0.20, 0, 'CODICE ISTITUTO', 1, 0, 'C');
//    $pdf->CellFitScale($w_page_parts*0.12, 0, 'ANNO SCOLASTICO', 1, 1, 'C');
//
//    $pdf->SetY($m_top + $h_header+6);
//    $font_dim;
//    $pdf->setCellPaddings(3);
    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2: Voti">
    //{{{ <editor-fold defaultstate="collapsed" desc="Voti">

//    $new_pg = false;
//    $pdf->setCellHeightRatio( 1.5 );

//    $pdf->ln(4);
    $pdf->SetFont($font, 'B', $font_dim);
    $pdf->Cell( $w_page_parts, 0, "VALUTAZIONI PERIODICHE", 1, 1, 'C');
    $pdf->SetFont($font, '', $font_dim);
    $y_start_voti = $pdf->GetY();

//    echo_debug($arr_voti_tot);
    // materie
    foreach ($arr_voti_tot as $voto)
    {
        $voto_tradotto_IQ_unico = '';
        $voto_IQ_unico = '';
        $voto_pentamestre_unico = '';
        $voto_trad_pentamestre_unico = '';
        $rel_IQ = $rel_fin = '';

        // significati voti per la traduzione
        foreach ($voto['1P']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $voto['1P']['voto_pagellina']) {
                $voto_tradotto_IQ_unico = ucwords($significato['valore_pagella']).'/decimi';
                $voto_IQ_unico = $voto['1P']['voto_pagellina'].'/10';
                $rel_IQ = ucwords($significato['valore_pagella']);
            }
        }
        foreach ($voto['2P']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $voto['2P']['voto_pagellina']) {
                $voto_trad_pentamestre_unico = ucwords($significato['valore_pagella']).'/decimi';
                $voto_pentamestre_unico = $voto['2P']['voto_pagellina'].'/10';
                $rel_fin = ucwords($significato['valore_pagella']);
            }
        }

        $cella_descrizione_materia = $voto['1P']['descrizione'];
        $pdf->SetFont($font, '', $font_dim);

        // RELIGIONE
        if ($voto['1P']['tipo_materia'] == 'RELIGIONE') {
            $pdf->MultiCell( $w_page_parts*0.30, 9, $cella_descrizione_materia, 1, 'L', false, 0, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.35, 9, "Trimestre", 1, 'C', false, 0, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.35, 9, "Pentamestre", 1, 'C', false, 1, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.30, 9, 'Voto', 1, 'L', false, 0, '', '', true, 0, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.35, 9, $rel_IQ, 1, 'C', false, 0, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.35, 9, $rel_fin, 1, 'C', false, 1, '', '', true, 1, false, true, 8, 'M', true);
        }
        else {
            $pdf->MultiCell( $w_page_parts*0.30, 9, $cella_descrizione_materia, 1, 'L', false, 0, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.35, 9, "Trimestre", 1, 'C', false, 0, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.35, 9, "Pentamestre", 1, 'C', false, 1, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.30, 0, 'Voto (in cifre e in lettere)', 1, 'L', false, 0, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.175, 0, "$voto_IQ_unico", 1, 'C', false, 0, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.175, 0, "$voto_tradotto_IQ_unico", 1, 'C', false, 0, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.175, 0, "$voto_pentamestre_unico", 1, 'C', false, 0, '', '', true, 1, false, true, 8, 'M', true);
            $pdf->MultiCell( $w_page_parts*0.175, 0, "$voto_trad_pentamestre_unico", 1, 'C', false, 1, '', '', true, 1, false, true, 8, 'M', true);
        }
    }
    $pdf->Ln(6);

    $pdf->setCellHeightRatio( 1.25 );

    $pdf->SetFont($font, 'B', $font_dim+2);
    $pdf->SetXY($x_start2, $m_top + $h_header+6);
    $pdf->CellFitScale($w_page_parts, 0, 'DESCRITTORI DEL COMPORTAMENTO', 0, 1, 'C');
    $pdf->SetFont($font, '', $font_dim);

    $tbl_comportamento = '<table cellpadding="4">'
            . "<tr>"
                . '<td><b>CONDOTTA</b></td>'
            . "</tr>"
            . "<tr>"
                . "<td>$condotta</td>"
            . "</tr>"
            . "<tr>"
                . "<td><i>Legenda</i><br>
                    <table>
                        <tr>
                            <td width=\"4%\">1</td>
                            <td width=\"96%\">Mette in atto in modo costante comportamenti corretti, collaborando e/o aiutando i compagni nella vita scolastica</td>
                        </tr>
                        <tr>
                            <td width=\"4%\">2</td>
                            <td width=\"96%\">Ordinariamente corretta e disciplinata</td>
                        </tr>
                        <tr>
                            <td width=\"4%\">3</td>
                            <td width=\"96%\">Presenza di richiami verbali e/o sporadiche note disciplinari da parte di uno o più docenti</td>
                        </tr>
                        <tr>
                            <td width=\"4%\">4</td>
                            <td width=\"96%\">Frequente disturbo delle attività didattiche e/o carente rispetto delle persone e delle regole segnalati dalla presenza di ripetute note disciplinari</td>
                        </tr>
                        <tr>
                            <td width=\"4%\">5</td>
                            <td width=\"96%\">Reiterato disturbo delle lezioni e/o mancanza di rispetto per i docenti e per i compagni segnalati dalla presenza di note che abbiano comportato un provvedimento di sospensione, che ha portato a un effettivo miglioramento</td>
                        </tr>
                        <tr>
                            <td width=\"4%\">6</td>
                            <td width=\"96%\">Mancata condivisione del Progetto Educativo, con la presenza o di gravi episodi di insubordinazione o di continui e reiterati richiami segnalati in varie occasioni all’allievo e alla famiglia, che abbiano comportato un provvedimento disciplinare (cfr. art. 7 del DPR 122/09), a cui non è corrisposto un significativo miglioramento del comportamento generale</td>
                        </tr>
                    </table>".
//    "<br>&nbsp;&nbsp;• Corretta e disciplinata
//    <br>&nbsp;&nbsp;• Ordinariamente corretta e disciplinata con sporadici richiami verbali
//    <br>&nbsp;&nbsp;• Presenza di ripetuti richiami verbali e/o sporadiche note disciplinari da parte di uno o più docenti
//    <br>&nbsp;&nbsp;• Frequente disturbo delle attività didattiche e/o carente rispetto delle persone e delle regole segnalati dalla presenza di ripetute note disciplinari
//    <br>&nbsp;&nbsp;• Reiterato disturbo delle lezioni e/o mancanza di rispetto per i docenti e per i compagni segnalati dalla presenza di note che abbiano comportato un provvedimento di sospensione
//    <br>&nbsp;&nbsp;• Mancata condivisione del Progetto Educativo, con la presenza o di gravi episodi di insubordinazione o di continui e reiterati richiami segnalati in varie occasioni all’allievo e alla famiglia, che abbiano comportato un provvedimento disciplinare (cfr. art. 7 del DPR 122/09), a cui non è corrisposto un significativo miglioramento del comportamento generale*
//    &nbsp;&nbsp;".
                "</td>"
            . "</tr>"

            . "<tr><td></td></tr>"

            . "<tr>"
                . '<td><b>PARTECIPAZIONE</b></td>'
            . "</tr>"
            . "<tr>"
                . "<td>$partecipazione</td>"
            . "</tr>"
            . "<tr>"
                . "<td><i>Legenda</i>
    <br>&nbsp;&nbsp;• Partecipa in modo costante e personale apportando un contributo originale alla vita scolastica
    <br>&nbsp;&nbsp;• Partecipa in modo costante alla vita scolastica
    <br>&nbsp;&nbsp;• Partecipa in modo discontinuo alla vita scolastica
    <br>&nbsp;&nbsp;• Partecipa in modo non sempre appropriato e/o pertinente alla vita scolastica
    <br>&nbsp;&nbsp;• Partecipa solo se sollecitato e/o guidato dal docente
    &nbsp;&nbsp;</td>"
            . "</tr>"

            . "<tr><td></td></tr>"

            . "<tr>"
                . '<td><b>APPLICAZIONE</b></td>'
            . "</tr>"
            . "<tr>"
                . "<td>$applicazione</td>"
            . "</tr>"
            . "<tr>"
                . "<td><i>Legenda</i>
    <br>&nbsp;&nbsp;• Studio personale costante e metodico. Rispetto puntuale delle consegne
    <br>&nbsp;&nbsp;• Studio personale costante. Rispetto delle consegne ordinariamente puntuale
    <br>&nbsp;&nbsp;• Studio personale e rispetto delle consegne costanti ma poco efficaci
    <br>&nbsp;&nbsp;• Studio personale e rispetto delle consegne incostanti
    <br>&nbsp;&nbsp;• Studio personale e rispetto delle consegne inadeguati
    &nbsp;&nbsp;</td>"
            . "</tr>"

            . "</table>";

    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, $x_start2, '', $tbl_comportamento, 0, 1);
    $pdf->ln(4);
    $pdf->writeHTMLCell(0, 0, $x_start2, '', '*  Specifica nota illustrativa di cui all’art. 2, comma 8, del D.P.R. 122/09', 0, 1);
    $pdf->ln(16);
    $pdf->writeHTMLCell($w_page_parts, 0, $x_start2, '', $firme,0, 1);

    //}}} </editor-fold>
    //}}} </editor-fold>
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'         => $id_classe,
    'data_day'          => $data_Day,
    'data_month'        => $data_Month,
    'data_year'         => $data_Year,
    'periodo_pagella'   => $periodo_pagella,
    'periodo'           => $periodo,
    'orientamento'      => $orientamento,
];

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
