<script type="text/javascript">
    var naxtapi_path = '../next-api/v1/';
    var selezionaTuttiTesto = '{mastercom_label}(Seleziona tutti){/mastercom_label}';
    var messFunzioneAppelloDisabilitato = "{mastercom_label}Non hai i permessi necessari per modificare l'appello.{/mastercom_label}";
    var elenco_videomeeting = JSON.parse(jsonEscape('{$elenco_videomeeting_json}'));
    var dati_appello_corsi = JSON.parse(jsonEscape('{$dati_appello_corsi}'));
    var tipo_appello_corsi_speciale = '{$tipo_appello_corsi_speciale}';
    var abb_mat_arg_nav = {};
    var elenco_arg_util_nav = {};
    var ass_did_dist = {};
    var motivazioniAssenze = [];
    var circolari = [];
    var repeat = null; //intervall circolari
    try {
        abb_mat_arg_nav = JSON.parse(jsonEscape('{$abb_mat_arg_nav_json}'));
        elenco_arg_util_nav = JSON.parse(jsonEscape('{$elenco_arg_util_nav_json}'));
    } catch (e) { }

    try {
        ass_did_dist = JSON.parse('{$array_assenze_studenti_didattica_distanza_js_json}');
    } catch (e) { }

    {literal}
    $('#modal-open').on("change", function (e)
    {
        var body = $('#body')[0];

        if (this.value === 'SI')
        {
            body.style.overflow='hidden';
            $('#btn_menu_utente').css("pointer-events", "none");
            $('#btn_giorno_precedente').attr("disabled", true);
            $('#btn_giorno_successivo').attr("disabled", true);
            $('#btn_calendario').attr("disabled", true);
            $('#btn_giorno').attr("disabled", true);
            $('#btn_settimana').attr("disabled", true);
            $('#btn_mese').attr("disabled", true);
            $('#select_id_classe').attr("disabled", true);
            $('#elenco_giorni').css('overflow','hidden');
        }
        else
        {
            body.style.overflow='visible';
            $('#btn_menu_utente').css("pointer-events", "auto");
            $('#btn_giorno_precedente').attr("disabled", false);
            $('#btn_giorno_successivo').attr("disabled", false);
            $('#btn_calendario').attr("disabled", false);
            $('#btn_giorno').attr("disabled", false);
            $('#btn_settimana').attr("disabled", false);
            $('#btn_mese').attr("disabled", false);
            $('#select_id_classe').attr("disabled", false);
            $('#elenco_giorni').css('overflow','auto');
        }
    });

    function chiudiSfondoOscurato()
    {
        if ($('#circolari_classe').is(":visible")){
            return;
        }

        $('#sfondo_oscurato').hide();
        $('#nuova_nota_disciplinare').hide();
        $('#modifica_argomento_compito').hide();
        $('.form_inserimento_compito_argomento').hide();
        $('.form_appello').hide();
        $('.form_appello').css('overflow','auto');
        $('#cambio_materia_orario_docente').hide();
        $('#popup_lezione_online').hide();
        $('#popup_presenze').hide();
        $('#assenze_didattica_distanza').hide();
        $('#giustifiche_studente_popup').remove();
        $('#menu_options_popup').remove();
        $('#modal-open').val('NO').trigger('change');

        $('#id_materia_argomento_ministeriale').val(-1);
        $('#id_materia_programmazione_didattica').val(-1);
    }

    function switchAssenze(btn, id_studente)
    {
        if (btn.style.backgroundColor === 'red')
        {
            //imposto lo studente presente
            btn.style.backgroundColor = '#ffc6c6';
            document.getElementById("assente_"+id_studente).value = 'NO';
        }
        else
        {
            //imposto lo studente assente
            btn.style.backgroundColor = 'red';
            document.getElementById("assente_"+id_studente).value = 'ASSENTE';

            //imposto l'assenza anche a mensa se abilitata
            var idSelect = 'select_mensa_'+id_studente;
            var select = $('#'+idSelect);
            if (select.length === 1 && select.is(":disabled") === false){
                select.val('--').trigger('change');

                let currentBGColor = select.css('backgroundColor');
                for (var i = 0; i < 2; i++ ) {
                    select
                        .animate( { backgroundColor: "#ffbdbd" }, 200 )
                        .animate( { backgroundColor: currentBGColor }, 100 );
                }
            }
        }
        cambiaStato("modificato_"+id_studente);
    }

    function switchAssenzeDidatticaDistanza(btn, id_studente, id_orario)
    {
        if (btn.style.backgroundColor === 'red')
        {
            //imposto lo studente presente
            btn.style.backgroundColor = '#ffc6c6';
            document.getElementById("assente_did_dist_"+id_studente).value = 'NO';
        }
        else
        {
            //imposto lo studente assente
            btn.style.backgroundColor = 'red';
            document.getElementById("assente_did_dist_"+id_studente).value = 'ASSENTE';
        }
        cambiaStato("modificato_did_dist_"+id_studente);
    }

    function switchStatoStudenteCorsi(btn, id_studente, tipo)
    {
        var cellaEntrate = $('#cella_entrate_studente_'+id_studente);
        var cellaUscite =  $('#cella_uscite_studente_'+id_studente);

        if (tipo == 'P'){
            if (document.getElementById("stato_studente_"+id_studente).value == 'P')
            {
                //imposto lo studente assente
                btn.style.backgroundColor = '#abd9ab';
                document.getElementById("stato_studente_"+id_studente).value = 'A';
                $("[data-ora-tipo='entrata'][data-id-studente='"+id_studente+"']").prop("disabled", true);
                $("[data-ora-tipo='uscita'][data-id-studente='"+id_studente+"']").prop("disabled", true);
                $("[data-ora-tipo='entrata'][data-id-studente='"+id_studente+"']").val("");
                $("[data-ora-tipo='uscita'][data-id-studente='"+id_studente+"']").val("");
            }
            else
            {
                //imposto lo studente presente
                btn.style.backgroundColor = 'green';
                document.getElementById("stato_studente_"+id_studente).value = 'P';
                $("[data-ora-tipo='entrata'][data-id-studente='"+id_studente+"']").prop("disabled", false);
                $("[data-ora-tipo='uscita'][data-id-studente='"+id_studente+"']").prop("disabled", false);
            }
        } else if (tipo == 'A'){
            if (document.getElementById("stato_studente_"+id_studente).value == 'A')
            {
                //imposto lo studente presente
                btn.style.backgroundColor = '#ffc6c6';
                document.getElementById("stato_studente_"+id_studente).value = 'P';
                $("[data-ora-tipo='entrata'][data-id-studente='"+id_studente+"']").prop("disabled", false);
                $("[data-ora-tipo='uscita'][data-id-studente='"+id_studente+"']").prop("disabled", false);
            }
            else
            {
                //imposto lo studente assente
                btn.style.backgroundColor = 'red';
                document.getElementById("stato_studente_"+id_studente).value = 'A';
                $("[data-ora-tipo='entrata'][data-id-studente='"+id_studente+"']").prop("disabled", true);
                $("[data-ora-tipo='uscita'][data-id-studente='"+id_studente+"']").prop("disabled", true);
                $("[data-ora-tipo='entrata'][data-id-studente='"+id_studente+"']").val("");
                $("[data-ora-tipo='uscita'][data-id-studente='"+id_studente+"']").val("");
            }
        }
        cambiaStato("modificato_"+id_studente);
    }

    function setCurrentTime(id)
    {
        var input = document.getElementById(id);
        var d = new Date(),
        h = d.getHours(),
        m = d.getMinutes();
        if (!input.disabled){
            input.value=fillZero(h)+':'+fillZero(m);
            input.onchange();
        }
    }

    function cambiaStato(id)
    {
        //setta l'hidden flag modificato uguale a SI
        document.getElementById(id).value = 'SI';
        $("#notify-badge-appello").show();
    }

    function resetAssenze(suff_id)
    {
        //assente entrata uscita modificato btn
        var assenza = $('#assente_'+suff_id);
        var btn_assenza = $('#btn_'+suff_id);
        var entrata = $('[id^="entrata_'+suff_id+'"]');
        var uscita = $('[id^="uscita_'+suff_id+'"]');
        var modificato = $('#modificato_'+suff_id);

        //setto l'assenza a NO e cambio il bottone
        assenza.val('NO');
        btn_assenza.html('A');
        btn_assenza.css("backgroundColor", '#ffc6c6');

        //vuoto le ore di entrate e uscite
        entrata.val('');
        uscita.val('');
        entrata.trigger('change');
        uscita.trigger('change');

        //setto il flag modificato a SI
        modificato.val('SI');
        $("#notify-badge-appello").show();
    }

    function resetAssenzeSpeciali(suff_id)
    {
        //assente entrata uscita modificato btn
        var stato_studente = $('#stato_studente_'+suff_id);
        var btn_assenza = $('#btn_'+suff_id);
        var entrate = $('[id^="entrata_'+suff_id+'"]');
        var uscite = $('[id^="uscita_'+suff_id+'"]');
        var modificato = $('#modificato_'+suff_id);

        tipo_appello_corsi_speciale

        //setto l'assenza/presenza e cambio il bottone
        if (tipo_appello_corsi_speciale == 'P'){
            stato_studente.val('A');
            btn_assenza.css("backgroundColor", '#abd9ab');
        } else {
            stato_studente.val('P');
            btn_assenza.css("backgroundColor", '#ffc6c6');
        }

        //vuoto le ore di entrate e uscite
        for (var i = 0; i < entrate.length; i++){
            $(entrate[i]).val('');
        }
        for (var i = 0; i < uscite.length; i++){
            $(uscite[i]).val('');
        }

        //setto il flag modificato a SI
        modificato.val('SI');
        $("#notify-badge-appello").show();
    }

    function resetAssenzeDad(suff_id)
    {
        //assente entrata uscita modificato btn
        var assenza = $('#assente_did_dist_'+suff_id);
        var btn_assenza = $('#btn_ass_did_dist_'+suff_id);
        var entrata = $('#entrata_did_dist_'+suff_id);
        var uscita = $('#uscita_did_dist_'+suff_id);
        var modificato = $('#modificato_did_dist_'+suff_id);

        //setto l'assenza a NO e cambio il bottone
        assenza.val('NO');
        btn_assenza.html('A');
        btn_assenza.css("backgroundColor", '#ffc6c6');

        //vuoto le ore di entrate e uscite
        entrata.val('');
        uscita.val('');
        entrata.trigger('change');
        uscita.trigger('change');

        //setto il flag modificato a SI
        modificato.val('SI');
    }

    function toggleStoriaAssenze(id){
        var item = $('#'+id)

        if(item.is(":visible")){
            slideUpDown(item, 'up');
        } else{
            slideUpDown(item, 'down');
        }
    }

    function caricaDatiCambioMateriaOrario(id_orario, data, fascia, materia){
        $('#id_ora_modifica_materia_orario').val(id_orario);
        $('#data_modifica_materia_orario').html(data);
        $('#ora_modifica_materia_orario').html(fascia);
        $('#materia_attuale_modifica_materia_orario').html(materia);
    }

    function settaDataOraNuovoCompito(ts_data, id_data_compito, id_ora_compito) {
        if (ts_data > 0)
        {
            //setto la data
            ts_data = parseInt(ts_data)*1000;
            let date = new Date(ts_data);

            let mese = date.getMonth()+1;
            let giorno = date.getDate();
            let ora = date.getHours();
            let minuti = date.getMinutes();

            let data_valida = date.getFullYear() + '-' + fillZero(mese) + '-' + fillZero(giorno);
            $('#'+id_data_compito).val(data_valida);

            let ora_valida = fillZero(ora)+':'+fillZero(minuti);
            $('#'+id_ora_compito+'_input').val(ora_valida);

            $('#'+id_data_compito).hide();
            $('#'+id_ora_compito).hide();
        }
        else
        {
            $('#'+id_data_compito).val('');
            $('#'+id_ora_compito+'_input').val('08:00');
            $('#'+id_data_compito).show();
            $('#'+id_ora_compito).show();
        }
    }

    function verificaDataCompitoFutura(idData, idOra)
    {
        let data = $('#'+idData).val();
        let ora = $('#'+idOra).val();

        let timeAttuale = new Date();
        let dataAttuale = timeAttuale.getFullYear()+'-'+fillZero(timeAttuale.getMonth()+1)+'-'+fillZero(timeAttuale.getDate());
        let oraAttuale = fillZero(timeAttuale.getHours())+':'+fillZero(timeAttuale.getMinutes());

        if (data !== '' && ora !== ''){
            if (data > dataAttuale){
                return true;
            } else if (data === dataAttuale && ora > oraAttuale){
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    function chiudiPopupGiustificazioni()
    {
        $("#giustifiche_studente_popup").animate({top: window.innerHeight+"px"},500,'easeInOutCubic', function(){$(this).remove();});
    }

    function switchGiustifica(checkbox, idAssenza, idStudente, macroTipo = "")
    {
        var mt = "";
        if (macroTipo != ""){
            mt = '[data-macro-tipo="'+macroTipo+'"]';
        }
        var assenza = $('[data-id-assenza="'+idAssenza+'"]'+mt);
        var giustifica = (checkbox.checked) ? 'SI' : 'NO';
        assenza.val(giustifica);
        cambiaStato("modificato_"+idStudente);
    }

    function toggleGiustif(valore, idLabelGiustificazioneImmediata)
    {
        var label = $('#'+idLabelGiustificazioneImmediata);
        var divMotivazione = $('#'+idLabelGiustificazioneImmediata+"_mot");

        if (label.length > 0)
        {
            if (valore !== '') {
                label.show();
                if ($(label).children('input[type="checkbox"]').is(":checked")){
                    divMotivazione.show();
                }
            } else {
                label.hide();
                divMotivazione.hide();
            }
        }
    }

    function creaPopupGiustificazioni(idStudente, solaVisualizzazioneAppello)
    {
        var assenze = $('[data-assenze-id-studente="'+idStudente+'"]');
        if (assenze.length){
            $('#giustifiche_studente_popup').remove();
            var maxw = (Math.max(document.documentElement.clientWidth, window.innerWidth || 0) - 10);
            var maxh = (Math.max(document.documentElement.clientHeight, window.innerHeight || 0) / 2);
            var bottom = 5;
            var options = "<div id='giustifiche_studente_popup' align='center' style='position: fixed; z-index: 5; top: "+window.innerHeight+"px; width: 100%;'>"
                        +   "<div align='left' style='overflow: auto; width: max-content; max-width: "+maxw+"px; max-height: "+maxh+";' class='scheda_informativa sfondo_bianco'>";

            assenze.each(function(index){
                var dati = $(assenze[index]).data();
                var giustifica = $(assenze[index]).val();

                if (index === 0){
                    options +=  "<div align='center' style='padding: 5px 5px 10px 5px; font-weight: bold;'>"+dati.studente+"</div>";
                    options +=  "<div align='left' class='padding_cella_generica annotazione_leggera'>{/literal}{mastercom_label}Da giustificare{/mastercom_label}{literal}:</div>";
                }

                options +=      "<div class='padding_cella_generica bordo_basso_generico'>"
                        +           "<label>";
                if (solaVisualizzazioneAppello == '0'){
                    options +=          "<input type='checkbox' onchange='switchGiustifica(this, "+dati.idAssenza+", "+dati.assenzeIdStudente+", \""+dati.macroTipo+"\");'";
                    if (giustifica === 'SI'){
                        options +=          " checked ";
                    }
                    options +=              ">";
                }

                if (dati.macroTipo == 'normale'){
                    options +=               "&emsp;"+dati.assenzaData+"&emsp;&emsp;{/literal}{mastercom_label}"+dati.assenzaTesto+"{/mastercom_label}{literal}";
                } else if (dati.macroTipo == 'dad_ddi') {
                    options +=               "&emsp;"+dati.assenzaDataIta+"&emsp;"+dati.assenzaInizioOra+" - "+dati.assenzaFineOra+" (DAD/DDI)&emsp;&emsp;";
                    if (dati.assenzaEntrataTradotta == "" && dati.assenzaUscitaTradotta == ""){
                        options +=           "{/literal}{mastercom_label}Assenza intera ora{/mastercom_label}{literal}";
                    } else {
                        if (dati.assenzaEntrataTradotta != ""){
                            options +=       "{/literal}{mastercom_label}Entrata alle{/mastercom_label}{literal} "+dati.assenzaEntrataTradotta+";&emsp;";
                        }
                        if (dati.assenzaUscitaTradotta != ""){
                            options +=       "{/literal}{mastercom_label}Uscita alle{/mastercom_label}{literal} "+dati.assenzaUscitaTradotta+";&emsp;";
                        }
                    }
                }

                options +=           "</label>"
                        +       "</div>";
            });

            if (motivazioniAssenze.NORMALE.length){
                options +=      "<div class='padding_cella_generica bordo_basso_generico'>";
                options +=          "<span class='annotazione_leggera'>{/literal}{mastercom_label}Motivazione{/mastercom_label}{literal}:</span> ";
                options +=          "<select class='select' id='select_motivazioni' onchange='settaMotivazione(this, \"motivazione_"+idStudente+"\");'>";
                options +=              "<option></option>";
                motivazioniAssenze.NORMALE.forEach(function(item){
                    options +=          "<option value=\""+item+"\">{/literal}{mastercom_label}"+item+"{/mastercom_label}{literal}</option>";
                });
                options +=              "<option value='Altro'>{/literal}{mastercom_label}Altro{/mastercom_label}{literal}</option>";
                options +=          "</select>";
                options +=          "<input type='text' onchange='settaMotivazionePersonalizzata(this, \"motivazione_"+idStudente+"\");' style='display: none;'>";
                options +=      "</div>";
            }

            options +=      "<div style='margin-top: 20px; margin-bottom: 5px; color: rgba(125, 0, 0, 0.8);'>"
                    +           "{/literal}{mastercom_label}Ricorda di salvare l'appello affinché le giustificazioni vengano registrate{/mastercom_label}{literal}"
                    +       "</div>"
                    +       "<div align='right'>"
                    +           "<button type='button'"
                    +           " onclick='chiudiPopupGiustificazioni();'"
                    +           " class='btn_flat sfondo_arancio testo_bianco ombra_testo ripples'>Ok</button>"
                    +       "</div>"
                    +   "</div>"
                    + "</div>";

            $('#elenco_giorni').append(options);
            $('#select_motivazioni').trigger('change');
            var elHeight = $("#giustifiche_studente_popup").height();
            var top = window.innerHeight - elHeight - bottom;
            $("#giustifiche_studente_popup").animate({top: top+"px"},500,'easeInOutCubic');
        }
    }

    function settaMotivazione(select, idInputMotivazione){
        var motivazione = $(select).find(':selected').text();
        var inputText = $(select).parent().children('input[type="text"]');

        if (motivazione == 'Altro'){
            $(inputText).show().val('Altro').select();
            $('#'+idInputMotivazione).val('Altro').trigger('change');
        } else {
            $(inputText).hide();
            $('#'+idInputMotivazione).val(motivazione).trigger('change');
        }
    }

    function settaMotivazionePersonalizzata(input, idInputMotivazione){
        var motivazione = $(input).val();
        $('#'+idInputMotivazione).val(motivazione).trigger('change');
    }

    function scaricaMotivazioniAssenze(){
        return new Promise((resolve, reject) => {
            $.ajax({
                type: "GET",
                url: naxtapi_path+"assenze/lista_motivazioni",
                cache: false,
                data: {"db_richiesto": $('#db_key').val()},
                headers: { 'Authorization': $('#current_key').val() },
                complete: function (response) {
                    motivazioniAssenze = response.responseJSON;
                    resolve(true);
                }
            });
        });
    }

    function aggiornaAssenza(tipo, idStudente)
    {
        var btn = $('#btn_'+idStudente);
        var hiddenInput = $('#assente_'+idStudente);

        switch (tipo) {
            case 'M':
                btn.css('backgroundColor', 'red').html('M');
                hiddenInput.val('MATTINA').trigger('change');
                cambiaStato('modificato_'+idStudente);
                break;
            case 'P':
                btn.css('backgroundColor', 'red').html('P');
                hiddenInput.val('POMERIGGIO').trigger('change');
                cambiaStato('modificato_'+idStudente);
                break;
            case 'A':
                btn.css('backgroundColor', 'red').html('A');
                hiddenInput.val('ASSENTE').trigger('change');
                cambiaStato('modificato_'+idStudente);
                break;
            case 'NO':
                btn.css('backgroundColor', '#ffc6c6').html('A');
                hiddenInput.val('NO').trigger('change');
                cambiaStato('modificato_'+idStudente);
                break;
            default:
                break;
        }
        $('#menu_options_popup').remove();
        $('.form_appello').css('overflow','auto');
    }

    function apriMenuAssenze(btn, idStudente)
    {
        $('#menu_options_popup').remove();
        $('.form_appello').css('overflow','hidden');
        var btnPosition = btn.getBoundingClientRect();
        var menuWidth = 170;
        var top = btnPosition['top']-30;
        var left = btnPosition['left']-menuWidth - 15;
        var options = "<div id='menu_options_popup' align='left' style='position: absolute; z-index: 3; width: "+menuWidth+"px; top: "+top+"px; left: "+left+"px;' class='div_scheda_generica'>"
                        + "<div>"
                            + "<div class='menu_option evidenzia_menu_option pointer' onclick='aggiornaAssenza(\"M\", "+idStudente+");'>{/literal}{mastercom_label}M - Mattina{/mastercom_label}{literal}</div>"
                            + "<div class='menu_option evidenzia_menu_option pointer' onclick='aggiornaAssenza(\"P\", "+idStudente+");'>{/literal}{mastercom_label}P - Pomeriggio{/mastercom_label}{literal}</div>"
                            + "<div class='menu_option evidenzia_menu_option pointer' onclick='aggiornaAssenza(\"A\", "+idStudente+");'>{/literal}{mastercom_label}A - Giornaliera{/mastercom_label}{literal}</div>"
                            + "<div class='menu_option evidenzia_menu_option pointer' onclick='aggiornaAssenza(\"NO\", "+idStudente+");'>{/literal}{mastercom_label}NESSUNA ASSENZA{/mastercom_label}{literal}</div>"
                            + "<div align='center' class='menu_option evidenzia_menu_option pointer annotazione_leggera padding8' onclick='aggiornaAssenza(\"\", "+idStudente+");'>{/literal}{mastercom_label}Chiudi{/mastercom_label}{literal}</div>"
                        + "</div>"
                        + "<div style='position: absolute; z-index: 3; left:100%; top:30px; width: 0; height: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; border-left: 10px solid #FAFAFA;'>"
                        + "</div>"
                    + "</div>";

        $('#elenco_giorni').append(options);
    }

    function messaggioStudenteBloccato()
    {
        var messaggio = "{/literal}{mastercom_label}Situazione non modificabile in quanto lo studente ha un permesso/evento in corso{/mastercom_label}{literal}";
        creaToast(messaggio, 'attenzione');
    }

    function caricaDatiAppelloSpeciale(id_orario, appelloSolaVisualizzazione)
    {
        if (parseInt(id_orario) > 0) {
            // btn_{$studente['id_studente']} => id bottone stato
            // stato_studente_{$studente['id_studente']} => id hidden stato ASSENTE/PRESENTE
            // id_stato_studente_{$studente['id_studente']} => id hidden id assenza/presenza se presente a db
            // cella_stato_studente_{$studente['id_studente']} => id td nel quale e' presente il bottone
            // cella_entrate_studente_{$studente['id_studente']} => id td nel quale verranno contenute le entrate
            // cella_uscite_studente_{$studente['id_studente']} => id td nel quale verranno contenute le uscite

            var righeAppello = $("[data-riga-appello-id-studente]");
            if (righeAppello.length > 0){
                for (let i = 0; i < righeAppello.length; i++){
                    var riga = righeAppello[i];
                    var idStudente = $(riga).data("rigaAppelloIdStudente");

                    var btn = $('#btn_'+idStudente);
                    var inpStatoStudente = $('#stato_studente_'+idStudente);
                    var inpIdStatoStudente = $('#id_stato_studente_'+idStudente);
                    var cellaEntrate = $('#cella_entrate_studente_'+idStudente);
                    var cellaUscite =  $('#cella_uscite_studente_'+idStudente);

                    //inizializzo gli input
                    let bgColor = (tipo_appello_corsi_speciale == 'P') ? "#abd9ab" : "#ffc6c6";
                    btn.css("backgroundColor", bgColor);
                    inpStatoStudente.val("");
                    inpIdStatoStudente.val("");
                    cellaEntrate.html("");
                    cellaUscite.html("");
                    $('#modificato'+idStudente).val("NO");
                    $("#notify-badge-appello").hide();

                    if (dati_appello_corsi && dati_appello_corsi.studenti){
                        for (key in dati_appello_corsi.studenti) {
                            var studente = dati_appello_corsi.studenti[key];
                            if (idStudente === parseInt(studente.id_studente)){
                                if (typeof studente.ore[id_orario] !== 'undefined') {
                                    // setto i dati dello stato dello studente e creo l'interfaccia per entrate e uscite
                                    var datiStudente = studente.ore[id_orario];
                                    if (datiStudente.stato.tipo == 'P') {
                                        if (tipo_appello_corsi_speciale == 'P'){
                                            btn.css("backgroundColor", "green");
                                        }
                                        inpStatoStudente.val("P");
                                        inpIdStatoStudente.val(datiStudente.stato.id_presenza_corso);
                                    } else if (datiStudente.stato.tipo == 'A') {
                                        if (tipo_appello_corsi_speciale == 'A') {
                                            btn.css("backgroundColor", "red");
                                        }
                                        inpStatoStudente.val("A");
                                        inpIdStatoStudente.val(datiStudente.stato.id_presenza_corso);
                                    }

                                    var htmle = htmlu = disabled = "";
                                    if (    (typeof datiStudente.stato.tipo !== 'undefined' && datiStudente.stato.tipo.toUpperCase() !== 'P')
                                            ||
                                            (typeof datiStudente.stato.tipo === 'undefined' && tipo_appello_corsi_speciale.toUpperCase() == 'P')
                                        ){
                                        disabled = ' disabled ';
                                    }

                                    if (datiStudente.entrate.length > 0){
                                        datiStudente.entrate.forEach(function (entrata, index){
                                            //(input entrate gia' presenti)
                                            htmle += "<div style='white-space: nowrap;'>";
                                            if (appelloSolaVisualizzazione == '0'){
                                                htmle += "    <button type='button'";
                                                htmle += "        class='btn_flat_tondo ripples'";
                                                htmle += "        title='{/literal}{mastercom_label}Imposta ora attuale{/mastercom_label}{literal}'";
                                                htmle += "        style='background-color: #FAFAFA; color: grey; padding: 3px 8px;'";
                                                htmle += "        onclick='setCurrentTime(\"entrata_"+idStudente+"_"+entrata.id_presenza_corso+"\");'";
                                                htmle += "        >•</button>";
                                            }
                                            htmle += "    <input type='time'";
                                            htmle += "        name='appello_studenti["+idStudente+"][entrate]["+index+"][ora_entrata]'";
                                            htmle += "        id='entrata_"+idStudente+"_"+entrata.id_presenza_corso+"'";
                                            htmle += "        value='"+entrata.ora_tradotta+"'";
                                            htmle += "        data-ora-tipo='entrata'";
                                            htmle += "        data-ora-valore=''";
                                            htmle += "        data-id-studente='"+idStudente+"'";
                                            htmle += "        style='border: none;'";
                                            htmle += disabled;
                                            htmle += "        title='{/literal}{mastercom_label}Ora di entrata in formato HH:mm{/mastercom_label}{literal}'";
                                            if (appelloSolaVisualizzazione == '0'){
                                                htmle += "        onchange=\"cambiaStato('modificato_"+idStudente+"');\"";
                                            } else {
                                                htmle += "        readonly";
                                            }
                                            htmle += "        >";
                                            htmle += "    <input type='hidden'";
                                            htmle += "    name='appello_studenti["+idStudente+"][entrate]["+index+"][id_entrata]'";
                                            htmle += "    value='"+entrata.id_presenza_corso+"'";
                                            htmle += "    >";
                                            htmle += "</div>";
                                        });
                                    }

                                    if (datiStudente.uscite.length > 0){
                                        datiStudente.uscite.forEach(function (uscita, index){
                                            //(input uscite gia' presenti)
                                            htmlu += "<div style='white-space: nowrap;'>";
                                            if (appelloSolaVisualizzazione == '0'){
                                                htmlu += "    <button type='button'";
                                                htmlu += "        class='btn_flat_tondo ripples'";
                                                htmlu += "        title='{/literal}{mastercom_label}Imposta ora attuale{/mastercom_label}{literal}'";
                                                htmlu += "        style='background-color: #FAFAFA; color: grey; padding: 3px 8px;'";
                                                htmlu += "        onclick='setCurrentTime(\"uscita_"+idStudente+"_"+uscita.id_presenza_corso+"\");'";
                                                htmlu += "        >•</button>";
                                            }
                                            htmlu += "    <input type='time'";
                                            htmlu += "        name='appello_studenti["+idStudente+"][uscite]["+index+"][ora_uscita]'";
                                            htmlu += "        id='uscita_"+idStudente+"_"+uscita.id_presenza_corso+"'";
                                            htmlu += "        value='"+uscita.ora_tradotta+"'";
                                            htmlu += "        data-ora-tipo='uscita'";
                                            htmlu += "        data-ora-valore=''";
                                            htmlu += "        data-id-studente='"+idStudente+"'";
                                            htmlu += "        style='border: none;'";
                                            htmlu += disabled;
                                            htmlu += "        title='{/literal}{mastercom_label}Ora di uscita in formato HH:mm{/mastercom_label}{literal}'";
                                            if (appelloSolaVisualizzazione == '0'){
                                                htmlu += "        onchange=\"cambiaStato('modificato_"+idStudente+"');\"";
                                            } else {
                                                htmlu += "        readonly";
                                            }
                                            htmlu += "        >";
                                            htmlu += "    <input type='hidden'";
                                            htmlu += "    name='appello_studenti["+idStudente+"][uscite]["+index+"][id_uscita]'";
                                            htmlu += "    value='"+uscita.id_presenza_corso+"'";
                                            htmlu += "    >";
                                            htmlu += "</div>";
                                        });
                                    }

                                    //(input entrata vuota per nuovo inserimento)
                                    htmle += "<div style='white-space: nowrap;'>";
                                    if (appelloSolaVisualizzazione == '0'){
                                        htmle += "    <button type='button'";
                                        htmle += "        class='btn_flat_tondo ripples'";
                                        htmle += "        title='{/literal}{mastercom_label}Imposta ora attuale{/mastercom_label}{literal}'";
                                        htmle += "        style='background-color: #FAFAFA; color: grey; padding: 3px 8px;'";
                                        htmle += "        onclick='setCurrentTime(\"entrata_"+idStudente+"\");'";
                                        htmle += "        >•</button>";
                                    }
                                    htmle += "    <input type='time'";
                                    htmle += "        name='appello_studenti["+idStudente+"][entrate]["+datiStudente.entrate.length+"][ora_entrata]'";
                                    htmle += "        id='entrata_"+idStudente+"'";
                                    htmle += "        value=''";
                                    htmle += "        data-ora-tipo='entrata'";
                                    htmle += "        data-ora-valore=''";
                                    htmle += "        data-id-studente='"+idStudente+"'";
                                    htmle += "        style='border: none;'";
                                    htmle += disabled;
                                    htmle += "        title='{/literal}{mastercom_label}Ora di entrata in formato HH:mm{/mastercom_label}{literal}'";
                                    if (appelloSolaVisualizzazione == '0'){
                                        htmle += "        onchange=\"cambiaStato('modificato_"+idStudente+"');\"";
                                    } else {
                                        htmle += "        readonly";
                                    }
                                    htmle += "        >";
                                    htmle += "    <input type='hidden'";
                                    htmle += "    name='appello_studenti["+idStudente+"][entrate]["+datiStudente.entrate.length+"][id_entrata]'";
                                    htmle += "    value=''";
                                    htmle += "    >";
                                    htmle += "</div>";
                                    cellaEntrate.html(htmle);

                                    //(input uscita vuota per nuovo inserimento)
                                    htmlu += "<div style='white-space: nowrap;'>";
                                    if (appelloSolaVisualizzazione == '0'){
                                        htmlu += "    <button type='button'";
                                        htmlu += "        class='btn_flat_tondo ripples'";
                                        htmlu += "        title='{/literal}{mastercom_label}Imposta ora attuale{/mastercom_label}{literal}'";
                                        htmlu += "        style='background-color: #FAFAFA; color: grey; padding: 3px 8px;'";
                                        htmlu += "        onclick='setCurrentTime(\"uscita_"+idStudente+"\");'";
                                        htmlu += "        >•</button>";
                                    }
                                    htmlu += "    <input type='time'";
                                    htmlu += "        name='appello_studenti["+idStudente+"][uscite]["+datiStudente.uscite.length+"][ora_uscita]'";
                                    htmlu += "        id='uscita_"+idStudente+"'";
                                    htmlu += "        value=''";
                                    htmlu += "        data-ora-tipo='uscita'";
                                    htmlu += "        data-ora-valore=''";
                                    htmlu += "        data-id-studente='"+idStudente+"'";
                                    htmlu += "        style='border: none;'";
                                    htmlu +=  disabled;
                                    htmlu += "        title='{/literal}{mastercom_label}Ora di uscita in formato HH:mm{/mastercom_label}{literal}'";
                                    if (appelloSolaVisualizzazione == '0'){
                                        htmlu += "        onchange=\"cambiaStato('modificato_"+idStudente+"');\"";
                                    } else {
                                        htmlu += "        readonly";
                                    }
                                    htmlu += "        >";
                                    htmlu += "    <input type='hidden'";
                                    htmlu += "    name='appello_studenti["+idStudente+"][uscite]["+datiStudente.uscite.length+"][id_uscita]'";
                                    htmlu += "    value=''";
                                    htmlu += "    >";
                                    htmlu += "</div>";
                                    cellaUscite.html(htmlu);
                                }
                            break;
                            }
                        };
                    }
                }
            }
            $('#id_orario_appelli_corsi_speciali').val(id_orario);
        } else {
            console.log('Errore id_orario: ' + id_orario);
        }
    }

    function popupCreazioneElencoLezioneOnline(tsInizio, tsFine, titolo, timestamp, creazione, elenco)
    {
        if (tsInizio == null){
            var today = new Date();
            tsInizio = today.getTime()/1000;
        }
        if (tsFine == null){
            var today = new Date();
            tsFine = today.getTime()/1000;
        }

        var oraInizio = trasformaTimestampInDataOra(tsInizio)['ora'];
        var oraFine = trasformaTimestampInDataOra(tsFine)['ora'];

        $('#videomeeting_ora_inizio').val(oraInizio);
        $('#videomeeting_ora_fine').val(oraFine);
        $('#videomeeting_titolo').val(titolo);
        $('#videomeeting_data').val(timestamp);

        if (creazione == 'SI'){
            $('#lezione_online_creazione').show();
        } else {
            $('#lezione_online_creazione').hide();
        }

        if (elenco == 'SI'){
            if (elenco_videomeeting !== null){
                if (typeof elenco_videomeeting[timestamp] !== 'undefined'){
                    var html = '';

                    html += "<table>";

                    elenco_videomeeting[timestamp].forEach(function(lezione){
                        html += "<tr class='bordo_basso_generico evidenzia'>";
                        html +=     "<td class='padding8'>"+lezione.periodo_tradotto+"</td>";
                        html +=     "<td class='padding8'>"+lezione.titolo+"</td>";
                        html +=     "<td class='padding8 nowrap' align='right'>";

                        if (lezione.tipo == 'bbb'){
                            var sfondo = 'sfondo_scuro testo_bianco ombra_testo';
                            var testo = 'Accedi';
                            var elimina = "eliminaVideomeeting("+lezione.meeting_id+", \""+lezione.tipo+"\");";
                            var accedi = "accediMeetingBBB("+lezione.meeting_id+");";
                        } else if (lezione.tipo == 'google'){
                            var sfondo = 'sfondo_verde testo_bianco ombra_testo';
                            var testo = 'Accedi';
                            var elimina = "eliminaVideomeeting(\""+lezione.id+"\", \""+lezione.tipo+"\");";
                            var accedi = "window.open(\""+lezione.hangoutLink+"\");";
                        } else if (lezione.tipo == 'microsoft'){
                            var sfondo = 'sfondo_ambra testo_nero';
                            var testo = 'Accedi';
                            var elimina = "eliminaVideomeeting(\""+lezione.id+"\", \""+lezione.tipo+"\");";
                            var accedi = "window.open(\""+lezione.onlineMeeting.joinUrl+"\");";
                        } else {
                            var sfondo = 'sfondo_dark_cyan testo_bianco ombra_testo';
                            var testo = 'Accedi';
                            var elimina = "eliminaVideomeeting("+lezione.id_videomeeting+", \""+lezione.tipo+"\");";
                            var accedi = "window.open(\""+lezione.link+"\");";
                        }

                        if (lezione.accessoMeeting && lezione.accessoMeeting == 'NO'){
                            html += "<span class='annotazione_leggera'>Evento in ";
                            if (lezione.tipo == 'google'){
                                html += "Google Calendar";
                            } else if (lezione.tipo == 'microsoft'){
                                html += "Microsoft Calendar";
                            }
                            html += "</span>";
                        } else {
                            if (lezione.status == 'disable'){
                                html += "<button type='button' class='btn_flat btn_padding_ridotto sfondo_grigio_scuro testo_bianco ombra_testo ripples' "+
                                    "onclick='alert(\"Lezione fuori dal periodo corrente\");' "+
                                    ">Accedi</button>";
                            } else {
                                html += "<button type='button' class='btn_flat btn_padding_ridotto "+sfondo+" ripples' "+
                                    "onclick='"+accedi+"' "+
                                    ">"+testo+"</button>";
                            }

                            try {
                                if (lezione.tipo == 'bbb' && lezione.invito == 'SI'){
                                    html += "<button type='button' class='btn_flat btn_padding_ridotto sfondo_verde testo_bianco ombra_testo ripples' "+
                                    " style='margin-left: 2px;' "+
                                    "onclick='invita(\""+lezione.unique_id+"\", \""+lezione.tipo+"\")' "+
                                    ">Invita</button>";
                                }
                            } catch (e) { console.log(e); }

                            try {
                                if (lezione.tipo == 'google' && lezione.invito == 'SI'){
                                    html += "<button type='button' class='btn_flat btn_padding_ridotto testo_verde bordo_verde sfondo_bianco ripples' "+
                                    " style='margin-left: 2px;' "+
                                    "onclick='invita(\""+lezione.hangoutLink+"\", \""+lezione.tipo+"\")' "+
                                    ">Invita</button>";
                                }
                            } catch (e) { console.log(e); }

                            if (lezione.presenze && lezione.presenze == 'SI'){
                                html += "<button type='button' class='btn_flat btn_padding_ridotto sfondo_azzurro testo_bianco ombra_testo ripples margin2' "+
                                    "onclick='elencoPresenze("+lezione.meeting_id+");'"+
                                    ">Presenze</button>";
                            }

                            if (lezione.eliminabile == 'SI'){
                                html += "<button type='button' class='btn_flat btn_padding_ridotto sfondo_rosso_scuro testo_bianco ombra_testo ripples margin2' "+
                                    "onclick='"+elimina+"' "+
                                    ">Elimina</button>";
                            }
                        }

                        html += "</td>";
                        html += "</tr>";
                    });

                    html += "</table>";

                    $('#elenco_lezioni_online').html(html);

                    $('#messaggio_no_lezioni_online').hide();
                    $('#elenco_lezioni_online').show();
                    $('#lezione_online_elenco').show();
                } else {
                    $('#messaggio_no_lezioni_online').show();
                    $('#elenco_lezioni_online').hide();
                }
            } else {
                $('#messaggio_no_lezioni_online').show();
                $('#elenco_lezioni_online').hide();
            }
        } else {
            $('#lezione_online_elenco').hide();
        }

        apriChiudiPopup('popup_lezione_online', 'sfondo_oscurato', true);

        $('#videomeeting_titolo').focus();
    }

    function caricaAppelloDidatticaDistanza(idMateria, idOrario, inizioOra, fineOra, appelloSolaVisualizzazione){
        var html = "";
        $('#jqxGeneralLoader').jqxLoader('open');
        $('#id_orario_assenze_didattica_distanza').val(idOrario);
        $('#inizio_ora_assenze_didattica_distanza').val(inizioOra);
        $('#fine_ora_assenze_didattica_distanza').val(fineOra);

        //if (typeof ass_did_dist[idOrario] != "undefined"){
        if (typeof ass_did_dist[inizioOra+'_'+fineOra+'_'+idMateria] != "undefined"){
            Object.keys(ass_did_dist[inizioOra+'_'+fineOra+'_'+idMateria]).forEach(function(index){
                var item = ass_did_dist[inizioOra+'_'+fineOra+'_'+idMateria][index];
            //ass_did_dist[idOrario].forEach(function(item){
                var assente  = 'NO';
                var idAssenza = entrata = uscita = '';
                var color = '#ffc6c6';

                if (item.assenze && item.assenze.length > 0){
                    assenza = item.assenze[0];
                    idAssenza = assenza.id_assenza_didattica_distanza;

                    if (assenza.entrata == 0 && assenza.uscita == 0){
                        assente  = 'SI';
                        color = 'red';
                    } else {
                        if (assenza.entrata > 0){
                            entrata = assenza.entrata_tradotta;
                        }
                        if (assenza.uscita > 0){
                            uscita = assenza.uscita_tradotta;
                        }
                    }
                }

                html += "<tr class='bordo_alto_generico'>";
                html +=     "<td colspan='2' align='left' class='padding_cella_generica' style='width: 20px;'>";
                html +=         item.registro+'.';
                html +=     "</td>";
                html +=     "<td align='left'>";
                html +=         item.cognome+' '+item.nome;
                if (item.necessita_sostegno == '1'){
                    html +=     "{/literal}{$param_simbolo_studente_sostegno|escape:javascript}{literal}";
                }
                if (item.stampa_esito == 'SI'){
                    html +=     " <i>(" + item.esito_corrente_calcolato + ")</i>";
                }
                html +=     "</td>";
                html +=     "<td align='center'>";
                if (item.disabilitato != 'SI'){
                    html +=         "<button type='button'";
                    html +=             "title='Assenza'";
                    html +=             "id='btn_ass_did_dist_"+item.id_studente+"'";
                    html +=             "class='btn_pieno_tondo ombra_testo ripples'";
                    html +=             "style='background-color: "+color+"; padding: 3px 8px;'";
                    if (appelloSolaVisualizzazione == '0'){
                        html +=                     "onclick='switchAssenzeDidatticaDistanza(this, "+item.id_studente+", "+idOrario+");'";
                    } else {
                        html +=                     "onclick='messaggioFunzioneAppelloDisabilitato();'";
                    }
                    html +=                 ">A</button>";
                    html +=         "<input type='hidden'";
                    html +=             "value='"+assente+"'";
                    html +=             "name='appello_studenti_didattica_distanza["+item.id_studente+"]["+idOrario+"][assente]'";
                    html +=             "id='assente_did_dist_"+item.id_studente+"'>";
                    html +=         "<input type='hidden'";
                    html +=             "name='appello_studenti_didattica_distanza["+item.id_studente+"]["+idOrario+"][id_assenza]'";
                    html +=             "value='"+idAssenza+"'";
                    html +=             ">";
                    html +=         "<input type='hidden'";
                    html +=             "value='NO'";
                    html +=             "name='appello_studenti_didattica_distanza["+item.id_studente+"]["+idOrario+"][modificato]'";
                    html +=             "id='modificato_did_dist_"+item.id_studente+"'>";
                }
                html +=     "</td>";
                html +=     "<td align='center'>";
                if (item.disabilitato != 'SI'){
                    html +=         "<input type='time'";
                    html +=             "value='"+entrata+"'";
                    html +=             "name='appello_studenti_didattica_distanza["+item.id_studente+"]["+idOrario+"][entrata]'";
                    html +=             "id='entrata_did_dist_"+item.id_studente+"' ";
                    if (appelloSolaVisualizzazione == '0'){
                        html +=             "onclick='cambiaStato(\"modificato_did_dist_"+item.id_studente+"\");'";
                    } else {
                        html +=             " readonly";
                    }
                    html += ">";
                }
                html +=     "</td>";
                html +=     "<td align='center'>";
                if (item.disabilitato != 'SI'){
                    html +=         "<input type='time'";
                    html +=             "value='"+uscita+"'";
                    html +=             "name='appello_studenti_didattica_distanza["+item.id_studente+"]["+idOrario+"][uscita]'";
                    html +=             "id='uscita_did_dist_"+item.id_studente+"' ";
                    if (appelloSolaVisualizzazione == '0'){
                        html +=             "onclick='cambiaStato(\"modificato_did_dist_"+item.id_studente+"\");'";
                    } else {
                        html +=             " readonly";
                    }
                    html += ">";
                }
                html +=     "</td>";
                html +=     "<td align='center'>";
                if (item.disabilitato != 'SI'){
                    html +=         "<button type='button'";
                    html +=             "class='btn_flat_tondo ripples'";
                    html +=             "title='{mastercom_label}Cancella{/mastercom_label}'";
                    html +=             "style='background-color: #FAFAFA; color: grey; padding: 3px 5px;' ";
                    if (appelloSolaVisualizzazione == '0'){
                        html +=             "onclick='resetAssenzeDad(\""+item.id_studente+"\");' ";
                    } else {
                        html +=             "onclick='messaggioFunzioneAppelloDisabilitato();' ";
                    }
                    html +=             ">&#10005;</button>";
                }
                html +=     "</td>";
                html += "</tr>";
            });
        } else {
            html += "<tr><td colspan='4' align='center'>Nessun orario trovato</td></tr>";
        }

        $('#tbody_assenze_ora_did_dist').html(html);
        $('#jqxGeneralLoader').jqxLoader('close');
    }

    function invita(uid, tipo) // uguale in agenda, valutare se unire in file esterno
    {
        if (tipo == 'bbb'){
            var link = 'https://' + document.location.hostname + (location.port ? ':'+location.port: '') + '/mastercom/guest-access.php?uid=' + uid;
        } else if (tipo == 'google'){
            var link = uid;
        }

        copyToClipboard(link);

        var messaggio = '';
        messaggio += link+"\n\n";
        messaggio += "Il link della pagina per l'accesso ospiti è stato copiato. Incollalo in una mail o un messaggio in modo da farlo avere alla persona interessata.";
        alert(messaggio);
    }

    function accediMeetingBBB(meetingId)
    {
        $('#jqxGeneralLoader').jqxLoader('open');
        var current_user = document.getElementById('current_user').value;
		var current_key = document.getElementById('current_key').value;
		var db_key = document.getElementById('db_key').value;
		var tipo_utente = document.getElementById('tipo_utente').value;
		var id_classe = document.getElementById('hidden_id_classe').value;

		var query = {
				current_user:  current_user,
				current_key: current_key,
				db_key: db_key,
				form_tipo_utente: tipo_utente,
				meeting_id: meetingId,
				form_azione: 'accesso_videomeeting_bbb'
			};
		$.ajax({ type: "POST",
            url: "ajax_professore.php",
            data: query,//no need to call JSON.stringify etc... jQ does this for you
            cache: false,
            success: function(response)
            {//check response: it's always good to check server output when developing...
            },
            complete: function(response) {
                r = response.responseJSON;
                if (typeof r.url != 'undefined') {
                    window.open(r.url);
                } else {
                    creaToast(r.message, 'errore');
                }
                $('#jqxGeneralLoader').jqxLoader('close');
            }
        });
    }

    function eliminaVideomeeting(idVideomeeting, tipo)
    {
        if (confirm("{/literal}{mastercom_label}Eliminare la lezione ONLINE?{/mastercom_label}{literal}")){
            document.getElementById('form_container').id_ora_selezionata.value = idVideomeeting; //uso id_ora_selezionata perche' gia' presente, comodita'
            document.getElementById('form_container').tipo_videomeeting.value = tipo;
            document.getElementById('form_container').operazione.value = 'elimina_lezione_online';
            document.getElementById('form_container').submit();
            $('#jqxGeneralLoader').jqxLoader('open');
        }
    }

    function elencoPresenze(meeting_id)
    {
        $('#jqxGeneralLoader').jqxLoader('open');
        var current_user = document.getElementById('current_user').value;
		var current_key = document.getElementById('current_key').value;
		var db_key = document.getElementById('db_key').value;
		var tipo_utente = document.getElementById('tipo_utente').value;

		var query = {
				current_user:  current_user,
				current_key: current_key,
				db_key: db_key,
				form_tipo_utente: tipo_utente,
				meeting_id: meeting_id,
				form_azione: 'log_videomeeting_bbb'
			};
		$.ajax({ type: "POST",
            url: "ajat_manager.php",
            data: query,//no need to call JSON.stringify etc... jQ does this for you
            cache: false,
            success: function(response)
            {//check response: it's always good to check server output when developing...
            },
            complete: function(response) {
                r = response.responseJSON;
                if (r.meeting_id){
                    var dataInizio = trasformaTimestampInDataOra(r.data_inizio);
                    var dataFine = trasformaTimestampInDataOra(r.data_fine);

                    $('#presenze_titolo').text(r.titolo);
                    $('#presenze_dal').text(dataInizio.data+' '+dataInizio.ora);
                    $('#presenze_al').text(dataFine.data+' '+dataFine.ora);

                    var tabella = creaTabellaPresenze(r.presenze);
                    $('#presenze_elenco').html(tabella);
                    $("#stampa_presenze").attr("onclick","stampaPresenze("+meeting_id+")");

                    chiudiSfondoOscurato();
                    apriChiudiPopup('popup_presenze', 'sfondo_oscurato', true);
                } else {
                    // errore
                    creaToast("Nessuna presenza trovata", 'errore');
                }
                $('#jqxGeneralLoader').jqxLoader('close');
            }
        });
    }

    function stampaPresenze(meeting_id)
    {
        var form = document.getElementById('form_container');
        $('#id_ora_selezionata').val(meeting_id);
        $('#tipo_stampa').val('presenze_meeting');
        $('#operazione').val('stampa');

        form.setAttribute('target', '_blank');
        form.submit();
        form.removeAttribute('target', '_blank');

        $('#operazione').val('');
        $('#tipo_stampa').val('');
        $('#id_ora_selezionata').val('');
    }

    function toggleFoto(btn)
    {
        btn.classList.toggle("testo_verde");
        btn.classList.toggle("bordo_verde");
        var foto = $(btn).data().statoFoto;

        if (foto === 'NO'){
            $("[data-colonna='foto']").slideDown(500);
            $(btn).data('statoFoto', 'SI');
        } else {
            $("[data-colonna='foto']").slideUp(500);
            $(btn).data('statoFoto', 'NO');
        }
    }

    function impostaOraTutti(ora, selector)
    {
        $(selector).each(function(){
            var dati = $(this).data();
            let copia = false;
            if (dati.oraValore === '' && $('#assente_'+dati.idStudente).val() === 'NO'){
                copia = true;
            }
            if ($(this).is('[readonly]') === true){
                copia = false;
            }
            if (copia){
                $(this).val(ora).trigger('change');
            }
        });
    }

    function carica_studenti(){
        var current_user = document.getElementById('current_user').value;
		var current_key = document.getElementById('current_key').value;
		var db_key = document.getElementById('db_key').value;
		var tipo_utente = document.getElementById('tipo_utente').value;
		var id_classe = document.getElementById('hidden_id_classe').value;

		var query = {
				current_user:  current_user,
				current_key: current_key,
				db_key: db_key,
				form_tipo_utente: tipo_utente,
				id_classe: id_classe,
				form_azione: 'jqx_carica_elenco_studenti_classe'
			};
		$.ajax({ type: "POST",
             url: "ajax_professore.php",
             data: query,//no need to call JSON.stringify etc... jQ does this for you
             cache: false,
             success: function(response)
             {//check response: it's always good to check server output when developing...
             },
			 complete: function(response) {
				if (response.responseJSON) {
					var risultato = response.responseJSON;
                    //inserisco il 'Seleziona tutti' con id -1 che poi escludo al momento dell'inserimento
                    risultato.unshift({nome: selezionaTuttiTesto, id_studente: '-1'});
					var sorgente_studenti =
					{
						/*{{{ */
						datatype: "array",
						datafields: [
							{ name: 'id_studente' },
							{ name: 'nome' },
						],
						localdata: risultato,
						/*}}}*/
					};
					var interfaccia_studenti = new $.jqx.dataAdapter(sorgente_studenti, {
						loadComplete: function (data) { },
						loadError: function (xhr, status, error) { }
					});

            // Codice per il funzionamento del 'SELEZIONA TUTTI'
            $("#elencoStudenti").bind('checkChange', function (event) {
                if (event.args) {
                    var item = event.args.item;
                    if (item) {
                        var valueelement = $("<div></div>");
                        valueelement.html("Value: " + item.value);
                        var labelelement = $("<div></div>");
                        labelelement.html("Label: " + item.label);
                        var checkedelement = $("<div></div>");
                        checkedelement.html("Checked: " + item.checked);
                        $("#selectionlog").children().remove();
                        $("#selectionlog").append(labelelement);
                        $("#selectionlog").append(valueelement);
                        $("#selectionlog").append(checkedelement);
                        var items = $("#elencoStudenti").jqxDropDownList('getCheckedItems');
                        var checkedItems = "";
                        $.each(items, function (index) {
                            checkedItems += this.label + ", ";
                        });
                        $("#checkedItemsLog").text(checkedItems);
                    }
                }
            });
            // handle "Select All" item.
            var handleCheckChange = true;
            $("#elencoStudenti").bind('checkChange', function (event) {
                if (!handleCheckChange)
                    return;
                if (event.args.value != '-1') {
                    handleCheckChange = false;
                    $("#elencoStudenti").jqxDropDownList('checkIndex', 0);
                    var checkedItems = $("#elencoStudenti").jqxDropDownList('getCheckedItems');
                    var items = $("#elencoStudenti").jqxDropDownList('getItems');
                    if (checkedItems.length == 1) {
                        $("#elencoStudenti").jqxDropDownList('uncheckIndex', 0);
                    }
                    else if (items.length != checkedItems.length) {
                        $("#elencoStudenti").jqxDropDownList('indeterminateIndex', 0);
                    }
                    handleCheckChange = true;
                }
                else {
                    handleCheckChange = false;
                    if (event.args.checked) {
                        $("#elencoStudenti").jqxDropDownList('checkAll');
                    }
                    else {
                        $("#elencoStudenti").jqxDropDownList('uncheckAll');
                    }
                    handleCheckChange = true;
                }
            });

            $("#elencoStudenti").jqxDropDownList({ source: interfaccia_studenti, displayMember: "nome", valueMember: "id_studente", theme: 'Bootstrap', checkboxes: true, filterable: true, filterPlaceHolder: "Cerca...", width: 300, placeHolder: "Seleziona studenti:", searchMode: 'containsignorecase'  });
                    $("#elencoStudenti").on('checkChange', function (event) {
					if (event.args) {
						var items = $("#elencoStudenti").jqxDropDownList('getCheckedItems');
                        var ids = new Array();
                        for (i in items)
                        {
                            ids.push(items[i].value);
                        }

                        var testo_hidden = ids.join();
                        $("#id_studenti_nota").val(testo_hidden);
					}
				});

				} else { console.log(response.responseText); }
			 }
    	});
    }

    function caricaProgrammazioniNavali(idMateria, postfisso){
        var pr = $('#navali_programmazione_inserisci_arg_'+postfisso);
        var mod = $('#navali_modulo_inserisci_arg_'+postfisso);
        var arg = $('#navali_argomento_inserisci_arg_'+postfisso);
        $('#tabelle_navali_'+postfisso).html("");

        pr.find('option').remove();
        mod.find('option').remove();
        mod.attr("disabled", true);
        arg.find('option').remove();
        arg.attr("disabled", true);

        if (abb_mat_arg_nav != null && typeof abb_mat_arg_nav[idMateria] != 'undefined'){
            pr.append(new Option("- Programmazione -", "-1"));
            Object.keys(abb_mat_arg_nav[idMateria]['programmazioni']).forEach(function (index){
                pr.append(new Option(abb_mat_arg_nav[idMateria]['programmazioni'][index]['descrProgrammazione'], abb_mat_arg_nav[idMateria]['programmazioni'][index]['idProgrammazSidi']));
            });
            pr.removeAttr('disabled');
        } else {
            pr.attr("disabled", true);
        }
    }

    function caricaModuliNavali(idProgrammazione, postfisso){
        var idMateria = $('#id_materia_argomento_ministeriale').val();
        var mod = $('#navali_modulo_inserisci_arg_'+postfisso);
        var arg = $('#navali_argomento_inserisci_arg_'+postfisso);
        $('#tabelle_navali_'+postfisso).html("");

        mod.find('option').remove();
        arg.find('option').remove();
        arg.attr("disabled", true);

        if (typeof abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione] != 'undefined'){
            mod.append(new Option("- Modulo -", "-1"));
            Object.keys(abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli']).forEach(function (index){
                mod.append(new Option(abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][index]['desModulo'], abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][index]['idModulo']));
            });
            mod.removeAttr('disabled');
        } else {
            mod.attr("disabled", true);
        }
    }

    function caricaArgomentiNavali(idModulo, postfisso){
        var idMateria = $('#id_materia_argomento_ministeriale').val();
        var idProgrammazione = $('#navali_programmazione_inserisci_arg_'+postfisso).val();
        var arg = $('#navali_argomento_inserisci_arg_'+postfisso);
        var tabelle = $('#tabelle_navali_' + postfisso);

        arg.find('option').remove();

        if (typeof abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo] != 'undefined'){
            arg.append(new Option("- Argomento -", "-1"));
            Object.keys(abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['argomenti']).forEach(function (index){
                arg.append(new Option(abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['argomenti'][index]['desArgomento'], abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['argomenti'][index]['idArgomento']));
            });
            arg.removeAttr('disabled');

            var html = "";

            if (abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['criteri_verifica'] !== null && abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['criteri_verifica'].length > 0){
                html += "<b>Criteri di verifica</b>";
                html += "<ul>";
                abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['criteri_verifica'].forEach(function(item){
                    html += "<li>" + item.desVerifica + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['competenze'] !== null && abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['competenze'].length > 0){
                html += "<b>Competenze</b>";
                html += "<ul>";
                abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['competenze'].forEach(function(item){
                    html += "<li>" + item.desCompetenza + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['conoscenze'] !== null && abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['conoscenze'].length > 0){
                html += "<b>Conoscenze</b>";
                html += "<ul>";
                abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['conoscenze'].forEach(function(item){
                    html += "<li>" + item.desConoscenza + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['abilita'] !== null && abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['abilita'].length > 0){
                html += "<b>Abilità</b>";
                html += "<ul>";
                abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['abilita'].forEach(function(item){
                    html += "<li>" + item.desAbilita + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['metodologie'] !== null && abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['metodologie'].length > 0){
                html += "<b>Metodologie</b>";
                html += "<ul>";
                abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['metodologie'].forEach(function(item){
                    html += "<li>" + item.desMetodologia + "</li>";
                });
                html += "</ul>";
            }

            if (abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['mezzi'] !== null && abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['mezzi'].length > 0){
                html += "<b>Mezzi</b>";
                html += "<ul>";
                abb_mat_arg_nav[idMateria]['programmazioni'][idProgrammazione]['moduli'][idModulo]['mezzi'].forEach(function(item){
                    html += "<li>" + item.desMezzo + "</li>";
                });
                html += "</ul>";
            }

            tabelle.html(html);
        } else {
            arg.attr("disabled", true);
            tabelle.html("");
        }
    }

    function salvaAppello(tipoAppelloOperazione){
        document.getElementById('form_container').operazione.value = tipoAppelloOperazione;
        document.getElementById('form_container').submit();
        $('#jqxGeneralModalLoader').jqxLoader('open');
    }

    function messaggioFunzioneAppelloDisabilitato(){
        creaToast(messFunzioneAppelloDisabilitato, 'error');
    }

    function checkFiltroReligione(soloReligioneMaterieOra){
        if (soloReligioneMaterieOra == "SI"){
            // nascondo gli esonerati se presenti
            $("[data-studente-esonero-religione='1']").hide();
            $("[data-studente-esonero-religione='1'] [id^='modificato_']").attr('disabled', 'disabled');
        } else {
            // mostro gli esonerati se presenti
            $("[data-studente-esonero-religione='1']").show();
            $("[data-studente-esonero-religione='1'] [id^='modificato_']").removeAttr('disabled');
        }
    }

    /*function checkCircolari(){
        messengerGetCircolariDaLeggereInClasse($('#select_id_classe').val()).then((c) => {
            circolari = c;

            if ($('#modal-open').val() == 'NO'){
                popupCircolari();
            } else {
                clearInterval(repeat);
                repeat = setInterval(() => {
                    if ($('#modal-open').val() == 'NO'){
                        popupCircolari();
                        clearInterval(repeat);
                    }
                }, 5000);
            }
        });
    }*/

    function popupCircolari(){
        $('#containerCircolari').html('');
        if (circolari.length > 0){
            var i;
            for (i in circolari){
                var circ = circolari[i];

                var cont = $("<div></div>")
                    .attr("id", circ.id)
                    .addClass('padding8 margin8 scheda_informativa');

                var d = new Date(circ.received);
                var dt = trasformaTimestampInDataOra(d.getTime()/1000);
                var dateDisplay = dt.data_ita+' '+dt.ora;
                var owner_date = $("<div></div>")
                    .addClass('font90p annotazione_leggera')
                    .css({ 'text-align': "right" })
                    .html(circ.owner_surname+' '+circ.owner_name+', '+dateDisplay);
                cont.append(owner_date);

                var title = $("<div></div>")
                    .addClass('bold')
                    .html(circ.name);
                var content = $("<p></p>")
                    .css({ "margin": "20px 0px" })
                    .html(circ.content);
                var contentBox = $("<div></div>")
                    .addClass('padding16')
                    .css({ "text-align": "left" })
                    .append(title)
                    .append(content);
                cont.append(contentBox);

                if (circ.files.length > 0){
                    var j;
                    var contAllegati = $("<div></div>")
                        .css({ "text-align": "left" });
                    for (j in circ.files){
                        var filePath = "/messenger/1.0/messages/"+circ.files[j]+"/raw";
                        var btn = $("<button></button>")
                            .attr("type", "button")
                            .addClass('btn_flat sfondo_azzurro testo_bianco ombra_testo ripples margin8')
                            .css({ "margin": "0px 0px 10px 10px" })
                            .append("<i class='fa fa-fw fa-paperclip'></i>&nbsp; Allegato")
                            .on('click', {"filePath": filePath}, function(event) { download('allegato', event.data.filePath); });
                        contAllegati.append(btn);
                    }

                    cont.append(contAllegati);
                }

                var btnCont = $("<div align='right'></div>");
                var btnRemind = $('<button></button>')
                    .attr("type", "button")
                    .addClass('btn_flat sfondo_grigio_scuro testo_bianco ombra_testo ripples margin-left8')
                    .append("<i class='fa fa-fw fa-clock'></i>&nbsp; Ricorda più tardi")
                    .on('click', {"id": circ.id}, function(event) { ricordaCircolare(event.data.id); });
                btnCont.append(btnRemind);
                var btnRead = $('<button></button>')
                    .attr("type", "button")
                    .addClass('btn_flat sfondo_verde testo_bianco ombra_testo ripples margin-left8')
                    .append("<i class='fa fa-fw fa-check'></i>&nbsp; Segna come letto")
                    .on('click', {"id": circ.id, "delivery": circ.delivery}, function(event) { segnaLettaCircolare(event.data.id, event.data.delivery); });
                btnCont.append(btnRead);

                cont.append(btnCont);

                $('#containerCircolari').append(cont);
            }

            if ($('#circolari_classe').is(":visible") === false){
                apriChiudiPopup('circolari_classe', 'sfondo_oscurato', true);
            }
        }
    }

    function ricordaCircolare(id, channel){
        $('#'+id).slideUp(400, function(){
            this.remove();
            checkAltreCircolariVisibili();
        });
    }

    function segnaLettaCircolare(id, delivery){
        var idClasse = $('#select_id_classe').val();
        getUuidsClass(idClasse).then((uuidp) => {
            call_messengerSetCircolareLetta(id, uuidp).then((res) => {
                if (res === true){
                    $('#'+id).slideUp(400, function(){
                        this.remove();

                        // Nota: viene anche marcato come letto sul Messenger per evitare
                        //       che un "letto" da registro rimanga comunque da leggere
                        //       anche sul Messenger.
                        call_messengerSetStatusMessages([id], 'received').then(() => {
                            call_messengerSetStatusMessages([id], 'readed').then(() => {
                                if (delivery){
                                    call_messengerSetStatusMessages([id], 'confirmed');
                                }
                            });
                        });

                        checkAltreCircolariVisibili();
                    });
                } else {
                    creaToast(res.responseJSON.message, 'errore');
                }
            });
        });

    }

    function checkAltreCircolariVisibili(){
        if ($('#containerCircolari').children().length == 0){
            apriChiudiPopup('circolari_classe', 'sfondo_oscurato', true);
        }
    }

    function download(name, url)
    {
        var link = document.createElement("a");
        link.download = name;
        link.href = url;
        document.body.appendChild(link);
        link.click();
    }


    function sistemaCalendario(id){
        var bounding = document.querySelector("#"+id).getBoundingClientRect();
        if (bounding.right > $(window).width())
        {
            document.getElementById(id).style.right = '-150px';
        }
    }

    window.addEventListener("orientationchange", function() {
        //if ($('#elenco_giorni').height() > $(window.innerWidth)[0] - $('#elenco_giorni').position().top - 10){
        //    $('#elenco_giorni').css('height', '');
            $('#elenco_giorni').delay(500).height($(window.innerWidth)[0] - $('#elenco_giorni').position().top - 15);
        //}
        {/literal}
            {if $is_mobile == 1}
                var altezzaSchermo = $(window.innerWidth)[0];
                var larghezzaSchermo = $(window.innerHeight)[0];

                if (altezzaSchermo > larghezzaSchermo)
                {
                    mostraMessaggioRuotaSchermo();
                }
            {/if}
        {literal}
    });

    $(document).ready( function(){
            $('#message').delay(5000).fadeOut();
            $('#messageVideomeeting').delay(15000).fadeOut();
            $('.tooltip').css('z-index', '1');

            if ($('#elenco_giorni').length)
            {
                if ($('#elenco_giorni').height() > $(window.innerHeight)[0] - $('#elenco_giorni').position().top - 10){
                    $('#elenco_giorni').height($(window.innerHeight)[0] - $('#elenco_giorni').position().top - 10);
                }
            }

            try {
                carica_studenti();
                scaricaMotivazioniAssenze();
            } catch(err){}

            //--- Calendario JQX
            $("#jqxCalendar").jqxCalendar({ width: '400px', height: '400px', enableTooltips: false, theme: 'Bootstrap', culture: 'it-IT', enableViews: false, columnHeaderHeight: 40, titleHeight: 40, showOtherMonthDays: false });
            $("#jqxCalendar").jqxCalendar('setDate', new Date());
            sistemaCalendario("jqxCalendar");
            $("#jqxCalendar").hide();
            $('#jqxCalendar').on('change', function (event)
            {
                var jsDate = new Date;
                jsDate = event.args.date;
                var type = event.args.type; // keyboard, mouse or null depending on how the date was selected.
                var input_hidden = $('#data_da_calendario');

                var dataIso = jsDate.getDate() + '/' + (jsDate.getMonth() + 1) + '/' + jsDate.getFullYear();
                input_hidden.val(dataIso);
                input_hidden[0].form.submit();
                $('#jqxGeneralLoader').jqxLoader('open');
            });
            //---

            setTimeout(() => {
                checkCircolari();
                setInterval(() => {
                    clearInterval(repeat);
                    checkCircolari();
                }, 60*1000*5) //5 minuti
            }, 2000);
        });
    {/literal}

    {if $is_mobile == 1}
        function creaMessaggioRuotaSchermo(){
            $('body').append('<div id="rotateScreen" align="center"></div>');
            $('#rotateScreen').css({
                "position": "fixed",
                "color": "white",
                "background-color": "#262626",
                "font-size": "20px",
                "border-radius": "15px",
                "padding": "8px 16px",
                "width": "50%",
                "left": "50%",
                "top": "50%",
                "transform": "translate(-50%, -50%)",
                "opacity": "0.9",
                "display": "none"
            });
            $('#rotateScreen').html('&#8634;<br>{mastercom_label}Ruotare lo schermo per una migliore visualizzazione{/mastercom_label}');
        }

        function mostraMessaggioRuotaSchermo()
        {
            $('#rotateScreen').fadeIn();
            $('#rotateScreen').delay(2000).fadeOut();
        }

        $(document).ready( function(){
            creaMessaggioRuotaSchermo();
            var altezzaSchermo = $(window.innerHeight)[0];
            var larghezzaSchermo = $(window.innerWidth)[0];

            if (altezzaSchermo > larghezzaSchermo)
            {
                mostraMessaggioRuotaSchermo();
            }
        });
    {/if}
</script>

<br>

<div id="sfondo_oscurato" style="width: 100%; height: 100%; top: 0; left: 0; position: fixed; background-color: #aaa; opacity: 0.5; z-index: 2; display: none;"
     onclick='chiudiSfondoOscurato();'></div>

<div id="toastsContainer" class="" style="position: absolute; z-index: 6; bottom: 0px; left: 0px; padding: 10px;"></div>

{if $messaggio}
    <div id="message" class="messaggio_basso_scomparsa" style="font-size: 110%">
        {$messaggio}
    </div>
{/if}
{if $messageVideomeeting}
    <div id="messageVideomeeting" class="messaggio_basso_scomparsa" style="font-size: 110%">
        {mastercom_label}{$messageVideomeeting}{/mastercom_label}
    </div>
{/if}

<form method='post' action='{$SCRIPT_NAME}' id="form_container">
    <div class="div_titolo_scheda_generica sfondo_scuro" style="width: 98%;" align="center">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div align="left" class="ombra_testo" style="padding-left: 10px; font-size: 120%;">
                {mastercom_label}Registro di classe{/mastercom_label}
            </div>
            <div style='white-space: nowrap;'>
                {* Calendario con informazioni su giorno Settimana Mese *}
                {if !isset($tipo_visualizzazione) || ($tipo_visualizzazione == 'giorno')}
                    {foreach $elenco_giorni as $giorno}
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno precedente *}
                        <input type="button"
                               class="btn_flat_bianco ombra_testo"
                               id="btn_giorno_precedente"
                               value="&#9001;"
                               title="{mastercom_label}Giorno precedente{/mastercom_label}"
                               style="font-size: 110%; padding: 8px;"
                               onclick="this.form.giorno_scelto.value='{$giorno['timestamp_giorno_precedente']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                               >
                        {/if}
                        {* Pulsante calendario con giorno corrente *}
                        <div style="position: relative; display: inline-block;">
                            <button type="button" class="btn_pieno ripples" id="btn_calendario" style="background-color: #FAFAFA; color:#3F51B5; border-radius: 32px" title="{mastercom_label}Apri il calendario{/mastercom_label}" onclick="apriChiudiPopup('jqxCalendar', '', true, ['btn_calendario']);">
                                {$giorno['giorno_tradotto']} {$giorno['data']}
                            </button>
                            <div style="position: absolute; z-index: 1000;" id="jqxCalendar"></div>
                        </div>
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno successivo *}
                        <input
                            type="button"
                            class="btn_flat_bianco ombra_testo"
                            id="btn_giorno_successivo"
                            value="&#9002;"
                            title="{mastercom_label}Giorno successivo{/mastercom_label}"
                            style="font-size: 110%; padding: 8px;"
                            onclick="this.form.giorno_scelto.value='{$giorno['timestamp_giorno_successivo']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                            >
                        {/if}
                        <input type="hidden" name="giorno_scelto" value="{$giorno['timestamp']}">
                        <input type="hidden" name="data_da_calendario"  id="data_da_calendario" value="" onchange="this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');">
                    {/foreach}
                {elseif ($tipo_visualizzazione == 'settimana')}
                    {foreach $elenco_giorni as $giorno}
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno precedente *}
                        <input type="button"
                               class="btn_flat_bianco ombra_testo"
                               id="btn_giorno_precedente"
                               value="&#9001;"
                               title="{mastercom_label}Settimana precedente{/mastercom_label}"
                               style="font-size: 110%; padding: 8px;"
                               onclick="this.form.giorno_scelto.value='{$giorno['timestamp_settimana_precedente']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                               >
                        {/if}
                        {* Pulsante calendario con giorno corrente *}
                        <div style="position: relative; display: inline-block;">
                            <button type="button" class="btn_pieno ripples" id="btn_calendario" style="background-color: #FAFAFA; color:#3F51B5; border-radius: 32px" title="{mastercom_label}Apri il calendario{/mastercom_label}" onclick="apriChiudiPopup('jqxCalendar', '', true, ['btn_calendario']);">
                                {$giorno['inizio_settimana']} - {$giorno['fine_settimana']}
                            </button>
                            <div style="position: absolute; z-index: 1000;" id="jqxCalendar"></div>
                        </div>
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno successivo *}
                        <input
                            type="button"
                            class="btn_flat_bianco ombra_testo"
                            id="btn_giorno_successivo"
                            value="&#9002;"
                            title="{mastercom_label}Settimana successiva{/mastercom_label}"
                            style="font-size: 110%; padding: 8px;"
                            onclick="this.form.giorno_scelto.value='{$giorno['timestamp_settimana_successiva']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                            >
                        {/if}
                        <input type="hidden" name="giorno_scelto" value="{$giorno['timestamp']}">
                        <input type="hidden" name="data_da_calendario"  id="data_da_calendario" value="" onchange="this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');">
                        {break}
                    {/foreach}
                {elseif ($tipo_visualizzazione == 'mese')}
                    {foreach $elenco_giorni as $giorno}
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno precedente *}
                        <input type="button"
                               class="btn_flat_bianco ombra_testo"
                               id="btn_giorno_precedente"
                               value="&#9001;"
                               title="{mastercom_label}Mese precedente{/mastercom_label}"
                               style="font-size: 110%; padding: 8px;"
                               onclick="this.form.giorno_scelto.value='{$giorno['timestamp_mese_precedente']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                               >
                        {/if}
                        {* Pulsante calendario con giorno corrente *}
                        <div style="position: relative; display: inline-block;">
                            <button type="button" class="btn_pieno ripples" id="btn_calendario" style="background-color: #FAFAFA; color:#3F51B5; border-radius: 32px" title="{mastercom_label}Apri il calendario{/mastercom_label}" onclick="apriChiudiPopup('jqxCalendar', '', true, ['btn_calendario']);">
                                {$giorno['intestazione_mese']}
                            </button>
                            <div style="position: absolute; z-index: 1000;" id="jqxCalendar"></div>
                        </div>
                        {if $is_mobile == 'false'}
                        {* Pulsante giorno successivo *}
                        <input
                            type="button"
                            class="btn_flat_bianco ombra_testo"
                            id="btn_giorno_successivo"
                            value="&#9002;"
                            title="{mastercom_label}Mese successivo{/mastercom_label}"
                            style="font-size: 110%; padding: 8px;"
                            onclick="this.form.giorno_scelto.value='{$giorno['timestamp_mese_successivo']}'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                            >
                        {/if}
                        <input type="hidden" name="giorno_scelto" value="{$giorno['timestamp']}">
                        <input type="hidden" name="data_da_calendario"  id="data_da_calendario" value="" onchange="this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');">
                        {break}
                    {/foreach}
                {/if}
            </div>
            <div class="ombra_testo">
                {* Scelta visualizzazione per Giorno Settimana Mese *}
                <button
                    type="button"
                    class="btn_flat_bianco ombra_testo ripples"
                    id="btn_giorno"
                    title="{mastercom_label}Visualizzazione per giorno{/mastercom_label}"
                    style="font-size: 100%; padding: 0px 8px; {if $tipo_visualizzazione == 'giorno'}color: yellow;{/if}"
                    onclick="this.form.tipo_visualizzazione.value='giorno'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                    >{if $is_mobile === 1}
                        {mastercom_label}G{/mastercom_label}
                    {else}
                        {mastercom_label}Giorno{/mastercom_label}
                    {/if}</button>|
                <button
                    type="button"
                    class="btn_flat_bianco ombra_testo ripples"
                    id="btn_settimana"
                    title="{mastercom_label}Visualizzazione per settimana{/mastercom_label}"
                    style="font-size: 100%; padding: 0px 8px; {if $tipo_visualizzazione == 'settimana'}color: yellow;{/if}"
                    onclick="this.form.tipo_visualizzazione.value='settimana'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                    >{if $is_mobile === 1}
                        {mastercom_label}S{/mastercom_label}
                    {else}
                        {mastercom_label}Settimana{/mastercom_label}
                    {/if}</button>|
                <button
                    type="button"
                    class="btn_flat_bianco ombra_testo ripples"
                    id="btn_mese"
                    title="{mastercom_label}Visualizzazione per mese{/mastercom_label}"
                    style="font-size: 100%; padding: 0px 8px; {if $tipo_visualizzazione == 'mese'}color: yellow;{/if}"
                    onclick="this.form.tipo_visualizzazione.value='mese'; this.form.submit();$('#jqxGeneralLoader').jqxLoader('open');"
                    >{if $is_mobile === 1}
                        {mastercom_label}M{/mastercom_label}
                    {else}
                        {mastercom_label}Mese{/mastercom_label}
                    {/if}</button>
            </div>
            {if $id_classe > 0}
                <div style="padding-right: 10px; width: 25%;" class="ombra_testo">
                    {* Scelta Classe *}
                    {mastercom_label}Classe{/mastercom_label}:
                    <select class="select" id='select_id_classe' style="background-color: white; width: 80%;" onchange="$('#hidden_id_classe').val(this.value).trigger('change');$('#jqxGeneralModalLoader').jqxLoader('open');">
                        <option value="0">-</option>
                        {foreach $elenco_classi as $classe}
                            <option value="{$classe['id_classe']}"
                                    {if $id_classe == $classe['id_classe']}selected{/if}
                                    >
                                {if $classe['ordinamento'] == 'CORSO'}
                                    (C) {$classe['descrizione_materia']}
                                {else}
                                    {$classe['classe']}{$classe['sezione']} {$classe['descrizione_indirizzi']}
                                {/if}
                            </option>
                        {/foreach}
                    </select>
                    <input type='hidden' name='id_classe' id='hidden_id_classe' value='{$id_classe}' onchange="this.form.submit();">
                </div>
            {/if}
        </div>
    </div>
    <div class="div_corpo_scheda_generica" id='elenco_giorni' align="center" style="width: 98%; color: black; font-weight: normal; overflow: auto; -webkit-overflow-scrolling: touch;">
        {if $id_classe > 0}
            {assign var="salva_firme" value='NO'}
            {foreach $elenco_giorni as $giorno}
                {* - INTESTAZIONE GIORNO - *}
                <div style="padding: 10px; display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <table style="border-collapse: collapse; border-left: 3px solid red;">
                            <tr>
                                <td rowspan="2" style="font-size: 60px; line-height: 60px; padding-left: 10px;">
                                    {$giorno['giorno']}
                                </td>
                                <td>
                                    <b>{$giorno['mese_tradotto']} {$giorno['anno']}</b>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" style="font-size: 18px; line-height: 18px;">
                                    <b>{$giorno['giorno_tradotto']}</b>
                                </td>
                            </tr>
                        </table>
                    </div>
                    {if $giorno['videomeeting']['creazione'] == 'SI' || $giorno['videomeeting']['elenco'] == 'SI'}
                    {*<div>
                        <button type="button"
                                class='btn_pieno testo_bianco sfondo_azzurro ombra_testo padding_ridotto ripples'
                                onclick="popupCreazioneElencoLezioneOnline(null, null, '', {$giorno['timestamp']}, '{$giorno['videomeeting']['creazione']}', '{$giorno['videomeeting']['elenco']}');"
                            >{mastercom_label}Lezioni ONLINE{/mastercom_label}</button>
                    </div>*}
                    {/if}
                    {if $giorno['compleanni'] != '' && $compleanni_registro == 'SI'}
                        <div align="center">
                            <img src="icone/cake.png" style="width: 30px;"/>
                            <span style="padding-left: 5px;"><b>{$giorno['compleanni']}</b></span>
                        </div>
                    {/if}
                    <div align="right">
                        {if $giorno['santi'] != '' && $santi_registro == 'SI'}
                            <table style="border-collapse: collapse; border-right: 3px solid #4d8e4d;">
                                <tr>
                                    <td align="right" style="padding-right: 10px; height: 30px;">
                                        {mastercom_label}Santi del giorno{/mastercom_label}
                                    </td>
                                </tr>
                                <tr>
                                    <td align="right" style="padding-right: 10px; height: 30px;">
                                        <b><i>{$giorno['santi']}</i></b>
                                    </td>
                                </tr>
                            </table>
                        {/if}
                    </div>
                </div>
                <table align="center" style="width: 100%;">
                    {if $id_classe > 0}
                        {if $giorno['ore']|@count > 0}
                            <tr width="100%" class="bordo_basso_generico titolo_colonna_tabella" style="opacity: 1;">
                                {* - Intestazione tabella giornata - *}
                                <td style="width: 125px; min-width: 125px;" class="padding_cella_generica bordo_destro_generico">
                                    {mastercom_label}Ora{/mastercom_label}
                                </td>
                               {* <td style="width: 130px;" class="padding_cella_generica bordo_destro_generico">
                                    {mastercom_label}Materia prevista{/mastercom_label}
                                </td>*}
                                <td style="width: 130px; min-width: 130px;" class="padding_cella_generica bordo_destro_generico">
                                    {mastercom_label}Materia{/mastercom_label}
                                </td>
                                <td style="width: 140px; min-width: 140px;" class="padding_cella_generica bordo_destro_generico">
                                    {mastercom_label}Assenze{/mastercom_label}
                                </td>
                                <td style="width: 20%; min-width: 140px;" class="padding_cella_generica bordo_destro_generico" style='overflow: hidden;'>
                                    {mastercom_label}Argomenti{/mastercom_label}
                                </td>
                                <td style="width: 20%; min-width: 140px;" class="padding_cella_generica bordo_destro_generico">
                                    {mastercom_label}Compiti{/mastercom_label}
                                </td>
                                <td style="width: 15%; min-width: 120px;" class="padding_cella_generica bordo_sinistro_generico">
                                    {mastercom_label}Note disc.{/mastercom_label}
                                </td>
                            </tr>
                            {assign var="cont_ore" value=0}
                            {foreach $giorno['ore'] as $ora}
                                {* - Ciclo le fasce orarie - *}
                                {assign var="cont_ore" value=$cont_ore + 1}
                                <tr width="100%" {if $ora['ora_attuale'] == 'SI'}style="background-color: #d6ffd6"{/if}>
                                    <td align="center" class="padding_cella_generica bordo_alto_generico bordo_destro_generico" style="width: 125px; min-width: 125px;">
                                        {* fascia oraria *}
                                        <b>{$cont_ore}ª<br>{$ora['fascia']}</b>

                                        {if $ora['videomeeting']['abilitato'] == 'SI'}
                                            {if $ora['videomeeting']['creazione'] == 'SI'}
                                                <br>
                                                <button type="button"
                                                    class='btn_pieno testo_bianco sfondo_azzurro ombra_testo padding_ridotto ripples'
                                                    style='margin-top: 8px;'
                                                    onclick="popupCreazioneElencoLezioneOnline({$ora['data_inizio']}, {$ora['data_fine']}, '{$ora['videomeeting']['titolo_suggerito']|escape}', {$giorno['timestamp']}, '{$giorno['videomeeting']['creazione']}', '{$giorno['videomeeting']['elenco']}');"
                                                >{mastercom_label}Crea lezione<br>ONLINE{/mastercom_label}</button>
                                            {/if}
                                            {if $ora['videomeeting']['elenco']|@count > 0}
                                                {foreach $ora['videomeeting']['elenco'] as $videomeeting}
                                                    <div style='margin-top: 10px; position: relative;'>
                                                        {if $videomeeting['tipo'] == 'voice'}
                                                            <button type="button"
                                                                class='btn_pieno testo_bianco
                                                                    {if $videomeeting['status'] == 'disable'}sfondo_grigio_scuro{else}sfondo_dark_cyan{/if} ombra_testo padding_ridotto ripples'
                                                                onclick="{if $videomeeting['status'] == 'disable'}alert('Lezione fuori dal periodo corrente');{else}window.open('{$videomeeting['link']}');{/if}"
                                                            >Accedi ONLINE<br>{$videomeeting['periodo_tradotto']}<br>{$videomeeting['titolo_ridotto']}</button>
                                                        {elseif $videomeeting['tipo'] == 'bbb'}
                                                            <button type="button"
                                                                class='btn_pieno testo_bianco
                                                                    {if $videomeeting['status'] == 'disable'}sfondo_grigio_scuro{else}sfondo_scuro{/if} ombra_testo padding_ridotto ripples'
                                                                onclick="{if $videomeeting['status'] == 'disable'}alert('Lezione fuori dal periodo corrente');{else}accediMeetingBBB({$videomeeting['meeting_id']});{/if}"
                                                            >Accedi ONLINE<br>{$videomeeting['periodo_tradotto']}<br>{$videomeeting['titolo_ridotto']}</button>
                                                        {elseif $videomeeting['tipo'] == 'microsoft'}
                                                            <button type="button"
                                                                class='btn_pieno testo_bianco
                                                                    {if $videomeeting['status'] == 'disable'}sfondo_grigio_scuro ombra_testo{else}sfondo_ambra testo_nero{/if} padding_ridotto ripples'
                                                                onclick="{if $videomeeting['status'] == 'disable'}alert('Lezione fuori dal periodo corrente');{else}window.open('{$videomeeting['onlineMeeting']['joinUrl']}');{/if}"
                                                            >Accedi ONLINE<br>{$videomeeting['periodo_tradotto']}<br>{$videomeeting['titolo_ridotto']}</button>
                                                        {elseif $videomeeting['tipo'] == 'google'}
                                                            {if $videomeeting['accessoMeeting'] == 'SI'}
                                                                <button type="button"
                                                                    class='btn_pieno testo_bianco
                                                                        {if $videomeeting['status'] == 'disable'}sfondo_grigio_scuro{else}sfondo_verde{/if} ombra_testo padding_ridotto ripples'
                                                                    onclick="{if $videomeeting['status'] == 'disable'}alert('Lezione fuori dal periodo corrente');{else}window.open('{$videomeeting['hangoutLink']}');{/if}"
                                                                >Accedi ONLINE<br>{$videomeeting['periodo_tradotto']}<br>{$videomeeting['titolo_ridotto']}</button>
                                                            {else}
                                                                <div class='bordrad5 testo_nero sfondo_grigio_chiaro btn_padding_ridotto font90p noselect'
                                                                >
                                                                    <span class="annotazione_leggera font70p">Evento Google Calendar</span>
                                                                    <br>
                                                                    {$videomeeting['periodo_tradotto']}<br>{$videomeeting['titolo_ridotto']}
                                                                </div>
                                                            {/if}
                                                        {/if}
                                                        {if $videomeeting['presenze'] == 'SI'}
                                                            <br>
                                                            <button type="button"
                                                                class="btn_pieno testo_bianco sfondo_azzurro btn_padding_ridotto ombra_testo ripples"
                                                                style="margin-top: 2px; width: 123px;"
                                                                    onclick="elencoPresenze({$videomeeting['meeting_id']});"
                                                                >Presenze</button>
                                                        {/if}
                                                        {if $videomeeting['eliminabile'] == 'SI'}
                                                            <br>
                                                            <button type="button"
                                                                class="btn_pieno testo_bianco sfondo_rosso_scuro btn_padding_ridotto ombra_testo ripples"
                                                                style="margin-top: 2px; width: 123px;"
                                                                {if $videomeeting['tipo'] == 'voice'}
                                                                    onclick="eliminaVideomeeting({$videomeeting['id_videomeeting']}, '{$videomeeting['tipo']}');"
                                                                {elseif $videomeeting['tipo'] == 'bbb'}
                                                                    onclick="eliminaVideomeeting({$videomeeting['meeting_id']}, '{$videomeeting['tipo']}');"
                                                                {elseif $videomeeting['tipo'] == 'google'}
                                                                    onclick="eliminaVideomeeting('{$videomeeting['id']}', '{$videomeeting['tipo']}');"
                                                                {elseif $videomeeting['tipo'] == 'microsoft'}
                                                                    onclick="eliminaVideomeeting('{$videomeeting['id']}', '{$videomeeting['tipo']}');"
                                                                {/if}
                                                                >Elimina</button>
                                                        {/if}
                                                    </div>
                                                {/foreach}
                                            {/if}
                                        {elseif $ora['videomeeting']['abilitato'] == 'SOLO_PRESENZE'}
                                            {if $ora['videomeeting']['elenco']|@count > 0}
                                                {foreach $ora['videomeeting']['elenco'] as $videomeeting}
                                                    {if $videomeeting['presenze'] == 'SI'}
                                                        <div style='margin-top: 10px; position: relative;'>
                                                            <button type="button"
                                                                class="btn_pieno testo_bianco sfondo_azzurro btn_padding_ridotto ombra_testo ripples"
                                                                style="margin-top: 2px; width: 123px;"
                                                                    onclick="elencoPresenze({$videomeeting['meeting_id']});"
                                                                >Presenze</button>
                                                        </div>
                                                    {/if}
                                                {/foreach}
                                            {/if}
                                        {/if}
                                    </td>
                                    <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico bordo_destro_generico">
                                        <table width="100%">
                                            {assign var="salva_firme" value='SI'}
                                            {assign var="solo_tipo_religione" value='SI'}
                                            {foreach $ora['materie'] as $materia}
                                                {if $materia['stampa'] != 'NO'}
                                                    <tr style="padding: 5px;">
                                                        <td style="padding-top: 8px; padding-bottom: 8px;">
                                                            <div style="min-width: 130px; overflow: auto;">
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td>
                                                                            <div>
                                                                                <b>{$materia['materia']}</b>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <i>{$materia['professore']}</i>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                            {if $materia['modifica_firma'] == 'SI' && $materia['firma'] == 'NO'}
                                                                {if $materia['tipo_materia'] != 'RELIGIONE'}
                                                                    {assign var="solo_tipo_religione" value='NO'}
                                                                {/if}
                                                                <div align="center">
                                                                    <div align="center"
                                                                        style="
                                                                            margin-top: 2px;
                                                                            padding: 1px 6px;
                                                                            background-color: #ff4949;
                                                                            color: white;
                                                                            width: max-content;
                                                                            height: 19px;
                                                                            font-weight: bold;
                                                                            border-radius: 10px;
                                                                            font-size: 90%;
                                                                            margin-left: auto;
                                                                            margin-right: auto;
                                                                            display: inline-block;
                                                                            ">
                                                                        <label class="noselect"
                                                                                    style="display: block;
                                                                                    padding-left: 15px;
                                                                                    text-indent: -15px;
                                                                                    margin-top: 3px;">
                                                                            <input type="checkbox"
                                                                                    name="firme_da_inserire[]"
                                                                                    title="{mastercom_label}Inserisci firma{/mastercom_label}"
                                                                                    {if $materia['ora_come_sostegno'] == 'SI'}
                                                                                        value="{$materia['id_materia_firma_sostegno']}_{$materia['id_professore']}_{$ora['data_inizio']}_{$ora['data_fine']}"
                                                                                    {else}
                                                                                        value="{$materia['id_materia']}_{$materia['id_professore']}_{$ora['data_inizio']}_{$ora['data_fine']}"
                                                                                    {/if}
                                                                                    style="width: 13px;
                                                                                         height: 13px;
                                                                                         padding: 0;
                                                                                         margin:0;
                                                                                         vertical-align: bottom;
                                                                                         position: relative;
                                                                                         top: -1px;
                                                                                         *overflow: hidden;
                                                                                       "
                                                                                    > {mastercom_label}Firma{/mastercom_label}
                                                                                    {if $materia['ora_come_sostegno'] == 'SI'}
                                                                                        <span style='font-weight: normal;'>({mastercom_label}Sostegno{/mastercom_label})</span>
                                                                                    {elseif $materia['ora_come_itp'] == 'SI'}
                                                                                        <span style='font-weight: normal;'>({mastercom_label}ITP{/mastercom_label})</span>
                                                                                    {/if}
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            {else}
                                                                {if $materia['tipo_materia'] != 'RELIGIONE'}
                                                                    {assign var="solo_tipo_religione" value='NO'}
                                                                {/if}
                                                                <div align="center">
                                                                    <div class="noselect"
                                                                        style="
                                                                            margin-top: 2px;
                                                                            padding: 1px;
                                                                            background-color: #4d8e4d;
                                                                            color: white;
                                                                            width: 70px;
                                                                            height: 19px;
                                                                            line-height: 19px;
                                                                            font-weight: bold;
                                                                            border-radius: 10px;
                                                                            margin-right: 3px;
                                                                            font-size: 90%;
                                                                            text-align: center;
                                                                            margin-left: auto;
                                                                            margin-right: auto;
                                                                            display: inline-block;
                                                                            ">
                                                                        {mastercom_label}Firmata{/mastercom_label}
                                                                    </div>
                                                                    {if $materia['ora_cancellabile'] == 'SI'}
                                                                        <div style="display: inline-block;">
                                                                            <button  type="button"
                                                                                    class="btn_flat_tondo testo_bianco ripples"
                                                                                    title="{mastercom_label}Elimina firma{/mastercom_label}"
                                                                                    style="background-color: #ff4949;
                                                                                         font-size: 90%;
                                                                                         padding: 3px 5px;
                                                                                     "
                                                                                    onclick="if (confirm('{mastercom_label}Eliminare la firma?{/mastercom_label}') === true){
                                                                                            document.getElementById('form_container').id_firma_selezionata.value='{$materia['id_firma']}';
                                                                                            document.getElementById('form_container').operazione.value='elimina_firma_corrente';
                                                                                            document.getElementById('form_container').submit();
                                                                                            $('#jqxGeneralLoader').jqxLoader('open');}
                                                                                            "
                                                                                    >&#10005;</button>
                                                                        </div>
                                                                    {/if}
                                                                </div>
                                                            {/if}
                                                        </td>
                                                    </tr>
                                                {/if}
                                            {/foreach}
                                        </table>
                                    </td>
                                    <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico bordo_destro_generico">
                                        {* assenti *}
                                        {if isset($ora['assenze_studenti']['uditori'])}
                                            <div style="padding-top: 4px; padding-bottom: 4px;" class="testo_rosso">
                                                {mastercom_label}Studenti Uditori{/mastercom_label}
                                                <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 5px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                    <b>{$ora['assenze_studenti']['uditori']|@count}</b>
                                                    <span class="tooltiptext">
                                                        {foreach $ora['assenze_studenti']['uditori'] as $studente}
                                                            {$studente}<br>
                                                        {/foreach}
                                                    </span>
                                                </div>
                                            </div>
                                        {/if}
                                        {if isset($ora['assenze_studenti']['intera_ora'])}
                                            <div style="padding-top: 4px; padding-bottom: 4px;">
                                                {mastercom_label}Assenti intera ora{/mastercom_label}
                                                <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 5px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                    <b>{$ora['assenze_studenti']['intera_ora']|@count}</b>
                                                    <span class="tooltiptext">
                                                        {foreach $ora['assenze_studenti']['intera_ora'] as $studente}
                                                            {$studente}<br>
                                                        {/foreach}
                                                    </span>
                                                </div>
                                            </div>
                                        {/if}
                                        {if isset($ora['assenze_studenti']['ritardo'])}
                                            <div style="padding-top: 4px; padding-bottom: 4px;">
                                                {mastercom_label}Entrati in ritardo{/mastercom_label}
                                                <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 16px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                    <b>{$ora['assenze_studenti']['ritardo']|@count}</b>
                                                    <span class="tooltiptext">
                                                        {foreach $ora['assenze_studenti']['ritardo'] as $studente}
                                                            {$studente}<br>
                                                        {/foreach}
                                                    </span>
                                                </div>
                                            </div>
                                        {/if}
                                        {if isset($ora['assenze_studenti']['uscita'])}
                                            <div style="padding-top: 4px; padding-bottom: 4px;">
                                                {mastercom_label}Usciti in anticipo{/mastercom_label}
                                                <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 14px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                    <b>{$ora['assenze_studenti']['uscita']|@count}</b>
                                                    <span class="tooltiptext">
                                                        {foreach $ora['assenze_studenti']['uscita'] as $studente}
                                                            {$studente}<br>
                                                        {/foreach}
                                                    </span>
                                                </div>
                                            </div>
                                        {/if}
                                        {if isset($ora['assenze_studenti']['didattica_distanza'])}
                                            <div style="padding-top: 4px; padding-bottom: 4px;">
                                                {mastercom_label}Assenti did. dist.{/mastercom_label}
                                                <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 14px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                    <b>{$ora['assenze_studenti']['didattica_distanza']|@count}</b>
                                                    <span class="tooltiptext">
                                                        {foreach $ora['assenze_studenti']['didattica_distanza'] as $studente}
                                                            {$studente}<br>
                                                        {/foreach}
                                                    </span>
                                                </div>
                                            </div>
                                        {/if}
                                        {if isset($ora['assenze_studenti']['didattica_distanza_entrata'])}
                                            <div style="padding-top: 4px; padding-bottom: 4px;">
                                                {mastercom_label}Entrati did. dist.{/mastercom_label}
                                                <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 14px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                    <b>{$ora['assenze_studenti']['didattica_distanza_entrata']|@count}</b>
                                                    <span class="tooltiptext">
                                                        {foreach $ora['assenze_studenti']['didattica_distanza_entrata'] as $studente}
                                                            {$studente}<br>
                                                        {/foreach}
                                                    </span>
                                                </div>
                                            </div>
                                        {/if}
                                        {if isset($ora['assenze_studenti']['didattica_distanza_uscita'])}
                                            <div style="padding-top: 4px; padding-bottom: 4px;">
                                                {mastercom_label}Usciti did. dist.{/mastercom_label}
                                                <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="margin-left: 14px; color: black; border-radius: 20px; padding: 2px 6px;">
                                                    <b>{$ora['assenze_studenti']['didattica_distanza_uscita']|@count}</b>
                                                    <span class="tooltiptext">
                                                        {foreach $ora['assenze_studenti']['didattica_distanza_uscita'] as $studente}
                                                            {$studente}<br>
                                                        {/foreach}
                                                    </span>
                                                </div>
                                            </div>
                                        {/if}
                                        {if $giorno['appello'][$id_classe]['abilitato'] === 'SI' && $ora['ora_del_docente'] == 'SI'
                                            && ($funzione_modifica_assenze == '1' || $ora['ora_attuale'] == 'SI')}
                                            <div width="100%" align="center">
                                                <button type="button"
                                                       class="btn_flat_indaco bordo_scuro btn_padding_ridotto ripples"
                                                       title="{mastercom_label}Apri l'appello della giornata{/mastercom_label}"
                                                       onclick="apriChiudiPopup('appello_{$giorno['timestamp']}', 'sfondo_oscurato', true);
                                                        checkFiltroReligione('{$solo_tipo_religione}');
                                                        {if $classe_corso == '1' && $appello_corsi_speciale == 'SI'}caricaDatiAppelloSpeciale({$materia['id_orario']}, '{$funzione_appello_sola_visualizzazione}');{/if}"
                                                       >{mastercom_label}Appello{/mastercom_label}</button>
                                            </div>

                                            {foreach $ora['materie'] as $materia}
                                                {if $materia['studenti_didattica_distanza']|@count > 0
                                                    && (
                                                        ($classe_corso == '1' && $appello_corsi_speciale != 'SI')
                                                        ||
                                                        ($classe_corso != '1')
                                                    )
                                                    &&
                                                    ($materia['id_professore_firma'] == $current_user)}
                                                    <div width="100%" align="center" class="margin6">
                                                        <button type="button"
                                                            class="btn_flat_indaco bordo_scuro btn_padding_ridotto ripples"
                                                            title="{mastercom_label}Apri l'appello dell'ora della didattica a distanza{/mastercom_label}"
                                                            onclick="apriChiudiPopup('assenze_didattica_distanza', 'sfondo_oscurato', true);
                                                                    caricaAppelloDidatticaDistanza('{$materia['id_materia']}', '{$materia['id_orario']}', {$ora['data_inizio']}, {$ora['data_fine']}, '{$funzione_appello_sola_visualizzazione}');"
                                                            >{mastercom_label}Did. distanza{/mastercom_label}</button>
                                                    </div>
                                                    {break}
                                                {/if}
                                            {/foreach}
                                        {/if}
                                    </td>
                                    <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico bordo_destro_generico">
                                        {* argomenti *}
                                        {if isset($ora['argomenti']) && $ora['argomenti']|@count > 0}
                                            {foreach $ora['argomenti'] as $argomento}
                                                <div style="overflow: auto; padding-bottom: 10px;">
                                                    {if $argomento['cancellabile'] == 'SI' && $funzione_argomenti_registro == '1'}
                                                        <div style="display: table-cell; padding-right: 6px;">
                                                            <button type="button"
                                                                    class="btn_flat_tondo testo_bianco ripples"
                                                                    title="{mastercom_label}Elimina Argomento{/mastercom_label}"
                                                                    style="background-color: #ff4949;
                                                                         font-size: 90%;
                                                                         padding: 3px 5px;
                                                                     "
                                                                    onclick="if (confirm('{mastercom_label}Eliminare l\'argomento?{/mastercom_label}') === true){
                                                                            document.getElementById('form_container').id_argomento_selezionato.value='{$argomento['id_argomento']}';
                                                                            document.getElementById('form_container').operazione.value='elimina_argomento';
                                                                            document.getElementById('form_container').submit();
                                                                            $('#jqxGeneralLoader').jqxLoader('open');}
                                                                            "
                                                                            >&#10005;</button>
                                                        </div>
                                                    {/if}
                                                    <div style="display: table-cell;">
                                                        {$argomento['modulo']|escape:"html"}: {$argomento['descrizione']|escape:"html"}
                                                        {if $argomento['note'] !== '' && $argomento['id_professore'] == $current_user}
                                                            <br>
                                                            <span class="annotazione_leggera">{$argomento['note']}</span>
                                                        {/if}
                                                        {if $argomento['tag_display'] != ''}
                                                            <br>
                                                            <span class="annotazione_leggera">Tag: {$argomento['tag_display']}</span>
                                                        {/if}
                                                        {if $argomento['id_programmazione_argomenti'] > 0}
                                                            <br>
                                                            <span class="annotazione_leggera">{mastercom_label}Progr.{/mastercom_label}: {$argomento['dettagli_programmazione']['programmazione_display']}</span>
                                                        {/if}
                                                        {if $argomento['id_modulo_ministeriale'] > 0}
                                                            <br>
                                                            <span class="annotazione_leggera">Arg. minist. navali: </span>
                                                            <br>
                                                            {$argomento['testo_argomento_ministeriale_composto']}
                                                        {/if}
                                                    </div>
                                                    <br><br>
                                                </div>
                                            {/foreach}
                                        {/if}
                                        {foreach $ora['materie'] as $materia}
                                            {if $materia['editabile'] == 'SI' && $materia['id_professore_firma'] == $current_user}
                                                {*<div width="100%" align="center" style="overflow: auto;">
                                                    <button type="button"
                                                           title="{mastercom_label}Inserisci argomento{/mastercom_label}"
                                                           class="btn_flat_indaco bordo_scuro btn_padding_ridotto ripples"
                                                           onclick="apriChiudiPopup('compito_argomento_firma_{$materia['id_firma']}', 'sfondo_oscurato', true);
                                                            $('#id_materia_argomento_ministeriale').val({$materia['id_materia']});
                                                            $('#id_materia_programmazione_didattica').val({$materia['id_materia']}); "
                                                           >{mastercom_label}Inserisci per {/mastercom_label}{$materia['materia']}</button>
                                                </div>*}
                                            {/if}
                                        {/foreach}
                                    </td>
                                    <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico bordo_destro_generico" style="overflow: auto;">
                                        {* compiti *}
                                        {if isset($ora['compiti']) && $ora['compiti']|@count > 0}
                                            {foreach $ora['compiti'] as $compito}
                                                <div width="100%" style="overflow: auto; padding-bottom: 10px;">
                                                    {if $compito['cancellabile'] == 'SI' && $funzione_argomenti_registro == '1'}
                                                        <div style="display: table-cell; padding-right: 6px;">
                                                            <button type="button"
                                                                    class="btn_flat_tondo testo_bianco ripples"
                                                                    title="{mastercom_label}Elimina Compito{/mastercom_label}"
                                                                    style="background-color: #ff4949;
                                                                         font-size: 90%;
                                                                         padding: 3px 5px;
                                                                     "
                                                                    onclick="if (confirm('{mastercom_label}Eliminare il compito?{/mastercom_label}') === true){
                                                                            document.getElementById('form_container').id_argomento_selezionato.value='{$compito['id_argomento']}';
                                                                            document.getElementById('form_container').operazione.value='elimina_compito';
                                                                            document.getElementById('form_container').submit();
                                                                            $('#jqxGeneralLoader').jqxLoader('open');}
                                                                            "
                                                                            >&#10005;</button>
                                                        </div>
                                                    {/if}
                                                    <div style="display: table-cell;">
                                                        <span style="font-size: 85%;">
                                                        {mastercom_label}Docente{/mastercom_label}: {$compito['docente']|escape} <br>
                                                        {mastercom_label}Materia{/mastercom_label}: {$compito['materia']|escape} <br>
                                                        {mastercom_label}Inserito il{/mastercom_label}: {$compito['data_inserimento_tradotta']} </span><br>
                                                        {$compito['assegnazioni']|escape:"html"}
                                                        {if $compito['note'] !== '' && $compito['id_professore'] == $current_user}
                                                            <br>
                                                            <span class="annotazione_leggera">{$compito['note']}</span>
                                                        {/if}
                                                        <br><br>
                                                    </div>
                                                </div>
                                            {/foreach}
                                        {/if}
                                        {foreach $ora['materie'] as $materia}
                                            {if $materia['editabile'] == 'SI' && $funzione_argomenti_registro == '1' && $materia['id_professore_firma'] == $current_user}
                                                {*<div width="100%" align="center" style="overflow: auto;">
                                                    <button type="button"
                                                           class="btn_flat_indaco bordo_scuro btn_padding_ridotto ripples"
                                                           onclick="apriChiudiPopup('compito_argomento_firma_{$materia['id_firma']}', 'sfondo_oscurato', true);
                                                                $('#id_materia_argomento_ministeriale').val({$materia['id_materia']});
                                                                $('#id_materia_programmazione_didattica').val({$materia['id_materia']});"
                                                           >{mastercom_label}Inserisci per{/mastercom_label} {$materia['materia']}</button>
                                                </div>*}
                                            {/if}
                                        {/foreach}
                                    </td>
                                    <td class="padding_cella_generica bordo_alto_generico bordo_sinistro_generico ">
                                        {* note *}
                                        {if isset($ora['note']) && $ora['note']|@count > 0}
                                            {foreach $ora['note'] as $nota}
                                                <div style="padding-top: 4px; padding-bottom: 4px;">
                                                    {$nota['testo_ufficiale']}
                                                    <br>
                                                    ({mastercom_label}studenti{/mastercom_label}:
                                                    <div class="tooltip btn_pieno sfondo_bianco" onclick="" style="color: black; border-radius: 20px; padding: 3px 8px;">
                                                        <b>{$nota['elenco_soggetti']|@count}</b>
                                                        <span class="tooltiptext" style="right: 100%;">
                                                            {foreach $nota['elenco_soggetti'] as $key => $studente}
                                                                {$studente}{if $nota['elenco_id_soggetti'][$key]|in_array:$id_studenti_sostegno}{$param_simbolo_studente_sostegno}{/if}<br>
                                                            {/foreach}
                                                        </span>
                                                    </div>
                                                        )
                                                </div>
                                            {/foreach}
                                        {/if}

                                        <div width="100%" align="center">
                                            <button type="button"
                                                    title="{mastercom_label}Inserisci nota disciplinare{/mastercom_label}"
                                                    class="btn_flat_indaco bordo_scuro btn_padding_ridotto ripples"
                                                    onclick="document.getElementById('data_nota_disciplinare').value='{$ora['data_inserimento_nota']}';
                                                            document.getElementById('ora_nota_disciplinare').value='{$ora['ora_inserimento_nota']}';
                                                            apriChiudiPopup('nuova_nota_disciplinare', 'sfondo_oscurato', true);"
                                                >{mastercom_label}Inserisci nota{/mastercom_label}</button>
                                        </div>
                                    </td>
                                </tr>
                            {/foreach}
                        {else}
                            <tr width="100%" class="bordo_basso_generico titolo_colonna_tabella" style="opacity: 1; border: none;">
                                {* - Intestazione tabella giornata - *}
                                <td colspan="7" style="padding: 10px 0px;" class="padding_cella_generica">
                                    {mastercom_label}Non ci sono ore in questa giornata{/mastercom_label}
                                </td>
                            </tr>
                        {/if}
                    {else}
                        <tr width="100%" class="bordo_basso_generico titolo_colonna_tabella" style="opacity: 1; border: none;">
                            {* - Intestazione tabella giornata - *}
                            <td colspan="7" style="padding: 10px 0px;" class="padding_cella_generica">
                                {mastercom_label}Seleziona una classe{/mastercom_label}
                            </td>
                        </tr>
                    {/if}
                    </table>
                    <div style="height: 30px;"></div>

                    {*form appello*}
                    {if $giorno['appello'][$id_classe]['abilitato'] === 'SI'}
                        <div  class="div_scheda_generica form_appello"
                                id="appello_{$giorno['timestamp']}"
                                style="position: fixed;
                                      max-width: 90%;
                                      max-height: 95%;
                                      left: 50%;
                                      top: 50%;
                                      transform: translate(-50%, -50%);
                                      display: none;
                                      z-index: 3;
                                      overflow-y: auto;"
                            >
                            <div align="center" style="width: 100%; height: 100%;">
                                <div align="center" style="padding: 15px 10px 10px 10px; font-size: 120%; width: auto;">
                                    <b>{if $classe_corso == '1' && $appello_corsi_speciale == 'SI'}{mastercom_label}APPELLO CORSO{/mastercom_label}{else}{mastercom_label}APPELLO{/mastercom_label}{/if}</b><br>
                                    <span style="font-size: 70%;" class="annotazione_leggera">{mastercom_label}aggiornato alle{/mastercom_label} {$giorno['appello'][$id_classe]['ora']}</span>
                                </div>
                                <table width="90%" align="center">
                                    <tr class="titolo_colonna_tabella">
                                        <td colspan="4" align="center" class="padding_cella_generica">
                                            {mastercom_label}Studenti{/mastercom_label}
                                            <button type="button"
                                                class="btn_flat_tondo testo_grigio_scuro bordo_grigio_scuro"
                                                style="padding: 2px 4px; float: left;"
                                                onclick="toggleFoto(this);"
                                                data-stato-foto="NO"
                                                >{mastercom_label}Foto{/mastercom_label}</button>
                                        </td>
                                        {if $classe_corso == '1' && $appello_corsi_speciale == 'SI'}
                                            <td align="center" class="padding_cella_generica">
                                                {if $tipo_appello_corsi_speciale == 'A'}
                                                    {mastercom_label}Assenti{/mastercom_label}
                                                {elseif $tipo_appello_corsi_speciale == 'P'}
                                                    {mastercom_label}Presenti{/mastercom_label}
                                                {/if}
                                            </td>
                                            <td align="center" class="padding_cella_generica">
                                                {mastercom_label}Entrata{/mastercom_label}
                                            </td>
                                            <td align="center" class="padding_cella_generica">
                                                {mastercom_label}Uscita{/mastercom_label}
                                            </td>
                                            <td align="center" class="padding_cella_generica">

                                            </td>
                                        {else}
                                            <td align="center" class="padding_cella_generica">
                                                {mastercom_label}Da giust.{/mastercom_label}
                                            </td>
                                            <td align="center" class="padding_cella_generica">
                                                {mastercom_label}Assenza{/mastercom_label}
                                            </td>
                                            {if $param_inserisci_ritardi_registro == 'SI'}
                                                <td align="center" class="padding_cella_generica">
                                                    {mastercom_label}Entrata{/mastercom_label}
                                                    {if $optional_extratime == 'OK'}
                                                    <br>
                                                    <div style="white-space: nowrap; border: 1px solid gray; display: inline-block; padding: 1px;">
                                                        <button type="button"
                                                            class="btn_flat_tondo ripples"
                                                            title="{mastercom_label}Imposta ora attuale{/mastercom_label}"
                                                            style="background-color: #FAFAFA; color: grey; padding: 3px 8px;"
                                                            onclick="setCurrentTime('entrata_standard');"
                                                                >•</button>
                                                        <input type="time"
                                                            id="entrata_standard"
                                                            value=""
                                                            style="border: none;"
                                                            title="{mastercom_label}Ora di entrata in formato HH:mm{/mastercom_label}"
                                                            >
                                                        <button type="button"
                                                            class="btn_flat_tondo sfondo_grigio_scuro testo_bianco ombra_testo ripples"
                                                            title="{mastercom_label}Copia entrata in vuoti{/mastercom_label}"
                                                            style="padding: 3px 8px;"
                                                            onclick="impostaOraTutti($('#entrata_standard').val(), '[data-ora-tipo=\'entrata\']');"
                                                                >Copia &#8675;</button>
                                                    </div>
                                                    {/if}
                                                </td>
                                            {/if}
                                            <td align="center" class="padding_cella_generica">
                                                {mastercom_label}Uscita{/mastercom_label}
                                                {if $optional_extratime == 'OK'}
                                                <br>
                                                <div style="white-space: nowrap; border: 1px solid gray; display: inline-block; padding: 1px;">
                                                    <button type="button"
                                                        class="btn_flat_tondo ripples"
                                                        title="{mastercom_label}Imposta ora attuale{/mastercom_label}"
                                                        style="background-color: #FAFAFA; color: grey; padding: 3px 8px;"
                                                        onclick="setCurrentTime('uscita_standard');"
                                                            >•</button>
                                                    <input type="time"
                                                        id="uscita_standard"
                                                        value=""
                                                        style="border: none;"
                                                        title="{mastercom_label}Ora di entrata in formato HH:mm{/mastercom_label}"
                                                        >
                                                    <button type="button"
                                                        class="btn_flat_tondo sfondo_grigio_scuro testo_bianco ombra_testo ripples"
                                                        title="{mastercom_label}Copia entrata in vuoti{/mastercom_label}"
                                                        style="padding: 3px 8px;"
                                                        onclick="impostaOraTutti($('#uscita_standard').val(), '[data-ora-tipo=\'uscita\']');"
                                                            >Copia &#8675;</button>
                                                </div>
                                                {/if}
                                            </td>
                                            {if $abilita_servizio_mensa == 'SI'}
                                                <td align="center" class="padding_cella_generica">
                                                    {mastercom_label}Mensa{/mastercom_label}
                                                </td>
                                            {/if}
                                            <td align="center" class="padding_cella_generica">

                                            </td>
                                        {/if}
                                    </tr>
                                    {foreach $giorno['appello'][$id_classe]['studenti'] as $studente}
                                        <tbody class="evidenzia">
                                            <tr class="bordo_alto_generico" data-riga-appello-id-studente="{$studente['id_studente']}" data-studente-esonero-religione="{$studente['esonero_religione']}">
                                                <td align="center" rowspan="2">
                                                    {if $studente['foto'] != ''}
                                                        <img src="foto_studenti/{$studente['foto']}" data-colonna="foto" style="max-width: 14rem; max-height: 14rem; display: none;"/>
                                                    {/if}
                                                </td>
                                                <td align="left" rowspan="2" class="padding_cella_generica" style="width: 20px;">
                                                    {$studente['registro']}.
                                                </td>
                                                <td align="left" rowspan="2" class="padding_cella_generica" style="width: 20px;">
                                                    <button type='button'
                                                        title="Visualizza storia delle assenze"
                                                        class="btn_flat_tondo sfondo_bianco bordo_grigio_scuro ripples"
                                                        style="padding: 3px 8px;"
                                                        onclick="toggleStoriaAssenze('storia_appello_{$studente['id_studente']}');  toggleStoriaAssenze('contatti_appello_{$studente['id_studente']}');"
                                                        >i</button>
                                                </td>
                                                <td align="left" class="padding_cella_generica">
                                                    {$studente['cognome']} {$studente['nome']}{if $studente['necessita_sostegno'] == '1'}{$param_simbolo_studente_sostegno}{/if}
                                                    {if $param_mostra_ammissione_registro == 'SI' && $studente['stampa_esito'] == 'SI'}<i>({$studente['esito_corrente_calcolato']})</i>{/if}
                                                </td>
                                                {if $classe_corso == '1' && $appello_corsi_speciale == 'SI'}
                                                    <td align="center" id="cella_stato_studente_{$studente['id_studente']}">

                                                            <button type='button'
                                                                title="{mastercom_label}Assenza{/mastercom_label}"
                                                                id="btn_{$studente['id_studente']}"
                                                                class="btn_pieno_tondo ombra_testo ripples"
                                                                style="background-color: {if $tipo_appello_corsi_speciale == 'A'}#ffc6c6{else}#abd9ab{/if};
                                                                            padding: 3px 8px;"
                                                                        {if $funzione_appello_sola_visualizzazione == '1'}
                                                                            onclick="messaggioFunzioneAppelloDisabilitato();"
                                                                        {else}
                                                                            onclick="switchStatoStudenteCorsi(this, {$studente['id_studente']}, '{$tipo_appello_corsi_speciale}');"
                                                                        {/if}
                                                                >{$tipo_appello_corsi_speciale}</button>
                                                            <input type="hidden"
                                                                name="appello_studenti[{$studente['id_studente']}][stato]"
                                                                id="stato_studente_{$studente['id_studente']}">
                                                            <input type="hidden"
                                                                name="appello_studenti[{$studente['id_studente']}][id_stato]"
                                                                id="id_stato_studente_{$studente['id_studente']}"
                                                                >
                                                    </td>
                                                    <td align="center" id="cella_entrate_studente_{$studente['id_studente']}">
                                                        <!-- VIA JAVASCRIPT -->-->
                                                    </td>
                                                    <td align="center" id="cella_uscite_studente_{$studente['id_studente']}">
                                                    <!--  VIA JAVASCRIPT -->
                                                    </td>
                                                    <td align="center">
                                                        <button type="button"
                                                            class="btn_flat_tondo ripples"
                                                            title="{mastercom_label}Cancella{/mastercom_label}"
                                                            style="background-color: #FAFAFA; color: grey; padding: 3px 5px;"
                                                            {if $studente['bloccato'] == 'SI'}
                                                                onclick="messaggioStudenteBloccato();"
                                                            {elseif $funzione_appello_sola_visualizzazione == '1'}
                                                                onclick="messaggioFunzioneAppelloDisabilitato();"
                                                            {else}
                                                                onclick="resetAssenzeSpeciali('{$studente['id_studente']}');"
                                                            {/if}
                                                            >&#10005;</button>
                                                    </td>
                                                {else}
                                                    <td align="center" class="padding_cella_generica">
                                                        {if $studente['assenze']|@count > 0}
                                                            <button type='button'
                                                                title="Visualizza assenze da giustificare"
                                                                class="btn_flat_tondo sfondo_bianco bordo_grigio_scuro ripples"
                                                                style="padding: 3px 8px;"
                                                                onclick="creaPopupGiustificazioni('{$studente['id_studente']}', '{$funzione_appello_sola_visualizzazione}');"
                                                                >{$studente['assenze']|@count}</button>
                                                            {foreach $studente['assenze'] as $assenza}
                                                                {if $assenza['tipo_assenza'] != "dad_ddi"}
                                                                    <input type='hidden'
                                                                        data-assenze-id-studente='{$studente['id_studente']}'
                                                                        data-macro-tipo='normale'
                                                                        data-assenza-data='{$assenza['data_tradotta']}'
                                                                        data-assenza-testo='{$assenza['descrizione_tipo']}'
                                                                        data-assenza-timestamp='{$assenza['data']}'
                                                                        data-id-assenza='{$assenza['id_assenza']}'
                                                                        data-studente = "{$studente['cognome']} {$studente['nome']}"
                                                                        id='giustifica_{$studente['id_studente']}_{$assenza['id_assenza']}'
                                                                        name='appello_studenti[{$studente['id_studente']}][giustifiche][{$assenza['id_assenza']}]'
                                                                        value='NO'>
                                                                {else}
                                                                    <input type='hidden'
                                                                        data-assenze-id-studente='{$studente['id_studente']}'
                                                                        data-macro-tipo='{$assenza['tipo_assenza']}'
                                                                        data-assenza-data='{$assenza['data_inizio_orario_tradotta']}'
                                                                        data-assenza-data-ita='{$assenza['data_inizio_orario_tradotta_ita']}'
                                                                        data-assenza-inizio-ora='{$assenza['ora_inizio_orario_tradotta']}'
                                                                        data-assenza-fine-ora='{$assenza['ora_fine_orario_tradotta']}'
                                                                        data-assenza-entrata-tradotta='{$assenza['entrata_tradotta']}'
                                                                        data-assenza-uscita-tradotta='{$assenza['uscita_tradotta']}'
                                                                        data-id-assenza='{$assenza['id_assenza_didattica_distanza']}'
                                                                        data-studente = "{$studente['cognome']} {$studente['nome']}"
                                                                        id='giustifica_{$studente['id_studente']}_{$assenza['id_assenza_didattica_distanza']}_dad'
                                                                        name='appello_studenti[{$studente['id_studente']}][giustifiche_dad][{$assenza['id_assenza_didattica_distanza']}]'
                                                                        value='NO'>
                                                                {/if}
                                                            {/foreach}
                                                            <input type='hidden'
                                                                    id='motivazione_{$studente['id_studente']}'
                                                                    name='appello_studenti[{$studente['id_studente']}][motivazione]'
                                                                    value=''>
                                                        {/if}
                                                    </td>
                                                    {if $studente['didattica_distanza'] == 'SI'}
                                                        <td align="center" colspan="5">
                                                            {if $studente['disabilitato'] !== 'SI'}
                                                                {mastercom_label}Studente in Didattica a Distanza, compilare l'apposita sezione dopo avere firmato{/mastercom_label}
                                                            {/if}
                                                        </td>
                                                    {else}
                                                        <td align="center">
                                                            {if $studente['disabilitato'] !== 'SI'}
                                                                <button type='button'
                                                                    title="{mastercom_label}Assenza{/mastercom_label}"
                                                                    id="btn_{$studente['id_studente']}"
                                                                    class="btn_pieno_tondo ombra_testo ripples"
                                                                    style="background-color: {if $studente['assente'] != 'NO'}red{else}#ffc6c6{/if};
                                                                                padding: 3px 8px;"
                                                                        {if $studente['bloccato'] == 'SI'}
                                                                            onclick="messaggioStudenteBloccato();"
                                                                        {elseif $funzione_appello_sola_visualizzazione == '1'}
                                                                            onclick="messaggioFunzioneAppelloDisabilitato();"
                                                                        {elseif $pomeriggio == 'SI'}
                                                                            onclick='apriMenuAssenze(this, {$studente['id_studente']});'
                                                                        {else}
                                                                            onclick="switchAssenze(this, {$studente['id_studente']});"
                                                                        {/if}
                                                                    >{if $studente['tipo']}{$studente['tipo']}{else}A{/if}</button>
                                                                <input type="hidden"
                                                                    value="{$studente['assente']}"
                                                                    name="appello_studenti[{$studente['id_studente']}][assente]"
                                                                    id="assente_{$studente['id_studente']}">
                                                                <input type="hidden"
                                                                    name="appello_studenti[{$studente['id_studente']}][id_assenza]"
                                                                    value="{$studente['id_assenza']}"
                                                                    >
                                                            {/if}
                                                        </td>
                                                        {if $param_inserisci_ritardi_registro == 'SI'}
                                                            <td align="center">
                                                                {if $studente['disabilitato'] !== 'SI'}
                                                                    {assign var='cont' value=1}
                                                                    {if $studente['entrate']|@count > 0}
                                                                        {foreach $studente['entrate'] as $entrata}
                                                                            <div style='white-space: nowrap;'>
                                                                                {if $funzione_appello_sola_visualizzazione == '0'}
                                                                                    <button type="button"
                                                                                        class="btn_flat_tondo ripples"
                                                                                        title="{mastercom_label}Imposta ora attuale{/mastercom_label}"
                                                                                        style="background-color: #FAFAFA; color: grey; padding: 3px 8px;"
                                                                                        {if $studente['bloccato'] == 'SI'}
                                                                                                onclick="messaggioStudenteBloccato();"
                                                                                        {else}
                                                                                                onclick="setCurrentTime('entrata_{$studente['id_studente']}_{$entrata['id_entrata']}');
                                                                                                        cambiaStato('modificato_{$studente['id_studente']}');"
                                                                                            {/if}
                                                                                                >•</button>
                                                                                {/if}
                                                                                <input type="time"
                                                                                    name="appello_studenti[{$studente['id_studente']}][entrate][{$cont}][ora_entrata]"
                                                                                    id="entrata_{$studente['id_studente']}_{$entrata['id_entrata']}"
                                                                                    value="{$entrata['ora_entrata']}"
                                                                                    data-ora-tipo="entrata"
                                                                                    data-ora-valore="{$entrata['ora_entrata']}"
                                                                                    data-id-studente="{$studente['id_studente']}"
                                                                                    style="border: none;"
                                                                                    title="{mastercom_label}Ora di entrata in formato HH:mm{/mastercom_label}"
                                                                                    {if $studente['bloccato'] == 'SI'}
                                                                                            onclick="messaggioStudenteBloccato();"
                                                                                            readonly
                                                                                        {elseif $funzione_appello_sola_visualizzazione == '1'}
                                                                                            onclick="messaggioFunzioneAppelloDisabilitato();"
                                                                                            readonly
                                                                                        {else}
                                                                                            onchange="cambiaStato('modificato_{$studente['id_studente']}');"
                                                                                        {/if}
                                                                                    >
                                                                                {if $is_mobile == 1 && $funzione_appello_sola_visualizzazione == '0'}
                                                                                    <button type="button"
                                                                                        class="btn_flat_tondo ripples"
                                                                                        title="{mastercom_label}Cancella{/mastercom_label}"
                                                                                        style="background-color: #FAFAFA; color: grey; padding: 3px 5px;"
                                                                                        onclick="$('#entrata_{$studente['id_studente']}_{$entrata['id_entrata']}').val('').trigger('change');"
                                                                                    >&#10005;</button>
                                                                                {/if}
                                                                                <input type="hidden"
                                                                                name="appello_studenti[{$studente['id_studente']}][entrate][{$cont}][id_entrata]"
                                                                                value="{$entrata['id_entrata']}"
                                                                                >
                                                                            </div>
                                                                            {assign var='cont' value=$cont+1}
                                                                        {/foreach}
                                                                    {/if}
                                                                    {if $cont <= 2}
                                                                        <div style='white-space: nowrap;'>
                                                                            {if $funzione_appello_sola_visualizzazione == '0'}
                                                                                <button type="button"
                                                                                    class="btn_flat_tondo ripples"
                                                                                    title="{mastercom_label}Imposta ora attuale{/mastercom_label}"
                                                                                    style="background-color: #FAFAFA; color: grey; padding: 3px 8px;"
                                                                                    {if $studente['bloccato'] == 'SI'}
                                                                                            onclick="messaggioStudenteBloccato();"
                                                                                    {else}
                                                                                            onclick="setCurrentTime('entrata_{$studente['id_studente']}');
                                                                                                    cambiaStato('modificato_{$studente['id_studente']}');"
                                                                                        {/if}
                                                                                            >•</button>
                                                                            {/if}
                                                                            <input type="time"
                                                                                name="appello_studenti[{$studente['id_studente']}][entrate][{$cont}][ora_entrata]"
                                                                                id="entrata_{$studente['id_studente']}"
                                                                                value=""
                                                                                data-ora-tipo="entrata"
                                                                                data-ora-valore=""
                                                                                data-id-studente="{$studente['id_studente']}"
                                                                                style="border: none;"
                                                                                title="{mastercom_label}Ora di entrata in formato HH:mm{/mastercom_label}"
                                                                                {if $studente['bloccato'] == 'SI'}
                                                                                        onclick="messaggioStudenteBloccato();"
                                                                                        readonly
                                                                                    {elseif $funzione_appello_sola_visualizzazione == '1'}
                                                                                        onclick="messaggioFunzioneAppelloDisabilitato();"
                                                                                        readonly
                                                                                    {else}
                                                                                        onchange="cambiaStato('modificato_{$studente['id_studente']}'); toggleGiustif(this.value, 'giust_e_{$studente['id_studente']}_{$cont}');"
                                                                                    {/if}
                                                                                >
                                                                            {if $funzione_giustificazioni_registro == '1'}
                                                                                <label class='annotazione_leggera' style='vertical-align: middle; display: none;' id='giust_e_{$studente['id_studente']}_{$cont}' title='{mastercom_label}Giustificata{/mastercom_label}'>
                                                                                    <input type="checkbox"
                                                                                            name="appello_studenti[{$studente['id_studente']}][entrate][{$cont}][giustificata]"
                                                                                            onchange="($(this).is(':checked')) ? $('#giust_e_{$studente['id_studente']}_{$cont}_mot').show() : $('#giust_e_{$studente['id_studente']}_{$cont}_mot').hide();"
                                                                                            value="SI">
                                                                                    G
                                                                                </label>
                                                                                <input type='hidden'
                                                                                    id='motivazione_{$studente['id_studente']}_entrata_{$cont}'
                                                                                    name='appello_studenti[{$studente['id_studente']}][entrate][{$cont}][motivazione]'
                                                                                    value=''>
                                                                            {/if}
                                                                            {if $is_mobile == 1 && $funzione_appello_sola_visualizzazione == '0'}
                                                                                <button type="button"
                                                                                    class="btn_flat_tondo ripples"
                                                                                    title="{mastercom_label}Cancella{/mastercom_label}"
                                                                                    style="background-color: #FAFAFA; color: grey; padding: 3px 5px;"
                                                                                    onclick="$('#entrata_{$studente['id_studente']}').val('').trigger('change');"
                                                                                >&#10005;</button>
                                                                            {/if}
                                                                            {if $funzione_giustificazioni_registro == '1'}
                                                                                <div id="giust_e_{$studente['id_studente']}_{$cont}_mot" style="display: none;">
                                                                                    <span class='annotazione_leggera'>Mot.:</span>
                                                                                    <select class='select' onchange='settaMotivazione(this, "motivazione_{$studente['id_studente']}_entrata_{$cont}");'>
                                                                                        <option></option>
                                                                                        {foreach $elenco_motivazioni['NORMALE'] as $motivazione}
                                                                                            <option>{$motivazione}</option>
                                                                                        {/foreach}
                                                                                        <option>Altro</option>
                                                                                    </select>
                                                                                    <br>
                                                                                    <input type='text' onchange='settaMotivazionePersonalizzata(this, "motivazione_{$studente['id_studente']}_entrata_{$cont}");' style='display: none;'>
                                                                                </div>
                                                                            {/if}
                                                                            <input type="hidden"
                                                                            name="appello_studenti[{$studente['id_studente']}][entrate][{$cont}][id_entrata]"
                                                                            value=""
                                                                            >
                                                                        </div>
                                                                    {/if}
                                                                {/if}
                                                            </td>
                                                        {/if}
                                                        <td align="center">
                                                            {if $studente['disabilitato'] !== 'SI'}
                                                                {assign var='cont' value=1}
                                                                {if $studente['uscite']|@count > 0}
                                                                    {foreach $studente['uscite'] as $uscita}
                                                                        <div style='white-space: nowrap;'>
                                                                            {if $funzione_appello_sola_visualizzazione == '0'}
                                                                                <button type="button"
                                                                                    class="btn_flat_tondo ripples"
                                                                                    title="{mastercom_label}Imposta ora attuale{/mastercom_label}"
                                                                                    style="background-color: #FAFAFA; color: grey; padding: 3px 8px;"
                                                                                    {if $studente['bloccato'] == 'SI'}
                                                                                            onclick="messaggioStudenteBloccato();"
                                                                                        {else}
                                                                                            onclick="setCurrentTime('uscita_{$studente['id_studente']}_{$uscita['id_uscita']}');
                                                                                                    cambiaStato('modificato_{$studente['id_studente']}');"
                                                                                        {/if}
                                                                                        >•</button>
                                                                            {/if}
                                                                            <input type="time"
                                                                                name="appello_studenti[{$studente['id_studente']}][uscite][{$cont}][ora_uscita]"
                                                                                id="uscita_{$studente['id_studente']}_{$uscita['id_uscita']}"
                                                                                value="{$uscita['ora_uscita']}"
                                                                                data-ora-tipo="uscita"
                                                                                    data-ora-valore="{$uscita['ora_uscita']}"
                                                                                    data-id-studente="{$studente['id_studente']}"
                                                                                style="border: none;"
                                                                                title="{mastercom_label}Ora di uscita in formato HH:mm{/mastercom_label}"
                                                                                {if $studente['bloccato'] == 'SI'}
                                                                                        onclick="messaggioStudenteBloccato();"
                                                                                        readonly
                                                                                    {elseif $funzione_appello_sola_visualizzazione == '1'}
                                                                                        onclick="messaggioFunzioneAppelloDisabilitato();"
                                                                                        readonly
                                                                                    {else}
                                                                                        onchange="cambiaStato('modificato_{$studente['id_studente']}');"
                                                                                    {/if}
                                                                                >
                                                                            {if $is_mobile == 1 && $funzione_giustificazioni_registro == '0'}
                                                                                <button type="button"
                                                                                    class="btn_flat_tondo ripples"
                                                                                    title="{mastercom_label}Cancella{/mastercom_label}"
                                                                                    style="background-color: #FAFAFA; color: grey; padding: 3px 5px;"
                                                                                    onclick="$('#uscita_{$studente['id_studente']}_{$uscita['id_uscita']}').val('').trigger('change');"
                                                                                >&#10005;</button>
                                                                            {/if}
                                                                            <input type="hidden"
                                                                                    name="appello_studenti[{$studente['id_studente']}][uscite][{$cont}][id_uscita]"
                                                                                    value="{$uscita['id_uscita']}"
                                                                            >
                                                                        </div>
                                                                        {assign var='cont' value=$cont+1}
                                                                    {/foreach}
                                                                {/if}
                                                                {if $cont <= 2}
                                                                    <div style='white-space: nowrap;'>
                                                                        {if $funzione_appello_sola_visualizzazione == '0'}
                                                                            <button type="button"
                                                                                class="btn_flat_tondo ripples"
                                                                                title="{mastercom_label}Imposta ora attuale{/mastercom_label}"
                                                                                style="background-color: #FAFAFA; color: grey; padding: 3px 8px;"
                                                                                {if $studente['bloccato'] == 'SI'}
                                                                                        onclick="messaggioStudenteBloccato();"
                                                                                    {else}
                                                                                        onclick="setCurrentTime('uscita_{$studente['id_studente']}');
                                                                                                cambiaStato('modificato_{$studente['id_studente']}');"
                                                                                    {/if}
                                                                                    >•</button>
                                                                        {/if}
                                                                        <input type="time"
                                                                            name="appello_studenti[{$studente['id_studente']}][uscite][{$cont}][ora_uscita]"
                                                                            id="uscita_{$studente['id_studente']}"
                                                                            value=""
                                                                            data-ora-tipo="uscita"
                                                                                data-ora-valore=""
                                                                                data-id-studente="{$studente['id_studente']}"
                                                                            style="border: none;"
                                                                            title="{mastercom_label}Ora di uscita in formato HH:mm{/mastercom_label}"
                                                                            {if $studente['bloccato'] == 'SI'}
                                                                                    onclick="messaggioStudenteBloccato();"
                                                                                    readonly
                                                                                {elseif $funzione_appello_sola_visualizzazione == '1'}
                                                                                    onclick="messaggioFunzioneAppelloDisabilitato();"
                                                                                    readonly
                                                                                {else}
                                                                                    onchange="cambiaStato('modificato_{$studente['id_studente']}'); toggleGiustif(this.value, 'giust_u_{$studente['id_studente']}_{$cont}');"
                                                                                {/if}
                                                                            >
                                                                        {if $funzione_giustificazioni_registro == '1'}
                                                                            <label class='annotazione_leggera' style='vertical-align: middle; display: none;' title='{mastercom_label}Giustificata{/mastercom_label}' id='giust_u_{$studente['id_studente']}_{$cont}'>
                                                                                <input type="checkbox"
                                                                                        name="appello_studenti[{$studente['id_studente']}][uscite][{$cont}][giustificata]"
                                                                                        onchange="($(this).is(':checked')) ? $('#giust_u_{$studente['id_studente']}_{$cont}_mot').show() : $('#giust_u_{$studente['id_studente']}_{$cont}_mot').hide();"
                                                                                        value="SI">
                                                                                G
                                                                            </label>
                                                                            <input type='hidden'
                                                                                    id='motivazione_{$studente['id_studente']}_uscita_{$cont}'
                                                                                    name='appello_studenti[{$studente['id_studente']}][uscite][{$cont}][motivazione]'
                                                                                    value=''>
                                                                        {/if}
                                                                        {if $is_mobile == 1 && $funzione_giustificazioni_registro == '0'}
                                                                            <button type="button"
                                                                                class="btn_flat_tondo ripples"
                                                                                title="{mastercom_label}Cancella{/mastercom_label}"
                                                                                style="background-color: #FAFAFA; color: grey; padding: 3px 5px;"
                                                                                onclick="$('#uscita_{$studente['id_studente']}').val('').trigger('change');"
                                                                            >&#10005;</button>
                                                                        {/if}
                                                                        {if $funzione_giustificazioni_registro == '1'}
                                                                            <div id="giust_u_{$studente['id_studente']}_{$cont}_mot" style="display: none;">
                                                                                <span class='annotazione_leggera'>Mot.:</span>
                                                                                <select class='select' onchange='settaMotivazione(this, "motivazione_{$studente['id_studente']}_uscita_{$cont}");'>
                                                                                    <option></option>
                                                                                    {foreach $elenco_motivazioni['NORMALE'] as $motivazione}
                                                                                        <option>{$motivazione}</option>
                                                                                    {/foreach}
                                                                                    <option>Altro</option>
                                                                                </select>
                                                                                <br>
                                                                                <input type='text' onchange='settaMotivazionePersonalizzata(this, "motivazione_{$studente['id_studente']}_uscita_{$cont}");' style='display: none;'>
                                                                            </div>
                                                                        {/if}
                                                                        <input type="hidden"
                                                                                name="appello_studenti[{$studente['id_studente']}][uscite][{$cont}][id_uscita]"
                                                                                value=""
                                                                        >
                                                                    </div>
                                                                {/if}
                                                            {/if}
                                                        </td>
                                                        {if $abilita_servizio_mensa == 'SI'}
                                                            <td align="center">
                                                                {if $studente['disabilitato'] !== 'SI'}
                                                                    {if $studente['mensa']['lista_pasti']|@count > 0}
                                                                        <select class='select'
                                                                                id='select_mensa_{$studente['id_studente']}'
                                                                                {if $studente['mensa']['abilita'] == 'NO'}disabled{/if}
                                                                                name="appello_studenti[{$studente['id_studente']}][pasto_prenotato]"
                                                                                onchange="cambiaStato('modificato_{$studente['id_studente']}');">
                                                                            {foreach $studente['mensa']['lista_pasti'] as $pasto}
                                                                                <option value='{$pasto['servizio']}'
                                                                                    {if ($studente['mensa']['tipo_check'] == 'prenotato' && $pasto['prenotato'] == '1') || ($studente['mensa']['tipo_check'] == 'predefinito' && $pasto['predefinito'] == '1')}
                                                                                        selected
                                                                                    {/if}
                                                                                    >{$pasto['descrizione']}</option>
                                                                            {/foreach}
                                                                        </select>
                                                                    {/if}
                                                                {/if}
                                                            </td>
                                                        {/if}
                                                        <td align="center">
                                                            {if $studente['disabilitato'] !== 'SI'}
                                                                <button type="button"
                                                                    class="btn_flat_tondo ripples"
                                                                    title="{mastercom_label}Cancella{/mastercom_label}"
                                                                    style="background-color: #FAFAFA; color: grey; padding: 3px 5px;"
                                                                    {if $studente['bloccato'] == 'SI'}
                                                                        onclick="messaggioStudenteBloccato();"
                                                                        {else}
                                                                            onclick="{if $funzione_appello_sola_visualizzazione == '1'}
                                                                                    messaggioFunzioneAppelloDisabilitato();
                                                                                {else}
                                                                                    resetAssenze('{$studente['id_studente']}');
                                                                                {/if}"
                                                                        {/if}
                                                                    >&#10005;</button>
                                                            {/if}
                                                        </td>
                                                    {/if}
                                                {/if}
                                                <input type="hidden"
                                                       value="NO"
                                                       name="appello_studenti[{$studente['id_studente']}][modificato]"
                                                       id="modificato_{$studente['id_studente']}">
                                            </tr>
                                            <tr>
                                                <td colspan="10" class="annotazione_leggera">
                                                {if $studente['permessi']|@count > 0}
                                                    {foreach $studente['permessi'] as $permesso}
                                                        <span class="testo_arancio sfondo_ambra_op20 bordrad5 bold"
                                                              style="padding: 1px 4px; margin-right: 5px"
                                                              title="{mastercom_label}Permesso{/mastercom_label}"
                                                            >P</span>
                                                        {$permesso['descrizione']}<br>
                                                    {/foreach}
                                                {/if}
                                                {if $studente['eventi']|@count > 0}
                                                    {foreach $studente['eventi'] as $evento}
                                                        <span class="testo_azzurro sfondo_azzurro_op20 bordrad5 bold"
                                                              style="padding: 1px 4px; margin-right: 5px"
                                                              title="{mastercom_label}Evento{/mastercom_label}"
                                                        >E</span>
                                                        {$evento['nome']}<br>
                                                    {/foreach}
                                                {/if}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="10" class="padding_cella_generica" style="display: none;" id="storia_appello_{$studente['id_studente']}">
                                                {if $studente['storia_assenze']|@count > 0}
                                                    {foreach $studente['storia_assenze'] as $storia}
                                                        <p>{$storia['descrizione']}</p>
                                                    {/foreach}
                                                {else}
                                                    {mastercom_label}Nessuna modifica ad assenze oggi{/mastercom_label}
                                                {/if}
                                                </td>
                                            </tr>
                                            {if $contatti_in_appello == 'SI'}
                                            <tr>
                                                <td colspan="10" class="padding_cella_generica" style="display: none;" id="contatti_appello_{$studente['id_studente']}">
                                                {if $studente['contatti']|@count > 0}
                                                    <b>{mastercom_label}Contatti{/mastercom_label}</b>
                                                    <table>
                                                    {foreach $studente['contatti'] as $contatti}
                                                        <tr>
                                                            <td class="padding_cella_generica" align="center">{$contatti['cognome']} {$contatti['nome']}</td>
                                                            <td class="padding_cella_generica" align="center">{mastercom_label}{$contatti['parentela_estesa']}{/mastercom_label}</td>
                                                            <td class="padding_cella_generica" align="center">{$contatti['email']}</td>
                                                            <td class="padding_cella_generica" align="center">{$contatti['telefono_cellulare']}</td>
                                                        </tr>
                                                    {/foreach}
                                                    </table>
                                                {else}
                                                    {mastercom_label}Nessun contatto per lo studente{/mastercom_label}
                                                {/if}
                                                </td>
                                            </tr>
                                            {/if}
                                        </tbody>
                                    {/foreach}
                                </table>
                                <div class="padding_cella_generica">
                                    <br>
                                    <button type="button"
                                           class="btn_flat_indaco"
                                            onclick="apriChiudiPopup('appello_{$giorno['timestamp']}', 'sfondo_oscurato', true);"
                                            >{mastercom_label}Chiudi{/mastercom_label}</button>
                                    <span style='position: relative;'>
                                        <button type="button"
                                                class="btn_pieno sfondo_scuro ripples"
                                                onclick="{if $funzione_appello_sola_visualizzazione == '1'}
                                                            messaggioFunzioneAppelloDisabilitato();
                                                        {else}
                                                            salvaAppello('{if $classe_corso == '1' && $appello_corsi_speciale == 'SI'}salva_appello_speciale{else}salva_appello{/if}');
                                                        {/if}"
                                                >{mastercom_label}Salva{/mastercom_label}</button>
                                        <div class="notify-badge" id='notify-badge-appello'></div>
                                    </span>
                                    <input type="hidden" name="id_orario_appelli_corsi_speciali" id="id_orario_appelli_corsi_speciali">
                                </div>
                            </div>
                        </div>
                    {/if}

                    {if $giorno['ore']|@count > 0}
                        {foreach $giorno['ore'] as $ora}
                            {foreach $ora['materie'] as $materia}
                                {if $materia['editabile'] == 'SI'}
                                    {*form inserimento compiti e argomenti*}
                                    <div  class="div_scheda_generica form_inserimento_compito_argomento"
                                        id="compito_argomento_firma_{$materia['id_firma']}"
                                        style="position: fixed;
                                              width: 60%;
                                              max-height: 80%;
                                              overflow-y: scroll;
                                              {*height: 60%;*}
                                              left: 50%;
                                              top: 50%;
                                              transform: translate(-50%, -50%);
                                              display: none;
                                              z-index: 3;"
                                    >
                                        <h1>{$materia['materia']}</h1>
                                        <div style="height: 10px; font-size: 90%;" class='annotazione_leggera'>{mastercom_label}(puoi inserire un argomento, un compito o entrambi){/mastercom_label}</div>
                                        <table width="95%" align="center" style="border-left: 3px solid #007fff;">
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <b>{mastercom_label}Inserisci argomento{/mastercom_label}</b>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    {mastercom_label}Ora{/mastercom_label}: <b>{$ora['fascia']}</b>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <select class="select" name="argomenti[{$materia['id_firma']}][id_argomento_duplica]" onchange="(this.value == '-1') ? $('#duplica_argomento{$materia['id_firma']}').show() : $('#duplica_argomento{$materia['id_firma']}').hide();">
                                                        <option value="-1" selected>{mastercom_label}Nuovo argomento{/mastercom_label}</option>
                                                        {foreach $giorno['lista_argomenti'][$materia['id_materia']] as $argomento_da_duplicare}
                                                        <option value="{$argomento_da_duplicare['id_argomento']}">{mastercom_label}Duplica{/mastercom_label} "{$argomento_da_duplicare['modulo']|@escape}"</option>
                                                        {/foreach}
                                                    </select>
                                                </td>
                                            </tr>
                                            <tbody id="duplica_argomento{$materia['id_firma']}">
                                                <tr>
                                                    <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                        {mastercom_label}Modulo{/mastercom_label}: <input type="text" name="argomenti[{$materia['id_firma']}][modulo]" placeholder="{mastercom_label}Modulo{/mastercom_label}" style="width: 50%;">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                        <textarea name="argomenti[{$materia['id_firma']}][testo]"
                                                                rows="3"
                                                                style="width: 80%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                                                placeholder="{mastercom_label}Argomento{/mastercom_label}.."></textarea>
                                                        <input type="hidden"
                                                            name="argomenti[{$materia['id_firma']}][id_materia]"
                                                            value="{$materia['id_materia']}">
                                                        <input type="hidden"
                                                            name="argomenti[{$materia['id_firma']}][data]"
                                                            value="{$ora['data_inizio']}">
                                                    </td>
                                                </tr>
                                                </tr>
                                                <tr>
                                                    <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                        <textarea name="argomenti[{$materia['id_firma']}][note_riservate]"
                                                                rows="1"
                                                                style="width: 90%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                                                placeholder="{mastercom_label}Eventuali note riservate{/mastercom_label}.."></textarea>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                        {mastercom_label}Tag{/mastercom_label}:
                                                        <select class="select" name="argomenti[{$materia['id_firma']}][id_tag]" placeholder="{mastercom_label}Tag{/mastercom_label}">
                                                            <option value = "">- {mastercom_label}Nessun tag{/mastercom_label} -</option>
                                                            {foreach $elenco_tag as $tag}
                                                                <option value="{$tag['id_tag']}">{$tag['descrizione']}</option>
                                                            {/foreach}
                                                        </select>
                                                    </td>
                                                </tr>
                                                {if $navale == 'SI'}
                                                    <tr>
                                                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                            <div class="bold">{mastercom_label}Argomento SIDI collegato (navali){/mastercom_label}</div>
                                                            <select id="navali_programmazione_inserisci_arg_{$materia['id_firma']}" class="select" name="argomenti[{$materia['id_firma']}][id_programmazione_ministeriale]" disabled placeholder="{mastercom_label}Programmazione{/mastercom_label}" onchange="caricaModuliNavali(this.value, {$materia['id_firma']});"></select>
                                                            &rarr; <select id="navali_modulo_inserisci_arg_{$materia['id_firma']}" class="select" name="argomenti[{$materia['id_firma']}][id_modulo_ministeriale]" disabled placeholder="{mastercom_label}Modulo{/mastercom_label}" onchange="caricaArgomentiNavali(this.value, {$materia['id_firma']});"></select>
                                                            &rarr; <select id="navali_argomento_inserisci_arg_{$materia['id_firma']}" class="select" name="argomenti[{$materia['id_firma']}][id_argomento_ministeriale]" disabled placeholder="{mastercom_label}Argomento{/mastercom_label}"></select>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td align="left" class="padding_cella_generica" style="padding-left: 15px;" id="tabelle_navali_{$materia['id_firma']}"></td>
                                                    </tr>
                                                {/if}
                                            </tr>
                                        </table>
                                        <div style="height: 10px;"></div>
                                        <table width="95%" align="center" style="border-left: 3px solid #ff9400;">
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <b>{mastercom_label}Inserisci compito{/mastercom_label}</b>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="min-height: 35px; padding-left: 15px;">
                                                    {mastercom_label}Data{/mastercom_label}:
                                                    {assign var='cont' value=1}
                                                    <select class="select" onchange="settaDataOraNuovoCompito(this.value, 'data_compito_{$id_classe}_{$materia['id_materia']}_{$materia['id_firma']}', 'ora_compito_{$id_classe}_{$materia['id_materia']}_{$materia['id_firma']}');">
                                                        <option value="0">{mastercom_label}Data libera{/mastercom_label}</option>
                                                    </select>
                                                    </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="min-height: 35px; padding-left: 15px;">
                                                    <input type="date"
                                                           name="compiti[{$materia['id_firma']}][data_compito]"
                                                           id="data_compito_{$id_classe}_{$materia['id_materia']}_{$materia['id_firma']}">
                                                    <span id="ora_compito_{$id_classe}_{$materia['id_materia']}_{$materia['id_firma']}">
                                                    &emsp;{mastercom_label}Ora{/mastercom_label}:
                                                    <input type="time"
                                                           id="ora_compito_{$id_classe}_{$materia['id_materia']}_{$materia['id_firma']}_input"
                                                           name="compiti[{$materia['id_firma']}][ora_compito]"
                                                           value="08:00">
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <textarea name="compiti[{$materia['id_firma']}][assegnazioni]"
                                                              rows="3"
                                                              style="width: 80%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                                              placeholder="{mastercom_label}Assegnazioni{/mastercom_label}.."></textarea>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                                    <textarea name="compiti[{$materia['id_firma']}][note_riservate]"
                                                            rows="1"
                                                            style="width: 90%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                                            placeholder="{mastercom_label}Eventuali note riservate{/mastercom_label}.."></textarea>
                                                </td>
                                            </tr>
                                        </table>
                                        <div style="height: 10px;"></div>
                                        <div align="center" style="padding: 10px;">
                                            <input type="hidden"
                                                   name="compiti[{$materia['id_firma']}][id_materia]"
                                                   value="{$materia['id_materia']}">
                                            <input type="hidden"
                                                   name="compiti[{$materia['id_firma']}][data]"
                                                   value="{$ora['data_inizio']}">
                                        </div>
                                        <div align="center" style="padding: 10px;">
                                            <button type="button"
                                                   class="btn_flat_indaco"
                                                    onclick="apriChiudiPopup('compito_argomento_firma_{$materia['id_firma']}', 'sfondo_oscurato', true);"
                                                    >{mastercom_label}Annulla{/mastercom_label}</button>
                                            <button type="button"
                                                   class="btn_pieno sfondo_scuro ripples"
                                                   onclick="if (verificaDataCompitoFutura('data_compito_{$id_classe}_{$materia['id_materia']}_{$materia['id_firma']}', 'ora_compito_{$id_classe}_{$materia['id_materia']}_{$materia['id_firma']}_input') === true){
                                                            document.getElementById('form_container').operazione.value = 'inserisci_argomento_compito';
                                                            document.getElementById('form_container').id_firma_selezionata.value='{$materia['id_firma']}';
                                                            document.getElementById('form_container').submit();
                                                            $('#jqxGeneralLoader').jqxLoader('open');
                                                        } else {
                                                        alert('{mastercom_label}Il compito deve avere una data futura{/mastercom_label}');}"
                                                    >{mastercom_label}Salva{/mastercom_label}</button>
                                        </div>
                                    </div>
                                {/if}
                            {/foreach}
                        {/foreach}
                    {/if}
                {/foreach}

                {*form modifica compiti e argomenti*}
                <div  class="div_scheda_generica form_modifica_compito_argomento"
                    id="modifica_argomento_compito"
                    style="position: fixed;
                          width: 60%;
                          max-height: 80%;
                          overflow-y: auto;
                          {*height: 60%;*}
                          left: 50%;
                          top: 50%;
                          transform: translate(-50%, -50%);
                          display: none;
                          z-index: 3;"
                >
                    <h1 id='modifica_argomento_compito_materia'></h1>
                    <div style="height: 10px;"></div>
                    <table width="95%" align="center" style="border-left: 3px solid #007fff;" id='modifica_argomento_tabella'>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                <b>{mastercom_label}Modifica argomento{/mastercom_label}</b>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                {mastercom_label}Ora{/mastercom_label}: <b id='modifica_argomento_fascia'></b>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                {mastercom_label}Modulo{/mastercom_label}: <input type="text" id='modifica_argomento_modulo' name='modifica_argomento_compito[modulo]' placeholder="{mastercom_label}Modulo{/mastercom_label}" style="width: 50%;">
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                <textarea id='modifica_argomento_testo'
                                          name='modifica_argomento_compito[testo]'
                                          rows="3"
                                          style="width: 80%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                          placeholder="{mastercom_label}Argomento{/mastercom_label}.."></textarea>
                            </td>
                        </tr>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                <textarea id='modifica_argomento_note_riservate'
                                          name='modifica_argomento_compito[note_riservate]'
                                        rows="1"
                                        style="width: 90%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                        placeholder="{mastercom_label}Eventuali note riservate{/mastercom_label}.."></textarea>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                {mastercom_label}Tag{/mastercom_label}:
                                <select class="select" id="modifica_argomento_tag" name="modifica_argomento_compito[id_tag]" placeholder="{mastercom_label}Tag{/mastercom_label}">
                                    <option value = "">- {mastercom_label}Nessun tag{/mastercom_label} -</option>
                                    {foreach $elenco_tag as $tag}
                                        <option value="{$tag['id_tag']}">{$tag['descrizione']}</option>
                                    {/foreach}
                                </select>
                            </td>
                        </tr>
                        {if $navale == 'SI'}
                            <tr>
                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                    <div class="bold">{mastercom_label}Argomento SIDI collegato (navali){/mastercom_label}</div>
                                    <input type="hidden" id="id_materia_argomento_ministeriale">
                                    <select id="navali_programmazione_inserisci_arg_modifica" class="select" name="modifica_argomento_compito[id_programmazione_ministeriale]" disabled placeholder="{mastercom_label}Programmazione{/mastercom_label}" onchange="caricaModuliNavali(this.value, 'modifica');"></select>
                                    &rarr; <select id="navali_modulo_inserisci_arg_modifica" class="select" name="modifica_argomento_compito[id_modulo_ministeriale]" disabled placeholder="{mastercom_label}Modulo{/mastercom_label}" onchange="caricaArgomentiNavali(this.value, 'modifica');"></select>
                                    &rarr; <select id="navali_argomento_inserisci_arg_modifica" class="select" name="modifica_argomento_compito[id_argomento_ministeriale]" disabled placeholder="{mastercom_label}Argomento{/mastercom_label}"></select>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" class="padding_cella_generica" style="padding-left: 15px;" id="tabelle_navali_modifica"></td>
                            </tr>
                        {/if}
                    </table>
                    <table width="95%" align="center" style="border-left: 3px solid #ff9400;" id='modifica_compito_tabella'>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                <b>{mastercom_label}Modifica compito{/mastercom_label}</b>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="min-height: 35px; padding-left: 15px;">
                                {mastercom_label}Data{/mastercom_label}:
                                <select class="select" id="modifica_compito_select_data" onchange="settaDataOraNuovoCompito(this.value, 'modifica_compito_data', 'modifica_compito_ora');">
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="min-height: 35px; padding-left: 15px;">
                                <input type="date"
                                       name="modifica_argomento_compito[data]"
                                       id="modifica_compito_data">
                                <span id="modifica_compito_ora">
                                &emsp;{mastercom_label}Ora{/mastercom_label}:
                                <input type="time"
                                       id="modifica_compito_ora_input"
                                       name="modifica_argomento_compito[ora]">
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                <textarea name="modifica_argomento_compito[assegnazioni]"
                                          id="modifica_compito_assegnazioni"
                                          rows="3"
                                          style="width: 80%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                          placeholder="{mastercom_label}Assegnazioni{/mastercom_label}.."></textarea>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" class="padding_cella_generica" style="padding-left: 15px;">
                                <textarea name="modifica_argomento_compito[note_riservate]"
                                        id="modifica_compito_note_riservate"
                                        rows="1"
                                        style="width: 90%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                        placeholder="{mastercom_label}Eventuali note riservate{/mastercom_label}.."></textarea>
                            </td>
                        </tr>
                    </table>
                    <div style="height: 10px;"></div>
                    <div align="center" style="padding: 10px;">
                        <button type="button"
                               class="btn_flat_indaco"
                                onclick="apriChiudiPopup('modifica_argomento_compito', 'sfondo_oscurato', true);"
                                >{mastercom_label}Annulla{/mastercom_label}</button>
                        <button type="button"
                               class="btn_pieno sfondo_scuro ripples"
                               onclick="if (verificaDataCompitoFutura('modifica_compito_data', 'modifica_compito_ora_input') === true){
                                            document.getElementById('form_container').operazione.value = 'modifica_argomento_compito';
                                            document.getElementById('form_container').submit();
                                            $('#jqxGeneralLoader').jqxLoader('open');
                                        } else {
                                            alert('{mastercom_label}Il compito deve avere una data futura{/mastercom_label}');}"
                                >{mastercom_label}Salva{/mastercom_label}</button>
                    </div>
                </div>

                {*form inserimento nota disciplinare*}
                <div  class="div_scheda_generica"
                        id="nuova_nota_disciplinare"
                        style="position: fixed;
                              width: 50%;
                              min-width: 500px;
                              left: 50%;
                              top: 50%;
                              transform: translate(-50%, -50%);
                              display: none;
                              z-index: 3;
                              overflow-y: auto;"
                    >
                    <div align="center" style="width: 100%; height: 100%;">
                        <table width="95%" align="center">
                            <tr>
                                <td align="center" style="padding: 15px 10px 20px 10px; font-size: 120%;">
                                    <b>{mastercom_label}INSERISCI NOTA{/mastercom_label}</b><br>
                                </td>
                            </tr>
                            <tr>
                                <td align="center">
                                    <div width="100%" style="display: flex; align-items: center; justify-content: space-around; flex-wrap: wrap;">
                                        <div class='padding_cella_generica'>
                                            {mastercom_label}Data{/mastercom_label}<br>
                                            <input type='date'
                                                   name="data_nota_disciplinare"
                                                   id="data_nota_disciplinare"
                                                   value="{$dati_inserimento['data']}"
                                                   style="border: 0.5px solid #ccc; padding: 2px;">
                                        </div>
                                        <div class='padding_cella_generica'>
                                            {mastercom_label}Ora{/mastercom_label}<br>
                                            <input type='time'
                                                   name="ora_nota_disciplinare"
                                                   id="ora_nota_disciplinare"
                                                   value="{$dati_inserimento['ora']}"
                                                   style="border: 0.5px solid #ccc; padding: 2px;">
                                        </div>
                                        <div>
                                            {mastercom_label}Studenti{/mastercom_label}<div id='elencoStudenti'></div>
                                            <input type="hidden" name="id_studenti_nota" id="id_studenti_nota">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" class='padding_cella_generica'>
                                    <div width="100%" style="display: flex; align-items: center; justify-content: space-around; flex-wrap: wrap;">
                                        <div class="padding_cella_generica">
                                            {mastercom_label}Valori predefiniti (opzionali){/mastercom_label}<br>
                                            <select class="select" onchange="$('#testo_nota_disciplinare').val($(this).val());">
                                                <option value="">-</option>
                                                {foreach $elenco_valori_note as $valore}
                                                    <option value="{$valore['descrizione']}">{$valore['descrizione']}</option>
                                                {/foreach}
                                            </select>
                                        </div>
                                        <div class="padding_cella_generica">
                                            {mastercom_label}Tipo{/mastercom_label}<br>
                                            <select class="select" name="id_tag_nota_disciplinare">
                                                {foreach $elenco_tag_note as $tag}
                                                    <option value="{$tag['id_tag']}">{$tag['descrizione']}</option>
                                                {/foreach}
                                            </select>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" class='padding_cella_generica'>
                                    <textarea name="testo_nota_disciplinare"
                                                id="testo_nota_disciplinare"
                                                rows="15"
                                                style="width: 95%; border: 0.5px solid grey; padding: 10px; font-size: 14px; font-family: inherit;"
                                                placeholder="{mastercom_label}Inserisci il testo della nota{/mastercom_label}.."
                                                ></textarea>
                                </td>
                            </tr>
                        </table>
                        <div class="padding_cella_generica">
                            <br>
                            <button type="button"
                                   class="btn_flat_indaco"
                                    onclick="apriChiudiPopup('nuova_nota_disciplinare', 'sfondo_oscurato', true);"
                                    >{mastercom_label}Chiudi{/mastercom_label}</button>
                            <button type="button"
                                    class="btn_pieno sfondo_scuro ripples"
                                    onclick="document.getElementById('form_container').operazione.value = 'inserisci_nota_disciplinare';
                                            document.getElementById('form_container').submit();
                                            $('#jqxGeneralLoader').jqxLoader('open');"
                                >{mastercom_label}Salva{/mastercom_label}</button>
                        </div>
                    </div>
                </div>

                {*form assenze ora didattica distanza*}
                <div  class="div_scheda_generica"
                        id="assenze_didattica_distanza"
                        style="position: fixed;
                              width: 50%;
                              min-width: 500px;
                              max-height: 95%;
                              left: 50%;
                              top: 50%;
                              transform: translate(-50%, -50%);
                              display: none;
                              z-index: 3;
                              overflow-y: auto;"
                    >
                    <div align="center" style="width: 100%; height: 100%;">
                        <h1>{mastercom_label}ASSENZE ORA DIDATTICA DISTANZA{/mastercom_label}</h1>
                        <table width="95%" align="center">
                            <tr>
                                <td align="center" colspan="3" class="bold padding_cella_generica">
                                    <b>{mastercom_label}Studente{/mastercom_label}</b><br>
                                </td>
                                <td align="center" class="bold padding_cella_generica">
                                    <b>{mastercom_label}Assenza{/mastercom_label}</b><br>
                                </td>
                                <td align="center" class="bold padding_cella_generica">
                                    <b>{mastercom_label}Entrata{/mastercom_label}</b><br>
                                </td>
                                <td align="center" class="bold padding_cella_generica">
                                    <b>{mastercom_label}Uscita{/mastercom_label}</b><br>
                                </td>
                                <td align="center" class="bold padding_cella_generica">
                                </td>
                            </tr>
                            <tbody id='tbody_assenze_ora_did_dist'>

                            </tbody>
                        </table>
                        <input type="hidden" name="id_orario_assenze_didattica_distanza" id="id_orario_assenze_didattica_distanza">
                        <input type="hidden" name="inizio_ora_assenze_didattica_distanza" id="inizio_ora_assenze_didattica_distanza">
                        <input type="hidden" name="fine_ora_assenze_didattica_distanza" id="fine_ora_assenze_didattica_distanza">
                        <div class="padding_cella_generica">
                            <br>
                            <button type="button"
                                   class="btn_flat_indaco"
                                    onclick="apriChiudiPopup('assenze_didattica_distanza', 'sfondo_oscurato', true);"
                                    >{mastercom_label}Chiudi{/mastercom_label}</button>
                            <button type="button"
                                    class="btn_pieno sfondo_scuro ripples"
                                    onclick="{if $funzione_appello_sola_visualizzazione == '1'}
                                                messaggioFunzioneAppelloDisabilitato();
                                            {else}
                                                salvaAppello('salva_assenze_didattica_distanza');
                                            {/if}"
                                >{mastercom_label}Salva{/mastercom_label}</button>
                        </div>
                    </div>
                </div>

                {*form cambio materia docente*}
                {if $materie_docente_classe|@count > 1 && $funzione_inserisci_orario_registro == '1'}
                    <div  class="div_scheda_generica"
                            id="cambio_materia_orario_docente"
                            style="position: fixed;
                                  width: auto;
                                  min-width: 500px;
                                  left: 50%;
                                  top: 50%;
                                  transform: translate(-50%, -50%);
                                  display: none;
                                  z-index: 3;
                                  overflow-y: auto;"
                        >
                        <div align="center" style="width: 100%; height: 100%;">
                            <table width="95%" align="center">
                                <tr>
                                    <td align="center" style="padding: 15px 10px 20px 10px; font-size: 120%;">
                                        <b>{mastercom_label}CAMBIA MATERIA IN ORARIO{/mastercom_label}</b><br>
                                        <input type='hidden' name='modifica_materia_orario[id_ora]' id='id_ora_modifica_materia_orario'>
                                        <input type='hidden' name='modifica_materia_orario[id_materia]' id='id_materia_modifica_materia_orario'>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" class='padding_cella_generica'>
                                        <div>
                                            <table>
                                            {foreach $materie_docente_classe as $materia}
                                                <tr>
                                                    <td>{$materia['descrizione']}</td>
                                                    <td>
                                                        <button class="btn_flat testo_verde ripples"
                                                                onclick="$('#id_materia_modifica_materia_orario').val({$materia['id_materia']});
                                                                        document.getElementById('form_container').operazione.value = 'cambia_materia_in_orario';
                                                                        $('#jqxGeneralModalLoader').jqxLoader('open');"
                                                            >{mastercom_label}Scegli{/mastercom_label}</button>
                                                    </td>
                                                </tr>
                                            {/foreach}
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <div class="padding_cella_generica">
                                <br>
                                <button type="button"
                                       class="btn_flat_indaco"
                                        onclick="apriChiudiPopup('cambio_materia_orario_docente', 'sfondo_oscurato', true);"
                                        >{mastercom_label}Chiudi{/mastercom_label}</button>
                            </div>
                        </div>
                    </div>
                {/if}

                {*form circolari da leggere in classe*}
                <div  class="div_scheda_generica padding8"
                        id="circolari_classe"
                        style="position: fixed;
                                width: auto;
                                width: 95vw;
                                height: 95vh;
                                left: 50%;
                                top: 50%;
                                transform: translate(-50%, -50%);
                                display: none;
                                z-index: 3;
                                overflow-y: auto;"
                    >
                    <div align="center" style="width: 100%; height: 100%;">
                        <div class="bold padding8 font120p">
                            {mastercom_label}DA LEGGERE IN CLASSE{/mastercom_label}
                        </div>
                        <div id="containerCircolari">

                        </div>
                    </div>
                </div>

                {*form creazione/elenco lezione online*}
                {if $optional_videomeeting == 'OK' || $optional_videomeeting_bbb == 'OK' || $optional_google_meet == 'OK'}
                    <div  class="div_scheda_generica"
                            id="popup_lezione_online"
                            style="position: fixed;
                                  width: auto;
                                  min-width: 500px;
                                  left: 50%;
                                  top: 50%;
                                  transform: translate(-50%, -50%);
                                  display: none;
                                  z-index: 3;
                                  overflow-y: auto;"
                        >
                        <div align="center" style="width: 100%; height: 100%; position: relative;">
                            <button type="button"
                                    style="position: absolute; top: 5px; right: 5px; font-size: 1.1em"
                                    class="btn_flat testo_grigio"
                                    onclick="apriChiudiPopup('popup_lezione_online', 'sfondo_oscurato', true);"
                                >&times;</button>
                            <div id="lezione_online_creazione">
                                <table width="95%" align="center">
                                    <tr>
                                        <td align="center" style="padding: 15px 10px 10px 10px; font-size: 120%;">
                                            <b>{mastercom_label}CREA LEZIONE ONLINE{/mastercom_label}</b><br>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" class='padding_cella_generica'>
                                            <div class="padding6 nowrap">
                                                {mastercom_label}Titolo{/mastercom_label}
                                                <input type="text" name="videomeeting[titolo]" id='videomeeting_titolo' style='width: 250;'>
                                                <input type="hidden" name="videomeeting[data]" id='videomeeting_data'>
                                            </div>
                                            <div class="padding6 nowrap">
                                                {mastercom_label}dalle{/mastercom_label}
                                                <input type="time" name="videomeeting[ora_inizio]" id='videomeeting_ora_inizio'>
                                                {mastercom_label}alle{/mastercom_label}
                                                <input type="time" name="videomeeting[ora_fine]" id='videomeeting_ora_fine'>
                                            </div>
                                        </td>
                                    </tr>
                                    {if $optional_videomeeting_bbb == 'OK'}
                                        <tr>
                                            <td align="center" colspan="2" class='padding_cella_generica'>
                                                <div class="padding6">
                                                    <label>
                                                        <input type="checkbox" name="videomeeting[prima_moderatore]" id='videomeeting_prima_moderatore' value='SI'>
                                                        {mastercom_label}Blocca accesso prima del moderatore{/mastercom_label}
                                                    </label
                                                </div>
                                            </td>
                                        </tr>
                                    {/if}
                                </table>
                                <div class="padding_cella_generica">
                                    {if $optional_videomeeting == 'OK'}
                                        <button type="button"
                                            style="margin-bottom: 15px;"
                                            class="btn_flat sfondo_azzurro testo_bianco ombra_testo ripples"
                                            onclick="document.getElementById('form_container').operazione.value = 'crea_lezione_online';
                                                    document.getElementById('form_container').submit();
                                                    $('#jqxGeneralLoader').jqxLoader('open');"
                                            >{mastercom_label}Crea{/mastercom_label}</button>
                                    {/if}
                                    {if $optional_videomeeting_bbb == 'OK'}
                                        <button type="button"
                                            style="margin-bottom: 15px;"
                                            class="btn_flat sfondo_scuro testo_bianco ombra_testo ripples"
                                            onclick="document.getElementById('form_container').operazione.value = 'crea_lezione_bbb';
                                                    document.getElementById('form_container').submit();
                                                    $('#jqxGeneralLoader').jqxLoader('open');"
                                            >{mastercom_label}Crea Avanzata{/mastercom_label}</button>
                                    {/if}
                                    {if $optional_google_meet == 'OK'}
                                        <button type="button"
                                            style="margin-bottom: 15px;"
                                            class="btn_flat sfondo_verde testo_bianco ombra_testo ripples"
                                            onclick="document.getElementById('form_container').operazione.value = 'crea_lezione_google_meet';
                                                    document.getElementById('form_container').submit();
                                                    $('#jqxGeneralLoader').jqxLoader('open');"
                                            >{mastercom_label}Crea Google Meet{/mastercom_label}</button>
                                    {/if}
                                    {if $optional_microsoft_calendar == 'OK'}
                                        <button type="button"
                                            style="margin-bottom: 15px;"
                                            class="btn_flat sfondo_ambra testo_nero ripples"
                                            onclick="document.getElementById('form_container').operazione.value = 'crea_lezione_microsoft_calendar';
                                                    document.getElementById('form_container').submit();
                                                    $('#jqxGeneralLoader').jqxLoader('open');"
                                            >{mastercom_label}Crea Microsoft Teams{/mastercom_label}</button>
                                    {/if}
                                </div>
                            </div>
                            <div id="lezione_online_elenco">
                                <table width="95%" align="center">
                                    <tr>
                                        <td align="center" style="padding: 15px 10px 10px 10px; font-size: 120%;">
                                            <b>{mastercom_label}ELENCO LEZIONE ONLINE DEL GIORNO{/mastercom_label}</b><br>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" class='padding_cella_generica'>
                                            <div id='elenco_lezioni_online' style="margin-bottom: 10px;"></div>
                                            <div id='messaggio_no_lezioni_online' class='annotazione_leggera'>
                                                {mastercom_label}Non sono presenti lezioni online nella giornata{/mastercom_label}
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div  class="div_scheda_generica"
                        id="popup_presenze"
                        style="position: fixed;
                                width: auto;
                                min-width: 300px;
                                max-width: 90%;
                                max-height: 90%;
                                left: 50%;
                                top: 50%;
                                transform: translate(-50%, -50%);
                                display: none;
                                z-index: 4;
                                overflow-y: auto;"
                    >
                        <div align="center" style="width: 100%; height: 100%; position: relative;">
                            <button type="button"
                                    style="position: absolute; top: 5px; right: 5px; font-size: 1.1em"
                                    class="btn_flat testo_grigio"
                                    onclick="apriChiudiPopup('popup_presenze', 'sfondo_oscurato', true);"
                                >&times;</button>
                            <div>
                                <div id="presenze_titolo" class="padding8" align="left" style="padding-top: 16px;"></div>
                                <div class="padding8" align="left">
                                    {mastercom_label}dal{/mastercom_label} <span id="presenze_dal"></span> {mastercom_label}al{/mastercom_label} <span id="presenze_al"></span>
                                </div>
                                <div class="div_testo_titolo_scheda_sx">{mastercom_label}Elenco{/mastercom_label}</div>
                                <div id="presenze_elenco" class="padding8" style="overflow: auto;"></div>
                                <div class="testo_azzurro btn_flat ripples"
                                    id="stampa_presenze">
                                    {mastercom_label}Stampa{/mastercom_label}
                                    <img
                                        src="icone/printer.png"
                                        style="vertical-align: middle;"
                                        class="pointer"
                                        title="{mastercom_label}Stampa{/mastercom_label}">
                                </div>
                            </div>
                        </div>
                    </div>
                {/if}

                {if $salva_firme == 'SI'}
                    <div align="center" style="padding: 20px 0px;" class="padding_cella_generica">
                        <button type="button"
                                class="btn_pieno sfondo_scuro ripples"
                                style="margin-left:10px"
                                onclick="document.getElementById('form_container').operazione.value = 'salva_firme';
                                        document.getElementById('form_container').submit();
                                        $('#jqxGeneralLoader').jqxLoader('open');"
                            >{mastercom_label}Salva Firme Selezionate{/mastercom_label}</button>
                    </div>
                {/if}
        {else}
            <div style="padding: 20px; width: 25%;">
                {* Scelta Classe *}
                <select name="id_classe" class="select" style="background-color: white; width: 80%;" onchange="this.form.submit(); $('#jqxGeneralModalLoader').jqxLoader('open');">
                    <option value="0">{mastercom_label}Seleziona una classe{/mastercom_label}</option>
                    {foreach $elenco_classi as $classe}
                        <option value="{$classe['id_classe']}"
                                >
                            {if $classe['ordinamento'] == 'CORSO'}
                                (C) {$classe['descrizione_materia']}
                            {else}
                                {$classe['classe']}{$classe['sezione']} {$classe['descrizione_indirizzi']}
                            {/if}
                        </option>
                    {/foreach}
                </select>
            </div>
        {/if}
    </div>
    <input type='hidden' name='form_stato' value='{$form_stato}'>
    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
    <input type='hidden' name='stato_secondario' id='stato_secondario' value='{$stato_secondario}'>
    <input type='hidden' name='tipo_visualizzazione' value='{$tipo_visualizzazione}'>
    <input type='hidden' name='operazione' id="operazione" value=''>
    <input type='hidden' name='id_ora_selezionata' id='id_ora_selezionata' value=''>
    <input type='hidden' name='tipo_videomeeting' value=''>
    <input type='hidden' name='id_firma_selezionata' value=''>
    <input type='hidden' name='id_argomento_selezionato' id='id_argomento_selezionato' value=''>
    <input type='hidden' name='id_professore' id='id_professore' value=''>
    <input type='hidden' name='tipo_stampa' id='tipo_stampa' value=''>
    <input type='hidden' name='current_user' id='current_user' value='{$current_user}'>
    <input type='hidden' name='current_key' id='current_key' value='{$current_key}'>
    <input type='hidden' id='db_key' value='{$db_key}'>
    <input type='hidden' id='tipo_utente' value='{$form_stato}'>
</form>
