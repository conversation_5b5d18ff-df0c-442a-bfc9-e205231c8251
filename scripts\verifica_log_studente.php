<?php

/*################### Script per verificare il log storico per le modifiche apportate sul mastercom agli studenti #########################
 * 
 * Con questo script è possibile estrarre dal log storico tutti i record che contengono il dato cercato (deve essere relativo allo studente) tipo codice fiscale
 * e vedere chi ha fatto la modifica e che modifica ha fatto
 * 
 * Lo script invia una mail di report all'indirizzo specificato
 * I parametri necessari sono due
 * es. php verifica_log_studente.php **************** <EMAIL>
 *      Il primo che indica il dato da verificare
 *      Il secondo la mail a cui si vuole spedire il report.
 *      Entrambi obbligatori
 * 
 */
require __DIR__ . '/../configs/mastercom.php';
require MC_PATH . '/functions/common.php';
require MC_PATH . '/class/NEXTAPI.inc.php';
require MC_PATH . "/class/NEXUS_MASTERCOM.class.php";
require MC_PATH . "/common/dbconnect.php";


//Se l'argomento 1 della riga di comando è impostato a "ESEGUI" esegue tutte le query altrimenti non modifica nulla e genera solo la mail
if(strlen(trim($argv[1])) > 0) {
    $campo_cercato = trim($argv[1]);
}else{
    echo "I campi da compilare sono 2:\n Il primo che indica il dato da cercare come ad esempio il codice fiscale e il secondo la mail a cui si vuole spedire il report.\n Entrambi obbligatori\n\n";
    exit;
}    

$email_per_invio_report = strtolower(trim($argv[2]));
if (filter_var($email_per_invio_report, FILTER_VALIDATE_EMAIL)) {
    echo "L'indirizzo email è valido.";
} else {
    echo "L'indirizzo email " . $email_per_invio_report . " non è valido.\n";
    exit;
}

$db_attuale = $db_key;

$dato = [];
foreach ($dbname as $key => $db_in_esame) {
    if (strpos($db_in_esame["nome"], 'mastercom') !== false) {
        $db_key = $db_in_esame["nome"];
 
        //estraggo l'elenco dei log da verificare
         $sql_elenco_log = "SELECT
                                    * 
                                FROM log_storico
                                WHERE
                                    dato ilike '%" . $campo_cercato . "%'                                             
                                ORDER BY data";
        $result = pgsql_query($sql_elenco_log);
        $elenco_log = pg_fetch_all($result);

        foreach ($elenco_log as $log) {
            $dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato'] = unserialize($log['dato']);
            $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quando'] = date('d-m-Y H:i:s', $log['data']);
            $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'] = explode('::', $log['chi'])[2];
            if(stripos($dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'], "nexus") !== false){
                $dato[$db_in_esame["nome"]][$log['id_log_storico']]['quale_utente'] = "Assistenza-importazione";
            }
            
            // Verifica se i dati sono relativi agli studenti
            if(isset($dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato']['studente_originale']) && 
               isset($dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato']['studente_modificato'])) {
                
                $dato[$db_in_esame["nome"]][$log['id_log_storico']]['modifiche'][] = "Studente: " . $dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato']['studente_originale']['cognome'] . " " . $dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato']['studente_originale']['nome'];
                
                foreach($dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato']['studente_originale'] as $chiave => $campo){
                    if(($chiave != 'chi_modifica') && ($chiave != 'data_modifica')){
                        if($dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato']['studente_modificato'][$chiave] != $campo){
                            if($chiave == 'data_nascita'){
                                $valore_originale = date('d-m-Y H:i', $campo);
                                $valore_nuovo = date('d-m-Y H:i', $dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato']['studente_modificato'][$chiave]);
                            }else{
                                $valore_originale = $campo;
                                $valore_nuovo = $dato[$db_in_esame["nome"]][$log['id_log_storico']]['dato']['studente_modificato'][$chiave];
                            }
                            if(strlen($valore_originale) > 0){
                                $valore_originale = $valore_originale;
                            }else{
                                $valore_originale = "VUOTO";
                            }
                            if(strlen($valore_nuovo) > 0){
                                $valore_nuovo = $valore_nuovo;
                            }else{
                                $valore_nuovo = "VUOTO";
                            }
                            $dato[$db_in_esame["nome"]][$log['id_log_storico']]['modifiche'][] = "Modifica rilevata in: " . $chiave . " da " . $valore_originale . " a " . $valore_nuovo;
                        }
                    }
                }
            } else {
                // Se non è un log di modifica studente strutturato, aggiungi informazioni generiche
                $dato[$db_in_esame["nome"]][$log['id_log_storico']]['modifiche'][] = "Log generico - Tipo: " . $log['tipo'];
                $dato[$db_in_esame["nome"]][$log['id_log_storico']]['modifiche'][] = "Contenuto: " . substr($log['cosa'], 0, 200) . (strlen($log['cosa']) > 200 ? '...' : '');
            }
        }
    }
}

$db_key = $db_attuale;

$tipo_invio = estrai_parametri_singoli('TIPO_INVIO_EMAIL');
$nome_scuola = estrai_parametri_singoli('NOME_SCUOLA');

if ($tipo_invio == 'MAILER'){
    $messages_api = [];
    $utenza = estrai_utenza_api_email();
    if (empty($utenza)) {
        $abilita_invio = false;
    } else {
        $abilita_invio = true;
    }
}

$html = "<html>
            <head>
            <style>
              /* Stile CSS inline per la tabella */
              .styled-table {
                border-collapse: collapse;
                width: 100%;
                max-width: 700px; /* Imposta la larghezza massima della tabella */
                margin: 0 auto; /* Centra la tabella nella larghezza disponibile */
                border: 2px solid #ccc; /* Aggiunge un bordo alla tabella */
              }

              .styled-table th, .styled-table td {
                padding: 8px;
                border-bottom: 1px solid #ddd; /* Aggiunge una riga di separazione tra le celle */
              }

              .styled-table th {
                background-color: #f2f2f2; /* Sfondo grigio per le celle dell'intestazione */
              }
            </style>
            </head>
            <body>";    

if ($abilita_invio) {
                
    $html .= '<table class="styled-table" style="margin-left: 0;">';
    $html .= "<tr><th align='center'>ELENCO MODIFICHE RISCONTRATE -- CAMPO DI RICERCA: " . $campo_cercato . "</th></tr>";
    $html .= "</table><br><br>";

    if((is_array($dato)) && (count($dato) > 0)){
        foreach ($dato as $db_in_analisi => $dato_anno) {
            foreach ($dato_anno as $log) {
                $html .= '<table class="styled-table" style="margin-left: 0;">';
                $html .= "<tr><th align='center'>MODIFICHE EFFETTUATE NEL DB " . $db_in_analisi . " IL: " . $log['quando'] . " da " . $log['quale_utente'] . "</th></tr>";
                foreach ($log['modifiche'] as $modifica) {
                    $html .= "<tr><td align='left'>";
                    $html .= $modifica;
                    $html .= "</td></tr>";
                }
                $html .= "</table><br><br>";
            }
        }
    }else{
        $html .= '<table class="styled-table" style="margin-left: 0;">';
        $html .= "<tr><th align='center'>NESSUN LOG TROVATO PER LA CHIAVE INDICATA</th></tr>";
        $html .= "</table><br><br>";
    }
           
    $html .= "</body>";
    $html .= "</html>";
    
    $message = [];
    $message['id'] = uniqid();
    $message['subject'] = "Rilevazione modifiche allo studente con campo di ricerca " . $campo_cercato . " - " . date('Y-m-d H:i') . " " . $nome_scuola;
    $message['content_subtype'] = "html";
    $message['to'] = [$email_per_invio_report];
    $message['body'] = $html;

    $messages_api = [];
    $messages_api[] = $message;
    $current_user = '-1';
    $invio_mailer = invia_email($messages_api, $current_user);

    if ($invio_mailer['status'] == 'KO'){
        $errori_mail = $invio_mailer['message'];
        $response = $invio_mailer['response'];
        //salvo gli errori in tmp
        $nome_file = 'verifica_log_studenti_invio_email.txt';
        file_put_contents('/tmp/'.$nome_file, print_r($errori_mail,true));
        file_put_contents('/tmp/'.$nome_file, print_r(chr(13),true), FILE_APPEND);
        file_put_contents('/tmp/'.$nome_file, print_r($response,true), FILE_APPEND);
        file_put_contents('/tmp/'.$nome_file, print_r(chr(13),true), FILE_APPEND);
    }else{
        var_dump($invio_mailer);
        echo "\n Mail inviata correttamente \n"; 
    }
}

file_put_contents('/tmp/verifica_log_studenti_esito.txt', print_r("ELENCO STUDENTI RILEVATI\n",true));
file_put_contents('/tmp/verifica_log_studenti_esito.txt', print_r($dato,true), FILE_APPEND);
exit;
