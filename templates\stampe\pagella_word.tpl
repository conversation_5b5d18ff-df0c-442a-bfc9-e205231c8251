<div id="sfondo_oscurato" style="width: 100%;height: 100%;top: 0;left: 0;position: fixed;background-color: #aaa;opacity: 0.5;z-index: 1020;display: none;"></div>
<div id="scheda_pagella_word" class="div_scheda_generica">
    <div class="sfondo_azzurro ombra_testo" style="padding: 10px 5px;">
        <div class="bold testo_bianco" align="center" style="font-size: 120%;">
            {$pagella_word['nome']}
        </div>
    </div>

    <div class="padding6">
        <div class="scheda_interna bordo_grigio_chiaro margin-top8 ">

            <div style="width: 100%; margin-bottom: 10px; overflow: auto;">
                <button type="button"
                    class="btn_flat_new sfondo_azzurro testo_bianco hover_opacity" 
                    style="float: right; margin-right: 20px;"
                    alt="Scarica il manuale"
                    onclick="//window.open('https://drive.google.com/file/d/1TN1dNRz7RXqrgevuckulqeUVFwyTBq0j/view?usp=drive_link', '_blank');
                    window.open('https://drive.google.com/uc?export=download&id=1TN1dNRz7RXqrgevuckulqeUVFwyTBq0j', '_blank');
                    return false;"
                ><i class="fas fa-book-open"></i> Manuale</button>
            </div> 
            {* to fix css @@@START *}
            <div class="scheda_flex_responsive">
                <div style="flex: 1; margin-right: 10px; min-width: 700px;">
                    <div style="text-align: center;" class="padding-bottom-10"><strong>Spunta le classi</strong></div>
                    {mastercom_grid_classes
                        mat_classi=$elenco_classi
                        mat_checks=$id_classi
                        onclick_submit='NO'
                        onclick_set_hidden_id='id_classe'
                        onclick_set_hidden_name='classe'
                        status_light ='no'
                        checks_active='si'
                        ver2_label_bottone='label'
                        bold_text='n'
                        only_main='y'
                        background = 'transparent'
                        background2 = '#97adff21'
                        background3 = '#97c0ff2b'
                        font_size = '90%'
                    }
                </div>

                <div class="parametri_stampa" style="flex: 1;"> 
                    <div style="text-align: center;" class="padding-bottom-10"><strong>Seleziona i parametri</strong></div>
                {* //vertical-center *}
                    <div class="parametro-container">
                        <div class="parametro-label">
                            Selezionare il periodo da stampare:
                        </div>
                        <div class="parametro-input">
                            <select name="periodo">
                                {foreach $elenco_periodi as $periodo => $descrizione}
                                    <option value="{$periodo}" {if $periodo_pagella_in_uso == $periodo}selected{/if}>{$descrizione}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    {foreach $word_parametri_stampa as $parametro}
                        {if ! isset($parametro['limiti']) or ($parametro['limiti']=='superutente' and $superutente_int=='SI')}
                            <div class="parametro-container">
                                <div class="parametro-label">
                                    {$parametro['label']}:
                                </div>
                                <div class="parametro-input">
                                    {if $parametro['tipo']|strstr:"multi-"}
                                        {foreach from=$parametro.inputs item=sub_parametro}
                                            {if $sub_parametro['tipo'] == 'date'}
                                                {mastercom_smart_date
                                                    prefix=$sub_parametro['nome']
                                                    start_year=$anno_inizio
                                                    end_year=$anno_fine
                                                    month_format="%m"
                                                    field_order="DMY"
                                                    array_input_name="parametri_personalizzati"
                                                }
                                            {/if}
                                        {/foreach}
                                    {elseif $parametro['tipo'] == 'date'}
                                        {mastercom_smart_date
                                            prefix=$parametro['nome']
                                            start_year=$anno_inizio
                                            end_year=$anno_fine
                                            month_format="%m"
                                            field_order="DMY"
                                            array_input_name="parametri_personalizzati"
                                        }
                                    {elseif $parametro['tipo'] == 'select'}
                                        <select name="parametri_personalizzati[{$parametro['nome']}]">
                                            {foreach $parametro['valori']['opzioni'] as $opzione}
                                                <option value="{$opzione['valore']}" {if $opzione['valore'] == $parametro['valori']['default']}selected{/if}>{$opzione['label']}</option>
                                            {/foreach}
                                        </select>
                                    {elseif $parametro['tipo'] == 'text'}
                                        <input type="text" name="parametri_personalizzati[{$parametro['nome']}]" value="{$parametro['valori']['default']}" />
                                    {/if}
                                </div>
                            </div>
                        {/if}
                    {/foreach}

                    <div class="parametro-container">
                        <div class="parametro-label">
                            Seleziona tipologia (opzione valida solo in caso di stampa):
                        </div>
                        <div class="parametro-input">
                            <select name="destinazione_stampa">
                                <option selected value="DOWNLOAD">PDF</option>
                                <option value="ZIP">Zip per firma digitale</option>
                                <option value="ZIP_WORD">Zip con file word</option>
                                {if $superutente_int == "SI"}
                                    <option value="ZIP_TEST">Zip con documenti e Strutture dati</option>
                                {/if}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            {* to fix css @@@END *}

            <div style="text-align: center; padding: 15px 15px 7px;">

                <input type='hidden' name='stato_stampa_pagella' value='stampa'>
                <input type='hidden' name='tipo_abbinamento' value='{$tipo_abbinamento_modello}'>
                <input type='hidden' name='tipo_pagella' value='{$tipo_pagella}'>
                <input type='hidden' name='tipo_stampa' value='{$tipo_stampa}'>
                <button 
                    type="submit" 
                    class="btn_flat_new sfondo_azzurro testo_bianco hover_opacity" 
                    style="margin: 5px 10px;" 
                    onclick="return validaFormStampa(this);">
                    <i class="fa fa-print"></i>&nbsp;Stampa&nbsp;    
                </button>

                <button type="button" class="btn_flat_new sfondo_verde testo_bianco hover_opacity" style="margin: 5px 10px;" onclick="return validaFormCode(this);"
                title="Inserisci code in base ai parametri selezionati"> 
                <i class="fas fa-plus-circle"></i>&nbsp;Aggiungi alle code di stampa&nbsp;</button>
            </div>
        </div>

        <div id="div_tabella_code" style="max-height: 75vw; overflow-y: auto;">  
            <table class="grid-container" id="code_template_{$pagella_word['id_template']}">{* @@@ CODE GRID *}
            <input type='hidden' name='db_key' value='{$db_key}'>
            <colgroup>
                <col style="width: 2%;">
                <col style="width: 15%;">
                <col style="width: 15%;">
                <col style="width: 10%; min-width: 145px;">
                <col style="width: 33%;">
                <col style="width: 25%;">
            </colgroup>
            <thead class="grid-header">
                <tr class="grid-row">
                    <th class="grid-header-cell">
                        <input type="checkbox" onclick="var checkboxes = document.querySelectorAll('input[name^=sel_code]'); for(var i=0; i<checkboxes.length; i++){ checkboxes[i].checked = this.checked; }">
                    </th>
                    <th class="grid-header-cell" style="cursor:pointer;" onclick="sortTable('code_template_{$pagella_word['id_template']}', 1)">Classe <span>&#8597;</span></th>
                    <th class="grid-header-cell" style="cursor:pointer;" onclick="sortTable('code_template_{$pagella_word['id_template']}', 2)">Periodo <span>&#8597;</span></th>
                    <th class="grid-header-cell" style="cursor:pointer;" onclick="sortTable('code_template_{$pagella_word['id_template']}', 3)">Stato
                        <span class="tooltip">&#9432;
                            <span class="tooltiptext" style="padding: 1px 2px; font-weight: 100; font-size: 90%; bottom: -45; left: 205px; width: 300px;">
                                <strong> - </strong>: Stato non specificato.<br>
                                <strong>In Attesa</strong>: In attesa di elaborazione.<br>
                                <strong>In Corso</strong>: Elaborazione in corso.<br>
                                <strong>Completato</strong>: Elaborazione completata con successo.<br>
                                <strong>Errore</strong>: Errore durante l'elaborazione.<br>
                                <strong>Pubblicato</strong>: Documento pubblicato con successo.<br>        
                            </span>
                        </span>
                        <span>&#8597;</span></th>
                    </th>
                    <th class="grid-header-cell">Parametri</th>
                    {* <th class="grid-header-cell">Note</th> *}
                    <th class="grid-header-cell" style="position: relative;">Azioni 
                        <div style="position: absolute; top: 3px; right: 3px; z-index: 10;"> 
                            <button 
                                type="button" 
                                class="btn_flat_new testo_bianco hover_opacity" 
                                title="Aggiorna code"
                                onclick="aggiornaCodeDaTimer('Aggiornate code all\'ultimo stato');"
                                style="padding: 5px 5px; font-size: 12px; background-color: #5b5b64;">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <span class="tooltip">&#9432;
                                <span id="last_update_code_text" class="tooltiptext" style="padding: 1px 2px; font-weight: 100; font-size: 90%; left: -50;bottom: 20;"></span>
                            </span>
                        </div>
                        <input type='hidden' id='last_update_code' onchange="document.getElementById('last_update_code_text').innerHTML = 'Ultimo aggiornamento: ' + this.value"/>
                    </th>
                </tr>
            </thead>

            <tbody id="tbody_coda_pagella_word">
                {if $code_pagella_word|@count > 0}
                    {foreach $code_pagella_word as $coda}
                    <tr class="grid-row" id="coda_pagella_word_{$coda.id_coda}">
                        <td class="grid-cell"><input type="checkbox" name="sel_code[{$coda.id_coda}]"></td>
                        <td class="grid-cell">{$coda.classe}{$coda.sezione} {$coda.descrizione_indirizzi}</td>
                        <td class="grid-cell">{$coda.periodo_desc}</td>
                        <td class="grid-cell">
                            <span id="btn_stato_{$coda.id_coda}" 
                                {if isset($coda.data_pubblicazione_ext) && $coda.data_pubblicazione_ext != ""}
                                    class="span_status sfondo_verde_light">Pubblicato
                                {else}
                                    {if $coda.stato == ""}
                                        class="span_status btn-waiting">In Attesa
                                    {elseif $coda.stato == "IN CORSO"}
                                        class="span_status btn-in-progress">In Corso
                                    {elseif $coda.stato == "COMPLETATO"}
                                        class="span_status btn-completed">Completato
                                    {elseif $coda.stato == "ERRORE"}
                                        class="span_status btn-error">Errore
                                    {else}
                                        >{$coda.stato|default:"-"}
                                    {/if}
                                {/if}</span>
                            <span class="tooltip">&#9432;
                                <span id="coda_pagella_word_{$coda.id_coda}_stato_tooltiptext" class="tooltiptext" style="padding: 1px 2px;">
                                    {if $coda.inizio_elaborazione_ext != ""}
                                        Inizio: {$coda.inizio_elaborazione_ext}<br>
                                    {/if}
                                    {if $coda.fine_elaborazione_ext != ""}
                                        Fine: {$coda.fine_elaborazione_ext}<br>
                                    {/if}
                                    {if $coda.data_pubblicazione_ext != ""}
                                        Pubblicato: {$coda.data_pubblicazione_ext}<br>
                                    {/if}
                                </span>
                            </span>
                            <input type="hidden"
                                id="stato_{$coda.id_coda}" 
                                value="{$coda.stato}" 
                                onchange="
                                    var btn = document.getElementById('btn_stato_{$coda.id_coda}');
                                    var stato = this.value;
                                    var dataPub = document.getElementById('data_pubblicazione_{$coda.id_coda}').value;
                                    if (dataPub > 0) {
                                    } else {
                                        // Set button text and class based on stato value
                                        btn.classList.remove('btn-waiting', 'btn-in-progress', 'btn-completed', 'btn-error');
                                        if (stato === '') {
                                            btn.textContent = 'In Attesa';
                                            btn.classList.add('btn-waiting');
                                        } else if (stato === 'IN CORSO') {
                                            btn.textContent = 'In Corso';
                                            btn.classList.add('btn-in-progress');
                                        } else if (stato === 'COMPLETATO') {
                                            btn.textContent = 'Completato';
                                            btn.classList.add('btn-completed');
                                        } else if (stato === 'ERRORE') {
                                            btn.textContent = 'Errore';
                                            btn.classList.add('btn-error');
                                        } else {
                                            btn.textContent = stato || '-';
                                        }
                                    }
                                "
                            />
                            <input type="hidden" id="inizio_elaborazione_{$coda.id_coda}" value="{$coda.inizio_elaborazione}" 
                                onchange="
                                    document.getElementById('coda_pagella_word_{$coda.id_coda}_stato_tooltiptext').innerHTML = 
                                        (this.value > 0  ? 'Inizio: ' + new Date(this.value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '') + 
                                        (document.getElementById('data_pubblicazione_{$coda.id_coda}').value > 0 ? 'Pubblicato: ' + new Date(document.getElementById('data_pubblicazione_{$coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '');
                                "
                            />
                            <input type="hidden" id="fine_elaborazione_{$coda.id_coda}" value="{$coda.fine_elaborazione}" 
                                onchange="
                                    document.getElementById('coda_pagella_word_{$coda.id_coda}_stato_tooltiptext').innerHTML = 
                                        (document.getElementById('inizio_elaborazione_{$coda.id_coda}').value > 0  ? 'Inizio: ' + new Date(document.getElementById('inizio_elaborazione_{$coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '') +
                                        (this.value > 0  ? 'Fine: ' + new Date(this.value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>'  : '') +
                                        (document.getElementById('data_pubblicazione_{$coda.id_coda}').value > 0 ? 'Pubblicato: ' + new Date(document.getElementById('data_pubblicazione_{$coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '');
                                "
                            />
                            <input type="hidden" id="data_pubblicazione_{$coda.id_coda}" value="{$coda.data_pubblicazione}" 
                                onchange="
                                    if (this.value > 0) {
                                        var btn = document.getElementById('btn_stato_{$coda.id_coda}');
                                        btn.classList.add('sfondo_verde_light');
                                        btn.textContent = 'Pubblicato';
                                        disabilitaBtnDefault('{$coda.id_coda}', true);
                                    } else {
                                        var btn = document.getElementById('btn_stato_{$coda.id_coda}');
                                        btn.classList.remove('sfondo_verde_light');
                                        // Set button text based on stato value
                                        var stato = document.getElementById('stato_{$coda.id_coda}').value;
                                        // Set button text and class based on stato value
                                        btn.classList.remove('btn-waiting', 'btn-in-progress', 'btn-completed', 'btn-error');
                                        if (stato === '') {
                                            btn.textContent = 'In Attesa';
                                            btn.classList.add('btn-waiting');
                                        } else if (stato === 'IN CORSO') {
                                            btn.textContent = 'In Corso';
                                            btn.classList.add('btn-in-progress');
                                        } else if (stato === 'COMPLETATO') {
                                            btn.textContent = 'Completato';
                                            btn.classList.add('btn-completed');
                                        } else if (stato === 'ERRORE') {
                                            btn.textContent = 'Errore';
                                            btn.classList.add('btn-error');
                                        } else {
                                            btn.textContent = stato || '-';
                                        }
                                        disabilitaBtnDefault('{$coda.id_coda}', false);
                                    }
                                    document.getElementById('coda_pagella_word_{$coda.id_coda}_stato_tooltiptext').innerHTML = 
                                        (document.getElementById('inizio_elaborazione_{$coda.id_coda}').value > 0 ? 'Inizio: ' + new Date(document.getElementById('inizio_elaborazione_{$coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '') +
                                        (document.getElementById('fine_elaborazione_{$coda.id_coda}').value > 0? 'Fine: ' + new Date(document.getElementById('fine_elaborazione_{$coda.id_coda}').value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>'  : '') +
                                        (this.value > 0 ? 'Pubblicato: ' + new Date(this.value * 1000).toLocaleString('it-IT', { hour: '2-digit', minute: '2-digit', day: '2-digit', month: '2-digit', year: 'numeric', hour12: false }) + '<br>' : '');"
                            />
                        </td>
                        <td class="grid-cell">
                            <span>
                                <a href="javascript:void(0);" 
                                   onclick="var el = document.getElementById('coda_pagella_word_{$coda.id_coda}_parametri_stampa_text'); el.style.display = (el.style.display === 'none' ? 'block' : 'none'); this.textContent = (el.style.display === 'none' ? '[+]' : '[-]');"
                                   style="text-decoration: underline; cursor: pointer;">[+]</a>
                                <span id="coda_pagella_word_{$coda.id_coda}_parametri_stampa_text" style="display: none; margin-left: 5px;">
                                {if empty($coda.parametri_personalizzati_txt)}
                                    Nessun parametro personalizzato
                                {else}
                                    {$coda.parametri_personalizzati_txt}
                                {/if}
                                </span>
                            </span>
                            <input type="hidden" 
                                id="parametri_stampa_{$coda.id_coda}" 
                                value="{foreach $coda.parametri_stampa as $key => $value}{$key}: {$value}; {/foreach}"/>
                            <input type="hidden" 
                                id="parametri_stampa_{$coda.id_coda}_convertito" 
                                value="{$coda.parametri_personalizzati_txt}"
                                onchange="document.getElementById('coda_pagella_word_{$coda.id_coda}_parametri_stampa_text').innerHTML = this.value"/>
                        </td>
                        {* <td class="grid-cell">{$coda.note|default:"-"}</td> *}
                        <td class="grid-cell">
                            <span style="display: flex; gap: 6px; flex-wrap: wrap;">
                            <button 
                                id="btn_download_{$coda.id_coda}"
                                {if !$coda.id_file}disabled{/if}
                                type="button" 
                                class="btn_flat_new sfondo_azzurro testo_bianco ripples hover_opacity btn_responsive_padding" 
                                title="Download"
                                onclick="downloadFile({$coda.id_file})">
                                &nbsp;<i class="fa fa-download"></i>&nbsp;
                            </button>
                            <button 
                                id="btn_pubblica_{$coda.id_coda}"
                                {if !$coda.id_file or $coda.data_pubblicazione > 0}disabled{/if}
                                type="button" 
                                class="btn_flat_new sfondo_verde_light testo_bianco ripples hover_opacity btn_responsive_padding" 
                                title="Pubblica"
                                onclick="validaPubblicaDocumentiCoda('{$coda.id_coda}', '{$coda.destinazione_pubblicazione}')">
                                &nbsp;<i class="fas fa-cloud-upload-alt"></i>&nbsp;
                            </button>
                            <button 
                                id="btn_spubblica_{$coda.id_coda}"
                                {if !$coda.id_file or $coda.data_pubblicazione == 0}disabled{/if}
                                type="button"
                                class="btn_flat_new sfondo_azzurro testo_bianco ripples hover_opacity btn_responsive_padding" 
                                title="Revoca pubblicazione" 
                                style="background-color: #ffc107c7; color: #000000cc;"
                                onclick="validaSpubblicaDocumentiCoda('{$coda.id_coda}', '{$coda.destinazione_pubblicazione}')">
                                &nbsp;<i class="far fa-eye-slash"></i>&nbsp;
                            </button>
                            <button 
                                id="btn_modifica_{$coda.id_coda}"
                                {if $coda.data_pubblicazione > 0}disabled{/if}
                                type="button" 
                                class="btn_flat_new sfondo_arancio_new testo_bianco ripples hover_opacity btn_responsive_padding" 
                                title="Modifica parametri coda"
                                onclick="validaModificaCoda('{$coda.id_coda}', 
                                    '{$coda.classe|escape:'javascript'}{$coda.sezione|escape:'javascript'} {$coda.descrizione_indirizzi|escape:'javascript'}', 
                                    '{$coda.periodo_desc}',
                                    '{$coda.tipo_abbinamento}')">
                                &nbsp;<i class="fas fa-edit"></i>&nbsp;
                            </button> 
                            <button 
                                id="btn_elimina_{$coda.id_coda}"
                                {if $coda.data_pubblicazione > 0}disabled{/if}
                                type="button" 
                                class="btn_flat_new sfondo_rosso testo_bianco ripples hover_opacity btn_responsive_padding"
                                title="Elimina coda"
                                onclick="validaEliminaCoda('{$coda.id_coda}')">
                                &nbsp;<i class="fa fa-trash"></i>&nbsp;
                            </button>
                            </span>
                        </td>
                    </tr>
                    {/foreach}
                {else}
                    <tr id="riga_nessuna_coda" class="grid-row">
                        <td  class="grid-cell" colspan="7" style="text-align: center;">Nessuna coda presente</td>
                    </tr>
                {/if}
            </tbody>
            </table>

            <div align="center" >
                <button id="btn_pubblica_massiva_code" type="button" class="btn_flat_new sfondo_verde_light testo_bianco hover_opacity" style="margin: 5px 10px;" onclick="validaPubblicaMassivaDocumentiCoda();return false;"
                title="Pubblica le code selezionate">Pubblica</button>
            </div>
        </div>
    </div>
</div>

<div class="div_scheda_generica" id="popupGestioneCode" style="position: fixed; width: fit-content; max-width: 97vw; min-width: 500px; min-height: 400px; max-height: 97vh; left: 50%; top: 50%; transform: translate(-50%, -50%); display: none; z-index: 1050; overflow: auto;">
    <div align="center" style="width: 100%; height: 100%;">
        <button type="button" class="btn_flat_new testo_grigio padding6 hover_opacity" style="position: absolute; right: 8px;" onclick="apriChiudiPopup('popupGestioneCode', 'sfondo_oscurato', true);">✕</button>
        <div class="padding_cella_generica bold font120p margin-top10">Modifica parametri coda</div>

        <div style="margin: 10px 12px; text-align: left;">
            <div style="display: block; margin-right: 15px;">Classe: <span id="modifica_coda_classe_text" style="font-weight: bold;"></span></div>
            <div style="display: block; margin-right: 15px;">Periodo: <span id="modifica_coda_periodo_text" style="font-weight: bold;"></span></div>
        </div>
        
        <div class="padding_cella_generica padding-bottom-10" id="popupManageCodeContent" style="overflow-x: auto; overflow-y: auto;">
            {foreach $word_parametri_stampa as $parametro}
                {if ! isset($parametro['limiti']) or ($parametro['limiti']=='superutente' and $superutente_int=='SI')}
                    <div class="parametro-container">
                        <div class="parametro-label">
                            {$parametro['label']}:
                        </div>
                        <div class="parametro-input">
                            {if $parametro['tipo']|strstr:"multi-"}
                                {foreach from=$parametro.inputs item=sub_parametro}
                                    {if $sub_parametro['tipo'] == 'date'}
                                        {mastercom_smart_date
                                            prefix=$sub_parametro['nome']
                                            start_year=$anno_inizio
                                            end_year=$anno_fine
                                            month_format="%m"
                                            field_order="DMY"
                                            array_input_name="modifica_coda_parametri_personalizzati"
                                        }
                                    {/if}
                                {/foreach}
                            {elseif $parametro['tipo'] == 'date'}
                                {mastercom_smart_date
                                    prefix=$parametro['nome']
                                    start_year=$anno_inizio
                                    end_year=$anno_fine
                                    month_format="%m"
                                    field_order="DMY"
                                    array_input_name="modifica_coda_parametri_personalizzati"
                                }
                            {elseif $parametro['tipo'] == 'select'}
                                <select class="input-mc" name="modifica_coda_parametri_personalizzati[{$parametro['nome']}]">
                                    {foreach $parametro['valori']['opzioni'] as $opzione}
                                        <option value="{$opzione['valore']}" {if $opzione['valore'] == $parametro['valori']['default']}selected{/if}>{$opzione['label']}</option>
                                    {/foreach}
                                </select>
                            {elseif $parametro['tipo'] == 'text'}
                                <input type="text" name="modifica_coda_parametri_personalizzati[{$parametro['nome']}]" value="{$parametro['valori']['default']}" />
                            {/if}
                        </div>
                    </div>
                {/if}
            {/foreach}
        </div>



        <div class="padding_cella_generica">
            <button type="button" class="btn_flat_indaco hover_opacity" onclick="apriChiudiPopup('popupGestioneCode', 'sfondo_oscurato', true);">Chiudi</button>
            <button id="btn_salva_gestione_code" type="button" class="btn_flat_new sfondo_azzurro testo_bianco ripples hover_opacity" onclick="salvaModificaCode()">Salva</button>
        </div>
    </div>
    <input type="hidden" name="modifica_coda_id_coda" id="modifica_coda_id_coda" value="" />
    <input type='hidden' name="modifica_tipo_abbinamento" id="modifica_tipo_abbinamento" value="" />
    <input type="hidden" name="modifica_coda_classe" id="modifica_coda_classe" value="" onchange="document.getElementById('modifica_coda_classe_text').textContent = this.value ? this.value : '';" />
    <input type="hidden" name="modifica_coda_periodo" id="modifica_coda_periodo" value="" onchange="document.getElementById('modifica_coda_periodo_text').textContent = this.value ? this.value : '';" />
</div>


<div id="message" class="messaggio_basso_scomparsa" style="font-size: 110%; display: none;"></div>
<script type="text/javascript" src="javascript/include_modelli_docx.js?v={$js_version}"></script>