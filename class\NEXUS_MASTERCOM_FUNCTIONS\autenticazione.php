<?php

/**
 * Modifica di tutte le password e dei nome utente degli studenti
 *
 * @param integer $current_user
 */
function modifica_utente_password_studenti($current_user, $current_key) {

    $query = "SELECT id_studente FROM studenti WHERE flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {

        // NOTA: omessa sincronizzazione Moodle perchè questa funzione viene
        //       eseguita solo in fase di setup
        //$moodle = moodle_status_check();

        for ($cont = 0; $cont < $numero; $cont++) {
            $id = pg_fetch_result($result, $cont, 'id_studente');

            $utente = genera_utente_password_new(null, $current_key);
            $suffix = suffisso_codice_studente();

            if (estrai_parametri_singoli('SINCRONIZZAZIONE_PASSWORD_MASTERCOM') != 'NO'){
                $query = "UPDATE studenti SET "
                        . "  codice_studente = '{$utente['utente']}{$suffix}', "
                        . "  password_studente = '{$utente['password']}', "
                        . "  password_modificata = 0, "
                        . "  email_confermata = 0 "
                        . "WHERE id_studente = {$id}";

                pgsql_query($query) or die("Invalid $query");

                inserisci_log(['id_studente' => $id], 'studenti', $current_user, "INTERFACCIA", "MODIFICA");
            }

            if ($current_key != ""){
                $utente_couch = estrai_utente_couch($id, 'S', $current_key);

                $id_couch = $utente_couch['_id'];

                if (strlen($id_couch) > 0) {
                    $path = 'user/' . $id_couch . '/update_credentials';
                    $parametri = [
                        'username' => $utente['utente'],
                        'password' => $utente['password']
                    ];

                    $utente_couch = nextapi_call($path, 'POST', $parametri, $current_key);
                }
            }
        }
    }
}

/**
 * Modifica di tutte le password e dei nome utente dei parenti
 *
 * @param integer $current_user
 */
function modifica_utente_password_parenti($current_user, $current_key) {
    $query = "SELECT id_parente FROM parenti WHERE flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $id = pg_fetch_result($result, $cont, "id_parente");

            $utente = genera_utente_password_new('parente', $current_key);

            // $query = "UPDATE parenti SET "
            //         . "  utente = '{$utente['utente']}', "
            //         . "  codice_attivazione = '{$utente['codice_attivazione']}', "
            //         . "  password = '{$utente['password']}' "
            //         . "WHERE id_parente = {$id}";

            // pgsql_query($query) or die("Invalid $query");

            modifica_utente_password_parente($id, $utente["utente"], $utente['codice_attivazione'], $current_user, $current_key, 'tutto');

            inserisci_log(["id_parente" => $id], "parenti", $current_user, "INTERFACCIA", "MODIFICA");
        }
    }
}

/**
 * Modifica di tutte le password e nomi utente degli studenti di una classe
 *
 * @param integer $id_classe
 * @param integer $current_user
 * @param string  $sovrascrivi
 * @return string
 */
function modifica_utente_password_classe_studenti($id_classe, $current_user, $current_key, $sovrascrivi = 'SI TUTTI') {
    if ($sovrascrivi === 'SI VUOTI') {
        $query = "SELECT studenti.id_studente FROM studenti
                  INNER JOIN classi_studenti ON classi_studenti.id_studente = studenti.id_studente
                  WHERE classi_studenti.id_classe = {$id_classe}
                    AND (
						studenti.codice_studente = ''
                        OR studenti.codice_studente = '" . suffisso_codice_studente() . "'
                        OR studenti.password_studente = ''
					)
                    AND studenti.flag_canc = 0";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);
    } elseif ($sovrascrivi === 'NO') {
        $numero = 0;
    } else {
        $query = "SELECT studenti.id_studente FROM studenti
                  INNER JOIN classi_studenti ON classi_studenti.id_studente = studenti.id_studente
                  WHERE classi_studenti.id_classe = {$id_classe}
                    AND studenti.flag_canc = 0";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);
    }

    if ($numero > 0) {

        $moodle = moodle_status_check();

        for ($cont = 0; $cont < $numero; $cont++) {
            $id = pg_fetch_result($result, $cont, 'id_studente');

            $utente = genera_utente_password_new(null, $current_key);
            $suffix = suffisso_codice_studente();

            $query = "UPDATE studenti SET "
                    . "  codice_studente = '{$utente['utente']}{$suffix}', "
                    . "  password_studente = '{$utente['password']}', "
                    . "  password_modificata = 0, "
                    . "  email_confermata = 0 "
                    . "WHERE id_studente = {$id}";

            pgsql_query($query) or die("Invalid $query");

            inserisci_log(['id_studente' => $id], 'studenti', $current_user, "INTERFACCIA", "MODIFICA");

            // TODO: sistemare con chiamata unica a Moodle
            if ($moodle) {
                $data = [
                    'username' => $utente['utente'],
                    'password' => $utente['password']
                ];
                $res = \MT\Mastercom\MoodleSync::updateStudente($id, $data, true);

                /* if (isset($res->exception)) {
                  return array(
                  'esito'     => 'KO',
                  'messaggio' => $res->exception,
                  'id_utente' => $id
                  );
                  } */
            }
        }
    }
}

/**
 * Modifica di tutte le password e nomi utente dei parenti di una classe
 *
 * @param integer $id_classe
 * @param integer $current_user
 * @param string  $sovrascrivi
 * @return string
 */
function modifica_utente_password_classe_parenti($id_classe, $current_user, $sovrascrivi = 'SI TUTTI', $current_key = "") {
    if ($sovrascrivi === 'SI VUOTI') {
       $query = "SELECT parenti.* FROM parenti
			     INNER JOIN parenti_studenti ON parenti_studenti.id_parente = parenti.id_parente
				 INNER JOIN studenti_completi ON parenti_studenti.id_studente = studenti_completi.id_studente
				 WHERE studenti_completi.id_classe = {$id_classe}
					AND (
						parenti.utente = ''
						OR parenti.codice_attivazione = ''
					)";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);
    } elseif ($sovrascrivi === 'NO') {
        $numero = 0;
    } else {
        $query = "SELECT parenti.* FROM parenti
                  INNER JOIN parenti_studenti ON parenti_studenti.id_parente = parenti.id_parente
                  INNER JOIN studenti_completi ON parenti_studenti.id_studente = studenti_completi.id_studente
                  WHERE studenti_completi.id_classe = {$id_classe}";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);
    }

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $id = pg_fetch_result($result, $cont, 'id_parente');

            if ($current_key != ""){
                $utente = genera_utente_password_new('parente', $current_key);
            } else {
                $utente = genera_utente_password('parente');
            }

            $query = "UPDATE parenti SET "
                    . "  utente = '{$utente['utente']}', "
                    . "  codice_attivazione = '{$utente['codice_attivazione']}', "
                    . "  password = '{$utente['password']}' "
                    . "WHERE id_parente = {$id}";

            pgsql_query($query) or die("Invalid $query");

            if ($current_key != "") {
                $utente_couch = estrai_utente_couch($id, 'G', $current_key);

                $id_couch = $utente_couch['_id'];

                if (strlen($id_couch) > 0) {
                    $path = 'user/' . $id_couch . '/update_credentials';
                    $parametri = [
                        'username' => $utente['utente'],
                        'password' => $utente['codice_attivazione']
                    ];

                    $utente_couch = nextapi_call($path, 'POST', $parametri, $current_key);
                }
            }

            inserisci_log(['id_parente' => $id], 'parenti', $current_user, "INTERFACCIA", "MODIFICA");
        }
    }
}

/**
 * Genera una password casuale
 *
 * @param integer $length
 * @return string
 */
function generaPassword($length = 10) {
    // NOTA: I caratteri O,I,L,0,1 sono stati volutamente rimossi poichè possono
    //       confondersi tra di loro
    //$chars = "abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ23456789.";
    // 2018-03-26 Tolto il . dai caratteri possibili
    $chars = "abcdefghjkmnpqrstuvwxyz23456789";
    $password = substr(str_shuffle($chars), 0, $length);
    // fa in modo che non vi sia un punto all'inizio o alla fine della password,
    // che non vi sia una password con solo caratteri minuscoli/maiuscoli/numeri
    // che contenga il punto
    if (substr($password, 0, 1) === '.'
            || $password[$length - 1] === '.'
            //|| ctype_lower(preg_replace('/[^a-zÀ-ÿ]/i', '', $password))
            //|| ctype_upper(preg_replace('/[^a-zÀ-ÿ]/i', '', $password))
            || $password === preg_replace('/[^\d]/', '', $password)
            //|| !strpos($password, '.')
            ) {
        $password = generaPassword($length);
    }

    return $password;
}

/**
 *
 * @return string
 */
function suffisso_codice_studente() {
    // $confFile = '/etc/mastercom/configuration.ini';
    // $suffix = '';
    // $nomedb = estrai_parametri_singoli('NOME_DATABASE');
    // if (is_readable($confFile)) {
    //     $conf = parse_ini_file($confFile, true);

    //     if (isset($conf['mastercom']) && isset($conf['mastercom']['id']) && strlen($conf['mastercom']['id']) > 0) {
    //         $suffix = "@{$conf['mastercom']['id']}.registroelettronico.com";
    //     }
    // }

    $master_id = estrai_parametro_parametri('master-id');
    $suffix = "@{$master_id}.registroelettronico.com";
    if ($nomedb == 'osdb')
    {
        $suffix = "@osdb.registroelettronico.com";
    }
    return $suffix;
}

/**
 * Generazione di codice utente e password
 *
 * @param string $tipo Tipo utente (parente|studente|professore|amministratore)
 * @return array
 */
function genera_utente_password($tipo = null) {
    $stato = false;
    $suffix = suffisso_codice_studente();

    while (!$stato) {
        //Generazione codice utente
        $codice = mt_rand(100000, 999999);

        $sql = "SELECT id_studente::bigint AS id FROM studenti "
                . "WHERE replace(codice_studente, '{$suffix}', '') = '{$codice}' "
                . "UNION "
                . "SELECT id_utente::bigint AS id FROM utenti WHERE utente = '{$codice}' "
                . "UNION "
                . "SELECT id_parente::bigint AS id FROM parenti WHERE utente = '{$codice}'";

        $results = pgsql_query($sql) or die("Invalid $sql");
        $numbers = pg_num_rows($results);

        $stato = $numbers > 0 ? false : true;
    }

    $password = generaPassword();

    if (preg_match(\MT\Mastercom\Utente::REGEXP_PASSWORD, $password) !== 1) {
        //return call_user_func(${__FUNCTION__}($tipo));
        return genera_utente_password($tipo);
    } else {
        return [
            "utente"             => $codice,
            "codice_attivazione" => $password,
            "password"           => $tipo === 'parente' ? MT\Utils\Pbkdf2::encode($password) : $password
        ];
    }
}

/**
 * Generazione di codice utente e password
 *
 * @param string $tipo Tipo utente (parente|studente|professore|amministratore)
 * @return array
 */
function genera_utente_password_new($tipo = null, $current_key) {
    return nextapi_call('user/generate_user_password', 'POST', ['type' => $tipo], $current_key);
}

/**
 * Generazione indirizzo email standard per gli utenti
 *
 * @param string $tipo Tipo utente P|S
 * @return string
 */
function genera_utente_email($tipo = null) {
    $stato = false;

    switch ($tipo) {
        case 'P':
            $tabella = 'utenti';
            $campo = 'email';
            break;
        case 'S':
            $tabella = 'studenti';
            $campo = 'email1';
            break;
        default:
            return false;
    }

    while (!$stato) {
        $email = $tipo . mt_rand(100000, 999999) . "@dominio.it";

        $sql = "SELECT {$campo} FROM {$tabella} WHERE {$campo} = '{$email}'";

        $results = pgsql_query($sql) or die("Invalid $sql");
        $numbers = pg_num_rows($results);

        $stato = $numbers > 0 ? false : true;
    }

    return $email;
}

/**
 * Verifica esistenza badge gia esistenti
 *
 * @param string $badge
 * @return boolean
 */
function verifica_badge($badge) {
    $query_ver = "SELECT badge FROM studenti_completi WHERE badge = '{$badge}'
				  UNION
				  SELECT badge FROM anagrafica_personale WHERE badge = '{$badge}'";

    $result_ver = pgsql_query($query_ver) or die("Invalid $query_ver");
    $numero_ver = pg_num_rows($result_ver);

    return $numero_ver > 0 ? true : false;
}
