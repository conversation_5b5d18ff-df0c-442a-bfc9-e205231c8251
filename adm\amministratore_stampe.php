<?php

ini_set('memory_limit', '2048M');

$anno_attuale_temp = date('Y');
$template->assign('anno_attuale_temp', $anno_attuale_temp);

$data_neutra = mktime(0, 0, 0, 9, 1, $anno_inizio);
$data_fine_anno = mktime(23, 59, 59, 9, 30, $anno_fine);

$elenco_sedi = estrai_sedi();

$template->assign('elenco_sedi', $elenco_sedi);
$template->assign('valle_aosta_abilitata', $valle_aosta_abilitata);

$template->assign('db_key', $db_key);

$parametro_abilita_descrizione_materia_straniera = estrai_parametri_singoli('ABILITA_DESCRIZIONE_MATERIA_STRANIERA');

$abilita_stampe_personalizzate = estrai_parametri_singoli('ABILITA_STAMPE_PERSONALIZZATE');

// stampe personalizzate/docx per sezioni diverse dalla G e H
$elenco_stampe_personalizzate = estrai_elenco_stampe_personalizzate();
$template->assign('elenco_stampe_personalizzate', $elenco_stampe_personalizzate);
$elenco_pagelle_word = estrai_modelli_word(null, $locazione='PAGELLE');
$elenco_pagelle_word_tmp = [];
foreach ($elenco_pagelle_word as $pagella_docw) {
    // aggiungo alla lista solo se il template ha almeno un abbinamento
    $abbinamenti_word = estrai_abbinamenti_modello_word($pagella_docw['id_template']);
    if (!empty($abbinamenti_word)) {
        // creo l'elenco in base alle sezioni (G-H)
        $elenco_pagelle_word_tmp[$pagella_docw['parametri_personalizzati']['sezione_stampa']][] = $pagella_docw;
    }
}
$elenco_pagelle_word = $elenco_pagelle_word_tmp;
$template->assign('elenco_pagelle_word', $elenco_pagelle_word);

$corsi_abilitati = estrai_parametri_singoli('ABILITAZIONE_GESTIONE_CORSI');
$template->assign('corsi_abilitati', $corsi_abilitati);

$param_nuovo_tabellone = estrai_parametri_singoli('ATTIVA_NUOVO_TABELLONE_COMPETENZE');
$template->assign("param_nuovo_tabellone", $param_nuovo_tabellone);

$limiti_classi = [];
if ($current_user > 0) {
    //{{{ <editor-fold defaultstate="collapsed" desc="imposto gli indirizzi solo su quelli non limitati">
    $elenco_limiti = estrai_limiti_indirizzi($current_user, false, "id_indirizzo");
    if (is_array($elenco_limiti) and count($elenco_limiti) > 0) {
        foreach ($elenco_limiti as $key => $indirizzo_ciclo) {
            $limiti_classi['indirizzi'][$key] = $key;
        }
    } else {
        $limiti_classi['indirizzi'] = null;
    }
    //}}} </editor-fold>
}

switch ($stato_secondario) {
    //{{{ <editor-fold defaultstate="collapsed" desc="pre-controllo per gestire operazioni certificati">
    case 'salva_storico_documento':
    case "elimina_storico_documento":
        if ($stato_secondario == 'salva_storico_documento') {
            // Salvo
            if ($form_id_storico_documento > 0) {
                modifica_storico_documento((int) $form_id_storico_documento, $form_descrizione, $documento_finale, $form_tipo, (int) $current_user);
            } else {
                $form_id_storico_documento = inserisci_storico_documento($form_descrizione, $documento_finale, $form_tipo, (int) $current_user);
            }
        } else if ($stato_secondario == 'elimina_storico_documento') {
            // Elimino
            elimina_storico_documento((int) $form_id_storico_documento, 'SI', (int) $current_user);
        }

        $stato_secondario = "stampa_elenchi_particolari_display";
        $tipo_stampa = "certificati";
    // break omesso per tornare alla pagine dei certificati dopo salvataggio o eliminazione di un certificato/verbale
    //    break;
    //}}} </editor-fold>
    case 'stampa_elenchi_particolari_display':
        //{{{ <editor-fold defaultstate="collapsed">
        $servizio_mensa = estrai_parametri_singoli('ABILITA_SERVIZIO_MENSA');
        $template->assign("servizio_mensa", $servizio_mensa);

        $tasse_mc2 = estrai_parametri_singoli('GESTIONE_TASSE_MC2');
        $template->assign("tasse_mc2", $tasse_mc2);

        $array_prof = [];
        $materie = estrai_materie();
        $template->assign("materie", $materie);

        $professori = estrai_professori('P','NO', $limiti_classi);
        $array_prof[0]['nome'] = 'TUTTI';
        $array_prof[0]['valore'] = 'TUTTI';
        $cont = 1;

        foreach ($professori as $singolo_prof) {
            $array_prof[$cont]['nome'] = $singolo_prof['cognome'] . ' ' . $singolo_prof['nome'];
            $array_prof[$cont]['valore'] = $singolo_prof['id_utente'];
            $cont++;
        }

        $classi = estrai_classi("classi", $limiti_classi);
        $template->assign("classi", $classi);

        $limiti_classi["specifici"] = "multiclassi";
        $elenco_classi_secondarie = estrai_classi($ordinamento, $limiti_classi);
        $template->assign("classi_secondarie", $elenco_classi_secondarie);

        $limiti_classi["specifici"] = "quinte_superiori";
        $classi_quinte = estrai_classi('base', $limiti_classi);
        $limiti_classi["specifici"] = "";

        $template->assign("classi_quinte", $classi_quinte);

        $template->assign("array_prof", $array_prof);
        $template->assign("classi_totali", $classi);
        $template->assign("tipo_stampa", $tipo_stampa);
        $template->assign("dati_checkbox", $classi);

        //calcolo, e lo passo al template, lo step per definire le colonne della tabella delle checkbox
        $tot_record = count($classi);
        $step = intval($tot_record / 5) + 1;
        $template->assign("step", $step);

        //creo, e lo passo al template, l'array contenente la partenza di ogni riga della tabella html
        $start = [];

        for ($cont = 0; $cont < $step; $cont++) {
            $start[$cont] = $cont;
        }

        $template->assign("start", $start);

        $elenco_tipi_tasse = estrai_tipi_tasse();
        $template->assign("elenco_tipi_tasse", $elenco_tipi_tasse);

        $array_comuni = estrai_comuni_istituto();
        $template->assign("array_comuni", $array_comuni);

        $gate_in_corso = estrai_parametri_singoli('GATE_IN_CORSO');
        $template->assign("gate_in_corso", $gate_in_corso);

        $template->assign("anno_scolastico", $anno_inizio . '/' . $anno_fine);

        $stringa_stampa = estrai_parametri_stampa('RIEPILOGO_ASSENZE_GIORNALIERE');

        $parametri_stampa = explode("@", $stringa_stampa);
        $cont = 0;

        $array_parametri_finale = [];

        foreach ($parametri_stampa as $singolo_parametro) {
            $temp_array = explode("#", $singolo_parametro);

            $array_parametri_finale[$cont]["nome"] = $temp_array[0];
            $array_parametri_finale[$cont]["valore"] = $temp_array[1];

            $template->assign("rapportino_" . $array_parametri_finale[$cont]["nome"], $array_parametri_finale[$cont]["valore"]);

            $cont++;
        }

        $tipo_periodo = estrai_parametri_singoli("PERIODI_SCOLASTICI");

        if ($tipo_periodo == "trimestri") {
            $trimestri = 'SI';
            $template->assign("trimestri", $trimestri);
        }

        $tipi_handicap = estrai_tipi_handicap();
        $template->assign("tipi_handicap", $tipi_handicap);
        $pagelle_ministeriali_sito = estrai_parametri_singoli('STAMPA_PAGELLE_MINISTERIALI_SITO_NUOVO');
        $template->assign("pagelle_ministeriali_sito", $pagelle_ministeriali_sito);

        if ($tipo_pagella == 'PAGELLA_TEMPLATE_WORD_ELEMENTARI_MEDIE' || $tipo_stampa == 'PAGELLA_TEMPLATE_WORD_ELEMENTARI_MEDIE'){
            // per le personalizzate cosi' si riconduce al giro normale, lasciare in questo ordine e questa posizione
            $tipo_stampa = 'stampa_pagelle_ministeriali';
            $template->assign("tipo_stampa", $tipo_stampa);

            $tipo_pagella = 'pagella_word_elementari_medie';
            $tipo_stampa = 'pagella_word_elementari_medie';
            $template->assign("tipo_pagella", $tipo_pagella);
        }

        // se è un modello word da sezione diversa da H/G (es. I), setto le variabili per eseguire le parti giuste
        if (strpos($tipo_pagella, 'pagella_word_') !== false) {
            $tipo_stampa = $tipo_pagella;
            $tipo_pagella = "";
        }

        if ($tipo_pagella == 'REGISTRO_GENERALE_VOTI_COMPETENZE' || $tipo_stampa == 'REGISTRO_GENERALE_VOTI_COMPETENZE'){
            // per le personalizzate cosi' si riconduce al giro normale, lasciare in questo ordine e questa posizione
            $tipo_stampa = 'stampa_pagelle_ministeriali';
            $template->assign("tipo_stampa", $tipo_stampa);

            $tipo_pagella = 'registro_generale_voti_competenze';
            $tipo_stampa = 'registro_generale_voti_competenze';
            $template->assign("tipo_pagella", $tipo_pagella);
        }

        // usare le pagelle standard per le pagelle nel caso ci siano pagelle standard e personalizzate insieme
        if (
                in_array($tipo_stampa, [
                    "RELIGIONE_ELEMENTARI",
                    "RELIGIONE_MEDIE",
                    "FRONTESPIZIO_PAGELLA",
                    "PAGELLA_SUPERIORI_CARTA_BIANCA",
                    "RELIGIONE_AS2012",
                    "PAGELLA_ELEMENTARI",
                    "PAGELLA",
                    "PAGELLA_AS2012",
                    "RELIGIONE",
                    "PAGELLA_MEDIE"
                ])
        ) {
            $tipo_pagella = $tipo_stampa;
            $tipo_stampa = 'stampa_pagelle_ministeriali';
            $template->assign("tipo_stampa", $tipo_stampa);
        }

        switch ($tipo_stampa) {
            case "stampa_registro_docente_materia":
                //{{{ <editor-fold defaultstate="collapsed">
                $array_filtro_prof = [];
                $elenco_professori = estrai_professori();

                $array_filtro_prof[0]['nome'] = 'TUTTI';
                $array_filtro_prof[0]['valore'] = 'TUTTI';
                $cont = 1;

                foreach ($elenco_professori as $singolo_prof) {
                    $array_filtro_prof[$cont]['nome'] = $singolo_prof['cognome'] . ' ' . $singolo_prof['nome'];
                    $array_filtro_prof[$cont]['valore'] = $singolo_prof['id_utente'];
                    $cont++;
                }

                $template->assign("array_filtro_prof", $array_filtro_prof);

                //}}} </editor-fold>
                break;
            case 'stampa_pagelle_ministeriali':
                //{{{ <editor-fold defaultstate="collapsed">
                $classi_totali = $classi;
                $template->assign("classi_totali", $classi_totali);
                $template->assign("tipo_pagella", $tipo_pagella);

                if ($tipo_pagella == 'PAGELLA_ELEMENTARI' || $tipo_pagella == 'PAGELLA_MEDIE' || $tipo_pagella == 'RELIGIONE_ELEMENTARI' || $tipo_pagella == 'RELIGIONE_MEDIE' || $tipo_pagella == 'PAGELLA_AS2012' || $tipo_pagella == 'RELIGIONE_AS20012' || $tipo_pagella == 'DIPLOMA' || $tipo_pagella == 'CERTIFICAZIONE_COMPETENZE') {
                    switch ($tipo_pagella) {
                        case 'PAGELLA_ELEMENTARI':
                            $parametri_da_estrarre = 'PAGELLA_MINISTERIALE_ELEMENTARI';
                            break;
                        case 'PAGELLA_MEDIE':
                            $parametri_da_estrarre = 'PAGELLA_MINISTERIALE_MEDIE';
                            break;
                        case 'RELIGIONE_ELEMENTARI':
                        case 'RELIGIONE_MEDIE':
                            $parametri_da_estrarre = 'PAGELLA_RELIGIONE_ELEMENTARI_MEDIE';
                            break;
                        case 'PAGELLA_AS2012':
                            $parametri_da_estrarre = 'PAGELLA_MINISTERIALE_SUPERIORI';
                            break;
                        case 'RELIGIONE_AS2012':
                            $parametri_da_estrarre = 'RELIGIONE_MINISTERIALE_SUPERIORI';
                            break;
                        case 'DIPLOMA':
                            $parametri_da_estrarre = 'CAMPO_ISTITUTO_PERSONALIZZATO_DIPLOMA';
                            break;
                        case 'CERTIFICAZIONE_COMPETENZE':
                            $classi_certificazioni = [];
                            foreach ($classi_totali as $classe) {
                                if ($classe['tipo_indirizzo'] == 6 && $classe['classe'] == 5) {
                                    // Primarie (elementari)
                                    $classi_certificazioni[] = $classe;
                                } elseif ($classe['tipo_indirizzo'] == 4 && $classe['classe'] == 3) {
                                    // Secondarie I grado (medie)
                                    $classi_certificazioni[] = $classe;
                                } elseif ($classe['tipo_indirizzo'] == 1 && in_array($classe['classe'], [4, 5, 1])) {
                                    // Liceo Classico
                                    $classi_certificazioni[] = $classe;
                                } elseif (in_array($classe['tipo_indirizzo'], [0, 2, 3, 5]) && in_array($classe['classe'], [1, 2, 3])) {
                                    // Altri licei
                                    $classi_certificazioni[] = $classe;
                                }
                            }
                            $template->assign("classi_certificazioni", $classi_certificazioni);
                            $template->assign("trentino_abilitato", $trentino_abilitato);
                            break;
                    }

                    $stringa_stampa = estrai_parametri_stampa($parametri_da_estrarre);

                    $parametri_stampa = explode("@", $stringa_stampa);
                    $cont_parametri = 0;
                    $array_parametri_finale = [];

                    if (is_array($parametri_stampa)) {
                        foreach ($parametri_stampa as $singolo_parametro) {
                            $temp_array = explode("#", $singolo_parametro);
                            $array_parametri_finale[$cont_parametri]["nome"] = $temp_array[0];
                            $array_parametri_finale[$cont_parametri]["valore"] = $temp_array[1];
                            $template->assign($array_parametri_finale[$cont_parametri]["nome"], $array_parametri_finale[$cont_parametri]["valore"]);
                            $cont_parametri++;
                        }
                    }

                    $template->assign('stringa_stampa', $stringa_stampa);
                }
                elseif ($tipo_pagella == 'STAMPA_CONSIGLIO_ORIENTATIVO') {
                    $classi_certificazioni = [];
                    foreach ($classi as $classe) {
                        if ($classe['tipo_indirizzo'] == 4 && $classe['classe'] == 3) {
                            // Secondarie I grado (medie)
                            $classi_certificazioni[] = $classe;
                        }
                    }
                    $template->assign("classi_certificazioni", $classi_certificazioni);
                } else { // stampe personalizzate
                    switch ($tipo_pagella) {
                        case 'ss_12345_infraperiodo_marconi_pr_01':
                            $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                            $template->assign('elenco_classi', $elenco_classi);
                            break;
                    }
                }
                //}}} </editor-fold>
                break;
            case 'riepilogo_tasse':
                //{{{ <editor-fold defaultstate="collapsed" desc="generazione elenco anni">
                $elenco_anni_scolastici_disponibili[0]['nome'] = 'TUTTI';
                $elenco_anni_scolastici_disponibili[0]['valore'] = 'TUTTI';
                $elenco_anni_scolastici_disponibili[1]['nome'] = ($anno_inizio - 5) . '/' . ($anno_fine - 5);
                $elenco_anni_scolastici_disponibili[1]['valore'] = ($anno_inizio - 5) . '/' . ($anno_fine - 5);
                $elenco_anni_scolastici_disponibili[2]['nome'] = ($anno_inizio - 4) . '/' . ($anno_fine - 4);
                $elenco_anni_scolastici_disponibili[2]['valore'] = ($anno_inizio - 4) . '/' . ($anno_fine - 4);
                $elenco_anni_scolastici_disponibili[3]['nome'] = ($anno_inizio - 3) . '/' . ($anno_fine - 3);
                $elenco_anni_scolastici_disponibili[3]['valore'] = ($anno_inizio - 3) . '/' . ($anno_fine - 3);
                $elenco_anni_scolastici_disponibili[4]['nome'] = ($anno_inizio - 2) . '/' . ($anno_fine - 2);
                $elenco_anni_scolastici_disponibili[4]['valore'] = ($anno_inizio - 2) . '/' . ($anno_fine - 2);
                $elenco_anni_scolastici_disponibili[5]['nome'] = ($anno_inizio - 1) . '/' . ($anno_fine - 1);
                $elenco_anni_scolastici_disponibili[5]['valore'] = ($anno_inizio - 1) . '/' . ($anno_fine - 1);
                $elenco_anni_scolastici_disponibili[6]['nome'] = $anno_inizio . '/' . $anno_fine;
                $elenco_anni_scolastici_disponibili[6]['valore'] = $anno_inizio . '/' . $anno_fine;
                $elenco_anni_scolastici_disponibili[7]['nome'] = ($anno_inizio + 1) . '/' . ($anno_fine + 1);
                $elenco_anni_scolastici_disponibili[7]['valore'] = ($anno_inizio + 1) . '/' . ($anno_fine + 1);
                $elenco_anni_scolastici_disponibili[8]['nome'] = ($anno_inizio + 2) . '/' . ($anno_fine + 2);
                $elenco_anni_scolastici_disponibili[8]['valore'] = ($anno_inizio + 2) . '/' . ($anno_fine + 2);

                $template->assign("elenco_anni_scolastici_disponibili", $elenco_anni_scolastici_disponibili);
                //}}} </editor-fold>
                break;
            case 'foglio_notizie':
                //{{{ <editor-fold defaultstate="collapsed" desc="generazione elenco db storici">
                foreach ($dbname as $key => $value) {
                    $elenco_elementi = explode("_", $value["nome"]);
                    if (is_numeric($elenco_elementi[1])) {
                        $elenco_db_storici[$cont_elementi_db]["nome"] = $elenco_elementi[1] . "/" . $elenco_elementi[2];
                        $elenco_db_storici[$cont_elementi_db]["valore"] = $elenco_elementi[1];
                    }
                    $cont_elementi_db++;
                }

                $template->assign("elenco_db_storici", $elenco_db_storici);

                $queryOrdiniScuola = "SELECT DISTINCT tipo_indirizzo
                                      FROM indirizzi
                                      WHERE flag_canc = 0 ORDER BY tipo_indirizzo DESC";
                $result = pgsql_query($queryOrdiniScuola) or die("Invalid $queryOrdiniScuola");
                $numero = pg_num_rows($result);

                if ($numero > 0) {
                    $listaIndirizzi = pg_fetch_all($result);
                    $ordiniScuola = [];

                    foreach ($listaIndirizzi as $indirizzo) {
                        if ($indirizzo['tipo_indirizzo'] == '4') {
                            $ordiniScuola[] = [
                                'ordine' => 'MM',
                                'descrizione' => 'Scuola Secondaria I Grado'
                            ];
                        } elseif ($indirizzo['tipo_indirizzo'] == '6') {
                            $ordiniScuola[] = [
                                'ordine' => 'EE',
                                'descrizione' => 'Scuola Primaria'
                            ];
                        } else {
                            $ordiniScuola[] = [
                                'ordine' => 'SS',
                                'descrizione' => 'Scuola Secondaria II Grado'
                            ];
                        }
                    }

                    $template->assign('ordini_scuola', $ordiniScuola);
                }
                //}}} </editor-fold>
                break;
            case 'elenco_assenti_giornalieri':
                //{{{ <editor-fold defaultstate="collapsed" desc="gestione gruppi stampa">
                if ($stato_gruppo == 'crea_gruppo') {
                    $gruppo_stampa = '';

                    foreach ($classi as $classe_tmp) {
                        if (${"checkbox" . $classe_tmp["id_classe"]} == 1) {
                            $gruppo_stampa .= $classe_tmp["id_classe"] . '##@@';
                        }
                    }

                    $gruppo_stampa = substr($gruppo_stampa, 0, -4);
                    $messaggio = inserisci_gruppo_stampa($gruppo_stampa, (int) $current_user);
                    $template->assign("messaggio", $messaggio);
                    $template->assign("tipo_stampa", $tipo_stampa);
                } elseif ($stato_gruppo == 'elimina_gruppo') {
                    $messaggio = elimina_gruppo_stampa($id_gruppo_stampa, (int) $current_user);
                    $template->assign("messaggio", $messaggio);
                    $template->assign("tipo_stampa", $tipo_stampa);
                }

                $elenco_gruppi_stampa = estrai_gruppi_stampa();
                $template->assign("elenco_gruppi_stampa", $elenco_gruppi_stampa);
                //}}} </editor-fold>
                break;
            case 'stampa_elenchi':
                //{{{ <editor-fold defaultstate="collapsed" desc="estraggo solo i dati relativi alle impostazioni di stampa">
                $tipo_dati = "5";
                $dati_scuola = estrai_dati_istituto($tipo_dati);

                //SETTAGGI DELLE VARIABILI NEL CASO IN CUI NON CI FOSSERO NEL DB
                $bordo_celle = 0;
                $dimensione_font = 8;
                $grassetto = "";
                $dimensione_prima_colonna = 67;
                $dimensione_colonne = 80;
                $altezza_righe = 5.6;

                for ($cont = 0; $cont < count($dati_scuola); $cont++) {
                    switch ($dati_scuola[$cont][1]) {
                        case "STAMPA_BORDO_ELENCHI":
                            if ($dati_scuola[$cont][2] == "1") {
                                $bordo_celle = 1;
                            } else {
                                $bordo_celle = 0;
                            }
                            $id_bordo_celle = $dati_scuola[$cont][3];
                            break;
                        case "DIMENSIONE_CARATTERE_ELENCHI":
                            if (intval($dati_scuola[$cont][2]) >= 8) {
                                $dimensione_font = $dati_scuola[$cont][2];
                            } else {
                                $dimensione_font = 8;
                            }
                            $id_dimensione_font = $dati_scuola[$cont][3];
                            break;
                        case "GRASSETTO_ELENCHI":
                            if ($dati_scuola[$cont][2] == "1") {
                                $grassetto = "B";
                            } else {
                                $grassetto = "";
                            }
                            $id_grassetto = $dati_scuola[$cont][3];
                            break;
                        case "DIMENSIONE_COLONNE":
                            if (intval($dati_scuola[$cont][2]) >= 60) {
                                $dimensione_colonne = $dati_scuola[$cont][2];
                            } else {
                                $dimensione_colonne = 80;
                            }
                            $id_dimensione_colonne = $dati_scuola[$cont][3];
                            break;
                        case "ALTEZZA_RIGHE":
                            if ($dati_scuola[$cont][2] >= 5) {
                                $altezza_righe = $dati_scuola[$cont][2];
                            } else {
                                $altezza_righe = 5.6;
                            }
                            $id_altezza_righe = $dati_scuola[$cont][3];
                            break;
                        default:
                            break;
                    }
                }

                $template->assign("bordo_celle", $bordo_celle);
                $template->assign("dimensione_font", $dimensione_font);
                $template->assign("grassetto", $grassetto);
                $template->assign("dimensione_prima_colonna", $dimensione_prima_colonna);
                $template->assign("dimensione_colonne", $dimensione_colonne);
                $template->assign("altezza_righe", $altezza_righe);

                $template->assign("id_bordo_celle", $id_bordo_celle);
                $template->assign("id_dimensione_font", $id_dimensione_font);
                $template->assign("id_grassetto", $id_grassetto);
                $template->assign("id_dimensione_prima_colonna", $id_dimensione_prima_colonna);
                $template->assign("id_dimensione_colonne", $id_dimensione_colonne);
                $template->assign("id_altezza_righe", $id_altezza_righe);
                //}}} </editor-fold>
                break;
            case 'certificati':
                //{{{ <editor-fold defaultstate="collapsed">
                $data_inizio_lezioni = estrai_parametri_singoli('DATA_INIZIO_LEZIONI');
                $anno = explode('/', estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE'));
                $data_controllo_start = mktime(0, 0, 0, 8, 1, $anno[0]);
                $data_controllo_end = mktime(0, 0, 0, 10, 1, $anno[1]);

                $mat_moduli = estrai_elenco_modelli_documenti_autoselect('moduli');
                $mat_certificati = estrai_elenco_modelli_documenti_autoselect('certificati');
                $mat_dichiarazioni = estrai_elenco_modelli_documenti_autoselect('dichiarazioni');
                $mat_documenti = estrai_elenco_modelli_documenti_autoselect('vari');
                $mat_verbali = estrai_elenco_modelli_documenti_autoselect('verbali');
                $mat_verbali_stato = estrai_elenco_modelli_documenti_autoselect('esami stato');
                $mat_verbali_stato_medie = estrai_elenco_modelli_documenti_autoselect('esami stato medie');
                $template->assign("mat_moduli", $mat_moduli);
                $template->assign("mat_certificati", $mat_certificati);
                $template->assign("mat_dichiarazioni", $mat_dichiarazioni);
                $template->assign("mat_documenti", $mat_documenti);
                $template->assign("mat_verbali", $mat_verbali);
                $template->assign("mat_verbali_stato", $mat_verbali_stato);
                $template->assign("mat_verbali_stato_medie", $mat_verbali_stato_medie);

                $mat_storico_moduli = estrai_elenco_storico_documenti_autoselect('moduli', $data_controllo_start, $data_controllo_end, 0);
                $mat_storico_certificati = estrai_elenco_storico_documenti_autoselect('certificati', $data_controllo_start, $data_controllo_end, 0);
                $mat_storico_dichiarazioni = estrai_elenco_storico_documenti_autoselect('dichiarazioni', $data_controllo_start, $data_controllo_end, 0);
                $mat_storico_documenti = estrai_elenco_storico_documenti_autoselect('vari', $data_controllo_start, $data_controllo_end, 0);
                $mat_storico_verbali = estrai_elenco_storico_documenti_autoselect('verbali', $data_controllo_start, $data_controllo_end, 0);
                $mat_storico_verbali_stato = estrai_elenco_storico_documenti_autoselect('esami stato', $data_controllo_start, $data_controllo_end, 0);
                $mat_storico_verbali_stato_medie = estrai_elenco_storico_documenti_autoselect('esami stato medie', $data_controllo_start, $data_controllo_end, 0);

                $template->assign("mat_storico_moduli", $mat_storico_moduli);
                $template->assign("mat_storico_certificati", $mat_storico_certificati);
                $template->assign("mat_storico_dichiarazioni", $mat_storico_dichiarazioni);
                $template->assign("mat_storico_documenti", $mat_storico_documenti);
                $template->assign("mat_storico_verbali", $mat_storico_verbali);
                $template->assign("mat_storico_verbali_stato", $mat_storico_verbali_stato);
                $template->assign("mat_storico_verbali_stato_medie", $mat_storico_verbali_stato_medie);
                //}}} </editor-fold>
                break;
            case 'elenchi_preiscritti':
                $elenco_classi_accessibili_generale = estrai_classi_plugin((int) $current_user, 'estrai_indirizzi_vuoti', 'anno_succ');
                $template->assign("elenco_classi_accessibili_generale", $elenco_classi_accessibili_generale);
                break;
            case 'stampa_reiscrizioni_interne':
                //{{{ <editor-fold defaultstate="collapsed" desc="estraggo i dati da passare alla stampa">
                $elenco_font = [];

                for ($cont = 6; $cont < 21; $cont++) {
                    $elenco_font[$cont][0] = $cont;
                }

                $template->assign("elenco_font", $elenco_font);

                for ($cont = 10; $cont < 290; $cont++) {
                    $margine_start[$cont][0] = $cont;
                }

                $template->assign("margine_start", $margine_start);
                $template->assign("id_classe", $id_classe);

                $tipo_periodo = estrai_parametri_singoli("PERIODI_SCOLASTICI", $id_classe, 'classe');

                if ($tipo_periodo == "trimestri") {
                    $trimestri = 'SI';
                    $template->assign("trimestri", $trimestri);
                }
                //}}} </editor-fold>
                break;
            case 'stampa_password_genitori':
                $modalita_utilizzo = estrai_parametri_stampa('CREDENZIALI_PARENTI');
                $campo_libero_tagliandino = estrai_parametri_stampa('CAMPO_LIBERO_TAGLIANDINO');
                $template->assign('modalita_utilizzo', $modalita_utilizzo);
                $template->assign('campo_libero_tagliandino', $campo_libero_tagliandino);
                break;
            case 'stampa_password_studenti':
                $modalita_utilizzo = estrai_parametri_stampa('CREDENZIALI_STUDENTI');
                $campo_libero_tagliandino = estrai_parametri_stampa('CAMPO_LIBERO_TAGLIANDINO');
                $template->assign('modalita_utilizzo', $modalita_utilizzo);
                $template->assign('campo_libero_tagliandino', $campo_libero_tagliandino);
                break;
            case 'comparazione_voti':
                $queryElencoClassi = "SELECT DISTINCT id_classe, classe, sezione, codice_indirizzi
                                      FROM classi_complete
                                      WHERE tipo_indirizzo = '4' AND classe = '1' AND classificazione_indirizzi = 'MM' ORDER BY sezione";
                $result = pgsql_query($queryElencoClassi) or die("Invalid $queryElencoClassi");
                $numero = pg_num_rows($result);

                $classi = [];

                if ($numero > 0) {
                    $listaClassi = pg_fetch_all($result);

                    foreach ($listaClassi as $classe) {
                        $classi[] = [
                            'id_classe' => $classe['id_classe'],
                            'descrizione' => $classe['classe'] . $classe['sezione'] . ' ' . $classe['codice_indirizzi']
                        ];
                    }
                }

                $template->assign('classi', $classi);
                break;
            case "stampa_monteore_corsi_classe":
                //$elenco_classi = estrai_classi_corsi();
                $elenco_classi = estrai_classi('base', $limiti_classi);


                $inizio_as = mktime(0, 0, 0, 9, 1, $anno_inizio);
                $template->assign('inizio_as', $inizio_as);

                $template->assign('elenco_classi', $elenco_classi);
                break;

            case "riepilogo_risorse":
                $elenco_categorie_risorse = estrai_elenco_categorie_risorse();
                $template->assign('elenco_categorie_risorse', $elenco_categorie_risorse);
                break;

            case "riepilogo_ore_cfp":
                $elenco_classi = estrai_classi();
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case "esportazione_fidae":
                $elenco_classi = estrai_classi();
                $template->assign('esportazione_fidae', $elenco_classi);
                break;

            case "vincoli_registro_presenze":
                $elenco_classi = estrai_classi();
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case "riepilogo_assenze_01":
                $elenco_classi = estrai_classi();
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case "riepilogo_esportazione_regione":
                $elenco_classi = estrai_classi();

                $template->assign('elenco_classi', $elenco_classi);

                if ($stato_esportazione == 'classe_scelta' &&
                        $id_classe != -1) {
                    $studenti_cl = estrai_studenti_classe($id_classe);
                    $template->assign('mostra_opzioni', 'SI');
                    $template->assign('studenti', $studenti_cl);
                    $template->assign('classe_selezionata', $id_classe);
                }
                break;

            case "export_riepilogo_assenze":
                $elenco_classi = estrai_classi();
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case "elenchi_servizi_sottoscritti":
                $servizi_marketplace = estrai_servizi_marketplace();
                $template->assign('servizi_marketplace', $servizi_marketplace);
                include "stampe/stampa_elenchi_servizi_sottoscritti.php";
                break;

            case "stampa_riepilogo_studenti_classi":
                $stati_personalizzati_studente = estrai_stati_studente_personalizzati();
                $template->assign('stati_personalizzati_studente', $stati_personalizzati_studente);
                break;

            case "campi_liberi_wizard":
                //{{{ <editor-fold defaultstate="collapsed">
                $template->assign('id_classe', $id_classe);
                $template->assign('periodo_selezionato', $periodo_selezionato);
                $template->assign('stato_wizard_campi_liberi', $stato_wizard_campi_liberi);
                $template->assign('stato_stampa', $stato_stampa);

                $dati_classe = estrai_classe($id_classe);
                if ($dati_classe['tipo_indirizzo'] == '4') {
                    $periodo_selezionato = $periodo_selezionato + 20;
                }

                if (($stato_wizard_campi_liberi == 'seleziona_campi') or ( $stato_wizard_campi_liberi == 'seleziona_campo_singolo')) {
                    $voti_pagella_finale = estrai_voti_tabellone_pagellina_classe_finale($id_classe, $periodo_selezionato, $current_user);
                    $lista_campi_liberi = array();
                    if (is_array($voti_pagella_finale)) {
                        foreach ($voti_pagella_finale as $id_studente => $studente) {
                            foreach ($studente as $id_materia => $materia) {
                                foreach ($materia['campi_liberi'] as $campo_libero) {
                                    if ($campo_libero['id_padre'] != '') {
                                        $campo_padre = estrai_campo_libero($campo_libero['id_padre']);
                                        $lista_campi_liberi[$campo_padre['id_campo_libero']]['nome'] = $campo_padre['nome'];
                                        $lista_campi_liberi[$campo_padre['id_campo_libero']]['figli'][$campo_libero['id_campo_libero']]['nome'] = $campo_libero['nome'];
                                    } else {
                                        $lista_campi_liberi[$campo_libero['id_campo_libero']]['nome'] = $campo_libero['nome'];
                                    }
                                }
                            }
                        }

                        foreach ($lista_campi_liberi as $key => $value) {
                            if ($value['figli']) {
                                $appoggio_chiave = $key;
                                $appoggio_valore = $value;
                                unset($lista_campi_liberi[$key]);
                                $lista_campi_liberi[$appoggio_chiave] = $appoggio_valore;
                            }
                        }
                    }

                    //$lista_campi_liberi = estrai_codici_campi_liberi_con_valori($periodo_selezionato);
                    $template->assign('lista_campi_liberi', $lista_campi_liberi);
                }
                $template->assign('stato_wizard_campi_liberi', $stato_wizard_campi_liberi);

                //}}} </editor-fold>
                break;

            case "griglia_per_classi":
                include "stampe/griglia_per_classi.php";
                break;

            case "elenco_tag":
                $lista_tag = estrai_elenco_tag();

                $array_prof = [];
                $professori = estrai_professori();
                $cont = 1;
                foreach ($professori as $singolo_prof) {
                    $array_prof[$cont]['nome'] = $singolo_prof['cognome'] . ' ' . $singolo_prof['nome'];
                    $array_prof[$cont]['valore'] = $singolo_prof['id_utente'];
                    $cont++;
                }

                $template->assign("array_prof", $array_prof);
                $template->assign('lista_tag', $lista_tag);
                break;

            case 'elenchi_studenti':
                //estraggo tutti i campi possibili e li passo al template
                $dati_personalizzati_studente = estrai_dati_personalizzati_studente();
                $template->assign("dati_personalizzati_studente", $dati_personalizzati_studente);
                $dati_medici_studente = estrai_dati_medici_studente();
                $template->assign("dati_medici_studente", $dati_medici_studente);
                $stati_studente_personalizzati = estrai_stati_studente_personalizzati_select();
                $template->assign("stati_studente_personalizzati",$stati_studente_personalizzati);
                break;
            case 'pagella_word_elementari_medie':
            case 'registro_generale_voti_competenze':
                $elenco_periodi_pagelle = [];
                $elenco_periodi_pagelle[1] = '1^ pagellina infra-periodale';
                $elenco_periodi_pagelle[2] = '2^ pagellina infra-periodale';
                $elenco_periodi_pagelle[3] = '3^ pagellina infra-periodale';
                $elenco_periodi_pagelle[4] = '4^ pagellina infra-periodale';
                // $elenco_periodi_pagelle[5] = '5^ pagellina infra-periodale';
                // $elenco_periodi_pagelle[6] = '6^ pagellina infra-periodale';
                $elenco_periodi_pagelle[7] = 'Pagella fine primo trimestre/quadrimestre';
                $elenco_periodi_pagelle[8] = 'Pagella fine secondo trimestre';
                $elenco_periodi_pagelle[9] = 'Pagella fine anno';
                // $elenco_periodi_pagelle[10] = 'Prove strutturate';
                // $elenco_periodi_pagelle[11] = 'Tabellone voti esame di licenza';
                $template->assign("elenco_periodi_pagelle", $elenco_periodi_pagelle);

                $periodo_pagella_in_uso = estrai_parametri_singoli("PERIODO_PAGELLA_IN_USO");
                $template->assign("periodo_pagella_in_uso", $periodo_pagella_in_uso);

                $elenco_classi = estrai_classi_plugin(0, 'solo_principali');
                $elenco_classi_pagelle_competenze = [];

                foreach ($elenco_classi as $indirizzo) {
                    if ($indirizzo['tipo_indirizzo'] == '6') {
                        foreach ($indirizzo['classi_1'] as $classe) {
                            $elenco_classi_pagelle_competenze[] = $classe;
                        }
                        foreach ($indirizzo['classi_2'] as $classe) {
                            $elenco_classi_pagelle_competenze[] = $classe;
                        }
                        foreach ($indirizzo['classi_3'] as $classe) {
                            $elenco_classi_pagelle_competenze[] = $classe;
                        }
                        foreach ($indirizzo['classi_4'] as $classe) {
                            $elenco_classi_pagelle_competenze[] = $classe;
                        }
                        foreach ($indirizzo['classi_5'] as $classe) {
                            $elenco_classi_pagelle_competenze[] = $classe;
                        }
                    }
                }
                $template->assign("elenco_classi_pagelle_competenze", $elenco_classi_pagelle_competenze);
                break;
            //case "stampa_elenco_videomeeting":
               //include "stampe/stampa_elenco_videomeeting.php";
               //break;
            // -- Pagelle Personalizzate
            //{{{ <editor-fold defaultstate="collapsed" desc="Trento 4">
            case 'ee_12345_intermedia_trento4_tn_01':
            case 'ee_12345_finale_trento4_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_trento4_tn_01':
            case 'mm_123_finale_trento4_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Trento 6">
            case 'ee_12_generica_trento6_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_345_generica_trento6_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_12_generica_trento6_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_3_generica_trento6_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Ladina di Fassa">
            case 'mm_123_intermedia_ladinadifassa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_intermedia_ladinadifassa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12_finale_A3_ladinadifassa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_345_finale_A3_ladinadifassa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_12_finale_A3_ladinadifassa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_3_finale_A3_ladinadifassa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'ss_12345_finale_A3_ladinadifassa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Comenius">
            case 'ee_12_generica_comenius_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_345_generica_comenius_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_12_generica_comenius_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_3_generica_comenius_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="IC Fermi Cusano">
            case 'ee_12345_generica_fermi_cusano_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_intermedia_fermi_cusano_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_fermi_cusano_mi_01':
            case 'ee_12345_finale_fermi_cusano_mi_02':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_religione_fermi_cusano_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_religione_fermi_cusano_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_alternativa_fermi_cusano_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_alternativa_fermi_cusano_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_generica_fermi_cusano_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_fermi_cusano_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_fermi_cusano_mi_01':
            case 'mm_123_finale_fermi_cusano_mi_02':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="IC Mezzocorona">
            case 'ee_12345_intermedia_icmezzocorona_tn_01':
            case 'ee_12345_finale_A3_icmezzocorona_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_icmezzocorona_tn_01':
            case 'mm_123_finale_A3_icmezzocorona_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Primiero">
            case 'ee_12_intermedia_primiero_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_345_intermedia_primiero_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_A3_primiero_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_primiero_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_primiero_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_A3_primiero_tn_01':
            case 'ss_12345_finale_A3_primiero_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Fondo di Revo">
            case 'ee_12345_intermedia_icfondo_tn_01':
            case 'ee_12345_finale_A3_icfondo_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_icfondo_tn_01':
            case 'mm_123_finale_A3_icfondo_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Scholl">
            case 'ss_12345_finale_A3_scholl_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="IC Bassa Anaunia - Tuenno">
            case 'ee_12345_intermedia_icbassa_tn_01':
            case 'ee_12345_finale_A3_icbassa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_icbassa_tn_01':
            case 'mm_123_finale_A3_icbassa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="IC Centro Valsugana">
            case 'ee_12345_intermedia_iccentrovalsugana_tn_01':
            case 'ee_12345_finale_A3_iccentrovalsugana_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_iccentrovalsugana_tn_01':
            case 'mm_123_finale_A3_iccentrovalsugana_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="IC Strigno Tesino">
            case 'ee_12345_intermedia_icstrignotesino_tn_01':
            case 'ee_12345_finale_A3_icstrignotesino_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_icstrignotesino_tn_01':
            case 'mm_123_finale_A3_icstrignotesino_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="IC Mezzolombardo Paganella">
            case 'ee_12345_intermedia_icmezzolombardopaganella_tn_01':
            case 'ee_12345_finale_A3_icmezzolombardopaganella_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_icmezzolombardopaganella_tn_01':
            case 'mm_123_finale_A3_icmezzolombardopaganella_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Alberghiero Rovereto">
            case 'ss_1234_generica_A3_alberghiero_rovereto_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Alberghiero Levico">
            case 'ss_123_generica_A3_alberghiero_levico_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="IC Borgo Valsugana">
            case 'ee_12345_intermedia_icborgovalsugana_tn_01':
            case 'ee_12345_finale_A3_icborgovalsugana_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_icborgovalsugana_tn_01':
            case 'mm_123_finale_A3_icborgovalsugana_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Pascoli Sesto San Giovanni">
            case 'ee_12345_finale_pascolisestosangiovanni_mi_01':
            case 'ee_12345_religione_pascolisestosangiovanni_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Calvino Sesto San Giovanni">
            case 'mm_123_finale_calvinosestosangiovanni_mi_01':
            case 'mm_123_religione_calvinosestosangiovanni_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Don Milani">
            case 'ss_12345_finale_donmilani_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Rogazionisti">
            case 'mm_123_infraperiodo_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_religione_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'ss_12345_infraperiodo_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", $includi_indirizzo_fake=true);
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", $includi_indirizzo_fake=true);
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", $includi_indirizzo_fake=true);
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_religione_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", $includi_indirizzo_fake=true);
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Centromoda Canossa">
            case 'ss_1234_infraperiodo_centromodacanossa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_centromodacanossa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_1234_finale_A3_centromodacanossa_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Duomo San Giuseppe Operaio">
            case 'ee_12345_finale_A3_sangiuseppe_pd_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_intermedia_sangiuseppe_pd_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_sangiuseppe_pd_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_infraperiodo_sangiuseppe_pd_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_sangiuseppe_pd_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Artigianelli">
            case 'ss_12345_intermedia_artigianelli_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'ss_12345_finale_A3_artigianelli_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Clair">
            case 'ee_12345_intermedia_clair_pd_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_A3_clair_pd_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Gesù Maria">
            case 'ee_12345_intermedia_gesumaria_pd_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_A3_gesumaria_pd_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Pergine 2">
            case 'mm_123_intermedia_pergine2_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_pergine2_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Pergine 1">
            case 'mm_123_intermedia_pergine1_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'mm_123_finale_pergine1_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Barbarigo">
            case 'mm_123_intermedia_barbarigo_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "MM", true);
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_barbarigo_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "MM", true);
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_barbarigo_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", true);
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_barbarigo_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", true);
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Gardascuola">
            case 'ee_12345_intermedia_gardascuola_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_gardascuola_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_gardascuola_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_A3_gardascuola_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_gardascuola_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_gardascuola_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="margherita ba">
            case 'mm_123_finale_A3_istitutomargherita_ba_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "MM", true);
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="maria degli angeli">
            case 'ee_12345_finale_A3_mariadegliangeli_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Centro Francesco Lonati">
            case 'ss_1234_finale_foppa_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'certificato_competenze_foppa_bs_01':
                $classi_certificazioni = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('classi_certificazioni', $classi_certificazioni);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="San Benedetto">
            case 'ss_12345_finale_benedetto_pc_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_benedetto_pc_02':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Opere Sociali Don Bosco">
            case 'mm_123_infraperiodo_donbosco_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_osdb_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_religione_osdb_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_infraperiodo_donbosco_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_donbosco_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_religione_osdb_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{<editor-fold defaultstate="collapsed" desc="Arcivescovile">
            case 'ee_12345_intermedia_arcivescovile_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'ee_12345_finale_A3_arcivescovile_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'mm_123_infraperiodo_arcivescovile_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_rovereto = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['codice_indirizzi'] == 'RSSPG') {
                        $elenco_classi_rovereto[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_rovereto);
                break;

            case 'mm_123_intermedia_arcivescovile_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'mm_123_finale_A3_arcivescovile_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;


            case 'ss_12345_intermedia_arcivescovile_tn_trento_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $elenco_classi_trento = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['codice_indirizzi'] != 'LIA') {
                        $elenco_classi_trento[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_trento);
                break;

            case 'ss_12345_intermedia_arcivescovile_rovereto_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $elenco_classi_rovereto = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['codice_indirizzi'] == 'LIA') {
                        $elenco_classi_rovereto[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_rovereto);
                break;

            case 'ss_12345_finale_A3_arcivescovile_tn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Madonna delle Nevi">
            case 'ee_12345_infraperiodo_madonnadn_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'ee_12345_intermedia_madonnadn_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'ee_12345_finale_A3_madonnadn_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'mm_123_intermedia_madonnadn_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'mm_123_infraperiodo_madonnadn_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_infraperiodo_madonnadn_bs_accoglienza_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_madonnadn_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_madonnadn_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Salesiano San Luca">
            case 'mm_123_intermedia_salesianosanluca_bo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_salesianosanluca_bo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_salesianosanluca_bo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_salesianosanluca_bo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_infraperiodo_salesianosanluca_bo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="San Vincenzo de Paoli">
            case 'ee_12345_generica_sanvincenzo_re_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Don Bosco (BS)">
            case 'mm_123_infraperiodo_A4_donbosco_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'mm_123_generica_A3_donbosco_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'ss_12345_intermedia_A3_donbosco_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="SA (MI)">
            case 'ss_12345_intermedia_sa_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_intermedia_A3_sa_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_religione_sa_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_sa_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_infraperiodo_salesiano_santambrogio_mi':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_A3_sa_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_sa_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_generica_salesiano_santambrogio_mi':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'sa_mi_db_mi_pagella_giudizi_sospesi_ss':
                // estrazione classi anno precedente
                $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
                $year = explode('/', $anno_scolastico);

                $db_key = "mastercom_" . ($year[0] - 1) . "_" . ($year[1] - 1);
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $db_key = "mastercom_" . $year[0] . "_" . $year[1];

                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'ss_12345_infraperiodo_salesiano_santambrogio_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="DB (MI)">
            case 'ss_12345_intermedia_db_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_infraperiodo_salesiano_santambrogio_mi':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="SAN BERNARDINO salesianichiari-bs">
            case 'ss_12345_generica_salesianichiari_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_salesianichiari_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="sanfrancesco (AO)">
            case 'ee_12345_infraperiodo_sanfrancesco_ao_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'mm_123_infraperiodo_A3_sanfrancesco_ao_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="sanfrancesco (AO)">
            case 'ee_12345_infraperiodo_einaudi_ao_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'mm_123_infraperiodo_A3_einaudi_ao_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="corjesu (MI)">
            case 'ee_12345_intermedia_corjesu_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_corjesu_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_corjesu_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_corjesu_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="dameinglesi (VI)">
            case 'ee_12345_intermedia_dameinglesi_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_infraperiodo_dameinglesi_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_dameinglesi_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $elenco_classi_pass = [];
                foreach ($elenco_classi as $id_classe_c => $dati_classe) {
                    if ($dati_classe['classe'] <= 5) {
                        $elenco_classi_pass[$id_classe_c] = $dati_classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_pass);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="ispe (cr)">
            case 'ss_12345_generica_ispe_cr_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", $includi_indirizzo_fake = true, $descrizione_estesa = 'NO');
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="cfpnazareno">
            case 'ss_12345_generica_cfpnazareno_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="salesianitreviglio">
            case 'mm_123_infraperiodo_salesianitreviglio_bg_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_salesianitreviglio_bg_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_intermedia_salesianitreviglio_bg_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_infraperiodo_salesianitreviglio_bg_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_salesianitreviglio_bg_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_salesianitreviglio_bg_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'mm_123_intermedia_salesianivarese_va_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_salesianivarese_va_02':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_salesianivarese_va_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_religione_salesianivarese_va_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_salesianitreviglio_bg_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="sanbenedetto-pr">
            case 'mm_123_infraperiodo_sanbenedetto_pr_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_generica_sanbenedetto_pr_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_generica_sanbenedetto_pr_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_generica_sanbenedetto_pr_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="istitutofarina-vi">
            case 'ee_12345_intermedia_istitutofarina_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_generica_A3_istitutofarina_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_infraperiodo_istitutofarina_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_istitutofarina_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_istitutofarina_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_infraperiodo_istitutofarina_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_istitutofarina_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
            case 'ss_12345_finale_A3_istitutofarina_vi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="steam-bo">
            case 'ss_12345_intermedia_steam_bo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_steam_bo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="salesianichatillon">
            case 'ss_12345_intermedia_salesianichatillon_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_salesianichatillon_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="fratelli maristi">
            case 'ee_12345_generica_fratellimaristigiugliano_na_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_infraperiodo_fratellimaristigiugliano_na_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_generica_fratellimaristigiugliano_na_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_generica_fratellimaristicesano_mb_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_generica_fratellimaristigiugliano_na_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_istitutochampagnat_ge_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_intermedia_istitutochampagnat_ge_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_istitutochampagnat_ge_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_istitutochampagnat_ge_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_sanleonemagno_rm_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_sanleonemagno_rm_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_sanleonemagno_rm_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_pioxii_rm_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_generica_pioxii_rm_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_pioxii_rm_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_pioxii_rm_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="coverflop">
            case 'ss_12345_infraperiodo_coverfopvercelli_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", $tipo_indirizzo=false); // da cambiare sulla scuola tipo indirizzo + nel code $tipo_indirizzo=false
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_generica_coverfopvercelli_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", $tipo_indirizzo=false); // da cambiare sulla scuola tipo indirizzo + nel code $tipo_indirizzo=false
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="angelocustodeprimaria-bg">
            case 'ee_12345_generica_angelocustodeprimaria_bg_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="scuolasantadoroteacasalgrande re">
            case 'ee_12345_generica_A3_scuolasantadoroteacasalgrande_re_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_intermedia_istitutosantadorotea_arcore_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_istitutosantadorotea_arcore_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_A3_istitutosantadorotea_arcore_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_istitutosantadorotea_arcore_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_religione_istitutosantadorotea_arcore_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'certificato_competenze_istitutosantadorotea_arcore_01':
                $classi_certificazioni = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('classi_certificazioni', $classi_certificazioni);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="bertoni-ud">
            case 'ee_12345_intermedia_bertoni_ud_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_bertoni_ud_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_bertoni_ud_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'ee_12345_finale_bertoni_ud_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_bertoni_ud_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_bertoni_ud_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="csdalbenga">
            case 'ss_12345_finale_A3_csdalbenga_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="sanluigi-bo">
            case 'ee_12345_generica_sanluigi_bo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="sacrafamigliatrento">
            case 'ee_12345_generica_sacrafamigliatrento_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="myschoolticino">
            case 'ee_12345_generica_myschoolticino_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_finale_myschoolticino_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'aa_finale_myschoolticino_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "AA");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="alexandria-al">
            case 'ee_12345_generica_alexandria_al_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="antida-vc">
            case 'ee_12345_generica_A3_antida_vc_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_religione_antida_vc_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="mariadegliangeli-bs">
            case 'ee_12345_intermedia_mariadegliangeli_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="siaimarchetti-va">
            case 'ss_12345_finale_A3_siaimarchetti_va_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="icbonafini-bs">
            case 'ee_12345_finale_icbonafini_bs_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="albesteiner-to">
            case 'ss_12345_finale_albesteiner_to_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="liceoluzzago">
            case 'ss_12345_generica_A3_liceoluzzago_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="donboscoborgo">
            case 'ss_12345_generica_donboscoborgo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_generica_donboscoborgo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="salesianinovara">
            case 'ss_12345_generica_salesianinovara_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_salesianinovara_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="donboscocumiana">
            case 'mm_123_generica_donboscocumiana_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_religione_donboscocumiana_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="leonexiii-mi">
            case 'ss_12345_alternativa_leonexiii_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_intermedia_leonexiii_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_leonexiii_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_leonexiii_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_infraperiodo_leonexiii_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_leonexiii_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="sacrocuoremodena">
            case 'mm_123_infraperiodo_sacrocuoremodena_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="immacolatinequinto-ge">
            case 'ee_12345_intermedia_immacolatinequinto_ge_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="salesianinovara">
            case 'mm_123_generica_salesianinovara_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="br-ing">
            case 'ee_12345_generica_br_ing_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_generica_br_ing_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="valsalice">
            case 'ss_12345_generica_valsalice_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_valsalice_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_valsalice_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_valsalice_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_valsalice_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="gajo-mi">
            case 'ee_12345_generica_gajo_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="novalisopenschool">
            case 'ss_12345_intermedia_novalisopenschool_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="valdoccoscuola">
            case 'mm_123_generica_A3_valdoccoscuola_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="gonzaga PA">
            case 'ee_12345_generica_gonzaga_pa_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="mrua">
            case 'mm_123_generica_mrua_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_generica_mrua_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_infraperiodo_mrua_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="sacrocuore-no">
            case 'ee_12345_intermedia_sacrocuore_no_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="salesianibra">
            case 'mm_123_generica_salesianibra_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="csdalbenga">
            case 'mm_123_generica_csdalbenga_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="collegiobalbi">
            case 'mm_123_intermedia_collegiobalbi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_collegiobalbi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_infraperiodo_collegiobalbi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_infraperiodo_collegiobalbi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="iislagos">
            case 'mm_123_generica_iislagos_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_religione_iislagos_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_generica_iislagos_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_religione_iislagos_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="marymount-roma">
            case 'ss_12345_generica_A3_marymount_roma_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_generica_A3_marymount_roma_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_religione_marymount_roma_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="viterbointernationalschool">
            case 'mm_123_generica_viterbointernationalschool_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ee_12345_generica_viterbointernationalschool_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="masgb-bo">
            case 'ee_12345_generica_A3_masgb_bo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="collegiogallio">
            case 'ss_12345_infraperiodo_collegiogallio_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_infraperiodo_collegiogallio_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_intermedia_collegiogallio_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_collegiogallio_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="astori tv">
            case 'ss_12345_infraperiodo_astori_tv_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_generica_astori_tv_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_astori_tv_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_finale_A3_astori_tv_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_religione_astori_tv_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="istitutoarche-cn istitutofiore-cn">
            case 'mm_123_intermedia_istitutofa_cn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_finale_A3_istitutofa_cn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_123_religione_A3_istitutofa_cn_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="istitutofarinamestre">
            case 'mm_123_intermedia_istitutofarinamestre_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="joseftehillot mi">
            case 'mm_123_generica_joseftehillot_mi_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="salesianicaserta">
            case 'mm_123_generica_A3_salesianicaserta_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_12345_generica_A3_salesianicaserta_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="livofamily">
            case 'ss_12345_finale_A3_livofamily_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="istitutofarinamestre">
            case 'mm_123_generica_A3_istitutofarinamestre_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="MODELLI STANDARD">
            case 'RELIGIONE_ELEMENTARI':
            case 'RELIGIONE_MEDIE':
                $classi_totali = $classi;
                $template->assign("classi_totali", $classi_totali);
                $template->assign("tipo_pagella", $tipo_stampa);

                $parametri_da_estrarre = 'PAGELLA_RELIGIONE_ELEMENTARI_MEDIE';
                $stringa_stampa = estrai_parametri_stampa($parametri_da_estrarre);
                $parametri_stampa = explode("@", $stringa_stampa);
                $cont_parametri = 0;
                $array_parametri_finale = [];

                if (is_array($parametri_stampa)) {
                    foreach ($parametri_stampa as $singolo_parametro) {
                        $temp_array = explode("#", $singolo_parametro);
                        $array_parametri_finale[$cont_parametri]["nome"] = $temp_array[0];
                        $array_parametri_finale[$cont_parametri]["valore"] = $temp_array[1];
                        $template->assign($array_parametri_finale[$cont_parametri]["nome"], $array_parametri_finale[$cont_parametri]["valore"]);
                        $cont_parametri++;
                    }
                }
                $template->assign('stringa_stampa', $stringa_stampa);
                break;

            // Sezione G
            case 'PAGELLA_AS2012':
                $classi_totali = $classi;
                $template->assign("classi_totali", $classi_totali);
                $template->assign("tipo_pagella", $tipo_stampa);

                $parametri_da_estrarre = 'PAGELLA_MINISTERIALE_SUPERIORI';
                $stringa_stampa = estrai_parametri_stampa($parametri_da_estrarre);
                $parametri_stampa = explode("@", $stringa_stampa);
                $cont_parametri = 0;
                $array_parametri_finale = [];

                if (is_array($parametri_stampa)) {
                    foreach ($parametri_stampa as $singolo_parametro) {
                        $temp_array = explode("#", $singolo_parametro);
                        $array_parametri_finale[$cont_parametri]["nome"] = $temp_array[0];
                        $array_parametri_finale[$cont_parametri]["valore"] = $temp_array[1];
                        $template->assign($array_parametri_finale[$cont_parametri]["nome"], $array_parametri_finale[$cont_parametri]["valore"]);
                        $cont_parametri++;
                    }
                }
                $template->assign('stringa_stampa', $stringa_stampa);
                break;
            case 'RELIGIONE_AS2012':
                $classi_totali = $classi;
                $template->assign("classi_totali", $classi_totali);
                $template->assign("tipo_pagella", $tipo_stampa);

                $parametri_da_estrarre = 'RELIGIONE_MINISTERIALE_SUPERIORI';
                $stringa_stampa = estrai_parametri_stampa($parametri_da_estrarre);
                $parametri_stampa = explode("@", $stringa_stampa);
                $cont_parametri = 0;
                $array_parametri_finale = [];

                if (is_array($parametri_stampa)) {
                    foreach ($parametri_stampa as $singolo_parametro) {
                        $temp_array = explode("#", $singolo_parametro);
                        $array_parametri_finale[$cont_parametri]["nome"] = $temp_array[0];
                        $array_parametri_finale[$cont_parametri]["valore"] = $temp_array[1];
                        $template->assign($array_parametri_finale[$cont_parametri]["nome"], $array_parametri_finale[$cont_parametri]["valore"]);
                        $cont_parametri++;
                    }
                }
                $template->assign('stringa_stampa', $stringa_stampa);
                break;
            case 'PAGELLA':
            case 'FRONTESPIZIO_PAGELLA':
            case 'PAGELLA_SUPERIORI_CARTA_BIANCA':
            case 'PAGELLA_AS2012':
            case 'RELIGIONE_AS2012':
            case 'RELIGIONE':
                $classi_totali = $classi;
                $template->assign("classi_totali", $classi_totali);
                $template->assign("tipo_pagella", $tipo_stampa);
                break;
            //}}}</editor-fold>
            // -- Stampe Personalizzate
            //{{{ <editor-fold defaultstate="collapsed" desc="Rogazionisti">
            case 'griglia_indicatori_singolo_medie_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'griglia_indicatori_comparativa_medie_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'griglia_indicatori_singolo_superiori_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", true);
                $template->assign('elenco_classi', $elenco_classi);
                $template->assign('tipo_stampa', $tipo_stampa);
                $template->assign('stato_stampa', $stato_stampa);
                $template->assign('id_classe', $id_classe);
                $template->assign('periodo_selezionato', $periodo_selezionato);
                $template->assign('stato_wizard_campi_liberi', $stato_wizard_campi_liberi);
                break;
            case 'griglia_indicatori_comparativa_superiori_rogazionisti_pd_01':
                $elenco_classi = estrai_classi("base", "nessuna", "anno_corr", "SS", true);
                $template->assign('elenco_classi', $elenco_classi);
                $template->assign('tipo_stampa', $tipo_stampa);

                if (($stato_wizard_campi_liberi == 'seleziona_campi') or ( $stato_wizard_campi_liberi == 'seleziona_campo_singolo')) {
                    $voti_pagella_finale = estrai_voti_tabellone_pagellina_classe_finale($id_classe, $periodo_selezionato, $current_user);
                    $lista_campi_liberi = array();
                    if (is_array($voti_pagella_finale)) {
                        foreach ($voti_pagella_finale as $id_studente => $studente) {
                            foreach ($studente as $id_materia => $materia) {
                                foreach ($materia['campi_liberi'] as $campo_libero) {
                                    if ($campo_libero['id_padre'] != '') {
                                        $campo_padre = estrai_campo_libero($campo_libero['id_padre']);
                                        $lista_campi_liberi[$campo_padre['id_campo_libero']]['nome'] = $campo_padre['nome'];
                                        $lista_campi_liberi[$campo_padre['id_campo_libero']]['figli'][$campo_libero['id_campo_libero']]['nome'] = $campo_libero['nome'];
                                    } else {
                                        $lista_campi_liberi[$campo_libero['id_campo_libero']]['nome'] = $campo_libero['nome'];
                                    }
                                }
                            }
                        }

                        foreach ($lista_campi_liberi as $key => $value) {
                            if ($value['figli']) {
                                $appoggio_chiave = $key;
                                $appoggio_valore = $value;
                                unset($lista_campi_liberi[$key]);
                                $lista_campi_liberi[$appoggio_chiave] = $appoggio_valore;
                            }
                        }
                    }

                    //$lista_campi_liberi = estrai_codici_campi_liberi_con_valori($periodo_selezionato);
                    $template->assign('lista_campi_liberi', $lista_campi_liberi);

                    $template->assign('id_classe', $id_classe);
                    $template->assign('periodo_selezionato', $periodo_selezionato);
                    $template->assign('stato_wizard_campi_liberi', $stato_wizard_campi_liberi);
                    $template->assign('stato_stampa', $stato_stampa);
                }
                break;
            case 'certificato_competenze_rogazionisti_pd_01':
                $classi_totali = estrai_classi();
                $classi_certificazioni = [];
                foreach ($classi_totali as $classe) {
                    if ($classe['tipo_indirizzo'] == 6 && $classe['classe'] == 5) {
                        // Primarie (elementari)
                        $classi_certificazioni[] = $classe;
                    } elseif ($classe['tipo_indirizzo'] == 4 && $classe['classe'] == 3) {
                        // Secondarie I grado (medie)
                        $classi_certificazioni[] = $classe;
                    } elseif ($classe['tipo_indirizzo'] == 1 && in_array($classe['classe'], [4, 5, 1])) {
                        // Liceo Classico
                        $classi_certificazioni[] = $classe;
                    } elseif (in_array($classe['tipo_indirizzo'], [0, 2, 3, 5]) && in_array($classe['classe'], [1, 2, 3])) {
                        // Altri licei
                        $classi_certificazioni[] = $classe;
                    }
                }
                $template->assign("classi_certificazioni", $classi_certificazioni);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="patronato">
            case 'certificato_competenze_ee_patronato':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Opere Sociali Don Bosco">
            case 'osdb_registro_voti_mm':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'osdb_giudizi_comportamento_religione_mm':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'stampa_tabellone_osdb_mi':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;

            case 'osdb_verbali_recuperi_quadrimestre':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="sa mi e db mi">
            case 'sa_mi_db_mi_modulo_debiti_aiuti_ss':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'sa_mi_registro_voti_mm':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'sa_mi_giudizi_comportamento_religione_mm':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="gonzaga PA">
            case 'gonzaga_pa_giudizio_tutoriale_ss':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="Madonna delle Nevi">
            case 'tabellone_mm_madonnadn_bs':
                $elenco_classi = array_filter($elenco_classi_accessibili_generale, function ($indirizzo) {
                    return ($indirizzo['tipo_indirizzo'] == 4);
                });

                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'registro_voti_ss_madonnadn_bs':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="salesiani treviglio bg">
            case 'tabellone_ee_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'tabellone_mm_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'tabellone_ss_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_modulo_mdi_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'certificato_competenze_medie_12_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_12 = [];
                foreach ($elenco_classi as $id_classe_c => $dati_classe) {
                    if ($dati_classe['classe'] < 3) {
                        $elenco_classi_12[$id_classe_c] = $dati_classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_12);
                break;
            case 'mm_3_assegnazione_tutor_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $id_classe_c => $dati_classe) {
                    if ($dati_classe['classe'] == 3) {
                        $elenco_classi_3[$id_classe_c] = $dati_classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            case 'registri_segreteria_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
//                $elenco_classi = array_merge($elenco_classi, estrai_classi("base", "classi_principali", "anno_corr", "EE"));

                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'consiglio_orientativo_mm_3_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $id_classe_c => $dati_classe) {
                    if ($dati_classe['classe'] == 3) {
                        $elenco_classi_3[$id_classe_c] = $dati_classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            case 'scheda_candidato_mm_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            case 'certificato_competenze_ss_4_salesianitreviglio_bg':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $elenco_classi_4 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 4) {
                        $elenco_classi_4[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_4);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="donbosco bs">
            case 'certificato_competenze_donbosco_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'scheda_softskills_ss1_donbosco_bs_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $elenco_classi_1 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 1 && $classe['codice_indirizzi']=='LIC') {
                        $elenco_classi_1[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_1);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="bertoni-ud">
            case 'ss_profilo_personale_bertoni_ud':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="salesianivarese-va">
            case 'salesianivarese_va_registro_voti_mm':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="donboscoborgo">
            case 'educativi_mm_123_generica_donboscoborgo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'educativi_ss_12345_generica_donboscoborgo_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="leonexiii-mi">
            case 'attestato_mm_3_leone_xiii_mi':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            case 'scheda_candidato_mm_3_leone_xiii_mi':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            case 'registro_voti_leone_xiii_mi':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="agnelli-to">
            case 'mm_registro_voti_agnelli_to':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="smt-mi">
            case 'statistiche_uomini_donne_smt_mi':
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="mrua">
            case 'certificato_competenze_mrua':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="valdoccoscuola">
            case 'scheda_candidato_mm_3_valdoccoscuola':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            case 'mm_registro_voti_valdoccoscuola_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="valsalice">
            case 'mm_registro_voti_valsalice_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="marymount-roma">
            case 'scheda_candidato_mm_marymount_roma':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="salesianinovara">
            case 'scheda_candidato_mm_salesianinovara':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="donboscoborgo">
            case 'scheda_candidato_mm_donboscoborgo':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            //}}}</editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="csdalbenga">
            case 'registro_voti_mm_csdalbenga':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'registro_voti_ss_csdalbenga':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="iislagos">
            case 'ss_registro_voti_iislagos_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'ss_registro_voti_generale_iislagos_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_registro_voti_iislagos_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'mm_registro_voti_generale_iislagos_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="liceoluzzago">
            case 'registro_voti_ss_liceoluzzago_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="astori tv">
            case 'astori_tv_ss_12345_lettera_01':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "SS");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="collegiogallio">
            case 'scheda_candidato_mm_collegiogallio':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            //}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="collegiobalbi">
            case 'collegiobalbi_tabellone_esiti_ee':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "EE");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'collegiobalbi_tabellone_esiti_mm':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $template->assign('elenco_classi', $elenco_classi);
                break;
            case 'scheda_candidato_mm_3_collegiobalbi':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            //}}} </editor-fold>
            //{{{<editor-fold defaultstate="collapsed" desc="Arcivescovile-tn">
            case 'scheda_candidato_mm_arcivescovile_tn':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            //}}}</editor-fold>
            //{{{<editor-fold defaultstate="collapsed" desc="">
            case 'certificato_esame_stato_mm_fratellimaristicesano_mb':
                $elenco_classi = estrai_classi("base", "classi_principali", "anno_corr", "MM");
                $elenco_classi_3 = [];
                foreach ($elenco_classi as $classe) {
                    if ($classe['classe'] == 3) {
                        $elenco_classi_3[] = $classe;
                    }
                }
                $template->assign('elenco_classi', $elenco_classi_3);
                break;
            //}}}</editor-fold>


            default:
                // se è un modello di pagella word
                if (
                        strpos($tipo_pagella, 'pagella_word_') !== false
                            ||
                        strpos($tipo_stampa, 'pagella_word_') !== false)
                {
                    $id_template_pagella_word = str_replace('pagella_word_', '', $tipo_stampa);
                    $pagella_word = estrai_modelli_word($id_template_pagella_word)[0];

                    $dict_periodo_desc = [
                        '1' => '1^ pagellina infra-periodale',
                        '2' => '2^ pagellina infra-periodale',
                        '3' => '3^ pagellina infra-periodale',
                        '4' => '4^ pagellina infra-periodale',
                        '5' => '5^ pagellina infra-periodale',
                        '6' => '6^ pagellina infra-periodale',
                        '7' => 'Fine primo trimestre/quadrimestre',
                        '8' => 'Fine secondo trimestre',
                        '9' => 'Fine anno',
                    ];

                    $abbinamenti_word = estrai_abbinamenti_modello_word($id_template_pagella_word);
                    $elenco_classi = $elenco_periodi = []; $tipo_abbinamento_modello = "";
                    foreach ($abbinamenti_word as $abb_word) {
                        // aggiungi le classi assegnati al modello ai parametri di stampa
                        $abb_id_classe = $abb_word['id_classe'];
                        $elenco_classi[$abb_id_classe] = estrai_classe((int) $abb_id_classe);

                        // aggiungi i periodi assegnati al modello ai parametri di stampa
                        $abb_periodo = $abb_word['periodo'];
                        $elenco_periodi[$abb_periodo] = $dict_periodo_desc[$abb_periodo];

                        // passa il tipo del modello al template (NORMALE, RELIGIONE, REGISTRO_DEI_VOTI, ALTERNATIVA)
                        $tipo_abbinamento_modello = $abb_word['tipo_abbinamento'];
                    }
                    $elenco_classi = adatta_classi_plugin($elenco_classi);
                    $periodo_pagella_in_uso = estrai_parametri_singoli("PERIODO_PAGELLA_IN_USO");

                    // code del modello
                    $code_pagella_word = estrai_code_modello($id_template_pagella_word, 'classe', $current_key);
                    // ppre($code_pagella_word); // @@@
                    
                    $template->assign("periodo_pagella_in_uso", $periodo_pagella_in_uso);
                    $template->assign('tipo_abbinamento_modello', $tipo_abbinamento_modello);
                    $template->assign('pagella_word', $pagella_word);
                    $template->assign('sezione_stampa', $pagella_word['parametri_personalizzati']['sezione_stampa']);
                    $template->assign('destinazione_pubblicazione', $pagella_word['parametri_personalizzati']['destinazione_pubblicazione']);
                    $template->assign('word_parametri_stampa', $pagella_word['parametri_personalizzati']['parametri']);
                    $template->assign('elenco_classi', $elenco_classi);
                    $template->assign("elenco_periodi", $elenco_periodi);
                    $template->assign("tipo_stampa", $tipo_stampa);
                    $template->assign("tipo_pagella", $tipo_stampa);
                    $template->assign("code_pagella_word", $code_pagella_word);
                }
        }
        //}}} </editor-fold>
        break;
    case 'stampa_elenchi_particolari_update':
        //{{{ <editor-fold defaultstate="collapsed">
        // usare le pagelle standard per le stampe standard nel caso ci siano pagelle standard e personalizzate insieme
        if (
                in_array($tipo_stampa, [
                    "RELIGIONE_ELEMENTARI",
                    "RELIGIONE_MEDIE",
                    "FRONTESPIZIO_PAGELLA",
                    "PAGELLA_SUPERIORI_CARTA_BIANCA",
                    "RELIGIONE_AS2012",
                    "PAGELLA_ELEMENTARI",
                    "PAGELLA",
                    "PAGELLA_AS2012",
                    "RELIGIONE",
                    "PAGELLA_MEDIE"
                ])
        ) {
            $tipo_pagella = $tipo_stampa;
            $tipo_stampa = 'stampa_pagelle_ministeriali';
            $stato_stampa_pagella = 'stampa';
        }

        if ($destinazione_stampa == 'SITO_GENITORI') {
            //{{{ <editor-fold defaultstate="collapsed" desc="stampa pdf singolo per studente">
            if ($id_classe > 0) {
                $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO', (int) $id_classe, 'classe');
                if ($periodo == '29' || $periodo == '9') {
                    $descrizione_periodo = "finale";
                } else {
                    $descrizione_periodo = "intermedia";
                }

                switch ($tipo_pagella) {
                    case 'PAGELLA_AS2012':
                    case 'PAGELLA_MEDIE':
                    case 'PAGELLA_ELEMENTARI':
                        $nome_pagella_per_file = 'Pagella ' . $descrizione_periodo . ' ' . $anno_inizio . '-' . $anno_fine;
                        break;
                    case 'RELIGIONE_AS2012':
                    case 'RELIGIONE_MEDIE':
                    case 'RELIGIONE_ELEMENTARI':
                        $nome_pagella_per_file = 'Pagella Religione ' . $descrizione_periodo . ' ' . $anno_inizio . '-' . $anno_fine;
                        break;
                    default:
                        $nome_pagella_per_file = '';
                        break;
                }

                if ($nome_pagella_per_file != '') {
                    $dati_studenti = estrai_studenti_classe((int) $id_classe, true);

                    if (is_array($dati_studenti)) {
                        $pagelle_generate = [];
                        $temp_dir = substr(md5(rand(0, 1000000)), 0, 12);
                        exec('mkdir ' .  MC_PATH . '/tmp_pdf/' . $temp_dir);

                        foreach ($dati_studenti as $dati_studente) {
                            $id_stud_per_stampa_sito = $dati_studente['id_studente'];
                            require 'adm/stampe/stampa_pagelle_ministeriali.php';
                            $file_name = $nome_pagella_per_file . '.pdf';
                            $pagella = MC_PATH . '/tmp_pdf/' . $temp_dir . '/' . $file_name;

                            if (file_exists($pagella)) {
                                unlink($pagella);
                            }

                            if (!($pdf->getNumPages() > 0)) {
                                continue;
                            }

                            $pdf->Output($pagella, "F");

                            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
                            $content = file_get_contents($pagella);

                            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
                            if ($file[0]['id']) {
                                messengerUpdateFile($file[0]['id'], $content);
                            } else {
                                // Destinatari: Studente + genitori
                                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                                messengerSaveFile([
                                    'content' => $content,
                                    'hidden' => false,
                                    'mime' => 'application/pdf',
                                    'name' => $file_name,
                                    'owner' => messengerGetUserID($current_user),
                                    'properties' => [
                                        'userId' => $id_stud_per_stampa_sito,
                                        'year' => "{$anno_inizio}/{$anno_fine}",
                                        'period' => $descrizione_periodo
                                    ],
                                    'recipients' => $recipients,
                                    'tags' => ['PAGELLE']
                                ]);
                            }

                            if (file_exists($pagella)) {
                                $pagelle_generate[] = "{$dati_studente['registro']}. {$dati_studente['cognome']} {$dati_studente['nome']}";
                                unlink($pagella);
                            }
                        }

                        exec('rm -fr ' . MC_PATH . '/tmp_pdf/' . $temp_dir);

                        if (empty($pagelle_generate)) {
                            echo "Errore generazione pagelle (contattare l'Assistenza)";
                        } else {
                            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
                        }
                    }
                } else {
                    echo "TIPO PAGELLA NON SALVABILE";
                }
            }
            //}}} </editor-fold>
        } elseif ($destinazione_stampa == 'ZIP') {
            //{{{ <editor-fold defaultstate="collapsed" desc="zip di pdf singoli per firma digitale">
            if ($id_classe > 0) {
                $dati_studenti = estrai_studenti_classe((int) $id_classe, true);

                if (is_array($dati_studenti)) {
                    $rel_dir = 'tmp_pdf/';
                    $base_dir = '/var/www-source/mastercom/' . $rel_dir;
                    $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
                    $dir = $temp_dir . '/';

                    exec('mkdir ' . $temp_dir);

                    //cancello tutte i file temporanei fatti da più di 1 ora
                    exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');

                    foreach ($dati_studenti as $studente) {
                        $id_stud_per_stampa_sito = $studente['id_studente'];
                        $cf_studente = $studente['codice_fiscale'];

                        require 'adm/stampe/stampa_pagelle_ministeriali.php';

                        $tipo_pagella_stampa = "";
                        switch ($tipo_pagella) {
                            case 'PAGELLA_AS2012':
                            case 'PAGELLA_MEDIE':
                            case 'PAGELLA_ELEMENTARI':
                                $tipo_pagella_stampa = 'PAG';
                                break;
                            case 'RELIGIONE_AS2012':
                            case 'RELIGIONE_MEDIE':
                            case 'RELIGIONE_ELEMENTARI':
                                $tipo_pagella_stampa = 'PAGREL';
                                break;
                            default:
                                $tipo_pagella_stampa = '';
                                break;
                        }

                        $nomefile = $id_stud_per_stampa_sito . '_' . $cf_studente . '_' .
                                $tipo_stampa . $tipo_pagella_stampa . '_' .
                                $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";

                        if (!($pdf->getNumPages() > 0)) {
                            continue;
                        }

                        $pdf->Output($dir . $nomefile, "F");
                    }

                    $nome = 'EXPORT_' . $tipo_pagella_stampa . '_' . $id_classe . '_' . date('YmdHis') . '.zip';
                    exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . $tipo_pagella_stampa . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
                    exec('rm -fr ' . $dir);
                    //Reindirizzamento JavaScript
                    echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";

                    exit;
                }
            }
            //}}} </editor-fold>
        } elseif ($destinazione_stampa == 'SEGRETERIA_DIGITALE') {
            //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
            if ($id_classe > 0) {
                $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO', (int) $id_classe, 'classe');

                if ($periodo == '29' || $periodo == '9') {
                    $descrizione_periodo = "finale";
                } else {
                    $descrizione_periodo = "intermedia";
                }

                switch ($tipo_pagella) {
                    case 'PAGELLA_AS2012':
                    case 'PAGELLA_MEDIE':
                    case 'PAGELLA_ELEMENTARI':
                        $nome_pagella_per_file = 'Pagella ' . $descrizione_periodo . ' ' . $anno_inizio . '-' . $anno_fine;
                        break;
                    case 'RELIGIONE_AS2012':
                    case 'RELIGIONE_MEDIE':
                    case 'RELIGIONE_ELEMENTARI':
                        $nome_pagella_per_file = 'Pagella Religione ' . $descrizione_periodo . ' ' . $anno_inizio . '-' . $anno_fine;
                        break;
                    default:
                        $nome_pagella_per_file = '';
                        break;
                }

                if ($nome_pagella_per_file != '') {
                    $external_data = [];
                    $dati_studenti = estrai_studenti_classe((int) $id_classe, true);

                    if (is_array($dati_studenti)) {
                        $pagelle_generate = [];
                        $temp_dir = substr(md5(rand(0, 1000000)), 0, 12);
                        exec('mkdir ' .  MC_PATH . '/tmp_pdf/' . $temp_dir);

                        foreach ($dati_studenti as $studente) {
                            $classe_studente = $studente['classe'] . $studente['sezione'] . ' - ' . $studente['descrizione_indirizzi'];

                            require 'adm/stampe/stampa_pagelle_ministeriali.php';

                            $file_name = $nome_pagella_per_file . '_' . strtoupper($studente['cognome']) . '_' . strtoupper($studente['nome']) . '.pdf';
                            $pagella = MC_PATH . '/tmp_pdf/' . $temp_dir . '/' . $file_name;

                            if (file_exists($pagella)) {
                                unlink($pagella);
                            }

                            if (!($pdf->getNumPages() > 0)) {
                                continue;
                            }

                            $pdf->Output($pagella, "F");

                            $recipients = messengerGetStudentsRecipients((int) $studente['id_studente']);
                            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $file_name);

                            foreach ($pagelle as $pagella) {
                                $external_data[basename($pagella)] = [
                                    'hidden' => false,
                                    'mime' => 'application/pdf',
                                    'name' => basename($pagella),
                                    'class' => $classe_studente,
                                    'owner' => messengerGetUserID($current_user),
                                    'properties' => [
                                        'userId' => $studente['id_studente'],
                                        'year' => "{$anno_inizio}/{$anno_fine}",
                                        'period' => $descrizione_periodo
                                    ],
                                    'recipients' => $recipients,
                                    'tags' => ['PAGELLE']
                                ];
                            }
                        }
                    }

                    $pagelle = glob(MC_PATH . '/tmp_pdf/' . $temp_dir . '/' . $nome_pagella_per_file . '_*.pdf');

                    $url_send_file = get_mc2_url()."/mc2-api/archive/document";
                    $data = [
                        'origin_id' => 3,
                        'dossier' => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
                    ];

                    $i = 0;
                    foreach ($pagelle as $pagella) {
                        $data['file[' . $i . ']'] = new CURLFile($pagella);
                        $i++;
                    }

                    $data['external_data'] = json_encode(array_values($external_data));

                    $ch = curl_init();

                    curl_setopt($ch, CURLOPT_URL, $url_send_file);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_HEADER, 0);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
                    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
                    curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
                    curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 100);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

                    $result = curl_exec($ch);

                    if ($result === FALSE) {
                        echo "Errore durante l\'invio della pagella.";
                        curl_close($ch);
                    } else {
                        echo 'Pagella inviata correttamente.';
                        curl_close($ch);
                    }

                    exec('rm -fr ' . MC_PATH . '/tmp_pdf/' . $temp_dir);
                }
            }
            //}}} </editor-fold>
        } else {
            if ($tipo_stampa != 'stampa_pagelle_ministeriali') {
                //{{{ <editor-fold defaultstate="collapsed" desc="impostazione formato pagina">
                $unita_misura = "mm";
                $margine_inferiore = 0;
                $margine_superiore = 0;
                $margine_sinistro = 0;
                $margine_destro = 0;

                if ($formato_pagina_selezionato == "A4") {
                    if ($orientamento_pagina == "L") {
                        $orientamento_pagina = "L";
                        $altezza_foglio = 210;
                        $larghezza_foglio = 297;
                    } else {
                        $orientamento_pagina = "P";
                        $altezza_foglio = 297;
                        $larghezza_foglio = 210;
                    }

                    $formato_pagina[0] = $larghezza_foglio + $margine_sinistro + $margine_destro;
                    $formato_pagina[1] = $altezza_foglio + $margine_inferiore + $margine_superiore;
                } elseif ($formato_pagina_selezionato == "A3") {
                    if ($orientamento_pagina == "L") {
                        $orientamento_pagina = "L";
                        $altezza_foglio = 297;
                        $larghezza_foglio = 420;
                    } else {
                        $orientamento_pagina = "P";
                        $altezza_foglio = 420;
                        $larghezza_foglio = 297;
                    }

                    $formato_pagina[0] = $larghezza_foglio + $margine_sinistro + $margine_destro;
                    $formato_pagina[1] = $altezza_foglio + $margine_inferiore + $margine_superiore;
                } else {
                    $orientamento_pagina = "P";

                    $altezza_foglio = 297;
                    $larghezza_foglio = 210;

                    $formato_pagina[0] = $larghezza_foglio + $margine_sinistro + $margine_destro;
                    $formato_pagina[1] = $altezza_foglio + $margine_inferiore + $margine_superiore;
                }

                $pdf = new NEXUS_PDF($orientamento_pagina, $unita_misura, $formato_pagina);
                //}}} </editor-fold>
            }

            $anno_scolastico_attuale_inserito = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
            $anno_scolastico_attuale_underscore = str_replace('/', '_', $anno_scolastico_attuale_inserito);

            $path_statistiche_istat = "adm/stampe/rilevazioni_integrative/";
            switch ($tipo_stampa) {
                case "stampa_reiscrizioni_interne":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe != "") {
                        $dati_studenti = estrai_studenti_classe((int) $id_classe);

                        $dati_classe = estrai_classe((int) $id_classe);
                        $nome_classe = $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5];

                        $pdf = new NEXUS_PDF;
                        //Data loading

                        for ($cont = 0; $cont < count($dati_studenti); $cont++) {
                            $pdf->SetFont('helvetica', 'B', $dimensione_font);

                            $pdf->AddPage();
                            $pdf->SetTextColor(0, 0, 0);

                            if (intval($dati_studenti[$cont]["ritirato"]) == 0) {
                                $pdf->SetXY($x_anno_prima_cifra, $y_anno);
                                $pdf->CellFitScale(30, 5, $anno_scolastico_selezionato, 0, 1, 'L');
                                $pdf->SetXY($x_cognome, $y_cognome);
                                $pdf->CellFitScale(80, 5, $dati_studenti[$cont][2], 0, 1, 'L');
                                $pdf->SetXY($x_nome, $y_nome);
                                $pdf->CellFitScale(80, 5, $dati_studenti[$cont][1], 0, 1, 'L');
                                $pdf->SetXY($x_classe_futura, $y_classe_futura);
                                $pdf->CellFitScale(80, 5, $classe_futura, 0, 1, 'L');

                                if (strtoupper($dati_studenti[$cont]["sesso"]) == "M") {
                                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 5);
                                    $pdf->SetXY($x_maschio, $y_crocetta);
                                    $pdf->CellFitScale(80, 5, "X", 0, 1, 'L');
                                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                                } else {
                                    $pdf->SetFont('helvetica', 'B', $dimensione_font + 5);
                                    $pdf->SetXY($x_femmina, $y_crocetta);
                                    $pdf->CellFitScale(80, 5, "X", 0, 1, 'L');
                                    $pdf->SetFont('helvetica', 'B', $dimensione_font);
                                }
                            }
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "riepilogo_assenze_studente":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe == "TUTTE") {
                        $elenco_classi = estrai_classi();
                    } else {
                        $elenco_classi[0]["id_classe"] = $id_classe;
                    }

                    //imposto i parametri per la stampa - filtri su tipi assenza
                    if ($checkbox_assenze == 1) {
                        $ctrl_assenze = 1;
                    } else {
                        $ctrl_assenze = 0;
                    }
                    if ($checkbox_entrate == 1) {
                        $ctrl_entrate = 1;
                    } else {
                        $ctrl_entrate = 0;
                    }
                    if ($checkbox_uscite == 1) {
                        $ctrl_uscite = 1;
                    } else {
                        $ctrl_uscite = 0;
                    }

                    $nuovo_elenco_studenti_assenti = [];
                    $nessuna_stampa = true;
                    foreach ($elenco_classi as $classe_attuale) {
                        $id_classe = $classe_attuale["id_classe"];
                        //imposto i parametri per la stampa - filtro data
                        $data_inizio = mktime(0, 0, 0, intval($inizio_Month), $inizio_Day, $inizio_Year);
                        $data_fine = mktime(23, 59, 1, intval($fine_Month), $fine_Day, $fine_Year);

                        $elenco_studenti_assenti = estrai_periodi_assenze_studenti_classe($data_inizio, $data_fine, (int) $id_classe, $ctrl_assenze, $ctrl_entrate, $ctrl_uscite);
                        if (is_array($elenco_studenti_assenti)) {
                            if ($tipo_elenco == "riepilogo_singolo") {
                                foreach ($elenco_studenti_assenti as $stud_sel) {
                                    $ctot = 0;
                                    foreach ($stud_sel['assenze'] as $k => $v) {
                                        $ctot += count($stud_sel['assenze'][$k]);
                                    }
                                    if ($ctot >= $periodi_assenza) {
                                        $id_stud = $stud_sel["id_studente"];
                                        include "stampe/stampa_riepilogo_assenze_singolo_studente.php";
                                        $nessuna_stampa = false;
                                    }
                                }
                            } else {
                                foreach ($elenco_studenti_assenti as $stud_sel) {
                                    $ctot = 0;
                                    foreach ($stud_sel['assenze'] as $k => $v) {
                                        $ctot += count($stud_sel['assenze'][$k]);
                                    }
                                    if ($ctot >= $periodi_assenza) {
                                        $nuovo_elenco_studenti_assenti[] = $stud_sel;
                                    }
                                }
                            }
                        }
                    }
                    if ($tipo_elenco != "riepilogo_singolo") {
                        $elenco_studenti_assenti = $nuovo_elenco_studenti_assenti;
                        include "stampe/stampa_riepilogo_assenze_elenco_totale.php";
                    } else if ($nessuna_stampa) {
                        $today = getdate();
                        $pdf->AddPage('P');
                        //stampo l'intestazione della scuola
                        inserisci_intestazione_pdf($pdf, (int) $id_classe, 37);

                        $pdf->SetFont('helvetica', 'B', 10);
                        $pdf->Cell(0, 10, "Riepilogo studenti assenti per il periodo selezionato: dal " . $inizio_Day . "/" . $inizio_Month . "/" . $inizio_Year . " al " . $fine_Day . "/" . $fine_Month . "/" . $fine_Year, 0, 1, "L");
                        $pdf->Cell(0, 10, 'Stampato il: ' . $today['mday'] . '/' . $today['mon'] . '/' . $today['year'], 0, 1, 'L');
                        $pdf->ln(5);
                        $pdf->Cell(0, 10, "Nessuno studente individuato con i criteri selezionati", 0, 1, "L");
                    }
                    //}}} </editor-fold>
                    break;
                case "riepilogo_convittori":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe == "TUTTE") {
                        $elenco_classi = estrai_classi();
                    } else {
                        $elenco_classi[0]["id_classe"] = $id_classe;
                    }
                    include "stampe/stampa_riepilogo_convittori.php";
                    //}}} </editor-fold>
                    break;
                case "curriculum":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe == "TUTTE") {
                        $elenco_classi = estrai_classi();
                    } else {
                        $elenco_classi[0]["id_classe"] = $id_classe;
                    }
                    $testo_excel = '<table>';
                    $testo_excel .= '<tr>';
                    $testo_excel .= '<td>Nome</td>';
                    $testo_excel .= '<td>Matricola</td>';
                    $testo_excel .= '<td>Nato a</td>';
                    $testo_excel .= '<td>Data nascita</td>';
                    $testo_excel .= '<td>A.S.</td>';
                    $testo_excel .= '<td>Classe frequentata</td>';
                    $testo_excel .= '<td>Esito</td>';
                    $testo_excel .= '<td>Email personale</td>';
                    $testo_excel .= '<td>Cellulare personale</td>';
                    $testo_excel .= '</tr>';

                    foreach ($elenco_classi as $classe_attuale) {
                        $id_classe = $classe_attuale["id_classe"];
                        $elenco_studenti = estrai_studenti_classe((int) $id_classe);
                        if (is_array($elenco_studenti)) {
                            if ($stampa_tipo_pdf == 'ELENCO') {
                                $pdf->AddPage('P');
                                inserisci_intestazione_pdf($pdf, $id_classe);
                            }
                            foreach ($elenco_studenti as $stud_sel) {
                                $id_stud = $stud_sel["id_studente"];
                                include "stampe/stampa_curriculum.php";
                            }
                        }
                    }
                    $testo_excel .= '</table>';
                    //}}} </editor-fold>
                    break;
                case "curriculum_bianco":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe == "TUTTE") {
                        $elenco_classi = estrai_classi();
                    } else {
                        $elenco_classi[0]["id_classe"] = $id_classe;
                    }

                    foreach ($elenco_classi as $classe_attuale) {
                        $id_classe = $classe_attuale["id_classe"];
                        $elenco_studenti = estrai_studenti_classe((int) $id_classe);
                        foreach ($elenco_studenti as $stud_sel) {
                            $id_stud = $stud_sel["id_studente"];
                            include "stampe/stampa_curriculum_bianco.php";
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "foglio_notizie":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($ordine_scuola == '') {
                        if ($id_classe == "TUTTE") {
                            $elenco_classi = estrai_classi();
                        } else {
                            $elenco_classi[0]["id_classe"] = $id_classe;
                        }
                    } else {
                        $elenco_classi = estrai_classi('base', 'no_preiscrizioni', 'anno_corr', $ordine_scuola);
                    }

                    foreach ($elenco_classi as $classe_attuale) {
                        $id_classe = $classe_attuale["id_classe"];
                        $elenco_studenti = estrai_studenti_classe((int) $id_classe);

                        foreach ($elenco_studenti as $stud_sel) {
                            $id_stud = $stud_sel["id_studente"];

                            include "stampe/stampa_foglio_notizie.php";
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "scheda_personale_candidato":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe == "TUTTE") {
                        $elenco_classi = estrai_classi('base', 'quinte_superiori');
                    } else {
                        $elenco_classi[0]["id_classe"] = $id_classe;
                    }

                    $sede_default = estrai_sedi_con_comune_e_provincia();

                    $aosta_abilitata = 'NO';
                    foreach ($sede_default as $singola_sede) {
                        if (($singola_sede['provincia'] == 'AO')) {
                            $aosta_abilitata = 'SI';
                        }
                    }

                    $today = getdate();
                    if ($db_key != $db_official) {
                        $anno_cons_sel = str_replace("_", "/", substr($db_key, 10));
                    } else {
                        $anno_scolastico_settato = explode("/", estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE"));

                        $anno_inizio_visual = $anno_scolastico_settato[0];
                        $anno_fine_visual = $anno_scolastico_settato[1];
                        $anno_cons_sel = $anno_inizio_visual . "/" . $anno_fine_visual;
                    }

                    foreach ($elenco_classi as $classe_attuale) {
                        $id_classe = $classe_attuale["id_classe"];
                        //$elenco_studenti = array();
                        //$elenco_studenti = estrai_studenti_classe($id_classe);
                        $dati_commissione = estrai_dati_singola_commissione_da_classe((int) $id_classe);

                        if ($dati_commissione["id_commissione"] > 0) {
                            $dati_commissari = estrai_abbinamenti_commissari((int) $dati_commissione["id_commissione"]);
                        }
                        $elenco_studenti = estrai_studenti_classe((int) $id_classe);
                        $almeno_uno = 'NO';
                        if (is_array($elenco_studenti)) {
                            foreach ($elenco_studenti as $studente) {
                                $id_stud = $studente["id_studente"];
                                if ($studente['ammesso_esame_quinta'] == 'SI') {
                                    $almeno_uno = 'SI';
                                }
                                if ($aosta_abilitata == 'SI') {
                                    include "stampe/stampa_scheda_personale_candidato_aosta.php";
                                } else {
                                    include "stampe/stampa_scheda_personale_candidato.php";
                                }
                            }
                            if ($almeno_uno == 'NO') {
                                $pdf->AddPage('P');
                                $pdf->SetAutoPageBreak("off", 1);
                                $messaggio = "Nella classe selezionata non sono presenti studenti ammessi all'esame di stato";
                                $nome_stampa = "STAMPA SCHEDA PERSONALE DEL CANDIDATO PER GLI ESAMI DI MATURITA'";
                                inserisci_messaggio_pdf($pdf, $messaggio, $nome_stampa);
                            }
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "scheda_personale_candidato_qualifiche":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe == "TUTTE") {
                        $elenco_classi = estrai_classi();
                    } else {
                        $elenco_classi[0]["id_classe"] = $id_classe;
                    }

                    if ($checkbox_assenze == 1) {
                        $ctrl_assenze = 1;
                    } else {
                        $ctrl_assenze = 0;
                    }

                    if ($checkbox_entrate == 1) {
                        $ctrl_entrate = 1;
                    } else {
                        $ctrl_entrate = 0;
                    }

                    if ($checkbox_uscite == 1) {
                        $ctrl_uscite = 1;
                    } else {
                        $ctrl_uscite = 0;
                    }

                    foreach ($elenco_classi as $classe_attuale) {
                        $id_classe = $classe_attuale["id_classe"];
                        $elenco_studenti = estrai_studenti_classe((int) $id_classe);
                        foreach ($elenco_studenti as $stud_sel) {
                            $id_stud = $stud_sel["id_studente"];
                            include "stampe/stampa_scheda_personale_candidato_qualifiche.php";
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "elenco_registri_classe":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe == "TUTTE") {
                        $elenco_classi = estrai_classi();
                    } else {
                        $elenco_classi[0]["id_classe"] = $id_classe;
                    }
                    include "stampe/stampa_numeri_registro_classe.php";
                    //}}} </editor-fold>
                    break;
                case "frontespizio_registro_voti":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe == "TUTTE") {
                        $elenco_classi = estrai_classi();
                    } else {
                        $elenco_classi[0]["id_classe"] = $id_classe;
                    }

                    foreach ($elenco_classi as $classe_attuale) {
                        $id_classe = $classe_attuale["id_classe"];
                        $quinta_o_terza_prof = "NO";
                        include "stampe/stampa_frontespizio_registro_voti.php";
                    }
                    //}}} </editor-fold>
                    break;
                case "elenco_assenti_giornalieri":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($stampa_gruppi == 'SI') {
                        $elenco_gruppi_stampa = estrai_gruppi_stampa();

                        foreach ($elenco_gruppi_stampa as $elenco) {
                            $cont_tmp = 0;
                            $elenco_classi_finali = [];
                            $elenco_classi = explode('##@@', $elenco['gruppo_stampa']);
                            foreach ($elenco_classi as $id_classe) {
                                $elenco_classi_finali[$cont_tmp]["id_classe"] = $id_classe;
                                $cont_tmp++;
                            }
                            include "stampe/stampa_elenco_assenti_giornalieri.php";
                        }
                    } else {
                        $elenco_classi = estrai_classi();
                        $cont_tmp = 0;
                        $elenco_classi_finali = [];
                        if ($checkbox_tutte == 1) {
                            $elenco_classi_finali = $elenco_classi;
                        } else {
                            foreach ($elenco_classi as $classe_tmp) {
                                if (${"checkbox" . $classe_tmp["id_classe"]} == 1) {
                                    $elenco_classi_finali[$cont_tmp]["id_classe"] = $classe_tmp["id_classe"];
                                    $cont_tmp++;
                                }
                            }
                        }
                        include "stampe/stampa_elenco_assenti_giornalieri.php";
                    }
                    //}}} </editor-fold>
                    break;
                case "elenco_note_disciplinari":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $elenco_classi = estrai_classi();
                    $cont_tmp = 0;
                    $elenco_classi_finali = [];
                    foreach ($elenco_classi as $classe_tmp) {
                        if (in_array($classe_tmp['id_classe'], $mat_classi)) {
                            $elenco_classi_finali[$cont_tmp]["id_classe"] = $classe_tmp["id_classe"];
                            $cont_tmp++;
                        }
                    }
                    include "stampe/stampa_elenco_note_disciplinari.php";
                    //}}} </editor-fold>
                    break;
                case "stampa_studenti_non_giustificati":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $data_inizio = mktime(1, 1, 1, intval($inizio_Month), $inizio_Day, $inizio_Year);
                    $data_fine = mktime(23, 59, 1, intval($fine_Month), $fine_Day, $fine_Year);

                    $dati_sede = estrai_sede_singola($id_sede);
                    $intestazione_stampa = $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"];

                    $pdf = new NEXUS_PDF;

                    if ($id_classe == "TUTTE") {
                        $elenchi_classi = estrai_classi();

                        if ($stampa_classe_studente == "classe") {
                            //{{{ <editor-fold defaultstate="collapsed">
                            for ($cont_classi = 0; $cont_classi < count($elenchi_classi); $cont_classi++) {
                                $id_classe = $elenchi_classi[$cont_classi][0];
                                $elenchi_assenze = estrai_studenti_non_giustificati($data_inizio, $data_fine, $giorni_ammessi, (int) $id_classe, $stampa_ritirati);
                                $dati_classe = estrai_classe((int) $id_classe);

                                $intestazione_stampa_seconda_riga = "Riepilogo Situazioni non Giustificate della classe: " . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[4];
                                $sotto_intestazione_stampa = "Periodo controllato, dal " .
                                        $inizio_Day . "/" . $inizio_Month . "/" . $inizio_Year .
                                        " al " .
                                        $fine_Day . "/" . $fine_Month . "/" . $fine_Year;
                                $pdf->AddPage('P');
                                $pdf->SetFont('helvetica', 'B', 12);
                                $pdf->Cell(0, 5, $intestazione_stampa, 0, 1, 'C');
                                $pdf->SetFont('helvetica', 'B', 10);
                                $pdf->ln(5);
                                $pdf->CellFitScale(0, 5, $sotto_intestazione_stampa, 0, 1, 'L');
                                $pdf->CellFitScale(0, 5, $intestazione_stampa_seconda_riga, 0, 1, 'L');
                                $pdf->ln(5);

                                for ($cont_stud = 0; $cont_stud < count($elenchi_assenze); $cont_stud++) {
                                    $cont_stampa = 0;
                                    $cont_entrate = 0;
                                    $cont_uscite = 0;
                                    $stampa_studente = "NO";

                                    for ($cont_ass = 0; $cont_ass < count($elenchi_assenze[$cont_stud]) - 2; $cont_ass++) {
                                        $cont_assenze_periodo = count($elenchi_assenze[$cont_stud][$cont_ass]);

                                        if ($elenchi_assenze[$cont_stud][$cont_ass][$cont_assenze_periodo - 1][0] <= strtotime("-" . $giorni_presenza . " days", $data_fine)) {
                                            $stampa_studente = "SI";
                                        }
                                    }

                                    if (((count($elenchi_assenze[$cont_stud]) - 2) >= $quantita_minima_periodi) && $stampa_studente == "SI") {
                                        $var_tmp = $elenchi_assenze[$cont_stud]["cognome"] . " " .
                                                $elenchi_assenze[$cont_stud]["nome"];
                                        $pdf->SetFont('helvetica', 'B', 10);
                                        $pdf->Cell(20, 5, $var_tmp, 0, 1, 'L');
                                        $pdf->SetFont('helvetica', '', 8);

                                        for ($cont_ass = 0; $cont_ass < count($elenchi_assenze[$cont_stud]) - 2; $cont_ass++) {
                                            $cont_assenze_periodo = count($elenchi_assenze[$cont_stud][$cont_ass]);

                                            if ($elenchi_assenze[$cont_stud][$cont_ass][$cont_assenze_periodo - 1][0] <= strtotime("-" . $giorni_presenza . " days", $data_fine)) {
                                                $var_tmp = "";

                                                for ($cont_int_ass = 0; $cont_int_ass < count($elenchi_assenze[$cont_stud][$cont_ass]); $cont_int_ass++) {
                                                    if (strlen($var_tmp) > 0) {
                                                        $var_tmp = $var_tmp . " -- " . date("d/m/Y", $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][0]);
                                                    } else {
                                                        $var_tmp = date("d/m/Y", $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][0]);
                                                    }
                                                    $tipo_attuale = $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][1];
                                                    $orario_attuale = $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][2];
                                                }
                                                switch ($tipo_attuale) {
                                                    case "1":
                                                        $cont_stampa = $cont_stampa + 1;
                                                        $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                        break;
                                                    case "6":
                                                        $cont_stampa = $cont_stampa + 1;
                                                        $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                        break;
                                                    case "7":
                                                        $cont_stampa = $cont_stampa + 1;
                                                        $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                        break;
                                                    case "2":
                                                        $cont_entrate = $cont_entrate + 1;
                                                        $pdf->MultiCell(200, 5, "Entrata n° " . $cont_entrate . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                        break;
                                                    case "8":
                                                        $cont_entrate = $cont_entrate + 1;
                                                        $pdf->MultiCell(200, 5, "Entrata n° " . $cont_entrate . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                        break;
                                                    case "3":
                                                        $cont_uscite = $cont_uscite + 1;
                                                        $pdf->MultiCell(200, 5, "Uscita n° " . $cont_uscite . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                        break;
                                                    case "9":
                                                        $cont_uscite = $cont_uscite + 1;
                                                        $pdf->MultiCell(200, 5, "Uscita n° " . $cont_uscite . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                        break;
                                                    default:
                                                        $cont_stampa = $cont_stampa + 1;
                                                        $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                        break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            //}}} </editor-fold>
                        } else {
                            //{{{ <editor-fold defaultstate="collapsed">
                            for ($cont_classi = 0; $cont_classi < count($elenchi_classi); $cont_classi++) {
                                $id_classe = $elenchi_classi[$cont_classi][0];
                                $elenchi_assenze = estrai_studenti_non_giustificati($data_inizio, $data_fine, $giorni_ammessi, (int) $id_classe, $stampa_ritirati);
                                $dati_classe = estrai_classe($id_classe);

                                $sotto_intestazione_stampa = "Periodo controllato, dal " .
                                        $inizio_Day . "/" . $inizio_Month . "/" . $inizio_Year .
                                        " al " .
                                        $fine_Day . "/" . $fine_Month . "/" . $fine_Year;

                                for ($cont_stud = 0; $cont_stud < count($elenchi_assenze); $cont_stud++) {
                                    $cont_stampa = 0;
                                    $cont_entrate = 0;
                                    $cont_uscite = 0;
                                    $stampa_studente = "NO";

                                    for ($cont_ass = 0; $cont_ass < count($elenchi_assenze[$cont_stud]) - 2; $cont_ass++) {
                                        $cont_assenze_periodo = count($elenchi_assenze[$cont_stud][$cont_ass]);

                                        if ($elenchi_assenze[$cont_stud][$cont_ass][$cont_assenze_periodo - 1][0] <= strtotime("-" . $giorni_presenza . " days", $data_fine)) {
                                            $stampa_studente = "SI";
                                        }
                                    }

                                    if (((count($elenchi_assenze[$cont_stud]) - 2) >= $quantita_minima_periodi) && $stampa_studente == "SI") {

                                        $intestazione_stampa_seconda_riga = "Riepilogo Situazioni non Giustificate dello studente: " .
                                                $elenchi_assenze[$cont_stud]["cognome"] .
                                                " " .
                                                $elenchi_assenze[$cont_stud]["nome"];
                                        $pdf->AddPage('P');
                                        $pdf->SetFont('helvetica', 'B', 12);
                                        $pdf->Cell(0, 5, $intestazione_stampa, 0, 1, 'C');
                                        $pdf->SetFont('helvetica', 'B', 10);
                                        $pdf->ln(5);
                                        $pdf->CellFitScale(0, 5, $sotto_intestazione_stampa, 0, 1, 'L');
                                        $pdf->CellFitScale(0, 5, $intestazione_stampa_seconda_riga, 0, 1, 'L');
                                        $pdf->ln(5);
                                        $var_tmp = $elenchi_assenze[$cont_stud]["cognome"] . " " .
                                                $elenchi_assenze[$cont_stud]["nome"];
                                        $pdf->SetFont('helvetica', 'B', 10);
                                        $pdf->Cell(20, 5, $var_tmp, 0, 1, 'L');
                                        $pdf->SetFont('helvetica', '', 8);

                                        for ($cont_ass = 0; $cont_ass < count($elenchi_assenze[$cont_stud]) - 2; $cont_ass++) {
                                            $cont_assenze_periodo = count($elenchi_assenze[$cont_stud][$cont_ass]);

                                            if ($elenchi_assenze[$cont_stud][$cont_ass][$cont_assenze_periodo - 1][0] <= strtotime("-" . $giorni_presenza . " days", $data_fine)) {
                                                $var_tmp = "";

                                                for ($cont_int_ass = 0; $cont_int_ass < count($elenchi_assenze[$cont_stud][$cont_ass]); $cont_int_ass++) {
                                                    if (strlen($var_tmp) > 0) {
                                                        $var_tmp = $var_tmp . " -- " . date("d/m/Y", $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][0]);
                                                    } else {
                                                        $var_tmp = date("d/m/Y", $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][0]);
                                                    }

                                                    $tipo_attuale = $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][1];
                                                    $orario_attuale = $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][2];
                                                }

                                                switch ($tipo_attuale) {
                                                    case "1":
                                                        $cont_stampa = $cont_stampa + 1;
                                                        $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 1, 'L');
                                                        break;
                                                    case "6":
                                                        $cont_stampa = $cont_stampa + 1;
                                                        $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 1, 'L');
                                                        break;
                                                    case "7":
                                                        $cont_stampa = $cont_stampa + 1;
                                                        $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 1, 'L');
                                                        break;
                                                    case "2":
                                                        $cont_entrate = $cont_entrate + 1;
                                                        $pdf->MultiCell(200, 5, "Entrata n° " . $cont_entrate . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 1, 'L');
                                                        break;
                                                    case "8":
                                                        $cont_entrate = $cont_entrate + 1;
                                                        $pdf->MultiCell(200, 5, "Entrata n° " . $cont_entrate . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 1, 'L');
                                                        break;
                                                    case "3":
                                                        $cont_uscite = $cont_uscite + 1;
                                                        $pdf->MultiCell(200, 5, "Uscita n° " . $cont_uscite . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 1, 'L');
                                                        break;
                                                    case "9":
                                                        $cont_uscite = $cont_uscite + 1;
                                                        $pdf->MultiCell(200, 5, "Uscita n° " . $cont_uscite . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 1, 'L');
                                                        break;
                                                    default:
                                                        $cont_stampa = $cont_stampa + 1;
                                                        $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 1, 'L');
                                                        break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            //}}} </editor-fold>
                        }
                    } else {
                        if ($stampa_classe_studente == "classe") {
                            //{{{ <editor-fold defaultstate="collapsed">
                            //estraggo i dati da passare alla stampa
                            $elenchi_assenze = estrai_studenti_non_giustificati($data_inizio, $data_fine, $giorni_ammessi, (int) $id_classe, $stampa_ritirati);
                            $dati_classe = estrai_classe((int) $id_classe);

                            $intestazione_stampa_seconda_riga = "Riepilogo Situazioni non Giustificate della classe: " . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[4];
                            $sotto_intestazione_stampa = "Periodo controllato: dal " . $inizio_Day . "/" . $inizio_Month . "/" . $inizio_Year . " al " . $fine_Day . "/" . $fine_Month . "/" . $fine_Year;
                            $pdf->AddPage('P');
                            $pdf->SetFont('helvetica', 'B', 12);
                            $pdf->Cell(0, 5, $intestazione_stampa, 0, 1, 'C');
                            $pdf->SetFont('helvetica', 'B', 10);
                            $pdf->ln(5);
                            $pdf->CellFitScale(0, 5, $sotto_intestazione_stampa, 0, 1, 'L');
                            $pdf->CellFitScale(0, 5, $intestazione_stampa_seconda_riga, 0, 1, 'L');
                            $pdf->ln(5);

                            for ($cont_stud = 0; $cont_stud < count($elenchi_assenze); $cont_stud++) {
                                $stampa_studente = "NO";

                                for ($cont_ass = 0; $cont_ass < count($elenchi_assenze[$cont_stud]) - 2; $cont_ass++) {
                                    $cont_assenze_periodo = count($elenchi_assenze[$cont_stud][$cont_ass]);

                                    if ($elenchi_assenze[$cont_stud][$cont_ass][$cont_assenze_periodo - 1][0] <= strtotime("-" . $giorni_presenza . " days", $data_fine)) {
                                        $stampa_studente = "SI";
                                    }
                                }

                                if (((count($elenchi_assenze[$cont_stud]) - 2) >= $quantita_minima_periodi) && $stampa_studente == "SI") {
                                    $cont_stampa = 0;
                                    $cont_entrate = 0;
                                    $cont_uscite = 0;

                                    $pdf->SetFont('helvetica', 'B', 10);
                                    $pdf->Cell(20, 5, $elenchi_assenze[$cont_stud]["cognome"] . " " . $elenchi_assenze[$cont_stud]["nome"], 0, 1, 'L');
                                    $pdf->SetFont('helvetica', '', 8);

                                    for ($cont_ass = 0; $cont_ass < count($elenchi_assenze[$cont_stud]) - 2; $cont_ass++) {
                                        $cont_assenze_periodo = count($elenchi_assenze[$cont_stud][$cont_ass]);

                                        if ($elenchi_assenze[$cont_stud][$cont_ass][$cont_assenze_periodo - 1][0] <= strtotime("-" . $giorni_presenza . " days", $data_fine)) {
                                            $var_tmp = "";
                                            $tutti_tipi = "";

                                            for ($cont_int_ass = 0; $cont_int_ass < count($elenchi_assenze[$cont_stud][$cont_ass]); $cont_int_ass++) {
                                                if (strlen($var_tmp) > 0) {
                                                    $var_tmp = $var_tmp . " -- " . date("d/m/Y", $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][0]);
                                                } else {
                                                    $var_tmp = date("d/m/Y", $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][0]);
                                                }

                                                $tipo_attuale = $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][1];
                                                $orario_attuale = $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][2];
                                                $tutti_tipi .= " " . $tipo_attuale;
                                            }
                                            switch ($tipo_attuale) {
                                                case "1":
                                                    $cont_stampa = $cont_stampa + 1;
                                                    $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                    break;
                                                case "6":
                                                    $cont_stampa = $cont_stampa + 1;
                                                    $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                    break;
                                                case "7":
                                                    $cont_stampa = $cont_stampa + 1;
                                                    $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                    break;
                                                case "2":
                                                    $cont_entrate = $cont_entrate + 1;
                                                    $pdf->MultiCell(200, 5, "Entrata n° " . $cont_entrate . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                    break;
                                                case "8":
                                                    $cont_entrate = $cont_entrate + 1;
                                                    $pdf->MultiCell(200, 5, "Entrata n° " . $cont_entrate . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                    break;
                                                case "3":
                                                    $cont_uscite = $cont_uscite + 1;
                                                    $pdf->MultiCell(200, 5, "Uscita n° " . $cont_uscite . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                    break;
                                                case "9":
                                                    $cont_uscite = $cont_uscite + 1;
                                                    $pdf->MultiCell(200, 5, "Uscita n° " . $cont_uscite . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                    break;
                                                default:
                                                    $cont_stampa = $cont_stampa + 1;
                                                    $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                    break;
                                            }
                                        }
                                    }
                                }
                            }
                            //}}} </editor-fold>
                        } else {
                            //{{{ <editor-fold defaultstate="collapsed">
                            //estraggo i dati da passare alla stampa
                            $elenchi_assenze = estrai_studenti_non_giustificati($data_inizio, $data_fine, $giorni_ammessi, (int) $id_classe, $stampa_ritirati);
                            $dati_classe = estrai_classe((int) $id_classe);

                            $sotto_intestazione_stampa = "Periodo controllato, dal " .
                                    $inizio_Day . "/" . $inizio_Month . "/" . $inizio_Year .
                                    " al " .
                                    $fine_Day . "/" . $fine_Month . "/" . $fine_Year;

                            for ($cont_stud = 0; $cont_stud < count($elenchi_assenze); $cont_stud++) {
                                $stampa_studente = "NO";

                                for ($cont_ass = 0; $cont_ass < count($elenchi_assenze[$cont_stud]) - 2; $cont_ass++) {
                                    $cont_assenze_periodo = count($elenchi_assenze[$cont_stud][$cont_ass]);

                                    if ($elenchi_assenze[$cont_stud][$cont_ass][$cont_assenze_periodo - 1][0] <= strtotime("-" . $giorni_presenza . " days", $data_fine)) {
                                        $stampa_studente = "SI";
                                    }
                                }

                                if (((count($elenchi_assenze[$cont_stud]) - 2) >= $quantita_minima_periodi) && $stampa_studente == "SI") {
                                    $intestazione_stampa_seconda_riga = "Riepilogo Situazioni non Giustificate dello studente: " .
                                            $elenchi_assenze[$cont_stud]["cognome"] .
                                            " " .
                                            $elenchi_assenze[$cont_stud]["nome"];
                                    $pdf->AddPage('P');
                                    $pdf->SetFont('helvetica', 'B', 12);
                                    $pdf->Cell(0, 5, $intestazione_stampa, 0, 1, 'C');
                                    $pdf->SetFont('helvetica', 'B', 10);
                                    $pdf->ln(5);
                                    $pdf->CellFitScale(0, 5, $sotto_intestazione_stampa, 0, 1, 'L');
                                    $pdf->CellFitScale(0, 5, $intestazione_stampa_seconda_riga, 0, 1, 'L');
                                    $pdf->ln(5);
                                    $cont_stampa = 0;
                                    $cont_entrate = 0;
                                    $cont_uscite = 0;
                                    $var_tmp = $elenchi_assenze[$cont_stud]["cognome"] . " " .
                                            $elenchi_assenze[$cont_stud]["nome"];
                                    $pdf->SetFont('helvetica', 'B', 10);
                                    $pdf->Cell(20, 5, $var_tmp, 0, 1, 'L');
                                    $pdf->SetFont('helvetica', '', 8);

                                    for ($cont_ass = 0; $cont_ass < count($elenchi_assenze[$cont_stud]) - 2; $cont_ass++) {
                                        $cont_assenze_periodo = count($elenchi_assenze[$cont_stud][$cont_ass]);

                                        if ($elenchi_assenze[$cont_stud][$cont_ass][$cont_assenze_periodo - 1][0] <= strtotime("-" . $giorni_presenza . " days", $data_fine)) {
                                            $var_tmp = "";
                                            $tutti_tipi = "";

                                            for ($cont_int_ass = 0; $cont_int_ass < count($elenchi_assenze[$cont_stud][$cont_ass]); $cont_int_ass++) {
                                                if (strlen($var_tmp) > 0) {
                                                    $var_tmp = $var_tmp . " -- " . date("d/m/Y", $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][0]);
                                                } else {
                                                    $var_tmp = date("d/m/Y", $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][0]);
                                                }

                                                $tipo_attuale = $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][1];
                                                $orario_attuale = $elenchi_assenze[$cont_stud][$cont_ass][$cont_int_ass][2];
                                                $tutti_tipi .= " " . $tipo_attuale;
                                            }
                                            switch ($tipo_attuale) {
                                                case "1":
                                                    $cont_stampa = $cont_stampa + 1;
                                                    $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                    break;
                                                case "6":
                                                    $cont_stampa = $cont_stampa + 1;
                                                    $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                    break;
                                                case "7":
                                                    $cont_stampa = $cont_stampa + 1;
                                                    $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                    break;
                                                case "2":
                                                    $cont_entrate = $cont_entrate + 1;
                                                    $pdf->MultiCell(200, 5, "Entrata n° " . $cont_entrate . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                    break;
                                                case "8":
                                                    $cont_entrate = $cont_entrate + 1;
                                                    $pdf->MultiCell(200, 5, "Entrata n° " . $cont_entrate . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                    break;
                                                case "3":
                                                    $cont_uscite = $cont_uscite + 1;
                                                    $pdf->MultiCell(200, 5, "Uscita n° " . $cont_uscite . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                    break;
                                                case "9":
                                                    $cont_uscite = $cont_uscite + 1;
                                                    $pdf->MultiCell(200, 5, "Uscita n° " . $cont_uscite . ", nel giorno: " . $var_tmp . " alle " . $orario_attuale, 0, 'L');
                                                    break;
                                                default:
                                                    $cont_stampa = $cont_stampa + 1;
                                                    $pdf->MultiCell(200, 5, "Periodo " . $cont_stampa . ", assente nei giorni: " . $var_tmp, 0, 'L');
                                                    break;
                                            }
                                        }
                                    }
                                }
                            }
                            //}}} </editor-fold>
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_assenze_classe":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $data_inizio = mktime(1, 1, 1, intval($inizio_Month), $inizio_Day, $inizio_Year);
                    $data_fine = mktime(1, 1, 1, intval($fine_Month), $fine_Day, $fine_Year);

                    //estraggo i dati da passare alla stampa
                    $elenchi_assenze = estrai_assenze_studenti_classe_periodo((int) $id_classe, $data_inizio, $data_fine + 86400);
                    $dati_classe = estrai_classe((int) $id_classe);
                    $dati_sede = estrai_sede_singola((int) $id_sede);

                    $intestazione_stampa = $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"];

                    $intestazione_stampa_seconda_riga = "Riepilogo Assenze della classe: " . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[4];
                    $sotto_intestazione_stampa = "Periodo controllato, dal " .
                            $inizio_Day . "/" . $inizio_Month . "/" . $inizio_Year .
                            " al " .
                            $fine_Day . "/" . $fine_Month . "/" . $fine_Year;

                    $pdf = new NEXUS_PDF;

                    if ($salto_pagina == "classe") {
                        $pdf->AddPage('P');
                        $pdf->ln(5);
                        $pdf->SetFont('helvetica', 'B', 12);
                        $pdf->SetTextColor('0', '0', '0');
                        $pdf->Cell(200, 5, $intestazione_stampa, 0, 1, 'C');
                        $pdf->Cell(200, 5, $intestazione_stampa_seconda_riga, 0, 1, 'C');
                        $pdf->ln(10);
                        $pdf->SetFont('helvetica', 'B', 10);
                        $pdf->Cell(200, 5, $sotto_intestazione_stampa, 0, 1, 'L');
                    }

                    for ($cont_studente = 0; $cont_studente < count($elenchi_assenze); $cont_studente++) {
                        if ($salto_pagina == "studenti") {
                            $pdf->AddPage('P');
                            $pdf->ln(5);
                            $pdf->SetFont('helvetica', 'B', 12);
                            $pdf->SetTextColor('0', '0', '0');
                            $pdf->Cell(200, 5, $intestazione_stampa, 0, 1, 'C');
                            $pdf->Cell(200, 5, 'Riepilogo Assenze', 0, 1, 'C');
                            $pdf->ln(10);
                            $pdf->SetFont('helvetica', 'B', 10);
                            $pdf->Cell(200, 5, $sotto_intestazione_stampa, 0, 1, 'L');
                        }

                        $pdf->ln(5);

                        $var_tmp = $elenchi_assenze[$cont_studente][0][0] . " " .
                                decode($elenchi_assenze[$cont_studente][0][8]);

                        $anag_stud = $elenchi_assenze[$cont_studente][0][1] . " " .
                                $elenchi_assenze[$cont_studente][0][2];

                        $pdf->SetFont('helvetica', 'B', 10);
                        $pdf->SetTextColor('0', '0', '0');
                        $pdf->Cell(20, 5, $var_tmp, 0, 1, 'L');
                        $pdf->Cell(20, 5, $anag_stud, 0, 1, 'L');
                        if ($elenchi_assenze[$cont_studente][0][9] != '') {
                            for ($cont = 0; $cont < count($elenchi_assenze[$cont_studente]); $cont++) {
                                if (
                                        (intval($elenchi_assenze[$cont_studente][$cont][3]) != 12 and $visualizza_rit_minimi == 'NO')
                                        or $visualizza_rit_minimi == 'SI'
                                ) {
                                    $var_tmp = $elenchi_assenze[$cont_studente][$cont][9] . " " . $elenchi_assenze[$cont_studente][$cont][4];

                                    if ($salto_pagina == "studenti"
                                            &&
                                        $motivazione_assenza == 'SI'
                                            &&
                                        !empty($elenchi_assenze[$cont_studente][$cont]['motivazione'])) {
                                        $var_tmp .= " - Motivazione: {$elenchi_assenze[$cont_studente][$cont]['motivazione']}";
                                    }

                                    $pdf->SetFont('helvetica', '', 10);

                                    if ($elenchi_assenze[$cont_studente][$cont][10] != "1") {
                                        $pdf->SetTextColor('190', '0', '0');
                                    } else {
                                        $pdf->SetTextColor('0', '0', '0');
                                    }

                                    $pdf->Cell(20, 5, "", 0, 0, 'L');
                                    $pdf->writeHTMLCell(0, 5, '', '', $var_tmp, 0, 1, false, true, 'L');
                                }
                            }
                        } else {
                            $pdf->SetFont('helvetica', '', 10);
                            $pdf->SetTextColor('0', '0', '0');
                            $pdf->Cell(10, 5, "", 0, 0, 'L');
                            $pdf->Cell(20, 5, "Nessuna assenza nel periodo controllato", 0, 1, 'L');
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_elenchi":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $dati_scuola[0][2] = $bordo_celle;
                    $dati_scuola[1][2] = $dimensione_font;
                    $dati_scuola[2][2] = $grassetto;
                    $dati_scuola[3][2] = $dimensione_colonne;
                    $dati_scuola[4][2] = $altezza_righe;

                    $dati_scuola[0][3] = $id_bordo_celle;
                    $dati_scuola[1][3] = $id_dimensione_font;
                    $dati_scuola[2][3] = $id_grassetto;
                    $dati_scuola[3][3] = $id_dimensione_colonne;
                    $dati_scuola[4][3] = $id_altezza_righe;

                    aggiorna_parametri_stampa_elenchi($dati_scuola, (int) $current_user);

                    //in questo modo estraggo solo i dati relativi alle impostazioni di stampa
                    $tipo_dati = "5";
                    $dati_scuola = estrai_dati_istituto($tipo_dati);

                    //SETTAGGI DELLE VARIABILI NEL CASO IN CUI NON CI FOSSERO NEL DB
                    $bordo_celle = 0;
                    $dimensione_font = 8;
                    $grassetto = "";
                    $dimensione_prima_colonna = 67;
                    $dimensione_colonne = 80;
                    $altezza_righe = 5.6;

                    for ($cont = 0; $cont < count($dati_scuola); $cont++) {
                        switch ($dati_scuola[$cont][1]) {
                            case "STAMPA_BORDO_ELENCHI":
                                if ($dati_scuola[$cont][2] == "1") {
                                    $bordo_celle = 1;
                                } else {
                                    $bordo_celle = 0;
                                }
                                $id_bordo_celle = $dati_scuola[$cont][3];
                                break;
                            case "DIMENSIONE_CARATTERE_ELENCHI":
                                if (intval($dati_scuola[$cont][2]) >= 8) {
                                    $dimensione_font = $dati_scuola[$cont][2];
                                } else {
                                    $dimensione_font = 8;
                                }
                                $id_dimensione_font = $dati_scuola[$cont][3];
                                break;
                            case "GRASSETTO_ELENCHI":
                                if ($dati_scuola[$cont][2] == "1") {
                                    $grassetto = "B";
                                } else {
                                    $grassetto = "";
                                }
                                $id_grassetto = $dati_scuola[$cont][3];
                                break;
                            case "DIMENSIONE_COLONNE":
                                if (intval($dati_scuola[$cont][2]) >= 60) {
                                    $dimensione_colonne = $dati_scuola[$cont][2];
                                } else {
                                    $dimensione_colonne = 80;
                                }
                                $id_dimensione_colonne = $dati_scuola[$cont][3];
                                break;
                            case "ALTEZZA_RIGHE":
                                if ($dati_scuola[$cont][2] >= 5) {
                                    $altezza_righe = $dati_scuola[$cont][2];
                                } else {
                                    $altezza_righe = 5.6;
                                }
                                $id_altezza_righe = $dati_scuola[$cont][3];
                                break;
                            default:
                                break;
                        }
                    }

                    //estraggo i dati da passare alla stampa
                    $elenchi_studenti = estrai_elenchi_studenti($limiti_classi['indirizzi']);

                    $pdf = new NEXUS_PDF;

                    if ($foglio_singolo == "1") {
                        for ($cont_classe = 0; $cont_classe < count($elenchi_studenti); $cont_classe++) {
                            $pdf->AddPage('L');
                            $pdf->SetFont('helvetica', $grassetto, 10);
                            $pdf->SetTextColor(0, 0, 0);

                            $pdf->Cell($dimensione_colonne, $altezza_righe, $elenchi_studenti[$cont_classe][0]['classe'], 0, 1, 'L');

                            foreach ($elenchi_studenti[$cont_classe] as $studente) {
                                $pdf->SetFont('helvetica', $grassetto, $dimensione_font);

                                $pdf->SetTextColor(0, 0, 0);
                                $pdf->CellFitScale(5, $altezza_righe, $studente['registro'], $bordo_celle, 0, 'R');

                                $testo_stud = $studente['cognome'] . ' ' . $studente['nome'];

                                if ($studente['esito_corrente_calcolato'] != 'In corso' && $studente['esito_corrente_calcolato'] != '' && strpos(strtolower($studente['esito_corrente_calcolato']), 'non')
                                ) {
                                    $pdf->SetTextColor(255, 30, 30);
                                }
                                $testo_stud != ' ' && $esito_stampa == 'SI' ? $testo_stud .= ' (' . $studente['esito_corrente_calcolato'] . ')' : null;

                                $pdf->CellFitScale(($dimensione_prima_colonna - 5), $altezza_righe, $testo_stud, $bordo_celle, 1, 'L');
                            }
                        }
                    } else {
                        for ($cont_classe = 0; $cont_classe < count($elenchi_studenti); $cont_classe = ($cont_classe + 4)) {
                            $pdf->AddPage('L');
                            $pdf->SetFont('helvetica', $grassetto, 10);
                            $pdf->SetTextColor(0, 0, 0);

                            $pdf->Cell($dimensione_colonne, $altezza_righe, $elenchi_studenti[$cont_classe][0]['classe'], 0, 0, 'L');
                            $pdf->Cell($dimensione_colonne, $altezza_righe, $elenchi_studenti[$cont_classe + 1][0]['classe'], 0, 0, 'L');
                            $pdf->Cell($dimensione_colonne, $altezza_righe, $elenchi_studenti[$cont_classe + 2][0]['classe'], 0, 0, 'L');
                            $pdf->Cell($dimensione_colonne, $altezza_righe, $elenchi_studenti[$cont_classe + 3][0]['classe'], 0, 1, 'L');

                            for ($cont = 0; $cont < 34; $cont++) {
                                $studente1 = $elenchi_studenti[$cont_classe][$cont];
                                $studente2 = $elenchi_studenti[$cont_classe + 1][$cont];
                                $studente3 = $elenchi_studenti[$cont_classe + 2][$cont];
                                $studente4 = $elenchi_studenti[$cont_classe + 3][$cont];

                                $pdf->SetFont('helvetica', $grassetto, $dimensione_font);

                                $pdf->SetTextColor(0, 0, 0);
                                $pdf->CellFitScale(5, $altezza_righe, $studente1['registro'], $bordo_celle, 0, 'R');

                                $testo_stud1 = $studente1['cognome'] . ' ' . $studente1['nome'];

                                if ($studente1['esito_corrente_calcolato'] != 'In corso' && $studente1['esito_corrente_calcolato'] != '' && strpos(strtolower($studente1['esito_corrente_calcolato']), 'non')
                                ) {
                                    $pdf->SetTextColor(255, 30, 30);
                                }
                                $testo_stud1 != ' ' && $esito_stampa == 'SI' ? $testo_stud1 .= ' (' . $studente1['esito_corrente_calcolato'] . ')' : null;

                                $pdf->CellFitScale(($dimensione_prima_colonna - 5), $altezza_righe, $testo_stud1, $bordo_celle, 0, 'L');

                                $pdf->SetTextColor(0, 0, 0);
                                $pdf->CellFitScale(5, $altezza_righe, $studente2['registro'], $bordo_celle, 0, 'R');

                                $testo_stud2 = $studente2['cognome'] . ' ' . $studente2['nome'];

                                if ($studente2['esito_corrente_calcolato'] != 'In corso' && $studente2['esito_corrente_calcolato'] != '' && strpos(strtolower($studente2['esito_corrente_calcolato']), 'non')
                                ) {
                                    $pdf->SetTextColor(255, 30, 30);
                                }
                                $testo_stud2 != ' ' && $esito_stampa == 'SI' ? $testo_stud2 .= ' (' . $studente2['esito_corrente_calcolato'] . ')' : null;

                                $pdf->CellFitScale(($dimensione_colonne - 5), $altezza_righe, $testo_stud2, $bordo_celle, 0, 'L');

                                $pdf->SetTextColor(0, 0, 0);
                                $pdf->CellFitScale(5, $altezza_righe, $studente3['registro'], $bordo_celle, 0, 'R');

                                $testo_stud3 = $studente3['cognome'] . ' ' . $studente3['nome'];

                                if ($studente3['esito_corrente_calcolato'] != 'In corso' && $studente3['esito_corrente_calcolato'] != '' && strpos(strtolower($studente3['esito_corrente_calcolato']), 'non')
                                ) {
                                    $pdf->SetTextColor(255, 30, 30);
                                }
                                $testo_stud3 != ' ' && $esito_stampa == 'SI' ? $testo_stud3 .= ' (' . $studente3['esito_corrente_calcolato'] . ')' : null;

                                $pdf->CellFitScale(($dimensione_colonne - 5), $altezza_righe, $testo_stud3, $bordo_celle, 0, 'L');

                                $pdf->SetTextColor(0, 0, 0);
                                $pdf->CellFitScale(5, $altezza_righe, $studente4['registro'], $bordo_celle, 0, 'R');

                                $testo_stud4 = $studente4['cognome'] . ' ' . $studente4['nome'];

                                if ($studente4['esito_corrente_calcolato'] != 'In corso' && $studente4['esito_corrente_calcolato'] != '' && strpos(strtolower($studente4['esito_corrente_calcolato']), 'non')
                                ) {
                                    $pdf->SetTextColor(255, 30, 30);
                                }
                                $testo_stud4 != ' ' && $esito_stampa == 'SI' ? $testo_stud4 .= ' (' . $studente4['esito_corrente_calcolato'] . ')' : null;

                                $pdf->CellFitScale(($dimensione_colonne - 5), $altezza_righe, $testo_stud4, $bordo_celle, 1, 'L');
                            }
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_assenze_non_giustificate":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($numero_assenze_minimo > 0) {
                        //data di verifica selezionata dall'utente
                        $data_verifica = mktime(1, 1, 1, intval($Date_Month), $Date_Day, $Date_Year);

                        //estraggo i dati da passare alla stampa
                        $elenchi_assenze = estrai_studenti_con_assenze_non_giustificate($data_verifica, $numero_assenze_minimo, $limiti_classi['indirizzi']);

                        $dati_sede = estrai_sede_singola($id_sede);

                        $intestazione_stampa = $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"];

                        $intestazione_stampa = $intestazione_stampa . " -- Riepilogo Assenze non Giustificate";
                        $sotto_intestazione_stampa = "Giorno del controllo: " . $Date_Day . "/" . $Date_Month . "/" . $Date_Year;

                        $pdf = new NEXUS_PDF;
                        $pdf->Open();

                        $id_classe_old = -1;
                        for ($cont_studente = 0; $cont_studente < count($elenchi_assenze); $cont_studente++) {
                            if ($id_classe_old != $elenchi_assenze[$cont_studente][10]) {
                                $pdf->AddPage('P');
                                $pdf->ln(5);

                                $pdf->SetFont('helvetica', 'B', 12);
                                $pdf->CellFitScale(0, 5, $intestazione_stampa, 0, 1, 'C');
                                $pdf->ln(10);
                                $pdf->SetFont('helvetica', 'B', 10);
                                $pdf->CellFitScale(0, 5, $sotto_intestazione_stampa, 0, 1, 'L');


                                $pdf->ln(5);
                                $pdf->Cell(200, 5, 'Classe: ' . $elenchi_assenze[$cont_studente][0] . " " . $elenchi_assenze[$cont_studente][8], 0, 1, 'L');

                                $id_classe_old = $elenchi_assenze[$cont_studente][10];
                            }
                            $pdf->ln(5);
                            $var_tmp = $elenchi_assenze[$cont_studente][1] . " " .
                                    $elenchi_assenze[$cont_studente][2] . " numero assenze non giustificate:" .
                                    $elenchi_assenze[$cont_studente][11];
                            $pdf->Cell(20, 5, $var_tmp, 0, 1, 'L');
                            if ($esplicita_giorni == "SI") {
                                $pdf->MultiCell(180, 5, $elenchi_assenze[$cont_studente][50]);
                            }
                        }
                        //cancello tutte i file temporanei fatti da più di un'ora
                        $dir = "tmp_pdf";
                        CleanFiles($dir);
                        //creo i nuovi file temporanei
                        $file = basename(tempnam($dir, 'tmp'));
                        rename($dir . "/" . $file, $dir . "/" . $file . '.pdf');
                        $file .= '.pdf';
                        //Salva il PDF come file
                        $pdf->Output($dir . "/" . $file, 'D');
                        //Reindirizzamento JavaScript
                        $nuovo_nome = $dir . '/' . $file;
                        echo "<HTML><SCRIPT>document.location='$nuovo_nome';</SCRIPT></HTML>";
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_riepilogo_assenze_annuale":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe > 0) {
                        //estraggo la descrizione della materia che sto guardando
                        $dati_classe = estrai_classe((int) $id_classe);

                        $mese_inizio = intval($inizio_Month);
                        $anno_inizio = intval($inizio_Year);

                        $mese_fine = intval($fine_Month);
                        $anno_fine = intval($fine_Year);

                        //estraggo i dati per la stampa, già formattati come mi servono nome, cognome, mese(a,e,u), media(a,e,u)
                        $dati_assenze = estrai_riepilogo_assenze_periodo_classe((int) $id_classe, $mese_inizio, $anno_inizio, $mese_fine, $anno_fine);

                        //estraggo i dati per la stampa, delle medie dell'istituto per lo stesso livello (tutte le prime, le seconde, ecc.)
                        $dati_assenze_totali = estrai_riepilogo_assenze_periodo_istituto($mese_inizio, $anno_inizio, $mese_fine, $anno_fine);

                        $pdf = new NEXUS_PDF;
                        $testo_excel = "";


                        //Intestazione
                        $pdf->SetFont('helvetica', '', 12);
                        $pdf->AddPage('L');
                        $pdf->Cell(290, 10, 'Riepilogo Assenze/Entrate/Uscite/ritardini della classe: ' . $dati_classe[2] . $dati_classe[3] . ' ' . $dati_classe[4], 0, 1, 'C');
                        $pdf->ln(5);
                        $pdf->SetFont('helvetica', 'B', 7);
                        $pdf->SetFillColor(224, 235, 255);
                        $pdf->SetTextColor(0, 0, 0);

                        $testo_excel .= "\tRiepilogo Assenze/Entrate/Uscite/ritardini della classe:";
                        $testo_excel .= "\n";

                        $testo_excel .= "\t" . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[4];
                        $testo_excel .= "\n";

                        $testo_excel .= "\tDal " . $mese_inizio . "/" . $anno_inizio . " al " . $mese_fine . "/" . $anno_fine . "\t";
                        $testo_excel .= "\n";

                        $fill = 1;

                        //calcolo il numero di mesi selezionati per la stampa
                        if ($anno_fine == $anno_inizio) {
                            $tot_mesi = ($mese_fine + 1) - $mese_inizio;
                        } else {
                            $tot_mesi = (12 - ($mese_inizio - 1)) + $mese_fine;
                        }

                        $larghezza_cella = (270 - 65) / ($tot_mesi + 1);

                        //inserisco la prima riga della tabella
                        $pdf->Cell(5, 5, 'N°', 'RTL', 0, 'C', 0);
                        $pdf->Cell(60, 5, 'STUDENTE', 'TRL', 0, 'C', 0);

                        $testo_excel .= "N°\t";
                        $testo_excel .= "STUDENTE\t";

                        for ($cont = 0; $cont < count($dati_assenze); $cont++) {
                            if ($cont < $tot_mesi) {
                                $pdf->Cell($larghezza_cella, 5, $dati_assenze[$cont][100][100], 1, 0, 'C', 0);
                                $testo_excel .= $dati_assenze[$cont][100][100] . "\t\t\t\t";
                            }
                        }

                        $pdf->Cell($larghezza_cella, 5, 'Totali', 1, 0, 'C', 0);
                        //$pdf->Cell($larghezza_cella,5, 'Medie',1,0,'C',0);
                        $pdf->Cell(0, 5, '', 'L', 1, 'C', 0);

                        $testo_excel .= "TOTALI\t\t\t\t";
                        $testo_excel .= "\n";

                        //inserisco la seconda riga della tabella
                        $pdf->Cell(5, 3, '', 'RLB', 0, 'R', 0);
                        $pdf->Cell(60, 3, '', 'RLB', 0, 'L', 0);

                        $testo_excel .= "\t\t";

                        for ($cont = 0; $cont < count($dati_assenze); $cont++) {
                            if ($cont < ($tot_mesi + 1)) {
                                $pdf->Cell($larghezza_cella / 4, 3, 'A', 1, 0, 'C', 0);
                                $pdf->Cell($larghezza_cella / 4, 3, 'E', 1, 0, 'C', 0);
                                $pdf->Cell($larghezza_cella / 4, 3, 'U', 1, 0, 'C', 0);
                                $pdf->Cell($larghezza_cella / 4, 3, 'e', 1, 0, 'C', 0);

                                $testo_excel .= "A\t";
                                $testo_excel .= "E\t";
                                $testo_excel .= "U\t";
                                $testo_excel .= "e\t";
                            }
                        }
                        $pdf->Cell(0, 3, '', 'L', 1, 'C', 0);

                        $testo_excel .= "\n";

                        //giro la matrice per motivi di comodità per la visualizzazione nei pdf
                        $nuovo_dati_assenze = [];

                        for ($cont = 0; $cont < count($dati_assenze); $cont++) {
                            for ($cont_int = 0; $cont_int < count($dati_assenze[$cont]); $cont_int++) {
                                $nuovo_dati_assenze[$cont_int][$cont][50] = $dati_assenze[$cont][$cont_int][50];
                                $nuovo_dati_assenze[$cont_int][$cont][51] = $dati_assenze[$cont][$cont_int][51];
                                for ($cont_ult = 0; $cont_ult < 21; $cont_ult++) {
                                    if (intval($dati_assenze[$cont][$cont_int][$cont_ult]) > 0) {
                                        $nuovo_dati_assenze[$cont_int][$cont][$cont_ult] = $dati_assenze[$cont][$cont_int][$cont_ult];
                                        $totali[$cont_int][0][$cont_ult] = $totali[$cont_int][0][$cont_ult] + $dati_assenze[$cont][$cont_int][$cont_ult];
                                    }
                                }
                            }
                        }

                        //scorro la matrice per stampare i risultati
                        for ($cont = 0; $cont < count($nuovo_dati_assenze); $cont++) {
                            if (strlen($nuovo_dati_assenze[$cont][0][51]) > 0) {
                                $pdf->Cell(5, 4, $nuovo_dati_assenze[$cont][0][50], 1, 0, 'R', $fill);
                                $pdf->Cell(60, 4, $nuovo_dati_assenze[$cont][0][51], 1, 0, 'L', $fill);

                                $testo_excel .= $nuovo_dati_assenze[$cont][0][50] . "\t";
                                $testo_excel .= $nuovo_dati_assenze[$cont][0][51] . "\t";

                                for ($cont_int = 0; $cont_int < count($nuovo_dati_assenze[$cont]); $cont_int++) {
                                    //questo serve per eliminare le celle vuote che verrebbero alla fine
                                    if ($cont_int < $tot_mesi) {
                                        $pdf->Cell($larghezza_cella / 4, 4, ($nuovo_dati_assenze[$cont][$cont_int][1] +
                                                $nuovo_dati_assenze[$cont][$cont_int][6] +
                                                $nuovo_dati_assenze[$cont][$cont_int][7]), 1, 0, 'C', $fill);
                                        $pdf->Cell($larghezza_cella / 4, 4, ($nuovo_dati_assenze[$cont][$cont_int][2] +
                                                $nuovo_dati_assenze[$cont][$cont_int][8]), 1, 0, 'C', $fill);
                                        $pdf->Cell($larghezza_cella / 4, 4, ($nuovo_dati_assenze[$cont][$cont_int][3] +
                                                $nuovo_dati_assenze[$cont][$cont_int][9]), 1, 0, 'C', $fill);
                                        //questa è quella dei ritardini
                                        $pdf->Cell($larghezza_cella / 4, 4, ($nuovo_dati_assenze[$cont][$cont_int][12] +
                                                $nuovo_dati_assenze[$cont][$cont_int][18]), 1, 0, 'C', $fill);

                                        $testo_excel .= ($nuovo_dati_assenze[$cont][$cont_int][1] +
                                                $nuovo_dati_assenze[$cont][$cont_int][6] +
                                                $nuovo_dati_assenze[$cont][$cont_int][7]) . "\t";
                                        $testo_excel .= ($nuovo_dati_assenze[$cont][$cont_int][2] +
                                                $nuovo_dati_assenze[$cont][$cont_int][8]) . "\t";
                                        $testo_excel .= ($nuovo_dati_assenze[$cont][$cont_int][3] +
                                                $nuovo_dati_assenze[$cont][$cont_int][9]) . "\t";
                                        $testo_excel .= ($nuovo_dati_assenze[$cont][$cont_int][12] +
                                                $nuovo_dati_assenze[$cont][$cont_int][18]) . "\t";



                                        $totali_finali[$cont_int][0] = $totali_finali[$cont_int][0] +
                                                $nuovo_dati_assenze[$cont][$cont_int][1] +
                                                $nuovo_dati_assenze[$cont][$cont_int][6] +
                                                $nuovo_dati_assenze[$cont][$cont_int][7];

                                        $totali_finali[$cont_int][1] = $totali_finali[$cont_int][1] +
                                                $nuovo_dati_assenze[$cont][$cont_int][2] +
                                                $nuovo_dati_assenze[$cont][$cont_int][8];

                                        $totali_finali[$cont_int][2] = $totali_finali[$cont_int][2] +
                                                $nuovo_dati_assenze[$cont][$cont_int][3] +
                                                $nuovo_dati_assenze[$cont][$cont_int][9];

                                        //ritardini
                                        $totali_finali[$cont_int][3] = $totali_finali[$cont_int][3] +
                                                $nuovo_dati_assenze[$cont][$cont_int][12] +
                                                $nuovo_dati_assenze[$cont][$cont_int][18];
                                    }
                                }
                                //totali
                                $pdf->Cell($larghezza_cella / 4, 4, ($totali[$cont][0][1] + $totali[$cont][0][6] + $totali[$cont][0][7]), 1, 0, 'C', $fill);
                                $pdf->Cell($larghezza_cella / 4, 4, ($totali[$cont][0][2] + $totali[$cont][0][8]), 1, 0, 'C', $fill);
                                $pdf->Cell($larghezza_cella / 4, 4, ($totali[$cont][0][3] + $totali[$cont][0][9]), 1, 0, 'C', $fill);
                                //ritardini
                                $pdf->Cell($larghezza_cella / 4, 4, ($totali[$cont][0][12] + $totali[$cont][0][18]), 1, 0, 'C', $fill);

                                $testo_excel .= ($totali[$cont][0][1] + $totali[$cont][0][6] + $totali[$cont][0][7]) . "\t";
                                $testo_excel .= ($totali[$cont][0][2] + $totali[$cont][0][8]) . "\t";
                                $testo_excel .= ($totali[$cont][0][3] + $totali[$cont][0][9]) . "\t";
                                $testo_excel .= ($totali[$cont][0][12] + $totali[$cont][0][18]) . "\t";
                                /*
                                  //medie
                                  $pdf->Cell($larghezza_cella/3, 5, round((($totali[$cont][0][1] + $totali[$cont][0][6] + $totali[$cont][0][7])/$tot_mesi),1),1,0,'C',$fill);
                                  $pdf->Cell($larghezza_cella/3, 5, round((($totali[$cont][0][2] + $totali[$cont][0][8])/$tot_mesi),1),1,0,'C',$fill);
                                  $pdf->Cell($larghezza_cella/3, 5, round((($totali[$cont][0][3] + $totali[$cont][0][9])/$tot_mesi),1),1,0,'C',$fill);
                                 */
                                $pdf->Cell(0, 4, '', 'L', 1, 'C', 0);

                                $fill = !$fill;
                            }

                            $testo_excel .= "\n";
                        }

                        $pdf->ln(2);

                        //$pdf->SetFont('helvetica','B',10);
                        $pdf->Cell(65, 4, 'TOTALI FINALI', 1, 0, 'C', $fill);

                        $testo_excel .= "\tTOTALI FINALI\t";


                        for ($cont_int = 0; $cont_int < count($totali_finali); $cont_int++) {
                            //inserisce la riga dei totali
                            $pdf->Cell($larghezza_cella / 4, 4, $totali_finali[$cont_int][0], 1, 0, 'C', $fill);
                            $pdf->Cell($larghezza_cella / 4, 4, $totali_finali[$cont_int][1], 1, 0, 'C', $fill);
                            $pdf->Cell($larghezza_cella / 4, 4, $totali_finali[$cont_int][2], 1, 0, 'C', $fill);
                            $pdf->Cell($larghezza_cella / 4, 4, $totali_finali[$cont_int][3], 1, 0, 'C', $fill);
                            $totali_generali_finali[0] = $totali_generali_finali[0] + $totali_finali[$cont_int][0];
                            $totali_generali_finali[1] = $totali_generali_finali[1] + $totali_finali[$cont_int][1];
                            $totali_generali_finali[2] = $totali_generali_finali[2] + $totali_finali[$cont_int][2];
                            $totali_generali_finali[3] = $totali_generali_finali[3] + $totali_finali[$cont_int][3];

                            $testo_excel .= $totali_finali[$cont_int][0] . "\t";
                            $testo_excel .= $totali_finali[$cont_int][1] . "\t";
                            $testo_excel .= $totali_finali[$cont_int][2] . "\t";
                            $testo_excel .= $totali_finali[$cont_int][3] . "\t";
                        }

                        $pdf->Cell($larghezza_cella / 4, 4, $totali_generali_finali[0], 1, 0, 'C', $fill);
                        $pdf->Cell($larghezza_cella / 4, 4, $totali_generali_finali[1], 1, 0, 'C', $fill);
                        $pdf->Cell($larghezza_cella / 4, 4, $totali_generali_finali[2], 1, 0, 'C', $fill);
                        $pdf->Cell($larghezza_cella / 4, 4, $totali_generali_finali[3], 1, 0, 'C', $fill);

                        $testo_excel .= $totali_generali_finali[0] . "\t";
                        $testo_excel .= $totali_generali_finali[1] . "\t";
                        $testo_excel .= $totali_generali_finali[2] . "\t";
                        $testo_excel .= $totali_generali_finali[3] . "\t";

                        $pdf->Cell(0, 4, '', 'L', 1, 'C', 0);

                        $testo_excel .= "\n";


                        $pdf->ln(5);
                        $pdf->SetFont('helvetica', 'B', 9);

                        for ($classe = 1; $classe < 6; $classe++) {
                            $pdf->Cell(0, 4, 'Totale di tutte le ' . $classe . '° : A=' .
                                    ($dati_assenze_totali[$classe][1] + $dati_assenze_totali[$classe][6] + $dati_assenze_totali[$classe][7]) .
                                    ' E=' . ($dati_assenze_totali[$classe][2] + $dati_assenze_totali[$classe][8]) .
                                    ' U=' . ($dati_assenze_totali[$classe][3] + $dati_assenze_totali[$classe][9]) .
                                    ' e=' . ($dati_assenze_totali[$classe][12] + $dati_assenze_totali[$classe][18]) .
                                    '  -  Media per studente di tutte le ' . $classe . '° : A=' .
                                    round((($dati_assenze_totali[$classe][1] + $dati_assenze_totali[$classe][6] + $dati_assenze_totali[$classe][7]) / $dati_assenze_totali[$classe][0]), 1) .
                                    ' E=' . round((($dati_assenze_totali[$classe][2] + $dati_assenze_totali[$classe][8]) / $dati_assenze_totali[$classe][0]), 1) .
                                    ' U=' . round((($dati_assenze_totali[$classe][3] + $dati_assenze_totali[$classe][9]) / $dati_assenze_totali[$classe][0]), 1) .
                                    ' e=' . round((($dati_assenze_totali[$classe][12] + $dati_assenze_totali[$classe][18]) / $dati_assenze_totali[$classe][0]), 1), 0, 1, 'L');
                            $totale_istituto[1] = $totale_istituto[1] + ($dati_assenze_totali[$classe][1] + $dati_assenze_totali[$classe][6] + $dati_assenze_totali[$classe][7]);
                            $totale_istituto[2] = $totale_istituto[2] + ($dati_assenze_totali[$classe][2] + $dati_assenze_totali[$classe][8]);
                            $totale_istituto[3] = $totale_istituto[3] + ($dati_assenze_totali[$classe][3] + $dati_assenze_totali[$classe][9]);
                            $totale_istituto[4] = $totale_istituto[4] + ($dati_assenze_totali[$classe][12] + $dati_assenze_totali[$classe][18]);
                            $totale_istituto[0] = $totale_istituto[0] + $dati_assenze_totali[$classe][0];
                        }

                        $pdf->Cell(0, 4, 'Totale istituto : A=' . $totale_istituto[1] .
                                ' E=' . $totale_istituto[2] . ' U=' . $totale_istituto[3] . ' e=' . $totale_istituto[4] . '  -  Media di istituto per studente: A=' . round(($totale_istituto[1] / $totale_istituto[0]), 1) .
                                ' E=' . round(($totale_istituto[2] / $totale_istituto[0]), 1) .
                                ' U=' . round(($totale_istituto[3] / $totale_istituto[0]), 1) .
                                ' e=' . round(($totale_istituto[4] / $totale_istituto[0]), 1), 0, 1, 'L');
                    } else {
                        $classi_totali = estrai_classi();
                        $template->assign("classi_totali", $classi_totali);
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_etichette":
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($id_classe > 0) {
                        $orientamento_pagina = "P";
                        $unita_misura = "mm";
                        $formato_pagina[0] = $lato_lungo_foglio + $margine_inferiore + $margine_superiore;
                        $formato_pagina[1] = $lato_corto_foglio + $margine_sinistro + $margine_destro;

                        $dati_classe = estrai_classe((int) $id_classe);

                        $dati_studenti = estrai_studenti_classe((int) $id_classe);

                        $pdf = new NEXUS_PDF($orientamento_pagina, $unita_misura, $formato_pagina);

                        $pdf->SetFillColor(224, 235, 255);
                        $pdf->SetTextColor(0, 0, 0);
                        $pdf->SetFont('helvetica', 'B', $dimensione_font);

                        $classe = "Classe " . $dati_classe["classe"] . "^" . $dati_classe["sezione"] . " " . $dati_classe["codice"];
                        $as = "Anno scolastico " . $anno_inizio . "/" . $anno_fine;
                        $cont_stud = 0;

                        $tot_stud = count($dati_studenti);
                        $tot_pagine = ceil(($tot_stud / ($numero_etichette_lungo * $numero_etichette_corto)));

                        for ($cont_pagine = 0; $cont_pagine < $tot_pagine; $cont_pagine++) {
                            //Data loading
                            $pdf->AddPage('P');
                            $pdf->SetMargins($margine_sinistro, $margine_superiore, $margine_destro);
                            $pdf->SetAutoPageBreak("off", 1);

                            for ($cont = 0; $cont < $numero_etichette_lungo; $cont++) {
                                $offset_verticale_etichetta = $cont * $altezza_etichetta;

                                for ($cont2 = 0; $cont2 < $numero_etichette_corto; $cont2++) {
                                    $studente = estrai_dati_studente((int) $dati_studenti[$cont_stud]["id_studente"]);
                                    $comune_residenza = estrai_provincia_comune($studente["codice_comune_residenza"]);
                                    $comune_nascita = estrai_provincia_comune($studente["codice_comune_nascita"]);
                                    $nazione_nascita = estrai_nazione($studente["stato_nascita"]);

                                    if ($info_classe == 'prox_as') {
                                        $classi_anno_succ = estrai_classi_studente((int) $dati_studenti[$cont_stud]["id_studente"], 'NO', 'anno_succ');
                                        $dati_classe = $classi_anno_succ[0];
                                        $classe = "Classe " . $dati_classe["classe"] . "^" . $dati_classe["sezione"] . " " . $dati_classe["codice"];
                                        $as = "Anno scolastico " . ($anno_inizio + 1) . "/" . ($anno_fine + 1);
                                    }
                                    if ($comune_residenza[0] == "ERROR") {
                                        $luogo_residenza = "";
                                    } else {
                                        $luogo_residenza = $studente["cap_residenza"] . ' ' . $comune_residenza["descrizione"] . " (" . $comune_residenza["codice"] . ")";
                                    }

                                    if ($comune_nascita[0] == "ERROR") {
                                        $luogo_nascita = $studente["citta_nascita_straniera"] . " (" . $nazione_nascita["descrizione"] . ")";
                                    } else {
                                        $luogo_nascita = $comune_nascita["descrizione"] . " (" . $comune_nascita["codice"] . ")";
                                    }

                                    if (($dati_studenti[$cont_stud]["sesso"] == "F") || ($dati_studenti[$cont_stud]["sesso"] == "f")) {
                                        if ($stampo_nome == 'GENITORE') {
                                            $nome = "Ai genitori di " . $dati_studenti[$cont_stud][2] . " " . $dati_studenti[$cont_stud][1];
                                        } elseif ($stampo_nome == 'TUTORE') {
                                            $nome = "Al tutore di " . $dati_studenti[$cont_stud][2] . " " . $dati_studenti[$cont_stud][1];
                                        } else {
                                            $nome = $dati_studenti[$cont_stud][2] . " " . $dati_studenti[$cont_stud][1];
                                        }

                                        $data_luogo_nascita = "Nata a " . $luogo_nascita . " il " . $dati_studenti[$cont_stud]["giorno_nascita"] . "/" . $dati_studenti[$cont_stud]["mese_nascita"] . "/" . $dati_studenti[$cont_stud]["anno_nascita"];
                                    } else {
                                        if ($stampo_nome == 'GENITORE') {
                                            $nome = "Ai genitori di " . $dati_studenti[$cont_stud][2] . " " . $dati_studenti[$cont_stud][1];
                                        } elseif ($stampo_nome == 'TUTORE') {
                                            $nome = "Al tutore di " . $dati_studenti[$cont_stud][2] . " " . $dati_studenti[$cont_stud][1];
                                        } else {
                                            $nome = $dati_studenti[$cont_stud][2] . " " . $dati_studenti[$cont_stud][1];
                                        }

                                        $data_luogo_nascita = "Nato a " . $luogo_nascita . " il " . $dati_studenti[$cont_stud]["giorno_nascita"] . "/" . $dati_studenti[$cont_stud]["mese_nascita"] . "/" . $dati_studenti[$cont_stud]["anno_nascita"];
                                    }

                                    if ($dicitura_residente == 'SI') {
                                        $indirizzo_residenza = "Residente in " . $dati_studenti[$cont_stud][4];
                                    } else {
                                        $indirizzo_residenza = $dati_studenti[$cont_stud][4];
                                    }

                                    $luogo_residenza = $luogo_residenza;
                                    $cont_stud++;
                                    $offset_orizzontale_etichetta = $cont2 * $larghezza_etichetta;

                                    if ($stampo_classe == "SI") {
                                        //setto posizione di "classe"
                                        $pdf->SetXY(($x_classe + $offset_orizzontale_etichetta + $margine_sinistro), ($y_classe + $offset_verticale_etichetta + $margine_superiore));
                                        $altezza_riga = $altezza_classe;
                                        $pdf->CellFitScale($larghezza_nome, $altezza_riga, $classe, 0, 1, 'L', 0);
                                    }

                                    if ($stampo_nome != "NO") {
                                        //setto posizione di "nome"
                                        $pdf->SetXY(($x_nome + $offset_orizzontale_etichetta + $margine_sinistro), ($y_nome + $offset_verticale_etichetta + $margine_superiore));
                                        $altezza_riga = $altezza_nome;
                                        $pdf->CellFitScale($larghezza_nome, $altezza_riga, $nome, 0, 1, 'L', 0);
                                    }

                                    if ($stampo_as == "SI") {
                                        //setto posizione di "as"
                                        $pdf->SetXY(($x_as + $offset_orizzontale_etichetta + $margine_sinistro), ($y_as + $offset_verticale_etichetta + $margine_superiore));
                                        $altezza_riga = $altezza_as;
                                        $pdf->CellFitScale($larghezza_as, $altezza_riga, $as, 0, 1, 'L', 0);
                                    }

                                    if ($stampo_data_luogo_nascita == "SI") {
                                        //setto posizione di "data_luogo_nascita"
                                        $pdf->SetXY(($x_data_luogo_nascita + $offset_orizzontale_etichetta + $margine_sinistro), ($y_data_luogo_nascita + $offset_verticale_etichetta + $margine_superiore));
                                        $altezza_riga = $altezza_data_luogo_nascita;
                                        $pdf->CellFitScale($larghezza_data_luogo_nascita, $altezza_riga, $data_luogo_nascita, 0, 1, 'L', 0);
                                    }

                                    if ($stampo_indirizzo_residenza == "SI") {
                                        //setto posizione di "indirizzo_residenza"
                                        $pdf->SetXY(($x_indirizzo_residenza + $offset_orizzontale_etichetta + $margine_sinistro), ($y_indirizzo_residenza + $offset_verticale_etichetta + $margine_superiore));
                                        $altezza_riga = $altezza_indirizzo_residenza;
                                        $pdf->CellFitScale($larghezza_indirizzo_residenza, $altezza_riga, $indirizzo_residenza, 0, 1, 'L', 0);
                                        $pdf->SetXY(($x_indirizzo_residenza + $offset_orizzontale_etichetta + $margine_sinistro), ($y_indirizzo_residenza + $altezza_indirizzo_residenza + $offset_verticale_etichetta + $margine_superiore));
                                        $altezza_riga = $altezza_indirizzo_residenza;
                                        $pdf->CellFitScale($larghezza_indirizzo_residenza, $altezza_riga, $luogo_residenza, 0, 1, 'L', 0);
                                    }

                                    if ($cont_stud >= $tot_stud) {
                                        $cont2 = $numero_etichette_corto;
                                        $cont = $numero_etichette_lungo;
                                    }
                                }
                            }
                        }
                    } else {
                        $classi = estrai_classi();
                        $template->assign("classi", $classi);
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_password_genitori":
                    //{{{ <editor-fold defaultstate="collapsed">
                    aggiorna_parametri_stampa($modalita_utilizzo, 'CREDENZIALI_PARENTI', (int) $current_user);
                    aggiorna_parametri_stampa($campo_libero_tagliandino, 'CAMPO_LIBERO_TAGLIANDINO', (int) $current_user);

                    $pdf = new NEXUS_PDF;

                    if ($testo_oggetto == '') {
                        $testo_oggetto = 'OGGETTO: Scheda dati - nuove modalità di comunicazione scuola-famiglia';
                    }

                    foreach ($mat_classi as $id_classe) {
                        $dati_studenti = estrai_studenti_classe((int) $id_classe);
                        $dati_classe = estrai_classe((int) $id_classe);
                        $nome_classe = $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5];

                        switch ($dimensione_font) {
                            case '6':
                                $altezza_celle = 3;
                                break;
                            case '7':
                                $altezza_celle = 4;
                                break;
                            case '8':
                                $altezza_celle = 4;
                                break;
                            case '9':
                                $altezza_celle = 4;
                                break;
                            case '10':
                                $altezza_celle = 5;
                                break;
                            case '11':
                                $altezza_celle = 5;
                                break;
                            case '12':
                                $altezza_celle = 6;
                                break;
                            case '13':
                                $altezza_celle = 6;
                                break;
                            case '14':
                                $altezza_celle = 6;
                                break;
                        }

                        for ($cont = 0; $cont < count($dati_studenti); $cont++) {
                            $cognome = $dati_studenti[$cont][2];
                            $nome = $dati_studenti[$cont][1];
                            $indirizzo = "{$dati_studenti[$cont]['descrizione_residenza']} ({$dati_studenti[$cont]['provincia_residenza_da_comune']}) --- {$dati_studenti[$cont][4]}";
                            $id_studente = $dati_studenti[$cont][0];

                            $genitori = estrai_genitori_studente((int) $id_studente, '', 'NO', 'NO', $current_key);

                            foreach ($genitori as $genitore) {
                                if (
                                        $genitore['non_vivente'] == 0 && (
                                        (
                                        ($scelta_credenziali == 'tutti' || $scelta_credenziali == 'unico') && in_array($genitore['parentela'], ['P',
                                            'M', 'T', 'O', 'Q'])
                                        ) || (
                                        $scelta_credenziali == 'padri' && $genitore['parentela'] == 'P'
                                        ) || (
                                        $scelta_credenziali == 'madri' && $genitore['parentela'] == 'M'
                                        ) || (
                                        $scelta_credenziali == 'tutori' && $genitore['parentela'] == 'T'
                                        ) || (
                                        $scelta_credenziali == 'affidatari' && ($genitore['parentela'] == 'O' || $genitore['parentela'] == 'Q')
                                        )
                                        )
                                ) {
                                    if ($scelta_credenziali == 'unico') {
                                        $testo_genitore = 'Credenziali di accesso famiglia';
                                    } else {
                                        $testo_genitore = 'Credenziali di accesso di ' . $genitore['cognome'] . ' ' . $genitore['nome'];
                                    }
                                    $codice_utente = $genitore['utente'];
                                    // $password_utente = MT\Utils\Pbkdf2::isValid($genitore['codice_attivazione'], $genitore['password']) || empty($genitore['password']) ? $genitore['codice_attivazione'] : '********';
                                    $password_utente = $genitore['codice_attivazione'];
                                    $pdf->AddPage('P');

                                    include 'stampe/stampa_password_studente.php';

                                    if ($scelta_credenziali == 'unico') {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_password_studenti":
                    //{{{ <editor-fold defaultstate="collapsed">
                    aggiorna_parametri_stampa($modalita_utilizzo, 'CREDENZIALI_STUDENTI', (int) $current_user);
                    aggiorna_parametri_stampa($campo_libero_tagliandino, 'CAMPO_LIBERO_TAGLIANDINO', (int) $current_user);

                    $pdf = new NEXUS_PDF;

                    foreach ($mat_classi as $id_classe) {
                        $dati_studenti = estrai_studenti_classe((int) $id_classe, false, false, null, null, $current_key);

                        $dati_classe = estrai_classe((int) $id_classe);
                        $nome_classe = $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5];

                        switch ($dimensione_font) {
                            case '6':
                                $altezza_celle = 3;
                                break;
                            case '7':
                                $altezza_celle = 4;
                                break;
                            case '8':
                                $altezza_celle = 4;
                                break;
                            case '9':
                                $altezza_celle = 4;
                                break;
                            case '10':
                                $altezza_celle = 5;
                                break;
                            case '11':
                                $altezza_celle = 5;
                                break;
                            case '12':
                                $altezza_celle = 6;
                                break;
                            case '13':
                                $altezza_celle = 6;
                                break;
                            case '14':
                                $altezza_celle = 6;
                                break;
                        }

                        for ($cont = 0; $cont < count($dati_studenti); $cont++) {
                            $cognome = $dati_studenti[$cont][2];
                            $nome = $dati_studenti[$cont][1];
                            $indirizzo = "{$dati_studenti[$cont]['descrizione_residenza']} ({$dati_studenti[$cont]['provincia_residenza_da_comune']}) --- {$dati_studenti[$cont][4]}";
                            $codice_utente = $dati_studenti[$cont][6];
                            $password_utente = $dati_studenti[$cont][7];
                            $id_studente = $dati_studenti[$cont][0];
                            $password_modificata = $dati_studenti[$cont]['password_modificata'];

                            $pdf->AddPage('P');
                            include 'stampe/stampa_password_studente.php';
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_password_docenti":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $prefisso_password_criptate = estrai_parametri_singoli("PREFISSO_PASSWORD_CRIPTATE");
                    $suffisso_password_criptate = estrai_parametri_singoli("SUFFISSO_PASSWORD_CRIPTATE");

                    switch ($tipo_stampa_pwd_docenti) {
                        case 'SINGOLO':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $elenco_professori = estrai_professori('P', 'NO', $limiti_classi);

                            $sotto_intestazione_stampa = "Riepilogo dati del docente";

                            $pdf = new NEXUS_PDF;

                            foreach ($elenco_professori as $singolo_prof) {
                                $id_professore = $singolo_prof['id_utente'];
                                $dati_prof = estrai_dati_professore((int) $id_professore, null, $current_key);
                                $elenco_funzioni = estrai_livello_abilitazione_funzioni_professore((int) $id_professore);
                                $url_esterno_prof = estrai_parametri_singoli("URL_ESTERNO_PROFESSORI");
                                $url_interno_prof = estrai_parametri_singoli("URL_INTERNO_PROFESSORI");
                                $elenco_abbinamenti = estrai_abbinamenti((int) $id_professore, "professore");
                                $dati_classe = estrai_classe((int) $elenco_abbinamenti[0]["id_classe"]);

                                $pdf->AddPage('P');
                                inserisci_intestazione_pdf($pdf, (int) $elenco_abbinamenti[0]["id_classe"]);
                                $pdf->ln(5);

                                $pdf->SetFont('Times', 'B', 14);
                                $pdf->Cell(0, 5, 'Riepilogo dati del docente', 0, 1, 'C');
                                $pdf->ln(15);
                                $pdf->SetFont('Times', 'B', 10);
                                $pdf->CellFitScale(100, 10, 'Cognome: ' . $dati_prof["cognome"], 0, 0, 'L');
                                $pdf->CellFitScale(100, 10, 'Nome: ' . $dati_prof["nome"], 0, 1, 'L');
                                $pdf->CellFitScale(100, 10, 'Utente: ' . $dati_prof["utente"], 0, 0, 'L');

                                $password_default = $prefisso_password_criptate . $dati_prof["utente"] . $suffisso_password_criptate;

                                if ($dati_prof['password_utente'] == md5($password_default)) {
                                    $pdf->CellFitScale(100, 10, 'Password: ' . $password_default, 0, 1, 'L');
                                } elseif ($dati_prof['password_utente'] == md5($password_default . '123')) {
                                    $pdf->CellFitScale(100, 10, 'Password: ' . $password_default . '123', 0, 1, 'L');
                                } elseif (!password_cambiata($password_default, $dati_prof['password_utente'])){
                                    $pdf->CellFitScale(100, 10, 'Password: ' . $password_default, 0, 1, 'L');
                                } elseif (!password_cambiata($password_default . '123', $dati_prof['password_utente'])){
                                    $pdf->CellFitScale(100, 10, 'Password: ' . $password_default . '123', 0, 1, 'L');
                                // } elseif (\MT\Utils\Pbkdf2::isValid($password_default, $dati_prof['password_utente'])) {
                                //     $pdf->CellFitScale(100, 10, 'Password: ' . $password_default, 0, 1, 'L');
                                // } elseif (\MT\Utils\Pbkdf2::isValid($password_default . '123', $dati_prof['password_utente'])) {
                                //     $pdf->CellFitScale(100, 10, 'Password: ' . $password_default . '123', 0, 1, 'L');
                                } else {
                                    if ($dati_prof['password_modificata'] == 'SI') {
                                        $pdf->CellFitScale(100, 10, 'Password: **********', 0, 1, 'L');
                                    } else {
                                        $pdf->CellFitScale(100, 10, 'Password: --vecchia password non modificata--', 1, 1, 'C');
                                    }
                                }

                                switch ($dati_prof['privilegi']) {
                                    case "0":
                                        $privilegio_attuale = "Informativi";
                                        break;
                                    case "1":
                                        $privilegio_attuale = "Dispositivi";
                                        break;
                                    case "3":
                                        $privilegio_attuale = "Nessun privilegio abilitato";
                                        break;
                                }

                                $pdf->CellFitScale(0, 10, 'Privilegi: ' . $privilegio_attuale, 0, 1, 'L');
                                $pdf->CellFitScale(0, 10, 'Funzionalità abilitate: ', 0, 1, 'L');
                                $pdf->SetFont('Times', '', 9);

                                for ($cont = 0; $cont < count($elenco_funzioni); $cont++) {
                                    if ($elenco_funzioni[$cont][4] == 1) {
                                        $pdf->CellFitScale(5, 5, '', 0, 0, 'L');
                                        $pdf->CellFitScale(0, 5, $elenco_funzioni[$cont][1] . " = " . $elenco_funzioni[$cont][2], 0, 1, 'L');
                                    }
                                }

                                $pdf->SetFont('Times', 'B', 10);
                                $pdf->CellFitScale(200, 10, 'INDIRIZZI PER COLLEGARSI AL SISTEMA', 0, 1, 'C');
                                $pdf->SetFont('Times', '', 9);
                                $pdf->CellFitScale(200, 10, 'Indirizzo interno: ' . $url_interno_prof, 0, 1, 'L');
                                $pdf->CellFitScale(200, 10, 'Indirizzo esterno: ' . $url_esterno_prof, 0, 1, 'L');
                            }
                            //}}} </editor-fold>
                            break;
                        case 'LISTA_XLS':
                            $tipo_file_esportato = 'xls';
                            $testo_excel = "Riepilogo Informazioni Professori";
                            $testo_excel .= "\n";
                            $testo_excel .= "\n";

                            $testo_excel .= "Professore\t";
                            $testo_excel .= "Utente\t";
                            $testo_excel .= "Password";
                            $testo_excel .= "\n";

                            $elenco_professori = estrai_professori('P', 'NO', $limiti_classi, $current_key);

                            for ($cont = 0; $cont < count($elenco_professori); $cont++) {
                                $id_professore = $elenco_professori[$cont]['id_utente'];
                                $dati_prof = estrai_dati_professore((int) $id_professore, null, $current_key);
                                $testo_excel .= $elenco_professori[$cont]['cognome'] . " " . $elenco_professori[$cont]['nome'] . "\t";
                                $testo_excel .= $elenco_professori[$cont]['utente'] . "\t";

                                $password_default = $prefisso_password_criptate . trim($elenco_professori[$cont]["utente"]) . $suffisso_password_criptate;

                                if ($dati_prof['password_utente'] == md5($password_default) ||
                                        !password_cambiata($password_default, $dati_prof['password_utente'])) {
                                    $testo_excel .= $password_default;
                                } elseif ($dati_prof['password_utente'] == md5($password_default . '123') ||
                                    !password_cambiata($password_default . '123', $dati_prof['password_utente'])) {
                                    $testo_excel .= $password_default . "123";
                                } else {
                                    if ($dati_prof['password_modificata'] == 'SI') {
                                        $testo_excel .= "***********";
                                    } else {
                                        $testo_excel .= "--vecchia password non modificata--";
                                    }
                                }
                                $testo_excel .= "\n";
                            }

                            break;
                        case 'ELENCO':
                        default:
                            //{{{ <editor-fold defaultstate="collapsed">
                            //estraggo i dati da passare alla stampa
                            $elenco_professori = estrai_professori('P', 'NO', $limiti_classi, $current_key);

                            $intestazione_stampa = "Riepilogo informazioni professori";

                            $sotto_intestazione_stampa = "Elenco Professori";

                            $pdf = new NEXUS_PDF;

                            $pdf->AddPage('P');
                            $pdf->ln(5);

                            $pdf->SetFont('Times', 'B', 14);
                            $pdf->Cell(200, 5, $intestazione_stampa, 0, 1, 'C');
                            $pdf->ln(10);
                            $pdf->SetFont('Times', 'B', 12);
                            $pdf->Cell(200, 5, $sotto_intestazione_stampa, 0, 1, 'C');
                            $pdf->ln(5);
                            $pdf->SetFont('Times', 'B', 10);
                            $pdf->Cell(80, 5, 'PROFESSORE', 1, 0, 'C');
                            $pdf->Cell(50, 5, 'UTENTE', 1, 0, 'C');
                            $pdf->Cell(50, 5, 'PASSWORD', 1, 1, 'C');

                            for ($cont = 0; $cont < count($elenco_professori); $cont++) {
                                $id_professore = $elenco_professori[$cont]['id_utente'];
                                $dati_prof = estrai_dati_professore((int) $id_professore, null, $current_key);
                                $pdf->CellFitScale(80, 5, $elenco_professori[$cont]['cognome'] . " " . $elenco_professori[$cont]['nome'], 1, 0, 'L');
                                $pdf->CellFitScale(50, 5, $elenco_professori[$cont]["utente"], 1, 0, 'C');
                                $password_default = $prefisso_password_criptate . trim($elenco_professori[$cont]["utente"]) . $suffisso_password_criptate;

                                if ($dati_prof['password_utente'] == md5($password_default)) {
                                    $pdf->CellFitScale(50, 5, $password_default, 1, 1, 'C');
                                } elseif ($dati_prof['password_utente'] == md5($password_default . '123')) {
                                    $pdf->CellFitScale(50, 5, $password_default . '123', 1, 1, 'C');
                                } elseif (!password_cambiata($password_default, $dati_prof['password_utente'])) {
                                    $pdf->CellFitScale(50, 5, $password_default, 1, 1, 'C');
                                } elseif (!password_cambiata($password_default . '123', $dati_prof['password_utente'])) {
                                    $pdf->CellFitScale(50, 5, $password_default . '123', 1, 1, 'C');
                                } else {
                                    if ($dati_prof['password_modificata'] == 'SI') {
                                        $pdf->CellFitScale(50, 5, '***********', 1, 1, 'C');
                                    } else {
                                        $pdf->CellFitScale(50, 5, '--vecchia password non modificata--', 1, 1, 'C');
                                    }
                                }
                            }
                            //}}} </editor-fold>
                            break;
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_abbinamenti":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $elenco_professori = estrai_professori('P', 'NO', $limiti_classi);
                    $template->assign("professori", $elenco_professori);
                    //echo_debug($elenco_professori);
                    $elenco_materie = estrai_materie_con_itp();
                    $template->assign("materie", $elenco_materie);

                    switch ($form_stampa) {
                        case "professore":
                            $elenco_abbinamenti = [];
                            if ($id_professore == 'TUTTI_PROF') {
                                foreach ($elenco_professori as $prof) {
                                    $elenco_abbinamenti[] = estrai_abbinamenti((int) $prof['id_utente'], $form_stampa);
                                }
                            } else {
                                $elenco_abbinamenti[] = estrai_abbinamenti((int) $id_professore, $form_stampa);
                            }

                            include "stampe/stampa_abbinamenti.php";
                            break;
                        case "materia":
                            $elenco_abbinamenti = [];
                            if ($id_materia == 'TUTTE_MATERIE') {
                                foreach ($elenco_materie as $materia) {
                                    if ($materia[7] == 'materia_principale') {
                                        $elenco_abbinamenti[] = estrai_abbinamenti((int) $materia[0], $form_stampa, $materia[1]);
                                    }
                                }
                            } else {
                                foreach ($elenco_materie as $materia) {
                                    if ($materia[0] == $id_materia) {
                                        $elenco_abbinamenti[] = estrai_abbinamenti((int) $materia[0], $form_stampa, $materia[1]);
                                    }
                                }
                            }

                            include "stampe/stampa_abbinamenti.php";
                            break;
                        case "classe":
                            $elenco_abbinamenti = [];
                            $elenco_classi_senza_abbinamenti = [];
                            $multi_classe_pagelle = strtoupper($includi_sottoclassi);

                            if ($id_classe == 'TUTTE_CLASSI') {
                                $elenco_classi = [];
                                if ($multi_classe_pagelle == 'SI') {
                                    $elenco_classi = estrai_classi($ordinamento, $esclusioni = "classi_principali");
                                    foreach ($elenco_classi as $classe) {
                                        $elenco_abbinamenti[] = estrai_materie_multi_classe($classe['id_classe']);
                                    }
                                } else {
                                    $elenco_classi = estrai_classi($ordinamento, $esclusioni = "classi_principali");
                                    foreach ($elenco_classi as $classe) {
                                        $elenco_abbinamenti[] = estrai_abbinamenti((int) $classe['id_classe'], $form_stampa);
                                        $ultimo = end($elenco_abbinamenti);
                                        if ($ultimo[0] == NULL) {
                                            $classe2 = estrai_classe($classe['id_classe']);
                                            array_push($elenco_classi_senza_abbinamenti, $classe2);
                                        }
                                    }
                                }
                                $singola_classe = 'NO';
                            } else {
                                //$multi_classe_pagelle = estrai_parametri_singoli('MULTI_CLASSE_PAGELLE', $id_classe, 'classe');
                                if ($multi_classe_pagelle == 'SI') {
                                    $elenco_classi = estrai_multi_classi_da_classe((int) $id_classe);

                                    if (is_array($elenco_classi)) {
                                        foreach ($elenco_classi as $classe) {
                                            $elenco_abbinamenti[] = estrai_abbinamenti((int) $classe['id_classe'], $form_stampa);
                                        }
                                    }
                                } else {
                                    $elenco_abbinamenti[] = estrai_abbinamenti((int) $id_classe, $form_stampa);
                                }

                                $singola_classe = 'SI';
                            }

                            include "stampe/stampa_abbinamenti.php";
                            break;
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_dati_commissione":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $dati_commissioni = estrai_dati_commissioni();
                    $numero = 0;

                    if (is_array($dati_commissioni)) {
                        foreach ($dati_commissioni as $commissione) {
                            $id_commissione = $commissione[0];
                            $altezza_celle = 6;
                            $pdf->AddPage('P');
                            $pdf->SetFillColor(220);

                            //stampo l'intestazione della stampa
                            inserisci_intestazione_pdf($pdf, $commissione['id_classe']);

                            $pdf->Ln(10);
                            $pdf->SetFont('helvetica', 'B', 10);
                            $pdf->CellFitScale(100, $altezza_celle, 'CLASSE : ', 0, 0, 'L');
                            $pdf->CellFitScale(35, $altezza_celle, $commissione[8], 1, 1, 'L');
                            $pdf->Ln(2);
                            $pdf->CellFitScale(100, $altezza_celle, 'CODICE MECCANOGRAFICO COMMISSIONE : ', 0, 0, 'L');
                            $pdf->CellFitScale(35, $altezza_celle, $commissione[3], 1, 1, 'L');
                            $pdf->Ln(2);
                            $pdf->CellFitScale(100, $altezza_celle, 'NUMERO ROMANO COMMISSIONE : ', 0, 0, 'L');
                            $pdf->CellFitScale(35, $altezza_celle, $commissione[5], 1, 1, 'L');
                            $pdf->Ln(2);
                            $pdf->CellFitScale(100, $altezza_celle, 'NUMERO COMMISSIONE : ', 0, 0, 'L');
                            $pdf->CellFitScale(35, $altezza_celle, $commissione['numero'], 1, 1, 'L');
                            $pdf->SetFont('helvetica', 'B', 10);
                            $pdf->Ln(30);
                            $pdf->CellFitScale(50, $altezza_celle, 'Cognome commissario', 1, 0, 'C');
                            $pdf->CellFitScale(50, $altezza_celle, 'Nome commissario', 1, 0, 'C');
                            $pdf->CellFitScale(40, $altezza_celle, 'Tipo commissario', 1, 0, 'C');
                            $pdf->CellFitScale(50, $altezza_celle, 'Ruolo', 1, 1, 'C');
                            $pdf->SetFont('helvetica', '', 10);

                            $query = "SELECT commissari.*,
											utenti.cognome,
											utenti.nome
											FROM
												commissari
												INNER JOIN
													utenti
												ON
													commissari.id_utente = utenti.id_utente
												WHERE
												commissari.flag_canc = 0
												AND
												id_commissione = $id_commissione
												ORDER BY ruolo_commissario_est DESC";

                            $result = pgsql_query($query) or die("Invalid $query");
                            $numero = pg_num_rows($result);
                            $commissari = [];

                            if ($numero > 0) {
                                for ($cont = 0; $cont < $numero; $cont++) {
                                    $commissari[] = pg_fetch_assoc($result, $cont);
                                }
                            }

                            foreach ($commissari as $commissario) {
                                if ($commissario['ruolo_commissario_est'] != '') {
                                    $tipo = 'Esterno';
                                } else {
                                    $tipo = 'Interno';
                                }

                                switch ($commissario['ruolo_interno']) {
                                    case "VS":
                                        $ruolo = 'Vicepresidente e segretario';
                                        break;
                                    case "V":
                                        $ruolo = 'Vicepresidente';
                                        break;
                                    case "S";
                                        $ruolo = 'Segretario';
                                        break;
                                    default:
                                        $ruolo = '';
                                        break;
                                }

                                if ($commissario['ruolo_commissario_est'] == 'P') {
                                    $ruolo = 'Presidente';
                                }

                                $pdf->CellFitScale(50, $altezza_celle, decode($commissario['cognome']), 1, 0, 'C');
                                $pdf->CellFitScale(50, $altezza_celle, decode($commissario['nome']), 1, 0, 'C');
                                $pdf->CellFitScale(40, $altezza_celle, $tipo, 1, 0, 'C');
                                $pdf->CellFitScale(50, $altezza_celle, $ruolo, 1, 1, 'C');
                            }
                            //echo_debug($commissari);
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_elenco_classi":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $elenco_classi = estrai_classi_debug($limiti_classi);
                    $intestazione_stampa = "Riepilogo informazioni classi";

                    $pdf = new NEXUS_PDF;
                    $pdf->AddPage('L');
                    $pdf->ln(5);
                    $pdf->SetFont('helvetica', 'B', 14);
                    $pdf->Cell(0, 5, $intestazione_stampa, 0, 1, 'C');
                    $pdf->ln(10);
                    $pdf->SetFont('helvetica', 'B', 10);

                    $pdf->CellFitScale(20, 5, 'Classe', 1, 0, 'C');
                    $pdf->CellFitScale(30, 5, 'Ordinamento', 1, 0, 'C');
                    $pdf->CellFitScale(60, 5, 'Indirizzo ministeriale', 1, 0, 'C');
                    $pdf->CellFitScale(60, 5, 'Indirizzo', 1, 0, 'C');
                    $pdf->CellFitScale(30, 5, 'Tipo indirizzo', 1, 0, 'C');
                    $pdf->CellFitScale(40, 5, 'Scuola', 1, 0, 'C');
                    $pdf->CellFitScale(20, 5, 'Cod. Debole', 1, 0, 'C');
                    $pdf->CellFitScale(15, 5, 'N. materie', 1, 1, 'C');
                    $pdf->SetFont('helvetica', '', 10);

                    if (is_array($elenco_classi)) {
                        foreach ($elenco_classi as $classe) {
                            $pdf->CellFitScale(20, 5, $classe['classe'] . ' ' . $classe['sezione'], 1, 0, 'C');
                            $pdf->CellFitScale(30, 5, $classe['descrizione_ordinamento'], 1, 0, 'C');
                            $pdf->CellFitScale(60, 5, $classe['classificazione_ministeriale'], 1, 0, 'C');
                            $pdf->CellFitScale(60, 5, $classe['descrizione_indirizzi'], 1, 0, 'C');
                            $pdf->CellFitScale(30, 5, $classe['descrizione_tipo_indirizzo'], 1, 0, 'C');
                            $pdf->CellFitScale(40, 5, $classe['descrizione_scuola'], 1, 0, 'C');
                            $pdf->CellFitScale(20, 5, $classe['codice_meccanografico_secondario'], 1, 0, 'C');
                            $pdf->CellFitScale(15, 5, $classe['numero_materie'], 1, 1, 'C');
                        }
                    }
                    //}}} </editor-fold>
                    break;
                case "stampa_monteore_corsi_classe":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $elenco_studenti = estrai_studenti_classe($id_classe);
                    $inizio_Month_int = intval($inizio_Month);
                    $fine_Month_int = intval($fine_Month);
                    $inizio = mktime(0, 0, 0, $inizio_Month_int, $inizio_Day, $inizio_Year);
                    $fine = mktime(0, 0, 0, $fine_Month_int, $fine_Day, $fine_Year) + 24 * 3600;
                    $data_stampa = date('d/m/Y');
                    $ora_stampa = date('H:i');

                    $pdf = new NEXUS_PDF('P', 'mm', 'A4');
                    $testo_excel = '';

                    $testo_excel .= "Stampato il $data_stampa alle $ora_stampa" . chr(10);
                    $testo_excel .= "RIEPILOGO DEL MONTEORE CORSI DELLO STUDENTE" . chr(10);
                    $testo_excel .= "PER IL PERIODO DAL $inizio_Day/$inizio_Month_int/$inizio_Year AL $fine_Day/$fine_Month_int/$fine_Year" . chr(10);

                    $testo_excel .= 'COGNOME' . chr(9);
                    $testo_excel .= 'NOME' . chr(9);
                    $testo_excel .= 'CLASSE' . chr(9);
                    $testo_excel .= 'MATERIA DEL CORSO' . chr(9);
                    $testo_excel .= 'DESCRIZIONE' . chr(9);
                    if ($stampa_lezioni == 'SI') {
                        $testo_excel .= 'LEZIONI' . chr(9);
                    }
                    $testo_excel .= 'MONTE ORE' . chr(9);
                    $testo_excel .= 'ASSENZE' . chr(9);
                    $testo_excel .= chr(10);

                    foreach ($elenco_studenti as $studente) {
                        include 'stampe/stampa_monteore_corsi_studente.php';
                    }

                    //}}} </editor-fold>
                    break;
                case "riepilogo_ore_cfp":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $elenco_classi[0]["id_classe"] = $id_classe;
                    include "stampe/stampa_riepilogo_ore_cfp.php";
                    //}}} </editor-fold>
                    break;
                case "vincoli_registro_presenze":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $data_inizio = strtotime("$inizio_Day-$inizio_Month-$inizio_Year");
                    $data_fine = strtotime("$fine_Day-$fine_Month-$fine_Year 22:00:00");

                    $testo_excel = '';
                    $testo_excel .= '"Codice fiscale studente"' . chr(9);
                    $testo_excel .= '"Cognome"' . chr(9);
                    $testo_excel .= '"Nome"' . chr(9);
                    $testo_excel .= '"Data"' . chr(9);
                    $testo_excel .= '"Ora inizio"' . chr(9);
                    $testo_excel .= '"Ora fine"' . chr(9);
                    $testo_excel .= '"Ore Presenza"' . chr(9);
                    $testo_excel .= '"Ore Assenza"' . chr(9);
                    $testo_excel .= '"Ore Assenza Giustificata"' . chr(9);
                    $testo_excel .= '"Rif. Evento Formativo"' . chr(9);
                    $testo_excel .= '"Tipologia"' . chr(9);
                    $testo_excel .= '"Codice fiscale docente"' . chr(9);
                    $testo_excel .= '"Materia"' . chr(9);
                    $testo_excel .= '"Contenuti materia"' . chr(9);
                    $testo_excel .= '"Sede fisica-virtuale"' . chr(9);
                    $testo_excel .= chr(10);

                    foreach ($mat_classi as $id_classe) {
                        $payload = [
                            "tipo_id"  => "classe",
                            "id" => [
                                "0" => $id_classe
                            ],
                            "data_inizio" => $data_inizio,
                            "data_fine" => $data_fine
                        ];

                        $result = nextapi_call("integrazioni_esterne/estrai_presenze_per_CRS", 'GET', $payload, $current_key);
                        $cls_dati = estrai_classe($id_classe);

                        foreach ($result['payload']['listaStudenti'] as $event)
                        {
                            $tipologia_ev = $event['tipologia'];
                            if ($cls_dati['classe'] == 4 && stripos($cls_dati['sezione'], 'APPRENDISTATO') !== false ) {
                                $tipologia_ev = 5;
                            }
                            if ($event['materia'] == 'SOSTEGNO') {
                                $tipologia_ev = 14;
                            }

                            $tot_minuti_evento = $event['orePresenza'] + $event['oreAssenza'] + $event['oreAssenzaGiustificata'];

//                            $ore_presenza_perc = round($event['orePresenza']/$tot_minuti_evento,2);
//                            $ore_assenza_perc = round($event['oreAssenza']/$tot_minuti_evento,2);
//                            $ore_assenza_giust_perc = round($event['oreAssenzaGiustificata']/$tot_minuti_evento,2);
                            $ore_presenza_perc = $event['orePresenza'];
                            $ore_assenza_perc = $event['oreAssenza'];
                            $ore_assenza_giust_perc = $event['oreAssenzaGiustificata'];

                            $testo_excel .= '"'.$event['codiceFiscale'].'"' . chr(9);
                            $testo_excel .= '"'.decode($event['cognome']).'"' . chr(9);
                            $testo_excel .= '"'.decode($event['nome']).'"' . chr(9);
                            $testo_excel .= '"'.$event['data'].'"' . chr(9);
                            $testo_excel .= '"'.$event['oraInizio'].'"' . chr(9);
                            $testo_excel .= '"'.$event['oraFine'].'"' . chr(9);
                            $testo_excel .= '"'.$ore_presenza_perc.'"' . chr(9);
                            $testo_excel .= '"'.$ore_assenza_perc.'"' . chr(9);
                            $testo_excel .= '"'.$ore_assenza_giust_perc.'"' . chr(9);
                            $testo_excel .= '"'.$result['payload']['idSezione'].'"' . chr(9);
                            $testo_excel .= '"'.$tipologia_ev.'"' . chr(9);
                            $testo_excel .= '"'.$event['codiceFiscaleDocente'].'"' . chr(9);
                            $testo_excel .= '"'.decode($event['materia']).'"' . chr(9);
                            $testo_excel .= '"'.decode($event['contenutiMateria']).'"' . chr(9);
                            $testo_excel .= '"'.decode($event['sede']).'"' . chr(9);
                            $testo_excel .= chr(10);
                        }
                    }

                    //cancello tutte i file temporanei fatti da più di un'ora
                    $dir = 'tmp_xls';

                    CleanFiles($dir);
                    //creo i nuovi file temporanei
                    $file = basename(tempnam($dir, 'tmp'));
                    rename($dir . '/' . $file, $dir . '/' . "Importa_Registro_Presenze" . '_' . date('Y-m-d_H-i_s') . '.xls');
                    $file = "Importa_Registro_Presenze" . '_' . date('Y-m-d_H-i_s') . '.xls';
                    //Salva il file xls come file
                    $nuovo_nome = $dir . '/' . $file;
                    $handle = fopen($nuovo_nome, 'w');
                    fwrite($handle, $testo_excel);
                    fclose($handle);

                    //Reindirizzamento JavaScript
                    echo "<HTML><SCRIPT>document.location='{$nuovo_nome}';</SCRIPT></HTML>";
                    //}}} </editor-fold>
                    break;
                case "riepilogo_esportazione_regione":
                    //{{{ <editor-fold defaultstate="collapsed">
                    include "stampe/riepilogo_esportazione_regione.php";
                    //}}} </editor-fold>
                    break;
                case "export_riepilogo_assenze":
                    //{{{ <editor-fold defaultstate="collapsed">
                    include "stampe/export_riepilogo_assenze.php";
                    //}}} </editor-fold>
                    break;
                case "riepilogo_assenze_01":
                    //{{{ <editor-fold defaultstate="collapsed">
                    include "stampe/stampe_personalizzate/riepilogo_assenze_01.php";
                    //}}} </editor-fold>
                    break;
                case "campi_liberi_cfp":
                    //{{{ <editor-fold defaultstate="collapsed">
                    $elenco_classi[0]["id_classe"] = $id_classe;
                    include "stampe/stampa_campi_liberi_cfp.php";
                    //}}} </editor-fold>
                    break;
                case "campi_liberi_wizard":
                    //{{{ <editor-fold defaultstate="collapsed">
                    include "stampe/wizard_stampa_campi_liberi.php";
                    //}}} </editor-fold>
                    break;

                case "elenco_recuperi_pai_1920":
                    //{{{ <editor-fold defaultstate="collapsed">
                    include "stampe/stampa_elenco_recuperi.php";
                    //}}} </editor-fold>
                    break;

                //{{{ <editor-fold defaultstate="collapsed" desc="SA (MI)">
                case 'ss_12345_intermedia_sa_mi_01':
                //}}}</editor-fold>
                //{{{ <editor-fold defaultstate="collapsed" desc="DB (MI)">
                case 'ss_12345_intermedia_db_mi_01':
                    include "stampe/pagelle_personalizzate/ss_12345_intermedia_sa_db_mi_01.php";
                    break;
                //}}}</editor-fold>
                //{{{ <editor-fold defaultstate="collapsed" desc="liceoluzzago">
                case 'registro_voti_ss_liceoluzzago_01':
                    include "stampe/stampe_personalizzate/registro_voti_ss_liceoluzzago_01.php";
                    break;
                //}}}</editor-fold>
                //{{{ <editor-fold defaultstate="collapsed" desc="statistiche istat (rilevazioni integrative)">
                case "statistiche_istat":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_default.php";
                    }

                    break;
                case "statistiche_istat_non_statali":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_non_statali_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_non_statali_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_non_statali_default.php";
                    }

                    break;
                case "statistiche_istat_elementari":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_elementari_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_elementari_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_elementari_default.php";
                    }

                    break;
                case "statistiche_istat_elementari_non_statali":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_elementari_non_statali_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_elementari_non_statali_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_elementari_non_statali_default.php";
                    }

                    break;
                case "statistiche_istat_infanzia":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_infanzia_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_infanzia_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_infanzia_default.php";
                    }

                    break;
                case "statistiche_istat_infanzia_non_statali":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_infanzia_non_statali_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_infanzia_non_statali_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_infanzia_non_statali_default.php";
                    }

                    break;
                case "statistiche_istat_scuole_medie":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_medie_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_medie_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_medie_default.php";
                    }

                    break;
                case "statistiche_istat_scuole_medie_non_statali":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_medie_non_statali_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_medie_non_statali_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_medie_non_statali_default.php";
                    }

                    break;
                case "statistiche_istat_esiti_scrutini":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_esiti_scrutini_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_esiti_scrutini_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_esiti_scrutini_default.php";
                    }

                    break;
                case "statistiche_istat_esiti_scrutini_medie":
                    if (file_exists($path_statistiche_istat . "stampa_statistiche_istat_esiti_scrutini_medie_{$anno_scolastico_attuale_underscore}.php")) {
                        include $path_statistiche_istat . "stampa_statistiche_istat_esiti_scrutini_medie_{$anno_scolastico_attuale_underscore}.php";
                    } else {
                        include $path_statistiche_istat . "stampa_statistiche_istat_esiti_scrutini_medie_default.php";
                    }

                    break;
                //}}} </editor-fold>
                default:
                    //{{{ <editor-fold defaultstate="collapsed" desc="Caso generale stampa: verifica esistenza include basato su nome stampa">
                    $file_short = 'adm/stampe/' . $tipo_stampa . '.php';
                    $file_long = 'adm/stampe/stampa_' . $tipo_stampa . '.php';
                    $report_custom = 'adm/stampe/pagelle_personalizzate/' . $tipo_stampa . '.php';
                    $file_custom = 'adm/stampe/stampe_personalizzate/' . $tipo_stampa . '.php';
                    if (file_exists($file_short)) {
                        require_once $file_short;
                    } elseif (file_exists($file_long)) {
                        require_once $file_long;
                    } elseif (file_exists($report_custom)) {
                        require_once $report_custom;
                    } elseif (file_exists($file_custom)) {
                        require_once $file_custom;
                    }
                    //}}} </editor-fold>
                    break;
            }

            if ($tipo_file_esportato == 'xls') {
                //cancello tutte i file temporanei fatti da più di un'ora
                $dir = 'tmp_xls';

                CleanFiles($dir);
                //creo i nuovi file temporanei
                $file = basename(tempnam($dir, 'tmp'));
                rename($dir . '/' . $file, $dir . '/' . $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls');
                $file = $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls';
                //Salva il file xls come file
                $nuovo_nome = $dir . '/' . $file;
                $handle = fopen($nuovo_nome, 'w');
                fwrite($handle, $testo_excel);
                fclose($handle);

                //Reindirizzamento JavaScript
                echo "<HTML><SCRIPT>document.location='{$nuovo_nome}';</SCRIPT></HTML>";
            } elseif ($tipo_file_esportato == 'csv') {
                //cancello tutte i file temporanei fatti da più di un'ora
                $dir = 'tmp_xls';

                CleanFiles($dir);
                //creo i nuovi file temporanei
                $file = basename(tempnam($dir, 'tmp'));
                rename($dir . '/' . $file, $dir . '/' . $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.csv');
                $file = $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.csv';
                //Salva il file xls come file
                $nuovo_nome = $dir . '/' . $file;
                $handle = fopen($nuovo_nome, 'w');
                fwrite($handle, $testo_excel);
                fclose($handle);

                //Reindirizzamento JavaScript
                echo "<HTML><SCRIPT>document.location='{$nuovo_nome}';</SCRIPT></HTML>";
            } else {
                $pdf->Output($tipo_stampa . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
                exit;
            }
        }
        //}}} </editor-fold>
        break;
    case 'stampa_prova_doc':
        $ret = nextapi_call('phpoffice/prova', 'POST', [], $current_key);
        break;
    case 'stampa_modello_word':
        ini_set('max_execution_time', '240');

        $parametri = [];
        if ($db_official != $db_key) {
            $parametri['anno_db'] = $db_key;
        }
        $variabili_template = nextapi_call('phpoffice/phpword/getTemplateVariables/' . $id_template, 'GET', $parametri, $current_key);
        $template_word = nextapi_call('phpoffice/phpword/getTemplate/' . $id_template, 'GET', $parametri, $current_key);

        // estrazione dati
        $array_dati = estrai_dati_template_word($variabili_template, $template_word['tipo'], $mat_studenti, 'studenti');

        //esempio per template singolo
        $array_dati_tmp = [];
        // $array_dati_tmp = [
        //     "cognome"   =>  "Lodi",
        //     "nome"          =>  "Mattia",
        //     "indirizzo"          =>  "Via Frassinara",
        //     "citta"          =>  "Correggio",
        //     "scuola"          =>  "Paperon",
        //     "indirizzo_scuola"  =>  "Via $$",
        //     "citta_scuola"      =>  "Paperopoli",
        //     "pippo"     =>  "Pippo",
        //     "pluto"     =>  "Pluto",
        //     "paperino"     =>  "Paperino"
        // ];
        //esempio per template di gruppo
        // $array_dati_tmp = [
        //     [
        //         "cognome"   =>  "Lodi",
        //         "nome"          =>  "Mattia",
        //         "indirizzo"          =>  "Via Frassinara",
        //         "citta"          =>  "Correggio",
        //         "scuola"          =>  "Paperon",
        //         "indirizzo_scuola"  =>  "Via $$",
        //         "citta_scuola"      =>  "Paperopoli",
        //         "pippo"     =>  "Pippo",
        //         "pluto"     =>  "Pluto",
        //         "paperino"     =>  "Paperino"
        //     ],
        //     [
        //         "cognome"   =>  "Pippo",
        //         "nome"          =>  "Pluto",
        //         "indirizzo"          =>  "Via Paperino",
        //         "citta"          =>  "Topolino",
        //         "scuola"          =>  "Paperon",
        //         "indirizzo_scuola"  =>  "Via $$",
        //         "citta_scuola"      =>  "Paperopoli",
        //         "pippo"     =>  "Pippo",
        //         "pluto"     =>  "Pluto",
        //         "paperino"     =>  "Paperino"
        //     ]
        // ];

        $rel_dir = 'tmp_word/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;

        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';
        exec('mkdir ' . $temp_dir);

        if ($template_word['tipo'] == 'singolo') {
            foreach ($array_dati as $studente) {
                $parametri['array_dati'] = $studente;
                $parametri['temp_dir'] = $dir;
                if ($db_official != $db_key) {
                    $parametri['anno_db'] = $db_key;
                }

                $filePath = nextapi_call('phpoffice/phpword/translateTemplateVariables/' . $id_template, 'POST', $parametri, $current_key);
            }
        } else {
            $parametri['array_dati'] = $array_dati;
            $parametri['temp_dir'] = $dir;
            if ($db_official != $db_key) {
                $parametri['anno_db'] = $db_key;
            }

            $filePath = nextapi_call('phpoffice/phpword/translateTemplateVariables/' . $id_template, 'POST', $parametri, $current_key);
        }

        $nome = 'export' . date('YmdHi') . '.zip';
        $ext = ($filePath['estensione']) ? $filePath['estensione'] : $template_word['estensione'];
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*' . "." . $ext);
        exec('rm -fr ' . $dir);

        $download_path = $rel_dir . $nome;

        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='{$download_path}';</SCRIPT></HTML>";

        break;
    case 'stampa_pagelle_template_word':
    case 'stampa_registro_generale_voti_competenze':
// debug
$id_template_pagella_word = str_replace('pagella_word_', '', $tipo_stampa);
$dati_template = estrai_modelli_word($id_template_pagella_word)[0];
// fine debug
        $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
        $payload = [
            "id_classe" => $id_classe,
            "periodo"   => $periodo,
            "tipo_abbinamento" => $tipo_abbinamento,
            "anno_db"   => $db_key
        ];
//        $dati_template = nextapi_call('phpoffice/phpword/getTemplatePagella', 'GET', $payload, $current_key);

        if (empty($dati_template)){
            print '<pre>';
            print("Attenzione: non presente alcun modello di stampa; contattare l'assistenza");
            print '</pre>';
            break;
        }
        $dati_classe = estrai_classe($id_classe);
        $nome = $dati_template['nome'] . ' ' . $dati_classe['classe'] . $dati_classe['sezione'] . ' ' . $dati_classe['codice_indirizzi'] . ' ' . date('YmdHi');

        $divisione_periodi = estrai_parametri_singoli('PERIODI_SCOLASTICI', $id_classe, 'classe');
        $periodo_tabellone = ($dati_classe['tipo_indirizzo'] == 4) ? $periodo + 20 : $periodo;
        $dati_tabellone = estrai_tabellone_classe_competenze($id_classe, $periodo_tabellone, $current_user, $current_key, 'amministratore', null, true);
        $dati_tabellone_1qt = [];
        $dati_tabellone_2t = [];

        // estraggo i dati degli scrutini precedenti per avere competenze e campi liberi
        switch ($periodo_tabellone) {
            case 8:
                $dati_tabellone_1qt = estrai_tabellone_classe_competenze($id_classe, 7, $current_user, $current_key, 'amministratore', null, true);
                break;
            case 28:
                $dati_tabellone_1qt = estrai_tabellone_classe_competenze($id_classe, 27, $current_user, $current_key, 'amministratore', null, true);
                break;
            case 9:
                $dati_tabellone_1qt = estrai_tabellone_classe_competenze($id_classe, 7, $current_user, $current_key, 'amministratore', null, true);
                if ($divisione_periodi == 'trimestri'){
                    $dati_tabellone_2t = estrai_tabellone_classe_competenze($id_classe, 8, $current_user, $current_key, 'amministratore', null, true);
                }
                break;
            case 29:
                $dati_tabellone_1qt = estrai_tabellone_classe_competenze($id_classe, 27, $current_user, $current_key, 'amministratore', null, true);
                if ($divisione_periodi == 'trimestri'){
                    $dati_tabellone_2t = estrai_tabellone_classe_competenze($id_classe, 28, $current_user, $current_key, 'amministratore', null, true);
                }
                break;
            default:
                break;
        }

        $rel_dir = 'tmp_word/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;

        $rand_dir = substr(md5(rand(0, 1000000)), 0, 12);
        $temp_dir = $base_dir . $rand_dir;
        $dir = $temp_dir . '/';
        exec('mkdir ' . $temp_dir);


        $coordinatore = estrai_coordinatore($id_classe);
        $docenti_classe = estrai_elenco_professori_multi_classe($id_classe);
        $elenco_docenti = $lista_docenti = [];
        foreach ($docenti_classe as $key => $value) {
            $docenti_classe[$key]['docente'] = $value['cognome'] . ' ' . $value['nome'];
            $lista_docenti[] = $value['cognome'] . ' ' . $value['nome'];
        }
        $docenti_classe = ordina_array($docenti_classe, 'docente');
        $array_file_stampa = [];
        $array_per_sito = [];

        $data_fine_primo_quadrimestre = date("d/m/Y", estrai_parametri_singoli('DATA_INIZIO_SECONDO_QUADRIMESTRE', $id_classe, 'classe') - (60*60*12));
        $data_fine_secondo_trimestre = date("d/m/Y", estrai_parametri_singoli('DATA_INIZIO_TERZO_TRIMESTRE', $id_classe, 'classe') - (60*60*12));
        $data_fine_lezioni = date("d/m/Y", estrai_parametri_singoli('DATA_FINE_LEZIONI', $id_classe, 'classe'));

        foreach ($dati_tabellone['elenco_studenti'] as $studente){
            if ($tipo_abbinamento == 'RELIGIONE' && $studente['esonero_religione'] == 1){
                continue;
            }
            if ($tipo_abbinamento == 'ALTERNATIVA' && $studente['esonero_religione'] == 0){
                continue;
            }

            $dati_studente_stampa = estrai_dati_studente($studente['id_studente']);
            // $dati_studente_stampa = $studente;
            if ( stripos($dati_studente_stampa['descrizione_nascita'], 'ESTERO') !== false ) {
                $dati_studente_stampa['descrizione_nascita'] = $dati_studente_stampa['citta_nascita_straniera'];
            }
            if ( stripos($dati_studente_stampa['provincia_nascita_da_comune'], 'EE') !== false ) {
                $dati_studente_stampa['provincia_nascita_da_comune'] = $dati_studente_stampa['descrizione_stato_nascita'];
            }

            $dati_studente_stampa['tipo'] = preg_replace('/[^A-Za-z\_]/', '', $studente['cognome'].'_'.$studente['nome']); // momentaneamente per metterlo nel nome file in output e non farlo sovrascrivere
            $dati_studente_stampa['data_stampa'] = $data_stampa_Day . '/'. $data_stampa_Month . '/'. $data_stampa_Year;
            $dati_studente_stampa['data_fine_primo_quadrimestre'] = $data_fine_primo_quadrimestre;
            $dati_studente_stampa['data_fine_secondo_trimestre'] = $data_fine_secondo_trimestre;
            $dati_studente_stampa['data_fine_lezioni'] = $data_fine_lezioni;
            $dati_studente_stampa['elenco_docenti'] = $docenti_classe;
            $dati_studente_stampa['lista_docenti'] = implode(', ', $lista_docenti);
            $dati_studente_stampa['anno_scolastico'] = $anno_scolastico;
            $dati_studente_stampa['coordinatore'] = $coordinatore['cognome'] . ' ' . $coordinatore['nome'];
            $dati_studente_stampa['ore_assenza_totali_periodo_attuale'] = $studente['ore_assenza_totali_periodo_selezionato'];
            $dati_studente_stampa['monteore_totali_periodo_attuale'] = $studente['monteore_totali_periodo_selezionato'];
            $dati_studente_stampa['ore_assenza_totali_1qt'] = '0:0';
            $dati_studente_stampa['monteore_totali_1qt'] = '0:0';
            $dati_studente_stampa['ore_assenza_totali_2t'] = '0:0';
            $dati_studente_stampa['monteore_totali_2t'] = '0:0';

            if ($dati_studente_stampa['sesso'] == 'F') {
                $dati_studente_stampa['suffisso_o_a'] = 'a';
                $dati_studente_stampa['suffisso_e_essa'] = 'essa';
                $dati_studente_stampa['suffisso_O_A'] = 'A';
                $dati_studente_stampa['suffisso_E_ESSA'] = 'ESSA';
                $dati_studente_stampa['esito_corrente_calcolato'] = str_replace('Iscritto', 'Iscritta', $dati_studente_stampa['esito_corrente_calcolato']);
                $dati_studente_stampa['esito_corrente_calcolato'] = str_replace('iscritto', 'iscritta', $dati_studente_stampa['esito_corrente_calcolato']);
                $dati_studente_stampa['esito_corrente_calcolato'] = str_replace('Ammesso', 'Ammessa', $dati_studente_stampa['esito_corrente_calcolato']);
                $dati_studente_stampa['esito_corrente_calcolato'] = str_replace('ammesso', 'ammessa', $dati_studente_stampa['esito_corrente_calcolato']);
            } else {
                $dati_studente_stampa['suffisso_o_a'] = 'o';
                $dati_studente_stampa['suffisso_e_essa'] = 'e';
                $dati_studente_stampa['suffisso_O_A'] = 'O';
                $dati_studente_stampa['suffisso_E_ESSA'] = 'E';
            }

            // ore assenza e monteore totali singoli periodi
            if (isset($dati_tabellone_1qt['elenco_studenti'])){
                foreach ($dati_tabellone_1qt['elenco_studenti'] as $studente_1qt){
                    if ($studente_1qt['id_studente'] == $studente['id_studente']){
                        $dati_studente_stampa['ore_assenza_totali_1qt'] = $studente_1qt['ore_assenza_totali_periodo_selezionato'];
                        $dati_studente_stampa['monteore_totali_1qt'] = $studente_1qt['monteore_totali_periodo_selezionato'];
                        break;
                    }
                }
            }

            if (isset($dati_tabellone_2t['elenco_studenti'])) {
                foreach ($dati_tabellone_2t['elenco_studenti'] as $studente_2t) {
                    if ($studente_2t['id_studente'] == $studente['id_studente']) {
                        $dati_studente_stampa['ore_assenza_totali_2t'] = $studente_2t['ore_assenza_totali_periodo_selezionato'];
                        $dati_studente_stampa['monteore_totali_2t'] = $studente_2t['monteore_totali_periodo_selezionato'];
                        break;
                    }
                }
            }

            // CURRICULUM
            $a = explode('/', $anno_scolastico);
            $anno_inizio = (int) $a[0];
            $anno_fine = (int) $a[1];
            $curriculum_studente = estrai_curriculum_studente((int) $studente['id_studente']);
            $anno_scolastico_precedente = ($anno_inizio - 1) . "/" . ($anno_fine - 1);
            //estraggo i dati di provenienza
            $scuola_provenienza = '';
            for ($cont_curr = 0; $cont_curr < count($curriculum_studente); $cont_curr++) {
                if ($curriculum_studente[$cont_curr]['esito'] != 'Iscritto') {
                    if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_precedente) {
                        if ($studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola']) {
                            $scuola_provenienza = $curriculum_studente[$cont_curr]['nome_scuola'] . $curriculum_studente[$cont_curr]['descrizione_libera_scuola'];
                        } else {
                            $scuola_provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'] . ' ' . $curriculum_studente[$cont_curr]['descrizione_libera_scuola'];
                        }
                    }
                }
            }
            $dati_studente_stampa['scuola_provenienza'] = $scuola_provenienza;

            // per registro voti
            $dati_studente_stampa['num_volte_iscritto_classe_attuale_testo'] = traduci_classe_in_lettere($dati_studente_stampa['num_volte_iscritto_classe_attuale']);
            $dati_studente_stampa['esito_classe_destinazione'] = $dati_studente_stampa['mat_esito']['classe_destinazione'];
            $dati_studente_stampa['esito_classe_precedente'] = $dati_studente_stampa['mat_esito']['classe_precedente'];
            $dati_studente_stampa['esito_descrizione_classe_precedente'] = $dati_studente_stampa['mat_esito']['descrizione_classe_precedente'];
            $dati_studente_stampa['esito_numero_volte_iscritto'] = $dati_studente_stampa['mat_esito']['numero_volte_iscritto'];
            $dati_studente_stampa['esito_numero_volte_iscritto_testo'] = traduci_classe_in_lettere($dati_studente_stampa['mat_esito']['numero_volte_iscritto']);

            $tot_assenze_fine_anno = 0;
            $tot_ore_fine_anno = 0;
            $scrutinato = false;

            if ($stampa_competenze_trasversali == 'NO'){
                unset($dati_tabellone['elenco_materie'][-1]);
            }

            foreach ($dati_tabellone['elenco_materie'] as $materia) {
                if (in_array($studente['id_studente'], $materia['elenco_studenti'])){
                    $materia_tmp = [];
                    $materia_tmp = $materia;

                    //pulisco dai dati generali
                    unset($materia_tmp['elenco_studenti']);
                    unset($materia_tmp['elenco_competenze']);
                    unset($materia_tmp['voti_pagelline']);

                    $materia_tmp['ore_assenza'] = $materia['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza_ore'] . ':' . $materia['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza_minuti'];
                    $materia_tmp['monteore'] = $materia['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale_ore'] . ':' . $materia['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale_minuti'];

                    $materia_tmp['ore_assenza_1qt'] = "0:00";
                    $materia_tmp['ore_assenza_2t'] = "0:00";
                    $materia_tmp['ore_assenza_finale'] = "0:00";
                    $materia_tmp['monteore_1qt'] = "0:00";
                    $materia_tmp['monteore_2t'] = "0:00";
                    $materia_tmp['monteore_finale'] = "0:00";

                    // cerco la materia nei tabelloni degli scrutini precedenti per potere poi fare il merge in un unico array per la materia
                    $materia_tabelloni = [];
                    $materia_tabelloni['tab_attuale'] = $materia;
                    if (!empty($dati_tabellone_1qt)){
                        foreach ($dati_tabellone_1qt['elenco_materie'] as $mat_tab_prec){
                            if ($mat_tab_prec['id_materia'] == $materia['id_materia']){
                                $materia_tabelloni['1qt'] = $mat_tab_prec;
                                break;
                            }
                        }
                    }
                    if (!empty($dati_tabellone_2t)){
                        foreach ($dati_tabellone_2t['elenco_materie'] as $mat_tab_prec){
                            if ($mat_tab_prec['id_materia'] == $materia['id_materia']){
                                $materia_tabelloni['2t'] = $mat_tab_prec;
                                break;
                            }
                        }
                    }
                    if (in_array($periodo, [9, 29])){
                        $materia_tabelloni['finale'] = $materia;
                    }


                    // COMPETENZE
                    foreach ($materia_tabelloni as $chiave_tipo_tabellone => $materia_tabellone){
                        foreach ($materia_tabellone['elenco_competenze'] as $id_template_competenze => $competenze_template){
                            foreach ($competenze_template as $competenza){
                                //verifico che lo studente abbia la competenza
                                if (isset($competenza['elenco_studenti'][$studente['id_studente']])){
                                    $competenza_tmp = [];
                                    $competenza_tmp['id'] = $competenza['id'];
                                    $competenza_tmp['tipo'] = $competenza['tipo'];
                                    $competenza_tmp['descrizione'] = $competenza['descrizione'];
                                    $competenza_tmp['codice'] = $competenza['codice'];
                                    $competenza_tmp['descrizione_stampa'] = $competenza['descrizione_stampa'];

                                    // VALUTAZIONI DELLO SCRUTINIO STAMPATO
                                    if ($chiave_tipo_tabellone == 'tab_attuale'){
                                        $dati_scrutinio = $competenza['elenco_studenti'][$studente['id_studente']]['scrutinio'][$periodo];
                                    } elseif ($chiave_tipo_tabellone == '1qt'){
                                        $dati_scrutinio = $competenza['elenco_studenti'][$studente['id_studente']]['scrutinio'][7];
                                    } else {
                                        $dati_scrutinio = $competenza['elenco_studenti'][$studente['id_studente']]['scrutinio'][8];
                                    }
                                    //voto
                                    $competenza_tmp['voto'] = $dati_scrutinio['voto']['descrizione_stampa']; //default per voto
                                    $competenza_tmp['voto_valore_numerico'] = $dati_scrutinio['voto']['valore_numerico'];
                                    $competenza_tmp['voto_codice'] = $dati_scrutinio['voto']['codice'];
                                    $competenza_tmp['voto_descrizione'] = $dati_scrutinio['voto']['descrizione'];
                                    $competenza_tmp['voto_descrizione_stampa'] = $dati_scrutinio['voto']['descrizione_stampa'];
                                    //proposta
                                    $competenza_tmp['proposta'] = $dati_scrutinio['proposta']['descrizione_stampa']; //default per proposta
                                    $competenza_tmp['proposta_valore_numerico'] = $dati_scrutinio['proposta']['valore_numerico'];
                                    $competenza_tmp['proposta_codice'] = $dati_scrutinio['proposta']['codice'];
                                    $competenza_tmp['proposta_descrizione'] = $dati_scrutinio['proposta']['descrizione'];
                                    $competenza_tmp['proposta_descrizione_stampa'] = $dati_scrutinio['proposta']['descrizione_stampa'];

                                    if ($competenza_tmp['voto_valore_numerico'] != ''){
                                        $scrutinato = true;
                                    }

                                    // VALUTAZIONI DI TUTTI GLI SCRUTINI 1quadr/trim, 2trim e fine anno
                                    $chiavi_periodi = ['7' => '_1qt', '8' => '_2t', '9' => '_finale'];
                                    foreach ($chiavi_periodi as $numero_periodo => $desc){
                                        $dati_scrutinio = $competenza['elenco_studenti'][$studente['id_studente']]['scrutinio'][$numero_periodo];
                                        //voto
                                        $competenza_tmp['voto' . $desc] = $dati_scrutinio['voto']['descrizione_stampa']; //default per voto
                                        $competenza_tmp['voto_valore_numerico' . $desc] = $dati_scrutinio['voto']['valore_numerico'];
                                        $competenza_tmp['voto_codice'.$desc] = $dati_scrutinio['voto']['codice'];
                                        $competenza_tmp['voto_descrizione' . $desc] = $dati_scrutinio['voto']['descrizione'];
                                        $competenza_tmp['voto_descrizione_stampa' . $desc] = $dati_scrutinio['voto']['descrizione_stampa'];
                                        //proposta
                                        $competenza_tmp['proposta' . $desc] = $dati_scrutinio['proposta']['descrizione_stampa']; //default per proposta
                                        $competenza_tmp['proposta_valore_numerico' . $desc] = $dati_scrutinio['proposta']['valore_numerico'];
                                        $competenza_tmp['proposta_codice' . $desc] = $dati_scrutinio['proposta']['codice'];
                                        $competenza_tmp['proposta_descrizione' . $desc] = $dati_scrutinio['proposta']['descrizione'];
                                        $competenza_tmp['proposta_descrizione_stampa' . $desc] = $dati_scrutinio['proposta']['descrizione_stampa'];
                                    }

                                    if ($quali_competenze_stampare == 'TUTTE'
                                        ||
                                        ($quali_competenze_stampare == 'SOLO_COMPETENZE_CON_VOTI'
                                            &&
                                            (
                                                ($chiave_tipo_tabellone == 'tab_attuale' && $competenza_tmp['voto_valore_numerico'] != '')
                                                ||
                                                ($chiave_tipo_tabellone !='tab_attuale' && $competenza_tmp['voto_valore_numerico_' . $chiave_tipo_tabellone] != '')
                                            )
                                        )
                                    ){
                                        if ($chiave_tipo_tabellone == 'tab_attuale'){
                                            $materia_tmp['elenco_competenze'][] = $competenza_tmp;
                                        } else {
                                            $materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone][] = $competenza_tmp;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // CAMPI LIBERI
                    foreach ($materia_tabelloni as $chiave_tipo_tabellone => $materia_tabellone) {
                        $posfisso_array = ($chiave_tipo_tabellone == 'tab_attuale') ? "" : "_" . $chiave_tipo_tabellone;

                        if (isset($materia_tabellone['voti_pagelline'][$studente['id_studente']])){
                            if ($chiave_tipo_tabellone == 'tab_attuale') {
                                $voto_pagellina = $materia_tabellone['voti_pagelline'][$studente['id_studente']][$periodo];
                            } elseif ($chiave_tipo_tabellone == '1qt') {
                                $voto_pagellina = $materia_tabellone['voti_pagelline'][$studente['id_studente']][7];
                                $materia_tmp['ore_assenza_1qt'] = $voto_pagellina['ore_assenza_ore'] . ':' . $voto_pagellina['ore_assenza_minuti'];
                                $materia_tmp['monteore_1qt'] = $voto_pagellina['monteore_totale_ore'] . ':' . $voto_pagellina['monteore_totale_minuti'];
                            } elseif ($chiave_tipo_tabellone == 'finale') {
                                $voto_pagellina = $materia_tabellone['voti_pagelline'][$studente['id_studente']][9];
                                $materia_tmp['ore_assenza_finale'] = $voto_pagellina['ore_assenza_ore'] . ':' . $voto_pagellina['ore_assenza_minuti'];
                                $materia_tmp['monteore_finale'] = $voto_pagellina['monteore_totale_ore'] . ':' . $voto_pagellina['monteore_totale_minuti'];

                                if ($voto_pagellina['in_media_pagelle'] == 'SI' || $voto_pagellina['tipo_materia'] == 'RELIGIONE') {
                                    $tot_assenze_fine_anno += intval($voto_pagellina['ore_assenza']);
                                    $tot_ore_fine_anno += intval($voto_pagellina['monteore_totale']);
                                }
                            } else {
                                $voto_pagellina = $materia_tabellone['voti_pagelline'][$studente['id_studente']][8];
                                $materia_tmp['ore_assenza_2t'] = $voto_pagellina['ore_assenza_ore'] . ':' . $voto_pagellina['ore_assenza_minuti'];
                                $materia_tmp['monteore_2t'] = $voto_pagellina['monteore_totale_ore'] . ':' . $voto_pagellina['monteore_totale_minuti'];
                            }

                            foreach ($voto_pagellina['elenco_campi_liberi']['normali'] as $campo_libero){
                                $campo_libero_tmp = [];
                                $campo_libero_tmp['id_campo_libero'] = $campo_libero['id_campo_libero'];
                                $campo_libero_tmp['nome'] = $campo_libero['nome'];
                                $campo_libero_tmp['descrizione'] = $campo_libero['descrizione'];
                                $campo_libero_tmp['tipo_stampa'] = $campo_libero['tipo_stampa'];
                                $campo_libero_tmp['separatore_descrizione_valore'] = $campo_libero['separatore_descrizione_valore'];
                                $campo_libero_tmp['valore'] = $campo_libero['valore'][$materia_tabellone['id_materia']]['valore'];

                                $campo_libero_valorizzato = false;
                                if (strlen($campo_libero_tmp['valore']) > 0) {
                                    $campo_libero_valorizzato = true;
                                }

                                $materia_tmp['elenco_campi_liberi_totali' . $posfisso_array][$campo_libero['id_campo_libero']] = $campo_libero_tmp;

                                if ($campo_libero_valorizzato){
                                    $materia_tmp['elenco_campi_liberi_totali' . $posfisso_array . '_popolati'][$campo_libero['id_campo_libero']] = $campo_libero_tmp;
                                }

                                if ($posfisso_array !="" && isset($materia_tmp['elenco_campi_liberi_totali'][$campo_libero['id_campo_libero']])) {
                                    $materia_tmp['elenco_campi_liberi_totali'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];

                                    if ($campo_libero_valorizzato) {
                                        $materia_tmp['elenco_campi_liberi_totali_popolati'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];
                                    }
                                }

                                switch ($campo_libero['tipo_stampa']) {
                                    case 'media':
                                        $materia_tmp['elenco_campi_liberi_condotta' . $posfisso_array][$campo_libero['id_campo_libero']] = $campo_libero_tmp;

                                        if ($campo_libero_valorizzato) {
                                            $materia_tmp['elenco_campi_liberi_condotta' .$posfisso_array . '_popolati'][$campo_libero['id_campo_libero']] = $campo_libero_tmp;
                                        }

                                        if ($posfisso_array !="" && isset($materia_tmp['elenco_campi_liberi_condotta'][$campo_libero['id_campo_libero']])) {
                                            $materia_tmp['elenco_campi_liberi_condotta'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];

                                            if ($campo_libero_valorizzato) {
                                                $materia_tmp['elenco_campi_liberi_condotta_popolati'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];
                                            }
                                        }

                                        $dati_studente_stampa['giudizio_finale' . $posfisso_array] .= $campo_libero_tmp['valore'] . " ";
                                        break;
                                    case 'singola':
                                    default:
                                        $materia_tmp['elenco_campi_liberi' . $posfisso_array][$campo_libero['id_campo_libero']] = $campo_libero_tmp;

                                        if ($campo_libero_valorizzato) {
                                            $materia_tmp['elenco_campi_liberi' . $posfisso_array . '_popolati'][$campo_libero['id_campo_libero']] = $campo_libero_tmp;
                                        }

                                        if ($posfisso_array !="" && isset($materia_tmp['elenco_campi_liberi'][$campo_libero['id_campo_libero']])){
                                            $materia_tmp['elenco_campi_liberi'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];

                                            if ($campo_libero_valorizzato) {
                                                $materia_tmp['elenco_campi_liberi_popolati'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];
                                            }
                                        }
                                        break;
                                }
                            }

                            foreach ($voto_pagellina['elenco_campi_liberi']['parenti'] as $campo_libero){
                                $campo_libero_tmp = [];
                                if ($campo_libero['tipo_valore'] == 'RAGGRUPPAMENTO'){
                                    foreach ($campo_libero['figli'] as $figlio){
                                        $campo_libero_tmp['id_campo_libero'] = $campo_libero['id_campo_libero'];
                                        $campo_libero_tmp['nome'] = $figlio['nome'];
                                        $campo_libero_tmp['descrizione'] = $figlio['descrizione'];
                                        $campo_libero_tmp['tipo_stampa'] = $campo_libero['tipo_stampa']; //del padre
                                        $campo_libero_tmp['separatore_descrizione_valore'] = $campo_libero['separatore_descrizione_valore']; //del padre
                                        $campo_libero_tmp['valore'] = $figlio['valore'][$materia_tabellone['id_materia']]['valore'];

                                        $campo_libero_valorizzato = false;
                                        if (strlen($campo_libero_tmp['valore']) > 0) {
                                            $campo_libero_valorizzato = true;
                                        }

                                        $materia_tmp['elenco_campi_liberi_totali' . $posfisso_array][$campo_libero['id_campo_libero']] = $campo_libero_tmp;

                                        if ($campo_libero_valorizzato){
                                            $materia_tmp['elenco_campi_liberi_totali' . $posfisso_array . '_popolati'][$campo_libero['id_campo_libero']] = $campo_libero_tmp;
                                        }

                                        if ($posfisso_array !="" && isset($materia_tmp['elenco_campi_liberi_totali'][$campo_libero['id_campo_libero']])) {
                                            $materia_tmp['elenco_campi_liberi_totali'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];

                                            if ($campo_libero_valorizzato) {
                                                $materia_tmp['elenco_campi_liberi_totali_popolati'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];
                                            }
                                        }

                                        switch ($campo_libero['tipo_stampa']) {
                                            case 'media':
                                                $materia_tmp['elenco_campi_liberi_condotta' . $posfisso_array][$figlio['id_campo_libero']] = $campo_libero_tmp;

                                                if ($campo_libero_valorizzato) {
                                                    $materia_tmp['elenco_campi_liberi_condotta' . $posfisso_array . '_popolati'][$figlio['id_campo_libero']] = $campo_libero_tmp;
                                                }

                                                if ($posfisso_array != "" && isset($materia_tmp['elenco_campi_liberi_condotta'][$figlio['id_campo_libero']])) {
                                                    $materia_tmp['elenco_campi_liberi_condotta'][$figlio['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];

                                                    if ($campo_libero_valorizzato) {
                                                        $materia_tmp['elenco_campi_liberi_condotta_popolati'][$figlio['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];
                                                    }
                                                }

                                                $dati_studente_stampa['giudizio_finale' . $posfisso_array] .= $campo_libero_tmp['valore'] . " ";
                                                break;
                                            case 'singola':
                                            default:
                                                $materia_tmp['elenco_campi_liberi' . $posfisso_array][$figlio['id_campo_libero']] = $campo_libero_tmp;

                                                if ($campo_libero_valorizzato) {
                                                    $materia_tmp['elenco_campi_liberi' . $posfisso_array . '_popolati'][$figlio['id_campo_libero']] = $campo_libero_tmp;
                                                }

                                                if ($posfisso_array !="" && isset($materia_tmp['elenco_campi_liberi'][$figlio['id_campo_libero']])) {
                                                    $materia_tmp['elenco_campi_liberi'][$figlio['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];

                                                    if ($campo_libero_valorizzato) {
                                                        $materia_tmp['elenco_campi_liberi_popolati'][$figlio['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];
                                                    }
                                                }
                                                break;
                                        }
                                    }
                                } else {
                                    $campo_libero_tmp['id_campo_libero'] = $campo_libero['id_campo_libero'];
                                    $campo_libero_tmp['nome'] = $campo_libero['nome'];
                                    $campo_libero_tmp['descrizione'] = $campo_libero['descrizione'];
                                    $campo_libero_tmp['tipo_stampa'] = $campo_libero['tipo_stampa'];
                                    $campo_libero_tmp['valore'] = $campo_libero['valore'][$materia_tabellone['id_materia']]['valore'];

                                    $campo_libero_valorizzato = false;
                                    if (strlen($campo_libero_tmp['valore']) > 0) {
                                        $campo_libero_valorizzato = true;
                                    }

                                    $materia_tmp['elenco_campi_liberi_totali' . $posfisso_array][$campo_libero['id_campo_libero']] = $campo_libero_tmp;

                                    if ($campo_libero_valorizzato){
                                        $materia_tmp['elenco_campi_liberi_totali' . $posfisso_array . '_popolati'][$campo_libero['id_campo_libero']] = $campo_libero_tmp;
                                    }

                                    if ($posfisso_array !="" && isset($materia_tmp['elenco_campi_liberi_totali'][$campo_libero['id_campo_libero']])) {
                                        $materia_tmp['elenco_campi_liberi_totali'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];

                                        if ($campo_libero_valorizzato) {
                                            $materia_tmp['elenco_campi_liberi_totali_popolati'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];
                                        }
                                    }

                                    switch ($campo_libero['tipo_stampa']) {
                                        case 'media':
                                            $materia_tmp['elenco_campi_liberi_condotta' . $posfisso_array][$campo_libero['id_campo_libero']] = $campo_libero_tmp;

                                            if ($campo_libero_valorizzato) {
                                                $materia_tmp['elenco_campi_liberi_condotta' . $posfisso_array . '_popolati'][$campo_libero['id_campo_libero']] = $campo_libero_tmp;
                                            }

                                            if ($posfisso_array !="" && isset($materia_tmp['elenco_campi_liberi_condotta'][$campo_libero['id_campo_libero']])) {
                                                $materia_tmp['elenco_campi_liberi_condotta'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];

                                                if ($campo_libero_valorizzato) {
                                                    $materia_tmp['elenco_campi_liberi_condotta_popolati'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];
                                                }
                                            }

                                            $dati_studente_stampa['giudizio_finale' . $posfisso_array] .= $campo_libero_tmp['valore'] . " ";
                                            break;
                                        case 'singola':
                                        default:
                                            $materia_tmp['elenco_campi_liberi' . $posfisso_array][] = $campo_libero_tmp;

                                            if ($campo_libero_valorizzato) {
                                                $materia_tmp['elenco_campi_liberi' . $posfisso_array . '_popolati'][] = $campo_libero_tmp;
                                            }

                                            if ($posfisso_array !="" && isset($materia_tmp['elenco_campi_liberi'][$campo_libero['id_campo_libero']])) {
                                                $materia_tmp['elenco_campi_liberi'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];

                                                if ($campo_libero_valorizzato) {
                                                    $materia_tmp['elenco_campi_liberi_popolati'][$campo_libero['id_campo_libero']]['valore' . $posfisso_array] = $campo_libero_tmp['valore'];
                                                }
                                            }
                                            break;
                                    }
                                }
                            }
                        }
                    }

                    // popolo delle variabili vuote di default se non popolati
                    if (empty($materia_tmp['elenco_campi_liberi_popolati'])){
                        $materia_tmp['elenco_campi_liberi_popolati'][-1]['nome'] = '';
                        $materia_tmp['elenco_campi_liberi_popolati'][-1]['separatore_descrizione_valore'] = '';
                        $materia_tmp['elenco_campi_liberi_popolati'][-1]['valore'] = '';
                        $materia_tmp['elenco_campi_liberi_popolati'][-1]['valore_1qt'] = '';
                        $materia_tmp['elenco_campi_liberi_popolati'][-1]['valore_2t'] = '';
                        $materia_tmp['elenco_campi_liberi_popolati'][-1]['valore_finale'] = '';
                    }
                    // ---

                    foreach ($materia_tmp as $key => $value) {
                        if (strpos($key, 'elenco_campi_liberi') !== false) {
                            if (is_array($value) && count($value) > 0) {
                                $materia_tmp[$key] = array_values($value);
                            }
                        }
                    }

                    // array con tutte le materie all'interno
                    $dati_studente_stampa['elenco_materie'][] = $materia_tmp;

                    switch ($materia['tipo_materia']) {
                        case 'RELIGIONE':
                            $materia_tmp['elenco_competenze_religione'] = $materia_tmp['elenco_competenze'];
                            // inserisco i sottoarray 1qt, 2t e finale quando presenti
                            foreach ($materia_tabelloni as $chiave_tipo_tabellone_tmp => $materia_tabellone_tmp) {
                                if (count($materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp]) > 0){
                                    $materia_tmp['elenco_competenze_religione_' . $chiave_tipo_tabellone_tmp] = $materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp];
                                }
                            }
                            $dati_studente_stampa['elenco_materie_religione'][] = $materia_tmp;

                            if ($studente['esonero_religione'] == 0){
                                $materia_tmp['elenco_competenze_religionealternativa'] = $materia_tmp['elenco_competenze'];
                                // inserisco i sottoarray 1qt, 2t e finale quando presenti
                                foreach ($materia_tabelloni as $chiave_tipo_tabellone_tmp => $materia_tabellone_tmp) {
                                    if (count($materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp]) > 0) {
                                        $materia_tmp['elenco_competenze_religionealternativa_' . $chiave_tipo_tabellone_tmp] = $materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp];
                                    }
                                }
                                $dati_studente_stampa['elenco_materie_religionealternativa'][] = $materia_tmp;

                            }
                            break;
                        case 'ALTERNATIVA':
                            $materia_tmp['elenco_competenze_alternativa'] = $materia_tmp['elenco_competenze'];
                            // inserisco i sottoarray 1qt, 2t e finale quando presenti
                            foreach ($materia_tabelloni as $chiave_tipo_tabellone_tmp => $materia_tabellone_tmp) {
                                if (count($materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp]) > 0) {
                                    $materia_tmp['elenco_competenze_alternativa_' . $chiave_tipo_tabellone_tmp] = $materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp];
                                }
                            }
                            $dati_studente_stampa['elenco_materie_alternativa'][] = $materia_tmp;

                            if ($studente['esonero_religione'] == 1) {
                                $materia_tmp['elenco_competenze_religionealternativa'] = $materia_tmp['elenco_competenze'];
                                // inserisco i sottoarray 1qt, 2t e finale quando presenti
                                foreach ($materia_tabelloni as $chiave_tipo_tabellone_tmp => $materia_tabellone_tmp) {
                                    if (count($materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp]) > 0) {
                                        $materia_tmp['elenco_competenze_religionealternativa_' . $chiave_tipo_tabellone_tmp] = $materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp];
                                    }
                                }
                                $dati_studente_stampa['elenco_materie_religionealternativa'][] = $materia_tmp;
                            }
                            break;
                        case 'CONDOTTA':
                            $materia_tmp['elenco_competenze_condotta'] = $materia_tmp['elenco_competenze'];
                            // inserisco i sottoarray 1qt, 2t e finale quando presenti
                            foreach ($materia_tabelloni as $chiave_tipo_tabellone_tmp => $materia_tabellone_tmp) {
                                if (count($materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp]) > 0) {
                                    $materia_tmp['elenco_competenze_condotta_' . $chiave_tipo_tabellone_tmp] = $materia_tmp['elenco_competenze_' . $chiave_tipo_tabellone_tmp];
                                }
                            }
                            $dati_studente_stampa['elenco_materie_condotta'][] = $materia_tmp;
                            break;
                        case 'NORMALE':
                        default:
                            $dati_studente_stampa['elenco_materie_normali'][] = $materia_tmp;
                            break;
                    }
                }
            }

            if ($tot_ore_fine_anno > 0) {
                $perc = round(($tot_assenze_fine_anno / $tot_ore_fine_anno) * 100);

                if ($perc < 25) {
                    $dati_studente_stampa['ha_non_ha_frequentato'] = 'ha frequentato';
                    $dati_studente_stampa['deroga'] = '';
                } else {
                    if ($scrutinato) {
                        $dati_studente_stampa['ha_non_ha_frequentato'] = 'ha frequentato';
                        $dati_studente_stampa['deroga'] = 'con deroga';
                    } else {
                        $dati_studente_stampa['ha_non_ha_frequentato'] = 'non ha frequentato';
                        $dati_studente_stampa['deroga'] = '';
                    }
                }
            } else {
                if ($scrutinato) {
                    $dati_studente_stampa['ha_non_ha_frequentato'] = 'ha frequentato';
                    $dati_studente_stampa['deroga'] = '';
                } else {
                    $dati_studente_stampa['ha_non_ha_frequentato'] = '';
                    $dati_studente_stampa['deroga'] = '';
                }
            }

            $parametri['array_dati'] = $dati_studente_stampa;
            $parametri['temp_dir'] = $dir;
            if ($destinazione_stampa != 'ZIP_WORD'){
                $parametri['formato_output'] = 'pdf';
            }
            if ($db_official != $db_key) {
                $parametri['anno_db'] = $db_key;
            }

            $filePath = nextapi_call('phpoffice/phpword/translateTemplateVariables/' . $dati_template['id_template'], 'POST', $parametri, $current_key);
            $array_file_stampa[] = $filePath;
            $array_per_sito[] = [
                'id_studente' => $studente['id_studente'],
                'cognome' => $studente['cognome'],
                'nome' => $studente['nome'],
                'registro' => $studente['registro'],
                'file'  => $filePath
            ];

            if ($quanti_studenti_stampare > 0 && count($array_file_stampa) == $quanti_studenti_stampare){
                break;
            }
        }

        switch ($destinazione_stampa){
            case 'ZIP':
            case 'ZIP_WORD':
                $nome .= '.zip';
                $ext = ($filePath['estensione']) ? $filePath['estensione'] : $template_word['estensione'];
                exec('zip -j \'' . $base_dir . $nome . '\' ' . $dir . '*' . "." . $ext);
                exec('rm -fr ' . $dir);

                $download_path = $rel_dir . $nome;

                //Reindirizzamento JavaScript
                echo "<HTML><SCRIPT>document.location='{$download_path}';</SCRIPT></HTML>";
                break;
            case "SITO_GENITORI":
                // Upload sito genitori
                $anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
                $anno_inizio = explode("/", $anno_scolastico)[0];
                $anno_fine = explode("/", $anno_scolastico)[1];
                switch ($periodo) {
                    case '7':
                    case '27':
                        $periodo_pagella = 'intermedia';
                        $periodo_pagella_descrittivo = 'intermedia';
                        break;
                    case '8':
                    case '28':
                        $periodo_pagella = 'pagellinast';
                        $periodo_pagella_descrittivo = 'secondo trimestre';
                        break;
                    case '9':
                    case '29':
                        $periodo_pagella = 'finale';
                        $periodo_pagella_descrittivo = 'finale';
                        break;
                    default:
                        $periodo_pagella = 'pagellina' . $periodo;
                        $periodo_pagella_descrittivo = 'pagellina' . $periodo;
                        break;
                }

                if ($tipo_abbinamento == 'RELIGIONE'){
                    $pref_nome_file = 'Pagella Religione ';
                } elseif ($tipo_abbinamento == 'ALTERNATIVA') {
                    $pref_nome_file = 'Pagella Religione ';
                } else {
                    $pref_nome_file = 'Pagella ';
                }
                $nome_pagella_per_file = $pref_nome_file . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
                $pagelle_generate = [];

                foreach ($array_per_sito as $studente_pagella) {
                    $id_stud_per_stampa_sito = $studente_pagella['id_studente'];
                    $file_name = $nome_pagella_per_file . '.pdf';
                    $pagella = $studente_pagella['file']['percorso_assoluto'] . $studente_pagella['file']['nome_file'];

                    $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
                    $content = file_get_contents($pagella);

                    // NOTA: presume vi sia al max un solo file per i criteri di ricerca
                    if ($file[0]['id']) {
                        messengerUpdateFile($file[0]['id'], $content);
                    } else {
                        // Destinatari: Studente + genitori
                        $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                        messengerSaveFile([
                            'content'    => $content,
                            'hidden'     => false,
                            'mime'       => 'application/pdf',
                            'name'       => $file_name,
                            'owner'      => messengerGetUserID($current_user),
                            'properties' => [
                                'userId' => $id_stud_per_stampa_sito,
                                'year'   => "{$anno_inizio}/{$anno_fine}",
                                'period' => $periodo_pagella
                            ],
                            'recipients' => $recipients,
                            'tags'       => ['PAGELLE']
                        ]);
                    }

                    if (file_exists($pagella)) {
                        $pagelle_generate[] = "{$studente_pagella['registro']}. {$studente_pagella['cognome']} {$studente_pagella['nome']}";
                    }
                }

                if (empty($pagelle_generate)) {
                    echo "Errore generazione pagelle (contattare l'Assistenza)";
                } else {
                    echo "Generate correttamente le pagelle per gli studenti:<br>" .  implode('<br>', $pagelle_generate);
                }
                break;
            case 'PDF':
            default:
                $nome .= '.pdf';
                $outputName = $dir . $nome;

                $cmd = "gs -q -dNOPAUSE -dBATCH -sDEVICE=pdfwrite -sOutputFile='$outputName' ";

                foreach ($array_file_stampa as $dati_file) {
                    $cmd .= $dati_file['percorso_assoluto'] . $dati_file['nome_file'] . " ";
                }

                $result = shell_exec($cmd);
                $download_path = $rel_dir . $rand_dir . '/' . $nome;

                //Reindirizzamento JavaScript
                echo "<HTML><SCRIPT>document.location='{$download_path}';</SCRIPT></HTML>";
                break;
        }
        break;

    case 'stampa_pagella_template_word':
        if (isset($mat_classi)) { // opzioni normali da sezione Stampe
            foreach ($mat_classi as $id_classe) {
                require 'adm/stampe/stampa_pagella_template_word.php';
            }
        } else { // Test / Dati da setup C15
            require 'adm/stampe/stampa_pagella_template_word.php';
        }
        break;
    default:
        break;
}

$template->assign('stato_secondario', $stato_secondario);
